<?php

/**
 * Description: 补丁更新记录表
 * User: <EMAIL>
 * Date: 2021/08/03 10:02
 * Version: $Id
 */

class DevicePatchCheck extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevicePatchCheck';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取指定设备未安装补丁详情.
     *
     * @param array $cond
     * @return false|mixed|null
     */
    public static function getDevicePatchCheck($cond = [])
    {
        if (!isset($cond['DeviceID'])) {
            return false;
        }
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        // OsType 0 windows、1 uos、2 kylin
        $where = "WHERE A.DeviceID = " . self::setData($cond['DeviceID']) . " and A.OsType = 0 ";
        $sql = "SELECT A.RevisionID,A.<PERSON>,B.IP,B.Mac,B.AscID,
                        func_getPatchKB(D.LastBundleRevID, D.RevisionID) AS KBIDS,
                        (Case When C.DownURL IS Null Then '' Else C.DownURL END) AS DownURL,
                        C.InstallCommand_Arguments FROM TDevicePatchCheck A 
                            LEFT JOIN TDevice B ON A.DeviceID=B.DeviceID 
                            LEFT JOIN TUpdateFile C ON A.RevisionID=C.RevisionID 
                            LEFT JOIN TUpdateDetail D ON A.RevisionID=D.RevisionID ".$where;
        cutil_php_log($sql, 'model_select');
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取指定Linux设备未安装补丁详情.
     *
     * @param array $cond
     * @return false|mixed|null
     */
    public static function getLinuxDevicePatchCheck($cond = [])
    {
        if (!isset($cond['DeviceID'])) {
            return false;
        }
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        // OsType 0 windows、1 uos、2 kylin
        $where = "WHERE A.DeviceID = " . self::setData($cond['DeviceID']) . " and A.OsType in (1,2) ";
        $sql = "SELECT A.RevisionID, A.DeviceID, B.IP, B.Mac, B.AscID, 
            C.PublicID AS KBIDS, C.DownURL , '' AS InstallCommand_Arguments 
            FROM TDevicePatchCheck A
	        LEFT JOIN TDevice B ON A.DeviceID = B.DeviceID
	        LEFT JOIN TLinuxPatchFile C ON A.RevisionID = C.RevisionID ".$where;
        cutil_php_log($sql, 'model_select');
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
