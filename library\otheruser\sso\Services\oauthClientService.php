<?php
/**
 * Description: Oauth
 * User: <EMAIL>
 * Date: 2022/02/24 10:52
 * Version: $Id$
 */

require_once(PATH_LIBRARY . '/otheruser/sso/common/ssoCommon.php');
require_once(PATH_LIBRARY . '/otheruser/sso/Interfaces/ssoAuth.php');

class oauthClientService implements ssoAuth
{
    public $params;

    public function __construct($params)
    {
        $this->params = $params;
        $this->params['expires'] = $this->params['expires'] ?? 3 * 60 * 60;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function logout()
    {
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        $url = ssoCommon::getBaseUrl();
        if (empty($basicConfig['LogoutUri'])) {
            $logoutURL = $basicConfig['LogoutUri'];
            $url .= $logoutURL;
        } else {
            T(21147005);
        }
        return $url;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function login($param = []): bool
    {
        if (!empty($param['ticketField'])) {
            $this->getAccessToken();
        }
        if ($this->isSSOLogin()) {
            return true;
        }

        $url = $this->setUrlParams('LoginUri');
        $url .= '?';
        $url .= $this->getSSOParams();
        $url .= '&redirect_uri=';
        $url .= ssoCommon::getRedirectUri(true, $this->params['isHttps']);
        $url .= '&state=' . Base64EnExt(
                json_encode(
                    [
                        'deviceId' => $this->params['deviceId'],
                        'resId' => $this->params['resId'],
                        'isClient' => $this->params['isClient'],
                        'resurl' => $this->params['resurl'],
                        'local_lguage_set' => $GLOBALS['CONFIG']['LANG_MAP'][LANG]
                    ],
                    JSON_THROW_ON_ERROR
                )
            );
        ssoCommon::recordLog('redirect_uri:' . $url);
        header('Location:' . $url);
        return false;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function checkLoginState()
    {
        if ($this->isSSOLogin()) {
            return ['username' => $this->dealUpUserInfo('UserName')];
        }
        T(21147001);
        return false;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function getUserInfo()
    {
        $this->getUserInfoByAccessCode();
        return $this->dealUpUserInfo();
    }

    /**
     * 预处理用户信息
     * @param string $key
     * @return array|mixed
     */
    public function dealUpUserInfo(string $key = '')
    {
        ssoCommon::recordLog('get user info');
        $upUserInfo = array();
        $synItemMapping = ssoCommon::getUserSynItemMapping();
        $reqParams = ssoCommon::getSSOConfig('response');

        $loginRes = ssoCommon::cacheGet($this->params['deviceId'], 'loginRes');
        ssoCommon::recordLog('dealUpUserInfo:loginRes:' . var_export($loginRes, true));
        if (empty($loginRes)) {
            return false;
        }
        foreach ($reqParams['ResParamKey'] as $k => $v) {
            $synItem = $synItemMapping[$reqParams['BindField'][$k]];
            $loginResJson = json_decode($loginRes, true);
            $isJson = false;
            if (!is_null($loginResJson)) {
                $isJson = true;
            }
            $loginResJson = $loginResJson['attributes'];
            if ($isJson) {
                $match[1] = $loginResJson[$v];
            } else {
                $reg = $reqParams['RepRule'][$k];
                if (empty($reg)) {
                    $reg = str_replace('#infogo:::Field', $reqParams['ResParamKey'][$k], $reqParams['RegMatch']);
                } else {
                    $reg = str_replace('#infogo:::Field', $reqParams['ResParamKey'][$k], $reg);
                }
                preg_match($reg, $loginRes, $match);
                ssoCommon::recordLog('key:' . $synItem . ' rule:' . $reg . ' match:' . var_export($match, true));
            }
            if (!empty($synItem) && !empty($match[1])) {
                $upUserInfo[$synItem] = $match[1];
            }
        }
        if (empty($upUserInfo['TrueNames'])) {
            $upUserInfo['TrueNames'] = $upUserInfo['UserName'];
        }
        $upUserInfo['UserName'] = $upUserInfo['userId'];
        ssoCommon::recordLog('UpUserInfo:' . var_export($upUserInfo, true));
        if (!empty($upUserInfo['UserName'])) {
            ssoCommon::cacheSet(
                $this->params['deviceId'],
                [
                    'userInfo' => $upUserInfo,
                    'userName' => addslashes($upUserInfo['UserName'])
                ],
                $this->params['expires']
            );
        }

        if (!empty($key)) {
            return $upUserInfo[$key];
        }
        return $upUserInfo;
    }

    /**
     * 通过AccessToken获取用户信息
     * @throws Exception
     */
    public function getUserInfoByAccessCode(): void
    {
        $tokenStr = $this->getAccessToken();
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');

        if (empty($basicConfig['ProfileUri'])) {
            ssoCommon::cacheSet(
                $this->params['deviceId'],
                ['loginRes' => false],
                $this->params['expires']
            );
        }

        // 通过配置的用户信息获取
        if ($basicConfig['ProfileUri'] !== '###infogo:::JwtClaims') {
            $url = $this->setUrlParams('ProfileUri');
            $url .= '?' . $tokenStr; //使用token 进行认证
            ssoCommon::recordLog('request Url:' . $url);
            $res = curl($url, 'GET', '', 10, 30);
            if ($res['code'] !== 200) {
                T(21147008);
            }
            ssoCommon::recordLog('get UserInfo source res:' . $res['data']);
            $userInfo = json_decode($res['data'], true);
            ssoCommon::recordLog('userInfo:' . var_export($userInfo, true));

            // 缓存信息
            ssoCommon::cacheSet(
                $this->params['deviceId'],
                ['loginRes' => $res['data']],
                $this->params['expires']
            );
        } else {
            // 通过jwtClaims解析用户信息
            $jwtStrArr = explode('=', $tokenStr);
            $res = self::getUserInfoByJwtClaims(@$jwtStrArr[1]);
            ssoCommon::recordLog('JwtClaims userInfo:' . var_export($res, true));

            // 缓存信息
            ssoCommon::cacheSet(
                $this->params['deviceId'],
                ['loginRes' => $res],
                $this->params['expires']
            );
        }
    }

    /**
     * @inheritDoc
     * @return bool
     */
    public function isSSOLogin(): bool
    {
        $accessToken = ssoCommon::cacheGet($this->params['deviceId'], 'accessToken');
        ssoCommon::recordLog('accessToken:' . var_export($accessToken, true));
        if (!empty($accessToken)) {
            return true;
        }
        return false;
    }

    /***
     * 获取请求参数
     * @param $type String string返回字符串形式 array返回数组形式
     * @return string|array
     */
    public function getSSOParams(string $type = 'string')
    {
        $paramUrl = [];

        $reqParams = ssoCommon::getSSOConfig('requestParam');
        foreach ($reqParams['ReqParamKey'] as $index => $paramKey) {
            if (!empty($paramKey) && !empty($reqParams['ReqParamVal'][$index])) {
                $paramUrl[$paramKey] = $reqParams['ReqParamVal'][$index];
            }
        }
        if ($type === 'string') {
            return http_build_query($paramUrl);
        }
        return $paramUrl;
    }

    /***
     * 生成请求基础url
     * @param string $key
     * @return bool|string
     */
    public function setUrlParams(string $key = '')
    {
        $url = ssoCommon::getBaseUrl();
        if (!$url) {
            ssoCommon::recordLog('Err: Request address is not specified,Quit!');
            return false;
        }
        if (!empty($key)) {
            $baseConfig = ssoCommon::getSSOConfig('basicConfig');
            $url .= $baseConfig[$key];
        }
        return $url;
    }

    /**
     * 获取AccessToken
     * @return mixed|string
     * @throws Exception
     */
    public function getAccessToken()
    {
        $accessToken = ssoCommon::cacheGet($this->params['deviceId'], 'accessToken');

        try {
            // 如果存在ticketField，则重新获取授权
            if (empty($accessToken) || !empty($this->params['ticketField'])) {
                $url = $this->setUrlParams('ValidateUri');

                $ssoParam = $this->getSSOParams('array');
                $basicConfig = ssoCommon::getSSOConfig('basicConfig');
                $ssoParam[$basicConfig['TicketField']] = $this->params['ticketField'];
                // ssoCommon::cacheTicket($this->params['deviceId'], '', 'oauth');
                $ssoParam['code'] = $this->params['ticketField']; // 兼容杭氧
                $ssoParam['redirect_uri'] = ssoCommon::getRedirectUri(false, $this->params['isHttps']);
                $ssoParam['state'] = $this->params['deviceId'];
                ssoCommon::recordLog('ssoParam:' . var_export($ssoParam, true));
                ssoCommon::recordLog('url:' . $url);

                // 请求oauth服务器
                $res = curl($url, 'POST', $ssoParam, 10, 30);
                ssoCommon::recordLog('get accessToken res:' . var_export($res, true));

                if ($res['code'] !== 200) {
                    T(21147006);
                }
                if (!empty($res['data'])) {
                    $accessToken = $res['data'];
                    if ($tokenItem = $this->getResRuleByBindType('accessToken')) {
                        preg_match($tokenItem['rule'], $res['data'], $match);
                        ssoCommon::recordLog(
                            'rule:' . $tokenItem['rule'] . ' str:' . $res['data'] . ' match:' . var_export($match, true)
                        );
                        if (is_array($match) && !empty($match[1])) {
                            $accessToken = "{$tokenItem['key']}={$match[1]}";
                        } else {
                            T(21147006);
                        }
                    }

                    // 校验成功，不需要，防止每次校验
                    $this->params['ticketField'] = '';
                    // 缓存
                    ssoCommon::cacheSet(
                        $this->params['deviceId'],
                        ['accessToken' => $accessToken],
                        $this->params['expires']
                    );

                    // 获取失效时间
                    $expiresInRule = '/"expires(?:_in)?":(.*?),/'; // 兼容杭氧
                    preg_match($expiresInRule, $res['data'], $match);
                    ssoCommon::recordLog('expires:' . var_export($match, true));
                    if (is_array($match) && !empty($match[1])) {
                        $this->params['expires'] = (int)$match[1];
                    }
                }
            }
        } catch (Exception $e) {
            ssoCommon::cacheDelete($this->params['deviceId']);
            throw $e;
        }
        return $accessToken;
    }

    /***
     * 获取返回规则通过绑定类型
     * @param string $type
     * @return array|bool
     */
    public function getResRuleByBindType($type = '')
    {
        $reqParams = ssoCommon::getSSOConfig()['response'];
        $index = array_search($type, $reqParams['BindField']);
        if (empty($type) || empty($reqParams['ResParamKey'][$index])) {
            return false;
        }
        $rule = $reqParams['RegMatch'];
        if (!empty($reqParams['RepRule'][$index])) {
            $rule = $reqParams['RepRule'][$index];
        }
        $rule = str_replace('#infogo:::Field', $reqParams['ResParamKey'][$index], $rule);
        return array('key' => $reqParams['ResParamKey'][$index], 'rule' => $rule);
    }

    /***
     * 通过Claims解析用户信息
     * @param $jwtStr
     * @return string
     */
    public static function getUserInfoByJwtClaims($jwtStr)
    {
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        ssoCommon::recordLog('pubKeyStr:' . $basicConfig['JwtPubKey'] . '  jwtStr:' . $jwtStr);
        $payload = self::getJwtClaims($jwtStr, $basicConfig['JwtPubKey']);
        return json_encode($payload);
    }

    /***
     * 获取jwt加密信息
     * @param string $jwtStr
     * @param string $pubKeyStr
     * @return array|object
     */
    public static function getJwtClaims($jwtStr, $pubKeyStr)
    {
        if (empty($jwtStr) || empty($pubKeyStr)) {
            return array();
        }
        $pubKeyArr = json_decode($pubKeyStr, true);
        $pem = self::createPemFromModulusAndExponent($pubKeyArr['n'], $pubKeyArr['e']);
        $payload = Firebase\JWT\JWT::decode(
            $jwtStr,
            $pem,
            array('ES384', 'ES256', 'HS256', 'HS384', 'HS512', 'RS256', 'RS384', 'RS512')
        );
        ssoCommon::recordLog('payload:' . var_export($payload, true));
        return $payload;
    }

    /**
     *获取 public key
     * @param $n
     * @param $e
     * @return string
     */
    public static function createPemFromModulusAndExponent($n, $e)
    {
        $modulus = Firebase\JWT\JWT::urlsafeB64Decode($n);
        $publicExponent = Firebase\JWT\JWT::urlsafeB64Decode($e);
        $components = array(
            'modulus'        => pack('Ca*a*', 2, self::encodeLength(strlen($modulus)), $modulus),
            'publicExponent' => pack('Ca*a*', 2, self::encodeLength(strlen($publicExponent)), $publicExponent)
        );
        $RSAPublicKey = pack(
            'Ca*a*a*',
            48,
            self::encodeLength(strlen($components['modulus']) + strlen($components['publicExponent'])),
            $components['modulus'],
            $components['publicExponent']
        );


        // sequence(oid(1.2.840.113549.1.1.1), null)) = rsaEncryption.
        $rsaOID = pack('H*', '300d06092a864886f70d0101010500'); // hex version of MA0GCSqGSIb3DQEBAQUA
        $RSAPublicKey = chr(0) . $RSAPublicKey;
        $RSAPublicKey = chr(3) . self::encodeLength(strlen($RSAPublicKey)) . $RSAPublicKey;

        $RSAPublicKey = pack(
            'Ca*a*',
            48,
            self::encodeLength(strlen($rsaOID . $RSAPublicKey)),
            $rsaOID . $RSAPublicKey
        );

        return "-----BEGIN PUBLIC KEY-----\r\n" .
            chunk_split(base64_encode($RSAPublicKey), 64) .
            '-----END PUBLIC KEY-----';
    }

    /***
     * 计算长度
     * @param $length
     * @return string
     */
    public static function encodeLength($length)
    {
        if ($length <= 0x7F) {
            return chr($length);
        }
        $temp = ltrim(pack('N', $length), chr(0));
        return pack('Ca*', 0x80 | strlen($temp), $temp);
    }
}