<?php
/**
 * Description: 用户名密码认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: UserAuthService.php 172249 2022-03-29 08:39:31Z chenpan $
 */

namespace Services\Auth\Services;

use DeviceExpandModel;
use DictModel;
use Exception;
use lib_alarm;
use Services\Auth\Interfaces\AuthServiceInterface;

class UserAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'User';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        $this->userTypeName = L(21120051);
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['redirectUri'] = Base64EnExt(request('redirect_uri', 'request',""));
        if (empty($this->params['formMobile'])) {
            $this->params['userName'] = trim(addslashes(Base64DeExt($this->params['userName'])));
            $this->params['password'] = Base64DeExt($this->params['password']);
            $this->params['idp'] = !empty($this->params['idp']) ? Base64DeExt($this->params['idp']) : '';
            $this->params['tokenId'] = !empty($this->params['tokenId']) ? Base64DeExt($this->params['tokenId']) : '';

            $priKey = getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');

            /** 经确认非密文解密后返回原文 兼容base64 */
            openssl_private_decrypt($this->params['password'], $this->params['password'], $priKey, OPENSSL_PKCS1_PADDING);
        }
        if (trim($this->params['password']) == "") {
            T(21120005);
        }
        $item = DictModel::getOneItem('AUTHPARAM', 'verifyCode');
        if (!empty($item['ItemValue'])) {
            $this->verificationCode();
        }
        return $this->params;
    }

    /**
     * 验证
     * @throws Exception
     */
    public function verificationCode()
    {
        if (!empty($this->params['qrlogin']) || !empty($this->params['dingQrlogin']) ||
            !empty($this->params['weworkQrlogin']) || !empty($this->params['feishuQrlogin']) ||
            !empty($this->params['fingerBind']) || !empty($this->params['noAuthCode'])) {
            return;
        }

        // 身份认证验证码
        $verifyCode = trim($this->params['verifyCode']);
        if (empty($verifyCode)) {
            T(21120033);
        }
        $redisVerifyCode = cache_get_info('verifyCode', (int)$this->deviceId);
        $havVerifyCode = urldecode($redisVerifyCode['vcode']);
        if (strtolower($verifyCode) !== $havVerifyCode) {
            cutil_php_log("{$verifyCode}<>{$havVerifyCode}", $this->logFileName);
            T(21120034);
        }
    }

    /**
     * 获取验证服务
     *
     * @return array
     * @throws Exception
     */
    public function getAuthServer(): array
    {
        if (!empty($this->params['qrlogin'])) {
            return ["Localhost"];
        }
        if (!empty($this->params['dingQrlogin'])) {
            return ["DingTalk"];
        }
        if (!empty($this->params['weworkQrlogin'])) {
            return ["WeWork"];
        }
        if (!empty($this->params['feishuQrlogin'])) {
            return ["FeiShu"];
        }
        $userDict = DictModel::getAll("User");
        $servers = [];
        $authServers = explode("|", $userDict['AuthServer']);
        if (!empty($userDict['FirstAuthServer'])) {
            $servers[] = $userDict['FirstAuthServer'];
            foreach ($authServers as $server) {
                if ($server != $userDict['FirstAuthServer']) {
                    $servers[] = $server;
                }
            }
        }
        $authserver = !empty($this->params['authserver']) ? Base64DeExt($this->params['authserver'], true) : '';
        if (!empty($authserver)) {
            // 校验认证服务器是否开启
            if (!in_array($authserver, $servers)) {
                T(21137017);
            }
            return [$authserver];
        }
        return $servers;
    }

    /**
     * 验证密码复杂度
     * @param $changePwdParams
     * @param $pwd
     * @param $user
     * @return array
     * @throws Exception
     */
    public function verifyPassword($changePwdParams, $pwd, $user)
    {
        $ret = array('pwdCheckResCode' => STRING_TRUE, 'pwdCheckResMsg' => '' );

        //不需要验证密码复杂度的时候
        if (!isset($changePwdParams['PassWordType']) || !$changePwdParams['PassWordType']) {
            return $ret;
        }

        //密码字符长度判断
        if (isset($changePwdParams['PassWordLength']) && $changePwdParams['PassWordLength'] > 0
            && $changePwdParams['PassWordLength'] > mb_strlen($pwd, 'UTF-8')) {
            $ret['pwdCheckResCode'] = STRING_FALSE;
            $ret['pwdCheckResMsg'] .=  L(21133007) . $changePwdParams['PassWordLength']  . L(21133008).', '  ;
        }

        //弱密码判断(管理员手动配置的禁用密码)
        if (isset($changePwdParams['errPassword']) && !empty($changePwdParams['errPassword'])) {
            $errPasswordArr = explode('||||', $changePwdParams['errPassword']);
            if (is_array($errPasswordArr) && !empty($errPasswordArr) && in_array($pwd, $errPasswordArr)) {
                $ret['pwdCheckResCode'] = STRING_FALSE;
                $ret['pwdCheckResMsg'] .= L(21133009).$pwd.', ';
            }
        }

        //不包含用户名
        if (isset($changePwdParams['dbUserName']) && $changePwdParams['dbUserName'] == 1) {
            if (false !== strpos((string)$pwd, (string)$user)) {
                $ret['pwdCheckResCode'] = STRING_FALSE;
                $ret['pwdCheckResMsg'] .= L(21133010).', ';
            }
        }

        //是否包含重复字母和数字
        if (isset($changePwdParams['dbStr']) && $changePwdParams['dbStr'] == 1) {
            if (preg_match("/([0-9a-zA-Z])\\1{1}/", $pwd)) {
                $ret['pwdCheckResCode'] = STRING_FALSE;
                $ret['pwdCheckResMsg'] .= L(21133011).', ';
            }
        }

        // 必须多少种类的字符。
        if (isset($changePwdParams['PassWordsetType']) && $changePwdParams['PassWordsetType'] > 0) {
            $isSetNub = 0;
            $isNum = false;
            $isLetter = false;

            if (preg_match("/[0-9]+/", $pwd)) {
                $isSetNub += 1;
                $isNum = true;
            }

            if (preg_match("/[A-Z]+/", $pwd)) {
                $isSetNub += 1;
                $isLetter = true;
            }

            if (preg_match("/[a-z]+/", $pwd)) {
                $isSetNub += 1;
                $isLetter = true;
            }

            if (preg_match("/\/|\~|\!|\@|\#|\\$|\%|\^|\&|\*|\+|\{|\}|\:|\<|\>|\?|\[|\]|\,|\s|\.|\/|\;|\-|\'|\`|\=|\\\|\|/", $pwd)) {
                $isSetNub += 1;
            }

            if ($changePwdParams['PassWordsetType'] == 1) {
                if (!$isNum || !$isLetter) {
                    $ret['pwdCheckResCode'] = STRING_FALSE;
                    $ret['pwdCheckResMsg'] .= L(21133012) .', ';
                }
            } else {
                if ($isSetNub < 3) {
                    $ret['pwdCheckResCode'] = STRING_FALSE;
                    $ret['pwdCheckResMsg'] .= L(21133013) .', ';
                }
            }
        }

        // 如果最后一个是,则替换为.
        if (substr(trim($ret['pwdCheckResMsg']), -1) === ',') {
            $ret['pwdCheckResMsg'] = substr($ret['pwdCheckResMsg'], 0, -2);
        }

        if (!$ret['pwdCheckResCode']) {
            $ret['pwdCheckResMsg'] = L(21133014) .'(' .$ret['pwdCheckResMsg']. ')';
        }

        return $ret;
    }

    /**
     * 获取敲门认证的参数
     *
     * @param $Data
     *
     * @return mixed
     * @throws Exception
     */
    public function getFwknopData($Data)
    {
        $params = parent::getFwknopData($Data);
        // 敲门的不走rsa加密
        $params['form_mobile'] = 1;
        switch($params['authserver']) {
            case 'DingTalk':
                $params['dingqrlogin'] = 1;
                break;
            case 'WeWork':
                $params['weworkqrlogin'] = 1;
                break;
            case 'FeiShu':
                $params['feishuqrlogin'] = 1;
                break;
            case 'Qrcode':
                $params['qrlogin'] = 1;
                break;
        }
        return $params;
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws \Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        parent::authAfter($data, $userInfo, $authType);
        if (!empty($this->params['syncToDevice'])) {
            $this->copyDevice($userInfo);
            if (!empty($data['DeviceExpand'])) {
                $this->copyDeviceExpand($data['DeviceExpand']);
            }
        }
        if (!empty($this->params['sendMessage'])) {
            $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>ShowUserOneMsg</WhatToDo><Msg>{$this->params['sendMessage']}</Msg></ASM>";
            $msgSenderInfo = ['DeviceId' => $this->deviceId, 'Content' => $content];
            lib_alarm::createMsgSender($msgSenderInfo);
        }
    }

    /**
     * 同步扩展信息
     *
     * @param $DeviceExpand
     *
     * @throws Exception
     */
    public function copyDeviceExpand($DeviceExpand)
    {
        foreach ($DeviceExpand as $expand) {
            $expand_str = utf8ToGbk($expand[1]);
            $Remark = L(21106057) . $expand_str . L(21106058);
            DictModel::updateItem('CLIENTCHECK', "Require{$expand[2]}", "0|{$expand_str}|text||", $Remark);
            //添加扩展信息
            $expandInfo = DeviceExpandModel::getOne($this->deviceId);
            $params = [$expand[2] => $expand[0]];
            if (empty($expandInfo)) {
                $params['DeviceID'] = $this->deviceId;
                DeviceExpandModel::insert($params);
            } else {
                DeviceExpandModel::update($this->deviceId, $params);
            }
        }
        sendSocketFunc('127.0.0.1', '36532', PATH_HTML . "/bin/UpDevExplend.php");
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'authserver', 'user_name', 'password', 'check_code', 'appid', 'redirect_uri'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'userName', 'password', 'qrlogin', 'callfrom', 'fwknopAuth', 'redirectUri'];
    }
}
