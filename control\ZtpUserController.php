<?php
/**
 * Description: 零信任用户
 * User: <EMAIL>
 * Date: 2022/05/06 23:32
 * Version: $Id$
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/ZtpController.php";

class ZtpUserController extends ZtpController
{

    /**
     * 不检查设备ID的方法 如：['auth' => true]
     * @var array
     */
    public $nocheck = ['userinfo' => true];

    /**
     * 获取用户信息
     *
     * @return mixed
     */
    public function userinfo()
    {
        return $this->session;
    }

    /**
     * 用户日志
     *
     * @return mixed
     */
    public function loglist()
    {
        $Keyword = request('keyword', 'request');
        $Start = request('start', 'request', 0, 'int');
        $Limit = request('limit', 'request', 100, 'int');
        $Cond = ['UserID' => $this->userId];
        if (!empty($Keyword)) {
            $Cond['Keyword'] = $Keyword;
        }
        $List = LoginServiceProvider::getUserLogList($Cond, $Start, $Limit);
        $Count = LoginServiceProvider::getUserLogCount($Cond);
        return ['total' => $Count, 'list' => $List];
    }
}
