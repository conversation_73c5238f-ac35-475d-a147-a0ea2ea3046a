<?php
/**
 * Description: 双因子/ukey辅助短信认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: SmsAuthService.php 157492 2021-09-22 14:30:26Z duanyc $
 */

namespace Services\Auth\Services;

class SmsAuthService extends MobileAuthService
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'SMS';

    /**
     * 初始化
     *
     * @param $params
     * @throws \Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
        $this->params['authType'] = $this->params['beforeAuthType'] ?: 'User';
    }

    /**
     * 获取验证服务
     *
     * @return array
     */
    public function getAuthServer(): array
    {
        return ["Sms"];
    }

    /**
     * 解析参数
     * @return array
     * @throws \Exception
     */
    public function parseParams()
    {
        $this->params['userName'] = Base64DeExt($this->params['userName'], true);
        return $this->params;
    }

    /**
     * 获取认证的字段
     * @return array
     */
    public function getAuthColumns()
    {
        return ['authType', 'beforeAuthType', 'mobilePhone', 'checkCode'];
    }
}
