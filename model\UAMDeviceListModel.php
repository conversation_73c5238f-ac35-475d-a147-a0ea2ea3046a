<?php

/**
 * Description: usb设备表
 * User: <EMAIL>
 * Date: 2021/08/16 10:02
 * Version: $Id
 */

class UAMDeviceListModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUAM_DeviceList';
    public const PRIMARY_KEY = 'UDeviceID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 分布式场景下数据漫游涉及到的相关表.
     * @param string $type
     * @return array
     */
    protected static function getTables(string $type = 'tables'):array
    {
        $tables = [
            'TUAM_DeviceList' => 'UDeviceID',
        ];

        // 需要将查询条件DeviceID替换的表及替换值
        $replaceTablesKey = [

        ];

        // 需要获取多行数据的表 $tables主键不能为空
        $multiRowTables = [

        ];

        return ${$type} ?? [];
    }

    /**
     * 插入多条数据条目（分布式数据漫游场景）
     * @param array $data
     * @param $DeviceID
     * @return bool|int
     */
    public static function insertIntoTable(array $data,$DeviceID)
    {
        if (is_array($data)) {
            /* 从asm上获取到了数据 */
            $NewUDeviceID = 0;
            $multiRowTables = self::getTables('multiRowTables');
            $tables = self::getTables('tables');
            foreach ($data as $key => $items) {
                if (in_array($key, $multiRowTables, true)) {
                    foreach ($items as $val) {
                        self::insertRow($key, $val, $NewUDeviceID, $DeviceID,$tables);
                    }
                } else {
                    $val = $items;
                    self::insertRow($key, $val, $NewUDeviceID, $DeviceID,$tables);
                }
            }
            if ($NewUDeviceID > 0) {
                return $NewUDeviceID;
            }
        }
        return false;
    }

    /**
     * 插入一条数据（分布式数据漫游场景）
     * @param $table
     * @param $data
     * @param $NewUDeviceID
     * @param $DeviceID
     * @param $tables
     */
    protected static function insertRow($table, $data, &$NewUDeviceID, $DeviceID, $tables = []): void
    {
        if (empty($tables)) {
            $tables = self::getTables('tables');
        }
        $_table = hlp_common::getSplitTable(null, $table);
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if ($key === 'DeviceID') {
                    $data[$key] = $DeviceID;
                }
            }

            $result = lib_database::insert($data,$_table['name'],$_table['index']);
            $lastId = 0;
            if ($result) {
                $lastId = lib_database::insertId();
            }
            cutil_php_log("lastId：" . var_export($lastId, true), 'get_dasm_usb_info');

            /* 保留插入的id值，用于计算关联关系 */
            if ($table === 'TUAM_DeviceList') {
                $NewUDeviceID = $lastId;
            }

            if (!empty($tables[$table])) {
                /* 将更新时间后延，让dasc强制上报一次更新，更新asm上的DASCID */
                self::$data = [];
                $sql = 'UPDATE ' . $table . ' SET UpdateTime=DATE_ADD(NOW(), interval 8 second) WHERE ' . $tables[$table] . '=' . self::setData($lastId);
                lib_database::query($sql,$_table['index'], false, self::$data);
            }
        } else {
            cutil_php_log("传递非数组类型数据，疑似出错：" . var_export($data, true), 'get_dasm_usb_info');
        }
    }

    /**
     * 获取USB设备信息
     * @param $UsbID
     * @return array
     */
    public static function getAllUsbInfo(string $UsbID): array
    {
        $data = array();
        $tables = self::getTables('tables');
        $multiRowTables = self::getTables('multiRowTables');

        foreach ($tables as $table => $key) {
            cutil_php_log("table:" . $table.' key:'.$key,'get_dasm_usb_info');
            $_table = hlp_common::getSplitTable(null, $table);
            if ($key !== '' && in_array($table, $multiRowTables, true)) {
                self::$data = [];
                $sql = 'SELECT * FROM ' . $_table['name'] . ' WHERE UsbID=' . self::setData($UsbID) . ' ORDER BY InsertTime DESC';
                $records = \lib_database::getAll($sql,$_table['index'], false, self::$data);
                if (is_array($records)) {
                    foreach ($records as $record) {
                        $tmpkey = $record[$key];
                        $record['DAscID'] = '11:11:11:11:11:11';
                        $record['OrigID'] = $tmpkey;
                        unset($record[$key], $record['UpdateTime']);
                        $data[$table][$tmpkey] = $record;
                    }
                }
            } else {
                self::$data = [];
                $sql = 'SELECT * FROM ' . $_table['name'] . ' WHERE UsbID=' . self::setData($UsbID) . ' ORDER BY InsertTime DESC';
                $record = \lib_database::getOne($sql,$_table['index'], false,1, self::$data);
                if (is_array($record)) {
                    if ($key !== '') {
                        $record['DAscID'] = '11:11:11:11:11:11';
                        $record['OrigID'] = $record[$key];
                        unset($record[$key]);
                    }
                    unset($record['UpdateTime']);
                    $data[$table] = $record;
                }
            }
        }
        return $data;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['UsbID'])) {
            $where .= "AND UsbID = ".self::setData($cond['UsbID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
