<?php

/**
 * Description: 获取钉钉用户信息
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: DingtalkController.php 173127 2022-04-12 07:56:33Z yinbz $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class DingtalkController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['user' => true, 'jump' => true, 'auth' => true];

    /**
     * 获取钉钉用户信息，对应老交易 get_dingtalk_user
     *
     * @return array
     * @throws Exception
     */
    public function user()
    {
        $deviceType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if ($deviceType === 'out') {
            return $this->userOut();
        }
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $code = request('code', 'request');
        // 准入检查是否扫码成功
        if ($action === 'check') {
            return $this->userCheck($deviceId);
        }
        $action != "verify" && hlp_check::checkEmpty($deviceId);
        hlp_check::checkEmpty($code);
        if ($action == 'verify') {
            $time = request('time', 'request');
            return $this->userVerify($code, $time);
        }
        $this->userCallback($deviceId, $code);
        return [];
    }

    /**
     * 外置服务器
     *
     * @return array
     * @throws Exception
     */
    private function userOut()
    {
        cutil_php_log(lib_request::$requests, 'Dingtalk');
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $code = request('code', 'request');
        $time = request('time', 'request');
        hlp_check::checkEmpty($deviceId);
        hlp_check::checkEmpty($time);
        // 准入检查是否扫码成功
        if ($action === 'check') {
            $data = cache_get_info('dingtalk_out', "{$deviceId}_{$time}");
            if (empty($data['token'])) {
                return ['state' => false, 'type' => 'Prescaned'];
            }
            cache_del_info('dingtalk_out', "{$deviceId}_{$time}");
            return ['state' => true, 'type' => 'Scaned', 'token' => $data['token'], 'username' => ''];
        }
        hlp_check::checkEmpty($code);
        $data = ['token' => $code];
        $res = cache_set_info('dingtalk_out', "{$deviceId}_{$time}", $data, 300);
        return ['res' => $res];
    }

    /**
     * 用户校验
     * @param $code
     * @param $time
     * @return array
     * @throws Exception
     */
    public function userVerify($code, $time): array
    {
        $config = cache_get_info("asm_database_", "AuthDomainTest_DingTalk_" . $time);
        hlp_check::checkEmpty($config['appid']);
        hlp_check::checkEmpty($config['secret']);
        hlp_check::checkEmpty($config['version']);
        $tmpConfig = ['appkey' => $config['appid'], 'secret' => $config['secret'], 'version' => $config['version'], 'from' => 'backend'];
        $data = ['servicePrefix' => 'DingTalk', 'code' => $code, 'isAgent' => false, 'tmpConfig' => $tmpConfig];
        $userinfo = AuthServiceProvider::callbackRpcAuthServer($data);
        if (is_array($userinfo) && isset($userinfo['ID'])) {
            cache_set_info("asm_database_", "AuthDomainTest_DingTalk_" . $time, ['user' => $userinfo['ID']], 300);
        }
        return [];
    }

    /**
     * 检查
     *
     * @param $deviceId
     * @return array
     */
    private function userCheck($deviceId)
    {
        $return = ['state' => false];
        try {
            if (empty($deviceId)) {
                T(21123005);
            }
            $cond = ['LikeToken' => '|||', 'DeviceID' => $deviceId, 'Type' => 'DingTalk'];
            $userList = QrcodeServiceProvider::getQrcodeUserList($cond);
            if (empty($userList)) {
                T(21123004);
            }
            foreach ($userList as $user) {
                if (!empty($user['UserID'])) {
                    $return['state'] = true;
                    $return['type'] = "Scaned";
                    $return['token'] = $user['Token'];
                    $return['username'] = AuthServiceProvider::getUserNameById($user['UserID']);
                    break;
                }
            }
            $data = cache_get_info('dingtalk', 'code');
            if (!empty($data['code'])) {
                cache_del_info('dingtalk', 'code');
                T($data['code']);
            }
            if ($return['state'] == false) {
                QrcodeServiceProvider::deleteQrcodeUser($deviceId);
                T(21123006);
            }
        } catch (Exception $e) {
            $typeConfig = [21123005 => 'EmptyDeviceID', 21123004 => 'NoSuchDeviceToken',
                21123006 => 'Prescaned', 21123007 => 'Prescaned'];
            $code = $e->getCode();
            $return['type'] = isset($typeConfig[$code]) ? $typeConfig[$code] : '';
            $return['message'] = $e->getMessage();
        }
        return $return;
    }

    /**
     * 回调
     *
     * @param $deviceId
     * @param $code
     *
     * @throws Exception
     */
    private function userCallback($deviceId, $code)
    {
        $GLOBALS['HeaderContentType'] = 'text/html';
        $code = trim($code);
        $data = ['servicePrefix' => 'DingTalk', 'code' => $code, 'isAgent' => false];
        $authUser = AuthServiceProvider::callbackRpcAuthServer($data);
        $params = ['DeviceID' => $deviceId, 'Type' => 'DingTalk'];
        if (!empty($authUser)) {
            /* 记录二维码扫描 */
            $token = $authUser['openid'];
            $params['Token'] = $token;
            $params['UserID'] = $authUser['ID'];
            QrcodeServiceProvider::addQrcodeUser($params);
            AuthServiceProvider::syncDeviceInfo($deviceId, $authUser);
            exit();
        } else {
            $params['Token'] = '|||';
            QrcodeServiceProvider::addQrcodeUser($params);
            T(21123004);
        }
    }

    /**
     * 钉钉跳转，对应老交易 /a/getotheruser_int/dingtalk/dingtalk.php
     * /access/dingtalk/jump
     *
     * @throws Exception
     */
    public function jump(): void
    {
        // 钉钉微应用共用该地址获取临时码
        $module = request('module', 'request', '', 'string');
        if ($module === 'resource') {
            $this->ddlogin();
        }

        $authKey = request('authKey', 'request', '', 'string');
        $dingtalkcode = request('dingtalkcode', 'request', '', 'string');
        $config = AuthServiceProvider::getAuthDomainConfig('DingTalk');
        $corpId = $config['corpId'];
        $closewindow = request('close', 'request', 0, 'int');
        $debug = request('debug', 'request', 0, 'int');
        $formUrl = "/mobile/ui/wel.html?route_type=dingtalk&" . @htmlspecialchars(hlp_common::otherAuthParams());
        if (!empty($authKey)) {
            if (!empty($dingtalkcode)) {
                QrcodeServiceProvider::saveAuthCode($authKey, $dingtalkcode);
                gotoUrl("/mobile/ui/wel.html?route_type=dingtalk&authKey={$authKey}#/access/authorizeSuccess");
            } else {
                $formUrl = "/access/dingtalk/jump?authKey={$authKey}&result=1";
            }
        }
        hlp_page::showDingtalk($corpId, $closewindow, $formUrl, $debug);
    }

    /**
     * 钉钉单点登录跳转地址
     * 通过该地址：/access/dingtalk/jump访问
     * @throws \Exception
     */
    private function ddlogin(): void
    {
        $ZTP_Cookie = request('ZTP_Cookie', 'cookie');
        try {
            [$session] = LoginServiceProvider::checkCookieLogin($ZTP_Cookie);
        } catch (Exception $e) {
            header("HTTP/1.1 200", true, 200);
            $session = false;
        }
        $resDomain = request('resDomain', 'request');
        $Resource = AgentServiceProvider::getResourceByRefer("https://{$resDomain}", 'microApp');
        if (empty($Resource)) {
            gotoErrorUrl("0x1021");
        }
        $config = AuthServiceProvider::getAuthDomainConfig('DingTalk');
        $corpId = $config['corpId'];
        hlp_page::showDingtalkLogin($corpId, $session);
    }

    /**
     * 钉钉自动认证并跳转到资源
     * @throws \Exception
     */
    public function auth(): void
    {
        try {
            $params = [];
            $resDomain = request('resDomain', 'request');
            $ClickID = request('ClickID', 'request');
            $Resource = AgentServiceProvider::getResourceByRefer("https://{$resDomain}", 'microApp');
            if (empty($Resource)) {
                T(21148024);
            }
            $Resource['OS_TYPE'] = request('osType', 'request');
            $ZTP_Cookie = request('ZTP_Cookie', 'cookie');
            try {
                [$session] = LoginServiceProvider::checkCookieLogin($ZTP_Cookie);
            } catch (Exception $e) {
                $params['deviceId'] = 0;
                $params['authType'] = 'DingTalk';
                $params['servicePrefix'] = 'DingTalk';
                $code = request('code', 'request');
                hlp_check::checkEmpty($code);
                lib_request::$requests['dingtalkcode'] = $code;
                $authService = AuthServiceProvider::initAuthService($params);
                $authService->parseParams();
                $authServers = $authService->getAuthServer();
                $authParams = $authService->getAuthParams();
                $data = AuthServiceProvider::validateRpcAuthServer($authServers, $authParams, $authService);
                if (empty($data['UserID'])) {
                    T(21123006);
                }
                $data['AuthInterval'] = 60; // 认证超时时间
                $data['DeviceID'] = -time(); // 设置随机设备ID
                $userInfo = $authService->getOneUserinfo($data['UserID']);
                $data['Token'] = LoginServiceProvider::getToken($data, $params);
                $authService->setCommonInfo($data, $userInfo, $params);
                ResourceServiceProvider::setUserPower($data['Token']);
                $session = LoginServiceProvider::setCookieLogin($data);
                cutil_php_log(['AuthApp data', json_encode($data), json_encode($session)], 'dingtalk');
                AuthServiceProvider::setSessionStatus($data['Token']);
                OtherAppUserTokenRedis::zAddMember(time(), $data['Token']);
                sleep(2);
            }
            $AuthApp = json_decode($session['AuthApp'], true);
            cutil_php_log(['AuthApp', json_encode($AuthApp), json_encode($session)], 'dingtalk');
            if (empty($AuthApp[$resDomain])) {
                T(21148001);
            }
            $Resource['ClickID'] = $ClickID;
            $accessUrl = AgentServiceProvider::getAccessUrl($session['LifeTime'], $session['Token'], $Resource);
            gotoUrl($accessUrl);
        } catch (Exception $e) {
            cutil_php_log(['error', $e->getMessage(), $e->getCode()], 'dingtalk');
            gotoErrorUrl("0x1020");
        }
    }
}
