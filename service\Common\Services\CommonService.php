<?php
/**
 * Description: 公共服务
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: CommonService.php 157742 2021-09-25 03:50:49Z renchen $
 */

namespace Services\Common\Services;

use Exception;

class CommonService
{
    /**
     * 日志名称.
     *
     * @var string
     */
    public $logFileName;

    /**
     * 返回状态码.
     *
     * @var int
     */
    public $code = 1;

    /**
     * 返回消息.
     *
     * @var string
     */
    public $message = 'success';

    /**
     * 返回数据.
     *
     * @var array
     */
    public $return = array();

    /**
     * 初始化
     */
    public function __construct()
    {
        $this->return = array(
            'code' => &$this->code,
            'message' => &$this->message,
            'data' => array(),
        );
    }

    /**
     * 写日志.
     *
     * @param mixed $content 日志内容
     * @param string $logType 日志级别
     *                        "DEBUG" ‐ debug信息、细粒度信息事件
     *                        "INFO" ‐ 重要事件、强调应用程序的运行过程
     *                        "NOTICE" ‐ 一般重要性事件、执行过程中较INFO级别更为重要的信息
     *                        "WARNING" ‐ 出现了非错误性的异常信息、潜在异常信息、需要关注并且需要修复
     *                        "ERROR" ‐ 运行时出现的错误、不必要立即进行修复、不影响整个逻辑的运行、需要记录并做检测
     *                        "CRITICAL" ‐ 紧急情况、需要立刻进行修复、程序组件不可用
     *                        "ALERT" ‐ 必须立即采取行动的紧急事件、需要立即通知相关人员紧急修复
     *                        "EMERGENCY" ‐ 系统不可用
     */
    public function writeLog(string $content, string $logType = 'INFO'):void
    {
        cutil_php_log($content, $this->logFileName, $logType);
    }

    /**
     * 记录抛出异常的错误日志.
     *
     * @param Exception $e
     * @param string $content
     */
    public function recordErrorMessage(Exception $e, string $content = ''):void
    {
        $this->code = $e->getCode();
        $this->message = $e->getMessage();
        $this->writeLog('Error code:'.$e->getCode().'--Error message:'.$e->getMessage() . ' in ' . $e->getFile() . ',Line number:' . $e->getLine(), 'ERROR');
        if ($content) {
            $this->writeLog('related data:'.$content, 'ERROR');
        }
    }

}
