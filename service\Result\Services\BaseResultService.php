<?php
/**
 * Description: 认证后上报结果服务
 * User: <EMAIL>
 * Date: 2021/07/22 15:53
 * Version: $Id: BaseResultService.php 159338 2021-10-19 06:06:41Z duanyc $
 */

namespace Services\Result\Services;

use AuthServiceProvider;
use Common\Facades\EventBrokerFacade;
use Services\Common\Services\CommonService;
use Services\Common\Services\DeviceOperationService;
use Services\Result\Traits\BaseResultServiceScoreTrait;

class BaseResultService extends CommonService
{
    // 结果判别/分数相关代码块
    use BaseResultServiceScoreTrait;
    public const CHECK_RESULT_SUCCESS = 'success'; // 安检通过
    public const CHECK_RESULT_false = 'false'; // 必须安检项不通过
    public const CHECK_RESULT_fault = 'fault'; // 非必须安检项不通过

    /**
     * 请求参数数组.
     *
     * @var array
     */
    protected $params;

    /**
     * 设备id.
     *
     * @var int
     */
    protected $deviceId;

    /**
     * 设备信息.
     *
     * @var array
     */
    protected $deviceInfo;

    /**
     * 根据语言转换后的配置.
     *
     * @var array
     */
    protected $clientCheck;

    /**
     * 规范信息.
     *
     * @var array
     */
    protected $policyInfo;

    /**
     * 检查结果保存.
     *
     * @var array
     */
    protected $checkResult;

    /**
     * 设备操作服务.
     *
     * @var DeviceOperationService
     */
    protected $deviceService;

    /**
     * windows外的全部类型
     * @var array
     */
    public static $ostypes = [
        OSTYPE_LINUX,
        OSTYPE_MAC,
        OSTYPE_ANDROID,
        OSTYPE_IOS,
        OSTYPE_HARMANYOS,
    ];

    /**
     * 初始化
     *
     * @param $params
     */
    public function __construct($params)
    {
        parent::__construct();
        $this->logFileName = 'submitResult';
        $this->params = $params;
        $this->deviceId = $params['deviceId'] ?? 0;
        $this->deviceService = new DeviceOperationService();
    }

    /**
     * 获取设备信息 原GetDeviceInfo
     * @throws \Exception
     */
    public function setDeviceInfo()
    {
        $this->deviceInfo = \DeviceModel::getJoinRelationComputer($this->deviceId, 'relation');

        if (empty($this->deviceInfo)) {
            T(21104005);
        }
    }

    /**
     * 只进行身份认证未进行安检
     */
    public function authNoSafeCheck()
    {
        $this->checkResult['Res'] = self::CHECK_RESULT_SUCCESS;
        $params = [];
        $params['LastCheckTID'] = 'null';
        $params['CheckResult'] = $this->checkResult['Res'];
        $params['LastFaultTime'] = 'now()';
        $params['CheckTime'] = 'now()';
        \RelationComputerModel::update($this->deviceId, $params);
        $this->checkResult['Desc'] = $this->clientCheck['PromptWords1'];
        // 未启用安检关闭隔离
        \ResultServiceProvider::isolationDevice(false, $this->deviceId);
        $this->writeLog(var_export($params, true));
    }

    /**
     * 获取并解析规范信息
     * @param $ostype
     * @throws \Exception
     */
    public function getAndParsePolicy($ostype = '')
    {
        $this->initPolicyInfo($ostype);
        $timeParams = ['DeviceID' => $this->deviceId, 'PolicyID' => $this->params['policyId'],
            'CheckResult' => self::CHECK_RESULT_SUCCESS, 'CheckScore' => 100,
            'InsertTime' => 'now()', 'RoleID' => $this->params['roleId']];
        $this->params['TimesID'] = \NacCheckTimesModel::insert($timeParams);
    }

    /**
     * 获取安检规范信息
     * @param $ostype
     * @throws \Exception
     */
    public function initPolicyInfo($ostype = ''): void
    {
        $ostype = !empty($ostype) ? $ostype : \hlp_compatible::getOsType();
        $this->policyInfo = \NacPolicyListModel::getOne($this->params['policyId'], 'policy');
        if (empty($this->policyInfo)) {
            T(21104006);
        }
        $policyBody = $this->getPolicyBody($ostype, $this->policyInfo['PolicyBody']);
        $xml = new \xml_safe_check_policy($policyBody); //解析规范
        $data = $xml->parseXml();
        $this->params['KeyItemID'] = $xml->getKeyItemID();
        $this->params['AutoRepairItem'] = $xml->getAutoRepairItemName();
        $this->params['RepairInterval'] = $data['RepairConf'];
    }

    /**
     * 隔离设备
     *
     * @param $deviceId
     * @param $checkResult
     *
     * @throws \Exception
     */
    public function isolationDevice($deviceId, $checkResult): void
    {
        //安检结果状态来隔离处理
        $deviceInfo = $this->getLastResultDeviceInfo($deviceId);
        if (empty($deviceInfo)) {
            return;
        }
        $isIsolation = ($checkResult === 'false' && $deviceInfo['Registered'] == '1')
                       || ($checkResult === 'fault' && $deviceInfo['Exceed_Expectations'] === 'Yes');
        \ResultServiceProvider::isolationDevice($isIsolation, $deviceInfo['DeviceID'], $checkResult);
    }

    /**
     * 获取上次安检分析結果
     * @param $deviceId
     * @return mixed
     * @throws \Exception
     */
    public function getLastResultDeviceInfo($deviceId)
    {
        $deviceInfo = cache_get_info('ASM_LastResult:', "{$deviceId}");
        if (empty($deviceInfo)) {
            $this->writeLog("缓存中未查询到设备信息，设备未在线，请认证后重试！");
            return false;
        }
        $deviceInfo['Exceed_Expectations'] = 'No';
        if ($deviceInfo['CheckResult'] === 'fault') {
            // 存在修复期限
            $lastFaultTime = strtotime(substr($deviceInfo['LastFaultTime'], 0, 10));
            $repairTime = strtotime("+" . $this->params['RepairInterval'] . " day", $lastFaultTime);
            $remainDate = ($repairTime - strtotime(func_time_getNow("Y-m-d"))) / 86400;
            if ($remainDate <= 0) {
                $deviceInfo['Exceed_Expectations'] = "Yes";
            }
        }
        $deviceInfo['Registered'] = $this->deviceInfo['Registered'] ?? '-2';
        return $deviceInfo;
    }

    /**
     * 分析单个检查项并保存检查记录
     * @throws \Exception
     */
    public function parseSingleItem()
    {
        $this->params['PassItemRate'] = 0;  //通过检查项分数
        $this->params['AllItemRate'] = 0;  //所有检查项分数
        $aItemID = explode("###||###", $this->params['itemsid']);
        $resultXml = explode("###||###", $this->params['checkres']);
        $resultXmlLength = count($resultXml);
        $insertDatas = [];
        for ($i = 0; $i < $resultXmlLength; $i++) {
            //检查结果报文 防止[μTorrent软件] 被解析成[&#61204;orrent]
            $resultXml[$i] = str_replace(['&#61204;'], ['μT'], stripslashes($resultXml[$i]));
            $is_key_item = @in_array($aItemID[$i], $this->params['KeyItemID']) ? 1 : 0;
            $itemInfo = \NacCheckItemModel::getJoinTemplate($aItemID[$i], 'submit');
            if (empty($itemInfo)) {
                T(21104007);
            }
            $itemInfo['Xml'] = $this->getItemXml($itemInfo['Xml']);
            $CustCheckItemName = strpos($itemInfo['ItemName'], 'CheckCustom_') !== false ?
                'CheckCustom' : $itemInfo['ItemName'];
            try {
                $xml = new \xml_check_item_result($resultXml[$i]);
            } catch (\Exception $e) {
                $this->writeLog("xml_check_item_result: " . $e->getMessage());
                $row['InsideName'] = $itemInfo['ItemName'];
                $row['OutsideName'] = $itemInfo['OutsideName'];
                $row['Remark'] = $resultXml[$i];
                $this->params['NoCheckItem'][] = $row; //统计检查失败的项
                continue;
            }
            $resultXmlInfo = $xml->parseXml(); //检查结果数据集
            $resultXmlInfo['CustCheckItemName'] = $CustCheckItemName;

            if (trim($resultXmlInfo['InsideName']) == "" || trim($resultXmlInfo['OutsideName']) == "") {
                $this->writeLog("[".$itemInfo['ItemName']."]：".$resultXml[$i]);
                continue;
            }
            $resultXmlInfo['ItemID'] = $aItemID[$i]; // 检查项ID
            $resultXmlInfo['CheckItem'] = $this->getCheckItemXml($itemInfo['Xml']);
            $aButton = explode("|", $itemInfo['OptionBtn']); // 获取检查项按钮名
            $resultXmlInfo['Btn'] = $resultXmlInfo['Result'] === "Yes" ? $aButton[0] : $aButton[1];
            // 生成单个检查项结果条目
            $insertDatas[] = array(
                'CheckTID' => $this->params['TimesID'],
                'ItemID' => $aItemID[$i],
                'IsKeyItem' => $is_key_item,
                'InsideName' => $resultXmlInfo['InsideName'],
                'OutsideName' => $resultXmlInfo['OutsideName'],
                'CheckXml' => $resultXml[$i],
                'CheckResult' => $resultXmlInfo['Result'],
                'DeviceID' => (int)$this->deviceId
            );
            if ($resultXmlInfo['Result'] !== "Yes") { // 归类到检查未通过的类别
                $this->params['NoPassItemID'][] = $aItemID[$i];
                $this->params['NoPassItemResult'][] = $resultXmlInfo;
            } else {  // 归类到检查已通过的类别
                $this->params['PassItemResult'][] = $resultXmlInfo;
                $this->params['PassItemRate'] += $itemInfo['Rate'];
            }
            $this->params['AllItemRate'] += $itemInfo['Rate'];
        }
        \NacCheckResultModel::insertPatch($insertDatas);
    }

    /**
     * 获取安检不通过告警信息
     * @param $PrefixMsg
     * @return string
     * @throws /Exception
     */
    public function getWarnningMsg($PrefixMsg = '')
    {
        $faultItmeStr = '';
        if (is_array($this->params['NoPassItemResult'])) {
            foreach ($this->params['NoPassItemResult'] as $item) {
                $faultItmeStr .= $item['OutsideName'].',';
            }
        }
        return  $PrefixMsg.L(21104012, ['checkItem' => rtrim($faultItmeStr, ',')]);
    }

    /**
     * 生成安检报警信息
     * @throws \Exception
     */
    public function checkProduceAlarmInfo()
    {
        switch ($this->checkResult['Res']) {
            case "false":
                $ALevel = 3;
                break;
            case "fault":
                $ALevel = 2;
                break;
            default:
                return;
        }
        $device = $this->deviceInfo;
        $desc = !empty($this->checkResult['Warning']) ? $this->checkResult['Warning'] : $this->checkResult['Desc'];
        $cmd = "warnner -l {$ALevel} -s 301 -d {$this->deviceId} -i {$device['IP']} -n \"{$device['DevName']}" .
               "\" \"[{$device['DevName']}][{$device['IP']}]：&nbsp;&nbsp;{$desc}\"";
        cutil_exec_no_wait($cmd);
    }

    /**
     * 解析获取PolicyBody
     *
     * @param $ostype
     * @param $PolicyBody
     *
     * @return mixed
     */
    public function getPolicyBody($ostype, $PolicyBody)
    {
        $prefix = \hlp_common::firstUpper($ostype);
        $aCheckItem_str = GetSubStr($PolicyBody, "<CheckItems>", "</CheckItems>");
        $aCheckItem_str_ostype = GetSubStr($PolicyBody, "<{$prefix}CheckItems>", "</{$prefix}CheckItems>");
        if ($ostype != OSTYPE_WINDOWS) {
            $PolicyBody = str_replace($aCheckItem_str, $aCheckItem_str_ostype, $PolicyBody);
        } else {
            foreach (self::$ostypes as $rostype) {
                $prefix = \hlp_common::firstUpper($rostype);
                $aCheckItem_str_ostype = GetSubStr($PolicyBody, "<{$prefix}CheckItems>", "</{$prefix}CheckItems>");
                $PolicyBody = str_replace("<{$prefix}CheckItems>" . $aCheckItem_str_ostype . "</{$prefix}CheckItems>", '', $PolicyBody);
            }
        }
        return $PolicyBody;
    }

    /**
     * 获取安检报文
     *
     * @param $xml
     *
     * @throws \Exception
     */
    public function getItemXml($xml)
    {
        $xml = utf8ToGbk($xml);
        $xml = str_replace("&nbsp;", " ", $xml);
        $xmlObj = @simplexml_load_string($xml);
        if (get_class($xmlObj) !== 'SimpleXMLElement') {
            T(21104008);
        }
        return $xml;
    }

    /**
     * 获取安检报文
     *
     * @param $xml
     *
     * @throws \Exception
     */
    public function getCheckItemXml($xml)
    {
        $CheckItem = str_replace(array("\r\n", "\n"), "", $xml);
        $CheckItem = str_replace('"', '\"', $CheckItem);
        // 处理在报文中，des节点中，英文版的情况下，出现单引号的情况
        $CheckItem = str_replace("'", '&#039;', $CheckItem);
        return gbkToUtf8($CheckItem);
    }

    /**
     * 重定向url获取
     *
     * @param $firsturl
     * @param string $ascid 默认是asm 也可能是asc
     *
     * @return int|null|string|string[]
     * @throws \Exception
     */
    public function getRedirectUrl($firsturl, $ascid)
    {
        $ascid = $ascid == "" ? "11:11:11:11:11:11" : $ascid;
        $firsturl = str_replace('?', '&', $firsturl);
        $firsturl = preg_replace("/&/", '?', $firsturl, 1);
        $ascLogUrl = $this->getAscLogUrl($ascid);
        $AccessUrl = $ascLogUrl == "1" ? $firsturl : $ascLogUrl;
        $AccessUrl = (($AccessUrl != "" && !preg_match("/^http(s)?:\/\//i", $AccessUrl)) ? HTTP_PROTOCOL : "") . $AccessUrl;
        return $AccessUrl;
    }

    /**
     * 获得控制器 安检后转向地址
     *
     * @param : $ascid 控制器ID
     * @return string 1：使用返回重定向前访问的页面 url地址：返回指定的页面 ""//不做任何操作
     * @throws \Exception
     */
    public function getAscLogUrl($ascid)
    {
        $aData = \AscLogUrlModel::getOne($ascid);
        // 使用全局配置
        if ($aData ['IsUseRemote'] == '1') {
            $c_path = PATH_ETC . "asm/tbridge_comm.ini";
            $cfg = parse_initfile($c_path);
            if ($cfg['LogSpecifiedURL'] == "" && !$cfg['LogFirstURL']) {
                // 不做任何操作
                return "";
            } elseif ($cfg['LogFirstURL'] == 1) {
                return 1;
            } else {
                return $cfg['LogSpecifiedURL'];
            }
        } else {
            if ($aData['IsUseBeforeUrl'] == "0" && $aData['LogSpecifiedURL'] == "") {
                // 不做任何操作
                return "";
            } elseif ($aData['IsUseBeforeUrl'] == '1') {
                // 返回重定向前访问的页面
                return 1;
            } else {
                return $aData['LogSpecifiedURL'];
            }
        }
    }

    /**
     * 客户端上报结果成功处理
     */
    public function clientResultSuccess(): void
    {
        try {
            $Token = \DeviceTokenRedis::getOne($this->deviceId);
            AuthServiceProvider::setSessionStatus($Token, SESSION_STATUS_SAFE_CHECK);
            // 先计算权限，才能异步通知队列，完成虚拟IP的分配。
            \ResourceServiceProvider::setUserPower($Token);
            $qdata = ['deviceId' => $this->deviceId, "Token" => $Token, "is_client" => IS_CLIENT, 'osType' => OS_TYPE, 'policyId' => $this->params['policyId'] ?? ''];
            \lib_queue::addJob('RESULT_SUCCESS', $qdata);
        } catch (\Exception $exception) {
            $this->writeLog("clientResultSuccess error:" . $exception->getMessage());
        }
    }

    /**
     * 浏览器上报结果成功处理
     * @throws \Exception
     */
    public function browserResultSuccess(): void
    {
        \ResultServiceProvider::resultClear($this->deviceId);
        $token = \LoginServiceProvider::getLoginToken();
        AuthServiceProvider::setSessionStatus($token, SESSION_STATUS_SAFE_CHECK);
        // 先计算权限，才能异步通知队列，完成虚拟IP的分配。
        \ResourceServiceProvider::setUserPower($token);
        $qdata = ['deviceId' => $this->deviceId, "Token"=>$token, "is_client"=>IS_CLIENT,'osType'=>OS_TYPE,'policyId'=>$this->params['policyId']??''];
        \lib_queue::addJob('RESULT_SUCCESS', $qdata);
    }

    /**
     * 发送IpRenew消息
     * @param $deviceId
     * @return void
     * @throws \Exception
     */
    public function publishIpRenew($deviceId)
    {
        $filepath = PATH_ETC . "dhcpd/DhcpSvr.ini";
        $dhcpConfig = read_inifile($filepath);
        if (isset($dhcpConfig['DHCP_Server_Enable']) && $dhcpConfig['DHCP_Server_Enable'] == 1) {
            $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>IpRenew</WhatToDo><Msg>Just IP Renew</Msg></ASM>";
            $msgSenderInfo = ['DeviceId' => $deviceId, 'Content' => $content, 'IsNull' => true];
            \lib_alarm::createMsgSender($msgSenderInfo);
        }
    }
    /**
     * 发布安检事件
     * @return void
     */
    public function publishCheckEvent($deviceID, string $source = '')
    {
        if ((int)$deviceID !== 0) {
            EventBrokerFacade::publish('Check', [['DeviceID' => (int)$deviceID, 'Remark' => 'Check']], 'WebAccess:' . $source);
        }
    }
}
