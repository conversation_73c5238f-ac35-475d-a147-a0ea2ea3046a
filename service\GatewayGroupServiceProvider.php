<?php
/**
 * Description: 网关组
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: GatewayServiceProvider.php 174952 2022-04-29 13:33:57Z duanyc $
 */

class GatewayGroupServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'gateway';

    /**
     * 获取分组数据
     *
     * @param $gatewayList
     *
     * @return array
     */
    public static function getGroupGatewayList($gatewayList): array
    {
        $gatewayIndexList = []; // 有分组的网关
        $groupGatewayList = [];
        $groupList = [];
        foreach ($gatewayList as $gateway) {
            $isEmergency = SystemServiceProvider::isEmergency($gateway["SysStatus"]);
            if ($gateway["GroupID"] > 0 && $gateway["SysStatus"] > 0 && !$isEmergency && time()-strtotime($gateway['LastUpTime'])<180) {
                $groupGatewayList[$gateway["GroupID"]][] = self::getGatewayOne($gateway, $gateway["IP"]);
                $gatewayIndexList[$gateway["ID"]] = [
                    'GroupID' => $gateway["GroupID"],
                    'IP'      => $gateway["IP"],
                ];
            }
        }

        if (!empty($gatewayIndexList)) {
            foreach ($gatewayIndexList as $gatewayId => $gateway) {
                $igatewayList = [];
                foreach ($groupGatewayList[$gateway["GroupID"]] as $igateway) {
                    $igatewayList[] = $igateway;
                }
                $groupList[$gatewayId] = $igatewayList;
            }
        }
        return $groupList;
    }

    /**
     * 组装单行数据
     *
     * @param $gateway
     * @param $ip
     *
     * @return array
     */
    public static function getGatewayOne($gateway, $ip): array
    {
        return [
            "ip"           => $ip,
            "httpprotocol" => "https",
            "httpport"     => isset($gateway["HttpPort"]) ? (int)$gateway["HttpPort"] : 443,
            "vpnprotocol"  => $gateway["VpnProtocol"] ?? "udp",
            "vpnport"      => isset($gateway["VpnPort"]) ? (int)$gateway["VpnPort"] : 61616,
            "id"           => $gateway["ID"],
        ];
    }

    /**
     * 获取网管组数据
     *
     * @param $gateway
     * @param $groupList
     *
     * @return array
     */
    public static function getGatewayList($gateway, $groupList)
    {
        if ((!$gateway["GroupID"] || !isset($groupList[$gateway["ID"]]))) {
            $gw = self::getGatewayOne($gateway, "127.0.0.1");
            return [$gw];
        }
        return $groupList[$gateway["ID"]];
    }

    /**
     * 获取网关组的网关数据
     *
     * @param $GroupID
     *
     * @return array|bool
     * @throws Exception
     */
    public static function getGatewayData($GroupID)
    {
        $gatewayList = GateWayModel::getList(['GroupID' => $GroupID]);
        if (empty($gatewayList)) {
            return false;
        }
        $aGatewayConf = []; // 网关配置
        $groupList = self::getGroupGatewayList($gatewayList);
        foreach ($gatewayList as $gateway) {
            if (empty($gateway["IP"])) {
                continue;
            }
            $key = $gateway["IP"];
            $Gateways = self::getGatewayList($gateway, $groupList);
            $aGatewayConf[$key] = [
                "Gateways"              => $Gateways,
            ];
        }

        // key为tradecode
        $return = [];
        $return[] = ['TradeCode' => 'gwconfig', 'AsgList' => $aGatewayConf];
        return $return;
    }
}
