<?php

/**
 * Description: 设备TNacOnLineDevice表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: NacOnLineDeviceModel.php 165094 2021-12-16 08:34:07Z duanyc $
 */

class NacOnLineDeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacOnLineDevice';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        'one'  => 'SceneID',
        'name' => 'DeviceID,UserName,RoleID,SceneID,AuthType,UserID',
        'role' => 'AuthType,RoleID,SceneID,AuthTime,LastHeartTime,UserID,Token,InsertTime',
        'user' => 'DeviceID,RoleID,SceneID,AuthType,UserName,AuthTime,LastHeartTime,UserID',
        'online' => 'B.IP, B.Devi<PERSON>ID, B.DevName, C.TypeName, A.AuthTime, A.AccessType',
        'merge' => 'DeviceID,InsertTime',
        'token' => 'DeviceID,Token',
        '*'    => '*',
    ];

    /**
     * 获取在线列表
     *
     * @param array $cond
     * @param mixed $order
     * @param int $start
     * @param int $limit
     *
     * @return mixed
     */
    public static function getListJoin($cond, $order = false, $start = 0, $limit = 10)
    {
        // 检查设备信息 todo 后续统一走用户ID查询
        if (!isset($cond['AuthType'], $cond['UserName'], $cond['DeviceID'])) {
            return false;
        }
        self::$data = [];
        $where = "WHERE ";
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $column = static::$columns['online'];
        $onlineColumn = ", UserName";
        $permitColumn = ", a.UserName";
        if (empty($cond['UserName']) || empty($cond['AuthType'])) {
            $permitWhere = '';
        } elseif ($cond['UserName'] === 'Guest' && $cond['AuthType'] === 'Guest') {
            $onlineWhere = "WHERE AuthType = ".self::setData($cond['AuthType'])." and UserName = ".self::setData($cond['UserName']);
            $permitWhere = "LEFT JOIN TAuthUser a ON p.UserID = a.ID WHERE a.Type = 'User' and a.UserName = ".self::setData('Guest');
        } elseif ($cond['AuthType'] === 'Mobile') {
            $onlineWhere = "WHERE AuthType = ".self::setData($cond['AuthType'])." and UserName = ".self::setData($cond['UserName']);
            $permitColumn = ", ".self::setData($cond['UserName'])." as UserName";
            $permitWhere = "WHERE p.BindID = ".self::setData($cond['DeviceID']);
        } elseif (isset($cond['UserID'])) {
            $permitColumn = ", a.UserName, a.Type as AuthType,UserID";
            $onlineColumn = ", UserName, AuthType,UserID";
            $onlineWhere = '';
            $permitWhere = "LEFT JOIN TAuthUser a ON p.UserID = a.ID WHERE a.UserName = ".self::setData($cond['UserName']);
            $where .= "A.UserID = ".self::setData($cond['UserID'])." AND";
        } else {
            $permitColumn = ", a.UserName, a.Type as AuthType";
            $onlineColumn = ", UserName, AuthType";
            $onlineWhere = '';
            $permitWhere = "LEFT JOIN TAuthUser a ON p.UserID = a.ID WHERE p.BindID = ".self::setData($cond['DeviceID']);
            $where .= "A.UserName = ".self::setData($cond['UserName'])." AND A.AuthType = ".self::setData($cond['AuthType'])." AND";
        }

        if (empty($permitWhere)) {
            $sql = "SELECT B.IP, B.DeviceID, B.DevName, C.TypeName, A.AuthTime, 'User' as AccessType 
                        FROM TDevice B LEFT JOIN TNacOnLineDevice A ON A.DeviceID = B.DeviceID 
                        LEFT JOIN TDeviceType C ON B.type = C.TypeID WHERE B.DeviceID = ".self::setData($cond['DeviceID']);
        } else {
            $where .= " A.DeviceID = B.DeviceID AND B.type = C.TypeID ";
            $sql = "SELECT {$column}  FROM ( 
                   SELECT DeviceID, 'User' as AccessType, AuthTime as AuthTime {$onlineColumn}
                   FROM TNacOnLineDevice {$onlineWhere}
                   UNION ALL
                   SELECT p.DeviceID, 'Permit' as AccessType, p.InsertTime AS AuthTime {$permitColumn}
                   FROM TPermitDevice p {$permitWhere}) A, 
                   TDevice B, TDeviceType C {$where} {$order} LIMIT {$start}, {$limit}";
        }

        cutil_php_log([$sql, self::$data], 'model_select');
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取最后在线表存在的设备ID
     * @param $DeviceIDs
     * @return bool|mixed
     */
    public static function getCorrectDeviceID($DeviceIDs)
    {
        if (empty($DeviceIDs)) {
            return false;
        }
        $cond = ['DeviceIds' => $DeviceIDs, 'column' => 'merge'];
        $deviceList = self::getList($cond);
        if (empty($deviceList)) {
            return false;
        }
        $maxInsertTime = 0;
        $return = [];
        foreach ($deviceList as $device) {
            $time = strtotime($device['InsertTime']);
            if ($time > $maxInsertTime) {
                $maxInsertTime = $time;
                $return['DeviceID'] = $device['DeviceID'];
            }
        }
        return $return;
    }

    /**
     * 获取角色ID、规范ID列表
     *
     * @param array $deviceIds
     *
     * @return mixed
     */
    public static function getRolePolicyList($deviceIds)
    {
        if (empty($deviceIds)) {
            return false;
        }

        self::$data = [];
        if (is_array($deviceIds)) {
            $where = " and A.DeviceID IN (" .self::setArrayData($deviceIds). ")";
        } else {
            $where = " and A.DeviceID=" .self::setData($deviceIds);
        }

        $typeAll = implode(',', [DEVICE_TYPE_PC, DEVICE_TYPE_MOBILE]);
        $sql = "select S.DeviceID,S.RoleID,  X.ItemValue as PolicyID  from (
         SELECT A.DeviceID,
         case when C.RoleID is not null and C.RoleID>0 then C.RoleID 
              when Y.ItemValue > 0 and F.RoleID >0 then F.RoleID
              when Y.ItemValue < 1 and A.RoleID >0 then A.RoleID
              when B.RoleID >0 then B.RoleID
              else 1 end as RoleID
         FROM TDevice A
         left join TDepart B on A.DepartId=B.DepartID
         left join TNacOnLineDevice C on A.DeviceID=C.DeviceID 
         left join TComputer D on A.DeviceID=D.DeviceID 
         left join TRelationComputer E on A.DeviceID=E.DeviceID 
         left join TNacAuthLog F on E.LastAuthID=F.RID,
         (SELECT ItemValue FROM TDict where Type='AuthParam' and ItemName='AuthPriority') Y where TIME_TO_SEC(TIMEDIFF(NOW(), A.LastTime))<=900 and A.Type in ({$typeAll}) " . $where . "
         ) S left join ( select a.ItemValue,b.RoleID FROM TRoleDict a ,TRoleRelation b where  a.ItemName='PolicyID' and a.Groups = 'PolicyList'  and b.ConfigID = a.ConfigID)X on S.RoleID=X.RoleID";
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取现在用户的数字配置
     * @return mixed
     */
    public static function getAllOnlineAuthInterval()
    {
        self::$data = [];
        $columnName = 'AuthInterval';
        $OrigID = '0';
        $sql = "select O.Token, O.DeviceID,O.LastHeartTime,case when D.ItemValue=0 then 8639913600 else D.ItemValue end as AuthInterval 
                    from TNacOnLineDevice O,TSceneDict D,TSceneRelation R where O.OrigID=" . self::setData($OrigID) . " and O.SceneID=R.SceneID 
                    and D.ConfigID=R.ConfigID and D.Groups=R.Groups and D.ItemName = " . self::setData($columnName);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Token'])) {
            $where .= "AND Token = ".self::setData($cond['Token']);
        }

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        if (!empty($cond['UserIds'])) {
            $where .= "AND UserID IN (".self::setArrayData($cond['UserIds']).") ";
        }

        if (!empty($cond['RoleIds'])) {
            $where .= "AND RoleID IN (".self::setArrayData($cond['RoleIds']).") ";
        }

        if (!empty($cond['DeviceIds'])) {
            $where .= "AND DeviceID IN (".self::setArrayData($cond['DeviceIds']).") ";
        }

        if (!empty($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
