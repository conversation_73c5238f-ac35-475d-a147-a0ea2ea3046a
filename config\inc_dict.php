<?php

/**
 * Description: 需要返回的配置
 * User: <EMAIL>
 * Date: 2021/08/03 15:53
 * Version: $Id: inc_dict.php 170454 2022-03-07 07:11:45Z huyf $
 */
/* 初始化全局配置变量 */
$GLOBALS['DICT'] = [];

$GLOBALS['DICT']['CLIENTCHECK'] = [
    'DeviceRegAudit'       => '',
    'AutoCheck'            => '',
    'IsAuth'               => '',
    'ShowScore'            => '',
    'AutoRepair'           => '',
    'DeviceAutoReg'        => '',
    'MobileAuth'           => '',
    'RequireTel'           => '',
    'RequirePos'           => '',
    'RequireDep'           => '',
    'RequireUser'          => '',
    'RequireRem'           => '',
    'FastAuth'             => '',
    'HttpsSwitch'          => '',
    'AuditIP'              => '',
    'NoAuditIP'            => '',
    'AuditDesc'            => '',
    'NoAuditDesc'          => '',
    'ControlType'          => '',
    'exceptionip'          => '',
    'ClientSavePass'       => '',
    'ClientSaveName'       => '',
    'ClientHunt'           => '',
    'RequireMail'          => '',
    'RequireDevType'       => '',
    'RequireExpand_1'      => '',
    'RequireExpand_2'      => '',
    'RequireExpand_3'      => '',
    'RequireExpand_4'      => '',
    'RequireExpand_5'      => '',
    'ControlPostion'       => '',
    'MobileAudit'          => '',
    'RegisterAudit'        => '',
    'VerBeforeAudit'       => '',
    'PromptWords1'         => '',
    'PromptWords2'         => '',
    'PromptWords3'         => '',
    'PromptWords4'         => '',
    'PromptWords5'         => '',
    'PromptWords6'         => '',
    'ClientSkin'           => '',
    'IsAuditCall'          => '',
    'AuditCallDay'         => '',
    'AuditCallContent'     => '',
    'Cust_RepairItem'      => '',
    'PromptWords1_en'      => '',
    'PromptWords2_en'      => '',
    'PromptWords3_en'      => '',
    'PromptWords4_en'      => '',
    'PromptWords5_en'      => '',
    'PromptWords6_en'      => '',
    'RequireUser_en'       => '',
    'RequireDep_en'        => '',
    'RequireTel_en'        => '',
    'RequireMail_en'       => '',
    'RequirePos_en'        => '',
    'RequireDevType_en'    => '',
    'RequireRem_en'        => '',
    'RequireExpand_1_en'   => '',
    'RequireExpand_2_en'   => '',
    'RequireExpand_3_en'   => '',
    'RequireExpand_4_en'   => '',
    'RequireExpand_5_en'   => '',
    'RequireExpand_6_en'   => '',
    'RequireExpand_7_en'   => '',
    'RequireExpand_8_en'   => '',
    'RequireExpand_9_en'   => '',
    'RequireExpand_10_en'  => '',
    'AuditCallContent_en'  => '',
    'RequireExpand_6'      => '',
    'RequireExpand_7'      => '',
    'RequireExpand_8'      => '',
    'RequireExpand_9'      => '',
    'RequireExpand_10'     => '',
    'resultpage_staytime'  => '',
    'reAudit'              => '',
    'entrance_version'     => '',
    'web_bgcolor'          => '',
    'mobile_Web_GoWeb'     => '',
    'mobile_Web_GoWeb_en'  => '',
    'IsGuestAuth'          => '',
    'GuestRequireUser'     => '',
    'GuestRequireUnit'     => '',
    'GuestRequireTel'      => '',
    'GuestRequireReason'   => '',
    'GuestRequireUser_en'     => '',
    'GuestRequireUnit_en'     => '',
    'GuestRequireTel_en'      => '',
    'GuestRequireReason_en'   => '',
    'AuditRequireUser'     => '',
    'AuditRequireTel'      => '',
    'AuditRequireUser_en'     => '',
    'AuditRequireTel_en'      => '',
    'GuestRequireExpand_1' => '',
    'GuestRequireExpand_2' => '',
    'GuestRequireExpand_3' => '',
    'GuestRequireExpand_4' => '',
    'GuestRequireExpand_5' => '',
    'GuestRequireExpand_1_en' => '',
    'GuestRequireExpand_2_en' => '',
    'GuestRequireExpand_3_en' => '',
    'GuestRequireExpand_4_en' => '',
    'GuestRequireExpand_5_en' => '',
    'IsAllowReg'           => '',
];

$GLOBALS['DICT']['INDEXREG'] = [
    'DevDisplay'  => '',
    'AgeeDisplay' => '',
    'IPMdReg'     => '',
];

$GLOBALS['DICT']['DSM'] = [
    'State '  => '',
    'Url'     => '',
    'linkDsm' => '',
];

$GLOBALS['DICT']['LDAP'] = [
    'Addr'     => '',
    'Port'     => '',
    'BaseDN'   => '',
    'Filter'   => '',
    'UserName' => '',
    'MapRole'  => '',
    'MapState' => '',
    'MapName'  => '',
    'AutoUser' => '',
    'TureName' => '',
    'Phone'    => 'telephoneNumber',
];

$GLOBALS['DICT']['SMS'] = [
    'SendType'                  => '',
    'FetionSend'                => '',
    'InfogoSMSIP'               => '',
    'InfogoSMSPort'             => '',
    'InfogoSMSUser'             => '',
    'TelAuthRemark'             => '',
    'ValidRegisteredPhone'      => '',
    'ValidRegisteredPhoneToDev' => '',
    'write_devtel'              => '',
    'SMSSuffix'                 => '',
    'SMSRole'                   => '',
    'TemplateStaff'             => '',
    'TemplateGuest'             => '',
    'TemplateUserPass'          => '',
    'TemplateAlarm'             => '',
];

$GLOBALS['DICT']['UKey'] = [
    'UKeyType'         => '',
    'IsUKey'           => '',
    'OnDev'            => '',
    'Isletter'         => '',
    'PIN'              => '',
    'HowLongNotInsert' => '',
    'UKeysmsalert'     => '',
    'CertValid'        => '',
    'SyncOU'           => '',
    'OName'            => '',
    'UKeyField'        => '',
    'IsOname'          => '',
    'download_letter'  => '',
    'Onsoftauth'       => '',
    'UkeyOtherIP'      => '',
    'UkeyOtherPort'    => '',
    'UkeyOtherAppFlag' => '',
    'ukeyConfig'       => '',
];

$GLOBALS['DICT']['ShowAllDevice'] = [
    'lazyLoading'      => '',
];

$GLOBALS['DICT']['ADMINISTRATORINFO'] = [
    'Title'                 => '',
    'CompanyName'           => '',
    'Tel'                   => '',
    'NumsLimit'             => '',
    'TimesLimit'            => '',
    'TimesUnit'             => '',
    'LimitYN'               => '',
    'LoginType'             => '',
    'RadiusIP'              => '',
    'RadiusPort'            => '',
    'RadiusKeys'            => '',
    'loginCode'             => '',
    'SetTimeOut'            => '',
    'sTitle'                => '',
    'platTitle'             => '',
    'sTitle_en'             => '',
    'PasswordLength'        => '',
    'PasswordOverdueDays'   => '',
    'PasswordStrengthCheck' => '',
    'Tel_en'                => '',
    'loginCodeType'         => '',
    'forPassWordCheck'      => '',
    'indextitle'            => '',
    'guideClose'            => '',
    'ShowAgreement'         => '',
    'AgreementContent'      => '',
    'isDefaultLogo'         => '',
    'isIndexLogo'           => '',
    'isIndexPageLogo'       => '',
    'AuthLogoID'            => '',
    'FactorAuth'            => '',
    'NoticeDisplay'         => '',
    'ThemeColorAdmin'       => '',
    'ThemeColorMobile'      => '',
    'ThemeColorPc'          => '',
    'CustomBackgroundImage' => '',
    'CompanyLogo'           => '',
    'SystemLogoPc'          => '',
    'SystemLogoMobile'      => '',
];

$GLOBALS['DICT']['AUTHPARAM'] = [
    'AllowAuthType'     => '',
    'AuthPriority'      => '',
    'DefaultAuthType'   => '',
    'UserBindDev'       => '',
    'IsTrustUser'       => '',
    'SpecialIpAuthType' => '',
    'verifyCode'        => '',
    'verifyCodeType'    => '',
];

$GLOBALS['DICT']['GUESTAUTH'] = [
    'State'              => '',
    'NetCode'            => '',
    'NetCodeTime'        => '',
    'INetRight'          => '',
    'RoleCode'           => '',
    'netCodeMsg'         => '',
    'QrcodeKey'          => '',
    'SmsState'           => '',
    'GuestApplyIP'       => '',
    'GuestApplyIPRemark' => '',
    'GuestApplySelf'     => '',
    'QrCode'             => '',
    'IsClearNoGuestUrl'  => '',
    'GuestIPState'       => '',
    'IsSendCode'         => '',
    'AllowSMS'           => '',
    'AllowGuestTeam'     => '',
    'IsNeedAppoint'     => '',
];

$GLOBALS['DICT']['ADDOMAIN'] = [
    'Ip'            => '',
    'adIp'          => '',
    'SpareIp_1'     => '',
    'SpareIp_2'     => '',
    'Domain'        => '',
    'AutoLogin'     => '',
    'MapState'      => '',
    'isPermit'      => '',
    'SearchDN'      => '',
    'Filter'        => '',
    'UserName'      => '',
    'MapName'       => '',
    'MapRole'       => '',
    'AutoUser'      => '',
    'errmsgDefault' => '',
    'errmsgInfo'    => '',
];

$GLOBALS['DICT']['User'] = [
    'AuthServer'          => '',
    'FirstAuthServer'     => '',
    'FactorAuth'          => '',
    'AuthServerAlias'     => '',
    'MFAServer'           => '',
    'MainAccountType'     => '',
];

$GLOBALS['DICT']['ChangePassword'] = [
    'IsChangePassword'     => '',
    'ChangeUrl'            => '',
    'PassWordType'         => '',
    'PassWordAuth'         => '',
    'PassWordContent'      => '',
    'PassWordLength'       => '',
    'PassWordOverdue'      => '',
    'PassWordOverdueNum'   => '',
    'errPassword'          => '',
    'dbUserName'           => '',
    'dbStr'                => '',
    'PassWordsetType'      => '',
    'PassWordoverdueAlert' => '',
    'ChangeUrl_Mobile'     => '',
    'changePassType'       => '',
    'IsForgetPassword'     => ''
];

$GLOBALS['DICT']['ClientAuth'] = [
    'IsClientAuth'       => '',
    'ClientUser'         => '',
    'ClientPassword'     => '',
    'onmobilewebauth'    => '',
    'onmobileclientauth' => '',
    'ClientUser_en'      => '',
    'ClientPassword_en'  => '',
];

$GLOBALS['DICT']['UserDevBind'] = [
    'IsUse'             => '',
    'IsUpdateUserToDev' => '',
    'IsUpDevName'       => '',
];

$GLOBALS['DICT']['QRAuth'] = [
    'state' => '',
];

$GLOBALS['DICT']['DingTalkConfig'] = [
    'MapRole'       => '',
    'corpId'        => '',
    'appIDScanner'  => '',
    'appkey'        => '',
    'FieldName'     => '',
    'QRCode'        => '',
    'CuttOffStatus' => '',
    'CuttOff30Msg'  => '',
    'CuttOffMsg'    => '',
    'version'       => '',
    'ServerProxy' => ''
];

$GLOBALS['DICT']['WeWorkConfig'] = [
    'corpId'              => '',
    'agentid'             => '',
    'MapRole'             => '',
    'FieldName'           => '',
    'QRCode'              => '',
    'isEnableSynchronous' => '',
    'BeforeDepart'        => '',
    'AfterDepart'         => '',
    'SynchronousTime'     => '',
    'UpdateTime'          => '',
    'UpdateRes'           => '',
    'IsSyncDepart'        => '',
    'ServerProxy' => ''
];

$GLOBALS['DICT']['UserAuthControl'] = [
    'UserAuthEnableQR' => '',
];

$GLOBALS['DICT']['FeiShuConfig'] = [
    'QRCode'      => '',
    'appid'       => '',
    'appsecret'   => '',
    'IP_1'        => '',
    'appid_1'     => '',
    'appsecret_1' => '',
    'ServerProxy' => ''
];

$GLOBALS['DICT']['DomainFake'] = [
    'IsDomainFake' => '',
    'DomainSid'    => '',
];

$GLOBALS['DICT']['SSO'] = [
    'State'    => '',
    'ForceSSO' => '',
    'Mode'     => '',
    'Name'     => ''
];

$GLOBALS['DICT']['ZTP'] = [
    'VpnType'    => '',
];
