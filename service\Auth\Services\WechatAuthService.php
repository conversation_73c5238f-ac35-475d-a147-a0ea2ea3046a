<?php
/**
 * Description: 微信认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: WechatAuthService.php 151036 2021-07-22 08:47:48Z duanyc $
 */

namespace Services\Auth\Services;

use Exception;
use lib_otheruser;
use OtherUserModel;
use Services\Auth\Interfaces\AuthServiceInterface;
use wechat;

class WechatAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'Wechat';

    /**
     * 微信操作对象
     * @var wechat
     */
    private $wechat = null;

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        $this->wechat = lib_otheruser::getInstance('wechat');
        parent::__construct($params);
    }

    /**
     * 操作数据库
     *
     * @param $serverip
     * @param $regUnit
     * @param $userinfo
     *
     * @return int
     */
    public function actionDB($serverip, $regUnit, $userinfo)
    {
        if (!empty($userinfo['openid']) && is_array($userinfo)) {
            $RegPosition[] = $userinfo['country'];
            $RegPosition[] = $userinfo['province'];
            $RegPosition[] = $userinfo['city'];
            $RegPosition = implode('/', array_filter($RegPosition));
            $otherUser = OtherUserModel::getOneByOpenId($userinfo['openid'], 'ID');
            $params = ['RedirectIP' => $serverip, 'NickName' => $userinfo['nickname'], 'Sex' => $userinfo['sex'],
                'Adress' => $RegPosition, 'LastUpTime' => date("Y-m-d H:i:s"), 'subscribe' => $userinfo['subscribe'],
                'Remark' => '当时客户IP:'.$this->wechat->CLIENTIP];
            if (empty($otherUser['ID'])) {
                $params['Openid'] = $userinfo['openid'];
                $params['AccountName'] = '';
                $params['subscribe'] = $userinfo['subscribe'];
                $params['headimgurl'] = $userinfo['headimgurl'];
                $params['RegPosition'] = $regUnit;
                $params['RegTime'] = date("Y-m-d H:i:s", $userinfo['subscribe_time']);
                $otherUser['ID'] = OtherUserModel::insert($params);
                $this->wechat->log(var_export($params, true));
            } else {//更新信息
                OtherUserModel::update($otherUser['ID'], $params);
                $this->wechat->log(var_export($params, true));
            }
            $this->wechat->log("return id: ".$otherUser['ID']);
            return $otherUser['ID'];
        } else {
            $this->wechat->log("userinfo Error:".var_export($userinfo, true));
            return 0;
        }
    }
}
