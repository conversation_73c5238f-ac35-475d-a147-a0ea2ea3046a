<?php

/**
 * Description: 设备网络状态相关
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id:
 */

namespace Services\Device\Services;

use AuthUserModel;
use Common\Facades\NACServiceFacade;
use ComputerModel;
use ConfigServiceProvider;
use DeviceModel;
use DeviceTokenRedis;
use DictModel;
use Exception;
use LoginServiceProvider;
use Services\Common\Services\RoleService;
use SessionRedis;
use SceneModel;
use SceneDictModel;
use GuestSelfApplyModel;
use UserInfoRedis;

class DeviceStatusService extends DeviceInfoService
{
    /**
     * 命令
     * @var string
     */
    private $serveNetworkStatusCmdPath = PATH_ASM . "sbin/GetDeviceNetworkStatus";

    /**
     * 场景公共服务
     *
     * @var RoleService
     */
    protected $roleService = null;

    /**
     * session会话信息
     * @var array
     */
    protected $session = [];

    /**
     * 初始化
     */
    public function __construct()
    {
        parent::__construct([]);
        $this->logFileName = 'NetworkStatus';
        $this->roleService = new RoleService();
    }

    /**
     * 通过deviceId获取网络状态
     * @param int $deviceId
     * @return array
     * @throws Exception
     */
    public function getByDeviceId(int $deviceId): array
    {
        $devInfo = DeviceModel::getJoinOnlineDevice(
            [
                'DeviceID' => $deviceId,
                'column' => 'networkStatus'
            ]
        );
        if (empty($devInfo)) {
            T(21103006);
        }
        $this->deviceId = $deviceId;
        if (!empty($devInfo['Token'])) {
            $this->session = SessionRedis::getOne($devInfo['Token'], 'status');
            $this->session['ClientToken'] = LoginServiceProvider::getLoginToken();
            // 是否已登录
            $this->session['IsLogin'] = ($devInfo['Token'] === $this->session['ClientToken']) ? true : false;
        }

        $roleCode = false;
        if (!empty($devInfo['RoleID'])) {
            $roleCode = $this->roleService->getRoleInfoOne((int)$devInfo['RoleID'], 'RoleCode', 'RoleCode');
        }
        $netCode = 0;
        $qrCode = 0;
        $isNeedAppoint = 0;
        $guestApplySelf = 0;
        $allowGuestTeam = 0;
        if (!empty($devInfo['UserID'])) {
            $AuthUser = AuthUserModel::getOne($devInfo['UserID'], 'sdclink');
            $IsOpenSDCLinkage = $AuthUser['IsOpenSDCLinkage'] ?? '0';
            if ($IsOpenSDCLinkage) {
                // 判断xp排除
                $computerInfo = ComputerModel::getSingle(['DeviceID' => $deviceId, 'column' => 'name']);
                if (strpos($computerInfo['OSName'], 'Windows XP') !== false) {
                    $IsOpenSDCLinkage = 0;
                }
            }
        }
        $sceneInfo = SceneModel::getOne($devInfo['SceneID']);
        if ((int)$sceneInfo['UserType'] === 1) {
            $gusetAuthorize = getmoduleregist(31);
            // 员工才会有接入接待场景权限
            $sceneconfig = SceneDictModel::getValue($devInfo['SceneID'], 'receiveAccess', 'ReceiveConfig');
            $netCode = stripos($sceneconfig, 'NetCode') !== false ? 1 : 0;
            $qrCode = stripos($sceneconfig, 'QrCode') !== false ? 1 : 0;
            $isNeedAppoint = $gusetAuthorize && stripos($sceneconfig, 'IsNeedAppoint') !== false ? 1 : 0;
            $guestApplySelf = stripos($sceneconfig, 'GuestApplySelf') !== false ? 1 : 0;
            $allowGuestTeam = $gusetAuthorize && stripos($sceneconfig, 'AllowGuestTeam') !== false ? 1 : 0;
        }

        $result = [
            'SessionStatus' => $this->session['SessionStatus'] ?? SESSION_STATUS_NO_LOGIN,
            'lastAuthType' => $devInfo['AuthType'],
            'checkTime' => $devInfo['CheckTime'] === DEFAULT_TIME ? '' : $devInfo['CheckTime'],
            'authTime' => $devInfo['AuthTime'],
            // 是否有权限申请来宾码
            'roleCode' => $roleCode && (int)$roleCode['Config'] === 1 ? 1 : 0,
            "NetCode" => $netCode, // 允许申请来宾码
            "QrCode" => $qrCode, // 允许扫码接待
            "IsNeedAppoint" => $isNeedAppoint, // 允许预约来宾手机号
            "GuestApplySelf" => $guestApplySelf, // 允许审批来宾自助申请
            "AllowGuestTeam" => $allowGuestTeam, // 允许团队接待
            'roleID' => $devInfo['RoleID'] ?? '0',
            'UserID' => $devInfo['UserID'] ?? '0',
            'SceneID' => $devInfo['SceneID'] ?? '0',
            'IsOpenSDCLinkage' => $IsOpenSDCLinkage ?? '0'
        ];
        $result['ZtpUser'] = AuthUserModel::getZtpUser($result['UserID']);
        $result['IsAlreadyAudit'] = ''; // 是否已审核
        $result['guestSelfID'] = 0;  //来宾id
        $result['guestlastAuthType'] = ''; // 来宾认证方式
        if ((int)$sceneInfo['UserType'] === 2) {
            $guestInfo = GuestSelfApplyModel::getSingle(["DeviceID" => $this->deviceId, "UserID" => $result['UserID']], "ID desc");
            if ($guestInfo) {
                $result['IsAlreadyAudit'] = $guestInfo['Status'];
                $result['guestSelfID'] = $guestInfo['ID'];
                $result['guestlastAuthType'] = $guestInfo['AccessType'];
            }
        }
        $deviceStatus = ['deviceStatus' => 0, 'IPListId' => 0, 'isEmergency' => 0, 'dot1xEscapeStatus' => 0];
        if (!empty($devInfo['IP'])) {
            $deviceStatus = $this->getByIpFromServe($devInfo['IP'], $deviceId);
        }
        $result['deviceStatus'] = $deviceStatus['deviceStatus'];
        // 已登录才返回用户信息，防止遍历设备ID获取到用户信息，后续该接口新增的字段也需要防止信息泄漏
        if ($this->isStrict()) {
            $result['Token'] = !empty($this->session['IsLogin']) ? $devInfo['Token'] : '';
            $result['userName'] = !empty($this->session['IsLogin']) ? $devInfo['UserName'] : '';
        } else {
            $result['Token'] = $devInfo['Token'];
            $result['userName'] = !empty($result['deviceStatus']) ? $devInfo['UserName'] : '';
        }
        $result['TokenTimestamp'] = (int)($this->session['LastTime'] ?? 0);
        $result['IPListId'] = $deviceStatus['IPListId'];
        $result['isEmergency'] = $deviceStatus['isEmergency'];
        // 802.1x逃生
        $result['dot1xEscapeStatus'] = (
            (int)ConfigServiceProvider::getDictOne('Dot1xConfig', 'Dot1x_Escape_Enable') === 1 &&
            (int)ConfigServiceProvider::getDictOne('Dot1xConfig', 'Dot1x_Escape_Status') === 1
        )
            ? '1' : '0';
        return $result;
    }

    /**
     * 获取设备是否在线
     * @param int $deviceID
     * @return bool true 在线 false 不在线
     * @throws \Exception
     */
    public function isOnlineDevice(int $deviceID): bool
    {
        $online = DeviceModel::getOne($deviceID, 'isOnline');
        $cacheOnline = cache_get_info('DeviceID_', $deviceID, 'TDevice.Online');
        return $cacheOnline || (is_array($online) && (int)$online['online'] === 1);
    }

    /**
     * 获取设备可访问安全域
     * @param int $deviceID 设备ID
     * @param string|null $mac 设备MAC
     * @return int -1 隔离域 0 没有域 1 安全域
     * @throws \Exception
     */
    public function getDeviceIpRegion(int $deviceID, string $mac = null): int
    {
        $region = \DevIpRegionModel::getSingle(['DeviceID' => $deviceID, 'column' => 'net']);
        if (!is_array($region)) {
            if (!is_null($mac)) {
                $region = \DevIpRegionModel::getSingle(['MAC' => $mac, 'column' => 'net']);
                if (!is_array($region)) {
                    return 0;
                }
            } else {
                return 0;
            }
        }
        // 奇偶判断，按位与，结果1为奇数，0为偶数
        if ((int)$region['IPListId'] & 1) {
            return 1;
        }
        return -1;
    }

    /**
     * 通过MAC获取网络状态
     * @param string $mac
     * @return array
     * @throws Exception
     */
    public function getByMac(string $mac): array
    {
        return $this->getByMacFromServe($mac);
    }

    /**
     * 通过ip获取网络状态
     * @param string $ip
     * @return array
     * @throws Exception
     */
    public function getByIp(string $ip): array
    {
        return $this->getByIpFromServe($ip);
    }

    /**
     * 获取在线设备的信息.
     *
     * @param int $deviceID
     * @param string $column
     * @return array
     */
    public function getOnlineDeviceByDeviceID(int $deviceID, string $column = 'name'): array
    {
        return \NacOnLineDeviceModel::getSingle(['DeviceID' => $deviceID, 'column' => $column]) ?: [];
    }

    /**
     * 从服务端通过IP获取网络状态
     * @param string $ip
     * @param int $deviceId
     * @return array
     * @throws Exception
     */
    private function getByIpFromServe(string $ip, $deviceId = 0): array
    {
        if (!IS_INTERNAL) {
            return $this->runLoginResult($deviceId);
        }
        return $this->runResult($this->serveRun('-i ' . $ip), $deviceId);
    }

    /**
     * 从服务端通过MAC获取网络状态
     * @param string $mac
     * @param int $deviceId
     * @return array
     * @throws Exception
     */
    private function getByMacFromServe(string $mac, $deviceId = 0): array
    {
        if (!IS_INTERNAL) {
            return $this->runLoginResult($deviceId);
        }
        return $this->runResult($this->serveRun('-m ' . $mac), $deviceId);
    }

    /**
     * 处理服务端返回网络状态结果
     * 字段命名方式可能不一样，在此需要重新做下映射
     * @param $result
     * @param $deviceId
     * @return array
     */
    private function runResult($result, $deviceId = 0): array
    {
        $return['deviceStatus'] = (int)($result['device_status'] ?? 0);
        if (isset($result['IPListId'])) {
            $return['IPListId'] = (int)$result['IPListId'];
        } else {
            $return['IPListId'] = (int)$return['deviceStatus'] > 0 ? 1 : 0;
        }
        $return['isEmergency'] = isset($result['isEmergency']) ? (int)$result['isEmergency'] : 0;

        //  net环境下，查询设备是放开的，同时去TDevIpRegion表查询是否有记录，没有的话则将网络放开状态设置为0
        if ($return['deviceStatus'] == 1 && $deviceId > 0) {
            $region = \DevIpRegionModel::getSingle(['DeviceID' => $deviceId, 'column' => 'net']);
            if (empty($region)) {
                $return['deviceStatus'] = 0;
            }
        }
        return $return;
    }

    /**
     * 获取有效token
     * @param $deviceId
     * @return string
     * @throws Exception
     */
    private function getEffectiveToken($deviceId): string
    {
        if (empty($deviceId)) {
            T(21103001);
        }
        $token = $this->session['Token'] ?? '';
        if (empty($token)) {
            T(21120054);
        }
        // 前端依赖了这个token返回，暂时去掉状态判断。
        if (!isset($this->session['Uuid'])) {
            T(21120054);
        }
        $message = "session-token:" . $token . ',reqToken:' . ($this->session['ClientToken'] ?? '') . ',Client:' . ($this->session['Client'] ?? '');
        if (empty($this->session['IsLogin'])) {
            $this->writeLog($message);
            T(21120054);
        }

        return $token;
    }

    /**
     * 是否严格模式
     * @return bool
     */
    private function isStrict()
    {
        // 外网默认严格模式
        if (!IS_INTERNAL) {
            return true;
        }
        // 内网根据sysdebug命令开关控制 web_open_dict ZTP Strict 1 进行开启
        static  $cache = null;
        if ($cache !== null) {
            return $cache;
        }
        $userConfig = DictModel::getOneItem('ZTP', 'Strict');
        $cache = !empty($userConfig['ItemValue']);
        return $cache;
    }



    /**
     * 登录态的返回入网状态
     *
     * @param $deviceId
     *
     * @return array
     */
    private function runLoginResult($deviceId): array
    {
        $return = [];
        try {
            $net = \NetServiceProvider::getDeviceNetAble($deviceId);
            if (!$net) {
                T(21104019);
            }
            $token = $this->getEffectiveToken($deviceId);
        } catch (Exception $e) {
            $this->writeLog("runLoginResult:{$deviceId}:" . $e->getCode() . ':' . $e->getMessage());
        }
        $return['deviceStatus'] = !empty($token) ? 1 : 0;
        $return['IPListId'] = 0;
        $return['isEmergency'] = 0;
        return $return;
    }

    /**
     * 通过服务端获取网络状态
     * @param string $config 配置
     * 调用桥模块接口返回: { "device_status":"0", "IPListId":"0", "isEmergency":"0" }
     * device_status：终端入网状态 0 : 未入网; 1: 已入网
     * IPListId：终端所处安全域ID, 若未查找到安全域，则 IPListId = 0
     * isEmergency：设备紧急状态, 0: 非紧急； 1: 紧急
     * @return mixed
     * @throws \Exception
     */
    private function serveRun(string $config)
    {
        $cmd = $this->serveNetworkStatusCmdPath . " " . $config;
        $result = cutil_exec_wait($cmd);
        $this->writeLog("config:$config---->result:$result-->DeviceID:$this->deviceId");
        return json_decode($result, true);
    }

    /**
     * 通过RPC获取网络状态
     * @return mixed
     */
    public function rpcRun()
    {
        $result = NACServiceFacade::netStatus("DeviceStatusService", $this->deviceId);
        if (!empty($result) && (int)$result['code'] === 1) {
            $return = json_decode($result['data'], true);
        } else {
            $return = ['device_status' => 0, 'IPListId' => 0, 'isEmergency' => 0, 'isPermitClient' => 0];
        }
        $this->writeLog("res:" . var_export($result['data'] ?? '', true) . "-->DeviceID:$this->deviceId");

        return $return;
    }
}
