---
kind: pipeline
type: docker
name: default
platform:
  os: linux
  arch: amd64
steps:
  - name: compile
    pull: if-not-exists
    image: hub.infogo.tech/library/php:7.3
    commands:
      - php -v
      - composer -V
      - composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
      - composer install --prefer-dist
      - git archive --format=tgz --prefix=var/www/access.asm.com/ -o ${DRONE_REPO_NAME}.tgz HEAD $(git diff --name-only 1f30134d7e5575d4e013cb9de4fb49bb8f550c4f)
      - screw ./service/SystemServiceProvider.php
#      - tar zcf ${DRONE_REPO_NAME}.tgz --exclude=.git * .[!.]*
    when:
      event:
        - push
      branch:
        - master
        - 6039*
        - custom*
  - name: phpstan
    pull: if-not-exists
    image: hub.infogo.tech/library/php:7.3
    volumes:
      - name: phpstan
        path: /tmp/phpstan
    commands:
      - php -v
      - composer -V
      - composer config -g repo.packagist composer https://mirrors.aliyun.com/composer/
      - composer install --prefer-dist
      - ./vendor/bin/phpstan analyse --memory-limit 1024M
    when:
      event:
        - pull_request
  - name: handle
    image: hub.infogo.tech/library/bender
    when:
      event:
        - push
      branch:
        - master
        - 6039*
        - custom*
      status:
        - failure
        - success
    settings:
      upload: ${DRONE_REPO_NAME}.tgz
...
