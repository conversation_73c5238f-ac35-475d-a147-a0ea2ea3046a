<?php

/**
 * Description: 短信验证码
 * User: <EMAIL>
 * Date: 2021/05/13 14:21
 * Version: $Id: SmsCodeModel.php 158219 2021-09-28 16:36:47Z duanyc $
 */

class SmsCodeModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TSMSCode';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 单条
     *
     * @param string $mobile
     * @param string $checkCode
     * @param string $effTime
     *
     * @return mixed
     */
    public static function getOneByInfo($mobile, $checkCode, $effTime)
    {
        if (empty($mobile) || empty($checkCode)) {
            return false;
        }

        self::$data = [];
        $effTime = intval($effTime);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Tel = ".self::setData($mobile)." AND CheckCode = ".self::setData($checkCode)." AND Flag=0 and  
            UNIX_TIMESTAMP( InsertTime ) > ( UNIX_TIMESTAMP( now( ) ) - {$effTime} *60 ) and
             (UseTime is null)";
        $sql = "SELECT ID FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Tel'])) {
            $where .= "AND Tel = ".self::setData($cond['Tel']);
        }

        if (isset($cond['Flag'])) {
            $where .= "AND Flag = ".self::setData($cond['Flag']);
        }

        if (isset($cond['CheckCode'])) {
            $where .= "AND CheckCode = ".self::setData($cond['CheckCode']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
