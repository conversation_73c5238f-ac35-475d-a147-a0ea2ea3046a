<?php

/**
 * Description: XXXXXX
 * User: Administrator
 * Date: 2022/6/20 0020 上午 10:44
 * Version: $Id$
 */
class AuthDomainModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TAuthDomain';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'info' => 'ID,ServerConfig,IPSegment'
    ];

    /**
     * 获取指定类型的认证源
     * @param array $cond
     * @param string $column
     * @return array
     */
    public static function getOneByType(array $cond = [], $column = 'info')
    {
        self::$data = [];
        $where = !empty($cond) ? self::getWhere($cond) : '';

        $column = self::$columns[$column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 所有条目
     *
     * @param $cond
     * @param $column
     * @return mixed
     */
    public static function getAll(array $cond = [], $column = 'info')
    {
        self::$data = [];
        $where = !empty($cond) ? self::getWhere($cond) : '';

        $column = self::$columns[$column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = " . self::setData($cond['Type']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}