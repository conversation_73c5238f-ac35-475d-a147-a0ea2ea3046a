<?php
/**
 * Description: 部门信息服务
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: DepartService.php 158669 2021-10-11 09:45:12Z duanyc $
 */

namespace Services\Common\Services;

use Services\Common\Exceptions\AsmException;
use Services\Common\Interfaces\DepartServiceInterface;

class DepartService extends CommonService implements DepartServiceInterface
{
    /**
     * 根据ip获取所在部门.
     *
     * @param $ip
     *
     * @throws AsmException
     *
     * @return int|mixed
     */
    public function getDepartByIp($ip)
    {
        try {
            //根据IP从部门表中取部门ID，默认为0
            $departId = 0;
            $aDepart = \DepartModel::getAll();
            $afDep = array();
            foreach ($aDepart as $item) {
                if ('' !== $item['IPSegment']) {
                    $aAllIp = $item['IPSegment'];
                    $aIp = explode('|', $aAllIp);
                    if (\is_array($aIp)) {
                        foreach ($aIp as $str) {
                            $ips = explode('-', $str);
                            if (2 === \count($ips)) {
                                list($startIp, $endIp) = $ips;
                                if (1 === FindInIP($ip, $startIp, $endIp)) {
                                    $afDep[$item['DepartID']] = CompareIP($startIp, $endIp, 1);
                                    break;
                                }
                            }
                        }
                    }
                }
            }
            if (\count($afDep) > 0) {
                krsort($afDep);
                $aDep = array_keys($afDep);
                $departId = $aDep[0];
            }
        } catch (\Exception $e) {
            // 公共service不进行异常处理由调用层去处理
            throw new AsmException($e->getMessage(), $e->getCode());
        }

        return $departId;
    }

    /**
     * 获取设备根部门.
     *
     * @param int $departId
     * @param int $level
     *
     * @throws AsmException
     *
     * @return bool|mixed
     */
    public function getRootDepartID($departId, $level = 0)
    {
        try {
            if ($level > 10) {
                return false;
            }
            $aResult = \DepartModel::getOne($departId, 'one');
            if (!\is_array($aResult)) {
                return false;
            }
            if (empty($aResult['UpID'])) {
                return $aResult['DepartID'];
            }

            return $this->getRootDepartID($aResult['UpID'], $level + 1);
        } catch (\Exception $e) {
            // 公共service不进行异常处理由调用层去处理
            throw new AsmException($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 更新子操作员部门管理范围
     *
     * @param $UpDepartID
     * @param $DepartID
     */
    public function updateOperatorManagerScope($UpDepartID, $DepartID)
    {
        //更新子操作员部门管理范围
        $aOperator = \OperatorModel::getAll('username');
        if (is_array($aOperator)) {
            $opParams = [];
            foreach ($aOperator as $row) {
                $cond = ['UserName' => $row['UserName'], 'DepartID' => $UpDepartID];
                $num = \OpDepartLimitModel::getCount($cond);
                if ($num > 0 || $row['UserName'] =='admin' || $row['UserName'] == 'secadmin') {
                    $opParams[] = ['UserName' => $row['UserName'], 'DepartID' => $DepartID];
                }
            }
            \OpDepartLimitModel::insertPatch($opParams);
        }
    }
}
