<?php

/**
 * Description: 用户信息
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: UserInfoRedis.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class DynamicPolicyRedis extends BaseStringRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const PREFIX = 'ASM_';
    public const TABLE_NAME = 'DynamicPolicy';


    /**
     * 单条
     *
     * @param $deviceID
     * @return mixed
     */
    public static function getOne($deviceID)
    {
        return self::get($deviceID);
    }

    /**
     * 单条
     *
     * @param $deviceID
     * @param  $data
     *
     * @return mixed
     */
    public static function setOne($deviceID,  $data)
    {
        return self::set(is_array($data)?json_encode($data):$data,0,$deviceID);
    }
}
