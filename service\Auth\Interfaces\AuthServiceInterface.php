<?php
/**
 * Description: 认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: AuthServiceInterface.php 158480 2021-10-08 08:34:33Z huyf $
 */

namespace Services\Auth\Interfaces;

interface AuthServiceInterface
{
    /**
     * AuthServiceInterface constructor.
     * @param $params
     */
    public function __construct($params);

    /**
     * 解析参数
     *
     * @return array
     */
    public function parseParams();

    /**
     * 认证后处理
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     * @return int
     */
    public function authAfter(&$data, $userInfo, $authType);

    /**
     * 获取敲门认证的参数
     *
     * @param $Data
     *
     * @return mixed
     */
    public function getFwknopData($Data);

}
