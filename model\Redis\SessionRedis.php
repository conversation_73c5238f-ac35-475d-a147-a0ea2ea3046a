<?php

/**
 * Description: 登录session
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: SessionRedis.php 175039 2022-05-05 07:33:41Z duanyc $
 */

class SessionRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'UserSession';

    /**
     * session key 用于批量查询key是否存在等场景
     *
     * @var string
     */
    public const SESSION_KEY = self::PREFIX . self::TABLE_NAME . ':';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'Uuid,AccountID,DeviceID,DevName,DevInfo,UserName,TrueNames,LastTime,DepartIDs,RoleID,UserType,LastHeartTime,IsInternal,AuthTime,' .
    'SessionStatus,ConnectIp,vpnIpList,vpnIpRangleAll,vpnRouteListAll,LifeTime,PolicyID,AuthApp,AuthAppID,SelectMode,GateDomain,LastMD5,ztpUserExceed,ResPowerIds,LastUpdateTime,' .
    'Policys,IpResPolicys,AgentGate,IpListGate,OsType,Client,SceneID,VirIp,VirIpInfo,Mac,IP,DepartName,upTrafficLimit,downTrafficLimit,Type,AuthorizationIDs,AuthAppGate,AuthAppAll';


    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = 'Uuid,DeviceID,DevName,DevInfo,UserName,TrueNames,LastTime,SessionStatus,ConnectIp,' .
    'IpResPolicys,vpnIpList,LifeTime,AuthApp,AuthAppID,VirIp,upTrafficLimit,downTrafficLimit';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['LifeTime', 'Uuid', 'IsInternal', 'DeviceID', 'Client'],
        'cookie' => ['Uuid', 'UserName', 'DeviceID', 'LifeTime'],
        'user' => ['Uuid', 'AccountID', 'Client','OsType','IsInternal', 'UserName', 'TrueNames', 'DeviceID', 'DevInfo', 'DepartIDs', 'RoleID', 'AuthAppID', 'AuthApp', 'LifeTime', 'VirIp', 'AuthorizationIDs'],
        'update' => ['UserName', 'DepartIDs', 'RoleID', 'TrueNames'],
        'scene' => ['Uuid', 'DeviceID', 'LifeTime', 'SceneID'],
        'policy' => ['Uuid', 'Client', 'AccountID', 'UserName', 'TrueNames', 'DepartIDs', 'RoleID', 'UserType', 'DeviceID', 'DevInfo', 'ConnectIp', 'DevName', 'IsInternal', 'AuthTime',
            'PolicyID', 'AuthAppID', 'vpnIpList', 'OsType', 'AuthorizationIDs', 'LastHeartTime', 'LifeTime', 'VirIp', 'vpnIpRangleAll', 'Mac', 'DepartName', 'SessionStatus', 'ResPowerIds', 'LastUpdateTime'],
        'online' => ['LifeTime', 'Uuid', 'DeviceID', 'LastHeartTime'],
        'ipres' => ['vpnIpList'],
        'check' => ['SessionStatus', 'Uuid','LastUpdateTime'],
        'status' => ['SessionStatus', 'LastTime', 'Uuid', 'Client'],
        'hisotry' => ['TrueNames', 'DepartName', 'IP', 'ConnectIp', 'Mac', 'OsType', 'Client', 'Type'],
        'gateway' => ['Uuid', 'IpListGate'],
        'usergw' => ['Uuid', 'AccountID', 'UserName', 'TrueNames', 'DeviceID', 'DepartIDs', 'RoleID', 'AuthAppID', 'LifeTime', 'VirIp', 'IpListGate', 'DevName', 'UserType', 'ConnectIp', 'VirIpInfo', 'vpnIpList', 'vpnIpRangleAll', 'Mac', 'DevInfo','AuthAppGate','AuthAppAll'],
        'gwvirip' => ['Uuid', 'VirIpInfo', 'GateDomain', 'IpListGate', 'vpnIpRangleAll'],
        'resourcePower' => ['vpnIpList', 'AuthApp', 'AuthAppID'],
        'ztpUserExceed' => ['ztpUserExceed']
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['AuthApp' => true, 'Policys' => true, 'AuthAppID' => true, 'GateDomain' => true, 'ResPowerIds' => true,
        'AgentGate' => true, 'IpListGate' => true, 'IpResPolicys' => true, 'vpnIpRangleAll' => true, 'vpnRouteListAll' => true, 'VirIp' => true, 'upTrafficLimit' => true, 'downTrafficLimit' => true];


    /**
     * 需要通知的字段
     * @var array
     */
    protected static $noticeColumns = ['GateDomain', 'LastMD5'];

    /**
     * 单条
     *
     * @param string $token
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($token, $column = 'one')
    {
        if (strlen($token) !== 32) {
            return false;
        }
        $result = self::get($column, $token);
        if (!empty($result)) {
            $result['Token'] = $token;
        }
        return $result;
    }

    /**
     * 单条
     *
     * @param string $token
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($token, $data)
    {
        if (strlen($token) !== 32) {
            return false;
        }
        $return = self::set($data, $token);
        if ($return) {
            $cDatas = self::getChangeDatas();
            $session = self::getOldDatas();
            // 是否需要调用resource/distributeConfig接口下发的字段，变更后发通知
            if (self::isNeedNotice(self::$noticeColumns, $cDatas, $session)) {
                NoticeServiceProvider::noticeZtpConfigChange($session['DeviceID']);
            } elseif (self::isNeedNotice(['vpnIpRangleAll', 'vpnRouteListAll'], $cDatas, $session)) {
                VpnServiceProvider::noticeClientChange($session, $cDatas);
            }
            if (isset($cDatas['vpnIpRangleAll'], $cDatas['vpnRouteListAll'])) {
                $IpListGate = $cDatas['IpListGate'] ?? $session['IpListGate'];
                VpnServiceProvider::noticeGatewayVpn($token, $IpListGate, $cDatas['vpnIpRangleAll'], $cDatas['vpnRouteListAll']);
            }
            //设置成功时返回为具体修改字段内容值
            $return=$cDatas;
        }
        return $return;
    }

    /**
     * 是否需要通知
     *
     * @param $noticeColumns
     * @param $data
     * @param $session
     * @return bool
     */
    private static function isNeedNotice($noticeColumns, $data, $session)
    {
        $isChange = false;
        foreach ($noticeColumns as $column) {
            if (isset($data[$column]) && isset($session[$column])) {
                $isChange = true;
                break;
            }
        }
        return $isChange;
    }

    /**
     * 清理脏数据
     * @param $TokenList
     * @return bool
     */
    public static function clearDirtyAll($TokenList): bool
    {
        if (empty($TokenList)) {
            return false;
        }
        $prefix = static::PREFIX . static::TABLE_NAME;
        $keys = lib_redis::keys($prefix);
        foreach ($keys as $key) {
            $token = str_replace($prefix . ":", '', $key);
            if (!isset($TokenList[$token])) {
                self::del($token);
            }
        }
        return true;
    }

    /**
     * 删除
     *
     * @param string $token
     *
     * @return mixed
     */
    public static function deleteOne($token)
    {
        return self::del($token);
    }

    /**
     * 删除全部
     *
     * @return mixed
     */
    public static function deleteAll()
    {
        return self::delPatch();
    }
}
