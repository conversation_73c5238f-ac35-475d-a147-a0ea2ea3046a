<?php

/**
 * Description: 资源访问日志
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: ResVisitLogModel.php 174776 2022-04-28 09:18:26Z duanyc $
 */

class ResVisitLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResVisitLog';
    public const PRIMARY_KEY = 'VisitID';
    protected static $columns = [
        '*'       => '*',
        'history' => 'MAX(VisitID) as VisitID, ResID',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
