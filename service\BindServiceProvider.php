<?php

/**
 * Description: 绑定认证相关逻辑service
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: LoginServiceProvider.php 174874 2022-04-29 03:36:56Z duanyc $
 */
class BindServiceProvider extends BaseServiceProvider
{

    /**
     * 获取主账号绑定认证相关配置
     * @return mixed
     */
    public static function getUserDict()
    {
        return \DictModel::getAll('User');
    }

    /**
     * 获取用户信息
     *
     * @param $UserID
     * @return mixed
     */
    public static function getUserInfo($UserID)
    {
        if (empty($UserID)) {
            return [];
        }
        $userData = AuthUserModel::getOne($UserID, 'qrcode');
        $userData['RelationConfig'] = \AccountModel::getList(['user_id' => $UserID, 'column' => 'one']);
        return $userData;
    }

    /**
     * 检查认证方式是否为指定的主账号类型的方式
     * @param $userDict
     * @param $authType
     * @return int
     */
    public static function checkAuthType($userDict, $authType)
    {
        if (empty($authType)) {
            return 1;
        }
        if ($userDict['IsMainAccount'] === '1' && $authType === $userDict['MainAccountType']) {
            return 0;
        }
        return 1;
    }

    /**
     * 准入认证的用户检查
     * @param $mainAccountType
     * @param $usreData
     * @return int
     */
    public static function checkUser($mainAccountType, $usreData)
    {
        foreach ($usreData['RelationConfig'] as $item) {
            if ($item['auth_type'] === $mainAccountType) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 主账号用户检查
     * @param $beforeAuthType
     * @param $usreData
     * @return int
     */
    public static function checkMainUser($beforeAuthType, $usreData)
    {
        foreach ($usreData['RelationConfig'] as $item) {
            if ($item['auth_type'] === $beforeAuthType) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * @param $relationJson string 用户绑定账号数据
     * @return int 1/0，1表需要绑定认证；0表不需要绑定认证
     */
    public static function getIsMainBind($relationJson)
    {
        $userDict = \DictModel::getAll('User');
        if ($userDict['MainAccountType'] === 'Localhost') {
            $userDict['MainAccountType'] = 'User';
        }
        if ($userDict['MainAccountType'] === 'Other') {
            $userDict['MainAccountType'] = 'WebAuth';
        }
        if (!empty($userDict['MainAccountType']) && $userDict['IsMainAccount'] === '1') {
            $userType = json_decode($relationJson, true);
            if (!is_array($userType) || isset($userType['Guest'])) {
                return 0;
            }
            if (!isset($userType[$userDict['MainAccountType']]) || $userType[$userDict['MainAccountType']] === 0) {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 主账号绑定处理
     * @param $User array 老用户
     * @param $MainUser array 主用户
     */
    public static function bindMainUser($User, $MainUser)
    {
        $accountInfo = [];
        foreach ($MainUser['RelationConfig'] as $mItem) {
            $accountInfo[$mItem['AccountID']] = $mItem['auth_type'];
        }
        $idInfo = [
            'in' => [], 'no' => []
        ];
        foreach ($User['RelationConfig'] as $item) {
            if (in_array($item['auth_type'], $accountInfo, true)) {
                $idInfo['in'][] = $item['AccountID'];
            } else {
                $idInfo['no'][] = $item['AccountID'];
            }
        }
        // 为空说明主用户关联的账号类型包含了老用户关联的账号类型
        if (!empty($idInfo['no'])) {
            //将前用户所关联的账号，关联到主账号上
            \AccountModel::updatePatch(['AccountID_in' => $idInfo['no']], ['user_id' => $MainUser['ID']]);
            $relationConfig = \AccountModel::getSingle(['user_id' => $MainUser['ID'], 'column' => 'json']);
            $updateColumns = ['RelationConfig' => $relationConfig['relation']];
            if ($User['Type'] === 'User') {
                // 禅道bugID=4147
                $updateColumns['PassUpdateTime'] = $User['PassUpdateTime'];
            }
            \AuthUserModel::update($MainUser['ID'], $updateColumns);
        }
        if (empty($idInfo['in'])) {
            //前用户没有关联了账号，删除之
            \AuthUserModel::delete(['ID' => $User['ID']]);
            $devtype = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
            if ($devtype === 'dasc') {
                // 如果是DASC，需要上报到DASM再由DASM下发到其他DASC
                single_table_pub('TAuthUser', ['ID' => $User['ID']], 'delete');
            }
        } else {
            // 更新前用户的关联信息
            $relationConfig = \AccountModel::getSingle(['user_id' => $User['ID'], 'column' => 'json']);
            \AuthUserModel::update($User['ID'], ['RelationConfig' => $relationConfig['relation']]);
        }

        unset($accountInfo, $idInfo, $relationConfig, $updateColumns);
    }

    /**
     * 主账号绑定前的合法性检查
     * @param $bindAuthServer string 主账号认证源类型
     * @param $userInfo array 老用户信息
     */
    public static function bindCheck($bindAuthServer, $userInfo)
    {
        $userDict = \DictModel::getAll('User');
        //检查认证类型是否为主账号类型
        if (\BindServiceProvider::checkAuthType($userDict, $bindAuthServer)) {
            T(********);
        }

        //检查前用户是否存在
        if (empty($userInfo['ID'])) {
            T(********);
        }

        //检查前用户是否已经关联主账号
        if (\BindServiceProvider::checkUser($userDict['MainAccountType'], $userInfo)) {
            T(********);
        }
    }

    /**
     * 检查主账号是否已经绑定了前用户认证的账号类型
     * @param $oldAccount
     * @param $mainUser
     * @param $params
     * @return int
     */
    public static function checkAccoutType($oldAccount, $mainUser, $params)
    {
        if (!empty($oldAccount)) {
            foreach ($mainUser['RelationConfig'] as $item) {
                if ($item['auth_type'] === $oldAccount['auth_type'] && $item['auth_name'] !== $oldAccount['auth_name']) {
                    return 1;
                }
            }
        }
        else{
            $auth_type = $params['bindAuthServer'];
            if ($auth_type === 'Localhost') {
                $auth_type = 'User';
            }
            if ($auth_type === 'Other') {
                $auth_type = 'WebAuth';
            }
            $oldAccount = [
                'auth_type' => $auth_type,
                'auth_name' => $params['userName']
            ];
            foreach ($mainUser['RelationConfig'] as $item) {
                if ($item['auth_type'] === $oldAccount['auth_type'] && $item['auth_name'] === $oldAccount['auth_name']) {
                    return 1;
                }
            }
        }
        return 0;
    }

    /**
     * 主账号绑定认证处理;返回是否需要主账号绑定认证标记
     * @param $data array 认证完需返回的数据
     * @param $userInfo array 认证用户信息
     * @param $params array 认证传递过来的参数
     */
    public static function bindMainUserDeal(&$data, $userInfo, &$params)
    {
        if (request('authFrom', 'request') === 'bind') {
            $olduserInfo = self::getUserInfo($params['userid']);
            $mainUserInfo = self::getUserInfo($data['ID']);
            //检查主账号是否已经绑定了前用户认证的账号类型
            $oldAccountInfo = AccountModel::getOne($params['oldAccountID'], 'one');
            if (self::checkAccoutType($oldAccountInfo, $mainUserInfo, $params)) {
                T(********);
            }
            //绑定处理
            self::bindMainUser($olduserInfo, $mainUserInfo);
            $params['factorAuth'] = false; //主账号绑定不要求做双因子
            $data['BindAuth'] = 0; //主账号绑定认证标记
        } else {
            $data['BindAuth'] = self::getIsMainBind($userInfo['RelationConfig']);//主账号绑定认证标记
            $userDict = DictModel::getOneItem('User', 'MFAServer');
            $data['User']['MFAServer'] = $userDict['ItemValue'];
            if (IS_MOBILE) { // 移动端暂不支持[企业微信]主账号绑定认证bugID:5617
                $userDict = DictModel::getOneItem('User', 'MainAccountType');
                if ($userDict['ItemValue'] === 'WeWork') {
                    $data['BindAuth'] = 0;
                }
            }
            unset($userDict);
        }
    }
}
