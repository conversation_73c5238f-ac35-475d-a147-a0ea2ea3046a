<?php

/**
 * Description: dasm的用户、部门相关 ，必须为protected，父类__call方法会包装数据
 * User: <EMAIL>
 * Date: 2021/08/03 15:53
 * Version: $Id: DuserController.php 168067 2022-01-27 02:47:50Z duanyc $
 */

require  PATH_ROOT . "/webroot/rpc/AsmYarServer.php";

class DuserController extends AsmYarServer
{
    /**
     * 指定服务
     * @var string
     */
    protected $_server = 'duser';

    /**
     * 添加用户，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function addUser($params)
    {
        $userId = UserServiceProvider::saveUser($params);
        return ['userId' => $userId];
    }

    /**
     * 添加部门，必须为protected
     *
     * @return mixed
     * @throws Exception
     */
    protected function addDepart($params)
    {
        $departId = UserServiceProvider::saveDepart($params);
        return ['departId' => $departId];
    }

    /**
     * 获取用户分布式ID，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getUserId($params)
    {
        $userId = UserServiceProvider::getSequenceId('user');
        return ['userId' => $userId];
    }

    /**
     * 获取账号分布式ID，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getAccountId($params)
    {
        $userId = UserServiceProvider::getSequenceId('account');
        return ['accountId' => $userId];
    }

    /**
     * 获取部门分布式ID，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getDepartId($params)
    {
        $departId = UserServiceProvider::getSequenceId('depart');
        return ['departId' => $departId];
    }

    /**
     * 获取配置信息，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getServerInfo($params)
    {
        ConfigServiceProvider::updateDictCache();
        $return = ServerServiceProvider::getServerInfo();
        ServerServiceProvider::setServerDict($return, false);
        $return['ADDOMAINSVR'] = ServerServiceProvider::getAdDomainSevInfo();
        // 获取客户端ip地址用于无控件判断
        $return['IPADD'] = getRemoteAddress();
        $return['iOS_URL'] = ServerServiceProvider::getIosDownloadUrl();
        $return['IPException'] = ServerServiceProvider::getIpException($return);
        $return['PRODUCTINFO'] = ServerServiceProvider::getProductInfo();
        $return['EnvSwitch'] = ServerServiceProvider::getEnvSwitchConfig();
        // 处理数据
        ServerServiceProvider::dealServerData($return);
        //ServerServiceProvider::dealFeiShuData($return);
        ServerServiceProvider::initBridgeInfo($return);
        ServerServiceProvider::initServerDevinfo($return);
        ServerServiceProvider::initDomainAddress($return);
        // 添加公钥信息
        getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');
        $return['pubKey'] = getRsaPubKey('net_auth', PATH_HTML . '/download/rsa/');
        $return['mobile'] = SystemServiceProvider::getMobileConfig();
        return $return;
    }

    /**
     * 修改登录信息，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function updateLoginUserInfo($params)
    {
        if (!isset($params['ObjectType'])) {
            return false;
        }
        switch ($params['ObjectType']) {
            case 0:
                if (!is_array($params['UserID'])) {
                    $params['UserID'] = [$params['UserID']];
                }
                foreach ($params['UserID'] as $userId) {
                    NoticeServiceProvider::noticeUpdateUserinfo($userId);
                }
                break;
            case 2:
                $DepartIds = is_array($params['DepartID']) ? $params['DepartID'] : [$params['DepartID']];
                NoticeServiceProvider::noticeUpdateDepartUserinfonotice($DepartIds);
                break;
            case 3:
                $DeviceIds = is_array($params['DeviceID']) ? $params['DeviceID'] : [$params['DeviceID']];
                NoticeServiceProvider::noticeUpdateDeviceUserinfo($DeviceIds);
                break;
        }
        return true;
    }

    /**
     * 通知用户下线
     * @param $params
     * @return bool
     */
    protected function noticeUserOffline($params)
    {
        if (empty($params['UserIDs'])) {
            return false;
        }
        foreach ($params['UserIDs'] as $userId) {
            LoginServiceProvider::offlineUserInfo($userId);
        }
        return true;
    }

    /**
     * 上报ASG的dns，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function reportDns($params)
    {
        if (!isset($params['DNS'], $params['managerip'])) {
            return false;
        }
        GatewayServiceProvider::updateGatewayDns($params['managerip'], $params['DNS'], $params['StdDNS'] ?? '');
        return [];
    }

    /**
     * 获取OTP二维码内容
     * @param $params
     * @return array|bool
     * @throws Exception
     */
    protected function getOTPQrcode($params)
    {
        if (!isset($params['userId'])) {
            return false;
        }
        $params['servicePrefix'] = 'OTP';
        $OTPService = AuthServiceProvider::initAuthService($params);
        $url = $OTPService->getOTPQrcodeImg($params['userId'], true);   //生成新的令牌激活密钥的二维码内容
        return ['url' => $url];
    }

    /**
     * 清理授权缓存，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function clearModuleRegist($params)
    {
        $RegistKeys = lib_redis::keys('ASG_ModuleRegist*');
        if (!empty($RegistKeys)) {
            lib_redis::mDel('', $RegistKeys);
        }
        return true;
    }

    /**
     * 生成sdpkey
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    protected function genSDPkey($params)
    {
        $path = PATH_ETC . "sdp.ini";
        $sdpInfo = get_ini_info($path);
        // 已生成过，则不再重新生成
        if ($sdpInfo['SDPKey'] !== 'init') {
            return true;
        }
        $sdpInfo['SDPKey'] = Base64EnExt(randomStr(16), ["_", "-", "."]);
        write_localfile($path, arrayToIniString($sdpInfo));
        return true;
    }

    /**
     * 来宾预约
     * @param $data
     * @return array
     * @throws Exception
     */
    protected function netcode($data)
    {
        lib_request::$requests = $data;
        $params = ['from' => 'html_netcode'];
        $params['UserID'] = $data['UserID'] ?? '';
        $params['userName'] = $data['userName'] ?? '';
        $params['AllowRegionIDs'] = $data['AllowRegionIDs'] ?? '1';
        $params['AllowTime'] = $data['AllowTime'] ?? 8;
        $params['IsNeedAudit'] = $data['IsNeedAudit'] ?? 0;
        $defaultStartTime = date('Y-m-d', time());
        $defaultEndTime = date('Y-m-d H:i:s', strtotime($defaultStartTime . ' +180 days') + 86400 - 1);
        $params['GuestStartTime'] = !empty($data['GuestStartTime']) ? $data['GuestStartTime'] : $defaultStartTime;
        $params['GuestEndTime'] = !empty($data['GuestEndTime']) ? date('Y-m-d H:i:s', strtotime($data['GuestEndTime']) + 86400 - 1) : $defaultEndTime;
        $params['guestName'] = $data['guestName'] ?? '';
        $params['guestMobile'] = $data['guestMobile'] ?? '';
        $params['guestCompany'] = $data['guestCompany'] ?? '';
        $params['content'] = $data['content'] ?? '';
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
        $return = $guestService->userGenerateNetcode($params, true);
        cutil_php_log($return, 'GuestNetcode');
        return $return;
    }
}
