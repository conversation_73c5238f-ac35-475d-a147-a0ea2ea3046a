<?php

/**
 * Description: Msep下发任务表
 * User: <EMAIL>
 * Date: 2024/04/08 10:32
 */

class LinkAgeTaskModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TLinkageTask';
    public const PRIMARY_KEY = 'TID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = " . self::setData($cond['Type']);
        }

        if (isset($cond['TID'])) {
            $where .= "AND TID in (" . self::setArrayData($cond['TID']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * 获取最新的RID
     * @param $deviceID
     * @return mixed
     * @throws Exception
     */
    public static function getLastRIds($deviceID)
    {
        if (empty($deviceID)) {
            return false;
        }
        $ids = explode(',', $deviceID);
        self::$data = [];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "select Min(TID) as TID from TLinkageTask where Status=0 and DeviceID in(".self::setArrayData($ids).") GROUP BY DeviceID";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }
}
