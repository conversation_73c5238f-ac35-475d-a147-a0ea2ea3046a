<?php

/**
 * Description: 零信任在线用户连接数10天趋势
 * User: <EMAIL>
 * Date: 2024/04/15 23:32
 */

class OnlineMaxUsersRedis extends BaseStringRedis
{

    public const PREFIX = 'ASM_';
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Online:MaxUsers';

    /**
     * 是否同步到ASG
     *
     * @var string
     */
    protected static $remote = false;

    /**
     * 单条
     *
     * @param string $date 日期
     * @return mixed
     */
    public static function getOne(string $date)
    {
        return self::get($date);
    }


    /**设置单条
     * @param $date //时间
     * @param $number  //数量
     * @param $expire  //过期时间
     * @return false|mixed
     */
    public static function setOne($date, $number, $expire)
    {
        return self::set($number, $expire, $date);
    }


    /**
     * 删除
     *
     * @param string $token
     *
     * @return bool
     */
    public static function deleteOne(string $token): bool
    {
        return self::del($token);
    }

}
