<?php

/**
 * Description: Linux补丁信息
 * User: <EMAIL>
 * Date: 2025/01/03 10:02
 * Version: $Id
 */

class LinuxPatchBaseModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TLinuxPatchBase';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取补丁详情.
     *
     * @param array $cond
     * @return false|mixed|null
     */
    public static function getDetail($cond = [])
    {
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE A.PublicID = " . self::setData($cond['revisionid']);
        $sql = "SELECT A.RevisionID, B.Title, B.Description, B.ReleaseDate as UpdateTime, B.Level as UpdateType, 
                        A.PublicID AS KBIDS,  A.DownURL, '' as InstallCommand_Arguments
                FROM  TLinuxPatchFile A inner JOIN TLinuxPatchBase B ON A.PublicID = B.PublicID " . $where;
        cutil_php_log($sql, 'model_select');
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }
}