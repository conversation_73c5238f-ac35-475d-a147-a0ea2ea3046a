<?php

/**
 * Description: 角色
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: NetRoleModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class NetRoleModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNetRole';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'ID AS RoleID,RoleName,CreateUser,ChangeUser,ChangeTime,IsPrivate,Remark',
        '*' => '*',
    ];

    /**
     * 来宾角色名称和ID
     *
     * @param $RoleID
     *
     * @return array|bool
     */
    public static function getGuestCodeRoleList($RoleID)
    {
        if (empty($RoleID)) {
            return false;
        }

        self::$data = [];
        $sql = "SELECT b.ID,b.RoleName from TGuestCodeRoleRelation a LEFT JOIN TNetRole b on  a.AssignedRoleID=b.ID 
         where a.RoleID = " . self::setData($RoleID) . " ORDER BY CASE WHEN  a.DefRoleID = a.AssignedRoleID  THEN 1 ELSE 2 END ASC";
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取可分配角色
     *
     * @param $RoleID
     *
     * @return bool|mixed
     */
    public static function getGuestAssignedRoleList($RoleID)
    {
        if (empty($RoleID)) {
            return false;
        }

        self::$data = [];
        $sql = "SELECT B.ID,B.RoleName,A.DefRoleID FROM TGuestCodeRoleRelation  A left join TNetRole B 
          ON A.AssignedRoleID=B.ID where RoleID = " . self::setData($RoleID);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取可分配安全域
     *
     * @param $RoleID
     *
     * @return bool|mixed
     */
    public static function getGuestAssignedRegionList($RoleID)
    {
        if (empty($RoleID)) {
            return false;
        }

        self::$data = [];
        $sql = "SELECT	B.RID,	B.RegionName, A.DefRoleID FROM TGuestCodeRoleRelation A LEFT JOIN TIpRegion B 
            ON A.AssignedRoleID = B.RID WHERE RoleID = " . self::setData($RoleID);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['RoleID'])) {
            $where .= "AND ID = " . self::setData($cond['RoleID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
