<?php

/**
 * Description: 记录下发vpn资源ID IP
 */

class ResIPModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResIP';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'list' => 'ID,ResID,IP'
    ];

    /**
     * 插入
     *
     * @param array $key_values
     *
     * @return bool|int
     */
    public static function duplicate($key_values = [])
    {
        if (empty($key_values) || empty($key_values['ResID']) || empty($key_values['IP'])) {
            return false;
        }

        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $result = lib_database::duplicate($key_values, $table['name'], $table['index']);
        cutil_php_log(json_encode(['model_insert', $table, $key_values]), "model_{$table['name']}");

        if ($result) {
            return lib_database::insertId();
        }

        return $result;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }
        if (!empty($cond['NotInResID'])) {
            $where .= "AND ResID NOT IN (" . self::setArrayData($cond['NotInResID']) . ")";
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
