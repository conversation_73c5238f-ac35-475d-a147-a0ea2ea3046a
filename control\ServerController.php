<?php

/**
 * Description: 服务器相关
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: ServerController.php 175451 2022-05-10 03:53:17Z huyf $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class ServerController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = [
        'lang' => true, 'info' => true, 'help' => true, 'parameter' => true,
        'imgcode' => true, 'ipv6' => true, 'start' => true, 'config' => true, 'getQrCode' =>true, 'getQrCodeImg' =>true
    ];

    /**
     * 启动时调用的接口
     * return array
     */
    public function start(): array
    {
        $return = [];
        $return['AsmConfig'] = ServerServiceProvider::getAsmConfig();
        return $return;
    }

    /**
     * 获取服务器信息，对应老交易 get_server_info
     * TDict表的数据需要在inc_dict.php配置才能返回
     *
     * @return array
     * @throws Exception
     */
    public function info()
    {
        // 监控平台调用时，金仓组建双机时特殊处理返回值，避免失联
        $from = request('from', 'request');
        if ($from === 'MP') {
            $return = ServerServiceProvider::initHaInfo();
            if (is_array($return)) {
                return $return;
            }
        }

        // 金仓组双机中，无法使用数据库，模拟接口正常返回
        $isMobile = request('isMobile', 'request', 0, 'int');
        $agentPromotion = request('agentPromotion', 'request', 0, 'int');
        $lang = request('local_lguage_set', 'request', '');
        $ascid = request('ascid', 'request');
        $firsturl = request('firsturl', 'request');
        $basIP = request('basIP', 'request', '');
        $version = request('version', 'request', '');
        // 如果版本号为空，则说明是老的版本需要提示客户端升级 (老的调用方式先不做限制)
        if (empty($version) && FORCED_UPGRADE == 1 && API_VERSION >= '1.0') {
            T(21101015);
        }
        $return = ServerServiceProvider::getServerInfo();
        ServerServiceProvider::setServerDict($return);
        $return['GUESTAUTH']['authority'] = getmoduleregist(31); // 来宾高级权限
        // 获取客户端ip地址用于无控件判断
        $return['IPADD'] = getRemoteAddress();
        $return['iOS_URL'] = ServerServiceProvider::getIosDownloadUrl();
        $return['Global_lang'] = ServerServiceProvider::getGlobalLang($lang);
        $return['IPException'] = ServerServiceProvider::getIpException($return);
        $return['PRODUCTINFO'] = ServerServiceProvider::getProductInfo();
        $return['ROOTDEPART'] = [];
        $return['ipamConfig'] = ServerServiceProvider::ipamConfig();
        $return['ADDOMAINSVR'] = ServerServiceProvider::getAdDomainSevInfo();
        // 移动终端界面的背景色
        $return['mobile_Web_Bgcolor'] = ConfigServiceProvider::getDictOne('Mobile', 'mobile_Web_Bgcolor');
        // 沙箱SDC
        $return['sdcState']=DictModel::getOneItem('SDC', 'State');
        // 处理数据
        ServerServiceProvider::dealServerData($return);
        //ServerServiceProvider::dealFeiShuData($return);
        ServerServiceProvider::initBridgeInfo($return);
        ServerServiceProvider::initServerDevinfo($return);
        ServerServiceProvider::initManagerIp($return, $basIP);//顺序提前，客户端文件软链依赖管理口IP
        // 复制客户端/控件安装文件
        ServerServiceProvider::initClientInstallFile($return, $agentPromotion);
        $return['TIME'] = func_time_getNow(); // 系统当前时间需要实时获取
        ServerServiceProvider::initDomInfo($return);
        $return['PrivateSet'] = ServerServiceProvider::getPrivateSet();
        ServerServiceProvider::initServerLang($lang, $return);
        // 增加IPV6 获取IPv6管理地址
        $ipv6Ini = get_ini_info(PATH_ETC . 'asm/asc/etc/tbridge_private.ini', 'ipv6_bridge_enable');
        $return['IPV6_BRIDGE_ENABLE'] = $ipv6Ini;
        $return['http_Port'] = getHttpPort();
        $return['https_Port'] = getHttpPort(true);
        $return['Encryption'] = ServerServiceProvider::getEncryptionConf();
        $return['RemoteCtrl'] = getmoduleregist(15); //远程维护模块授权
        $return['IPIsInException'] = GatewayServiceProvider::getIPIsInException();
        //获取主题色配置
        ServerServiceProvider::getThemeColor($return);

        if (IS_MOBILE) {
            $isForceUpdate = DictModel::getOneItem('Mobile', 'isForceUpdate');
            $return['mobileForceUpdate'] = [
                'isForceUpdate' => $isForceUpdate === false ? '0' : $isForceUpdate['ItemValue']
            ];
            // 如果是IOS，则返回IOS的版本号，否则返回ASM版本
            if (OS_TYPE === OSTYPE_IOS) {
                $IosVersion = DictModel::getOneItem('Mobile', 'IosVersion');
                $return['mobileForceUpdate']['engine'] = $IosVersion === false ? '1.0.0' : $IosVersion['ItemValue'];
            } else {
                $return['mobileForceUpdate']['engine'] = SystemServiceProvider::getEngine(true);
            }
        }
        // 获取重定向信息
        $return['Redirect'] = SystemServiceProvider::getRedirectInfo($firsturl, $ascid);
        // 添加公钥信息
        getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');
        $return['pubKey'] = getRsaPubKey('net_auth', PATH_HTML . '/download/rsa/');
        $return['fakeHard'] = DeviceServiceProvider::getDeviceDiskID();
        if (!empty($isMobile)) {
            $return['mobile'] = $this->mobileConfig();
        }
        $return['ZtpModule'] = getmoduleregist(11);
        //是否开启了客户端分发
        $return['clientDistribute'] = (int)ConfigServiceProvider::getDictValue('ClientDistribute', 'switch', 0);
        $return['BrowserDeviceID'] = $this->getSessionData('DeviceID');
        //是否开启802.1x
        $return['dot1x'] = ConfigServiceProvider::getDot1x();
        // 获取真实的系统引擎版本
        $return['Engine'] = SystemServiceProvider::getRealEngine();
        // 当前服务器版本，支持的最小客户端引擎版本号
        $return['MiniSupportClientEngine'] = SystemServiceProvider::getMiniSupportClientEngine();

        return $return;
    }

    /**
     * 获取服务器语言，对应老交易 get_server_lang
     *
     * @return array
     * @throws Exception
     */
    public function lang()
    {
        $return = [];
        if (file_exists(PATH_ETC . "version.ini")) {
            $ini_arr = read_inifile(PATH_ETC . "version.ini");
            $return['Global_lang'] = trim($ini_arr['lang']);
        } else {
            $return['Global_lang'] = "zh";
        }
        //PC终端界面的语言设置
        $return['PCLang'] = ConfigServiceProvider::getDictOne('GlobalVar', 'Language');
        //移动终端界面的语言设置
        $return['MobileLang'] = ConfigServiceProvider::getDictOne('Mobile', 'Language');
        return $return;
    }

    /**
     * 获取服务器时间，对应老交易 get_server_time
     *
     * @return array|string
     * @throws Exception
     */
    public function time()
    {
        $server_time = func_time_getNow();
        return $server_time;
    }

    /**
     * 获取系统授权点数是否超过状态，对应老交易 authorization
     *
     * @return bool
     * @throws Exception
     */
    public function authorization()
    {
        return SystemServiceProvider::isRegistrable();
    }

    /**
     * 上报日志，对应老交易 writelog
     *
     * @return array
     * @throws Exception
     */
    public function writelog()
    {
        $params = [];
        $params['content'] = request('content', 'request');
        hlp_check::checkEmpty($params['content']);
        $params['clientIP'] = getRemoteAddress();
        cutil_php_log(var_export(Base64DeExt($params['content']), true), "activecall_" . $params['clientIP']);
        $return = ['clientIP' => $params['clientIP']];
        return $return;
    }

    /**
     * 上报IPv6信息，对应老交易 sendIPv6xml
     *
     * @return array
     * @throws Exception
     */
    public function ipv6xml()
    {
        $ipv6data['TradeCode'] = 'AddIPv6AddrForDevice';
        $ipv6data['DeviceID'] = request('DeviceID', 'request', 0, 'int');
        hlp_check::checkEmpty($ipv6data['DeviceID']);
        $ipv6data['IPv6'] = request('client_ipv6', 'request');
        $ipv6data['Mac'] = '';
        $ipv6data['source'] = 'web_access';
        $res = DeviceServiceProvider::addIPv6AddrForDevice($ipv6data);
        return ['res' => $res];
    }

    /**
     * 获取ipv6，对应老交易 get_client_ipv6
     * @throws Exception
     */
    public function ipv6()
    {
        $client_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'];
        $client_ip = trim($client_ip);
        $callback = request('callback', 'request');
        hlp_check::checkEmpty($callback);
        exit("{$callback}({client_ip:\"{$client_ip}\"});");
    }

    /**
     * 运行时间，对应老交易 traderuntime
     * ps: 该交易已废弃，为了兼容旧API不报错，写个空方法替换
     * @return bool
     * @throws Exception
     */
    public function runtime(): bool
    {
        return true;
    }

    /**
     * 获取帮助信息 原始js文件存储位置：/asm_22_isofile/bindir/var/www/html/download/personality/help_en.js
     * @return array
     */
    public function help()
    {
        $dir = PATH_HTML . "/download/personality/";
        $file = "help.json";
        if (LANG == 'en_US') {
            $file = "help_en.json";
        }
        $result = file_get_contents($dir . $file);
        $result = gbkToUtf8($result);
        $return = json_decode($result, true);
        return $return;
    }

    /**
     * 获取提示信息 原/download/personality/parameter.json
     * @return array
     */
    public function parameter()
    {
        return SystemServiceProvider::getAccessTips();
    }

    /**
     * 上传文件接口
     * @return mixed
     * @throws Exception
     */
    public function upload()
    {
        // 接收参数
        $_file = request('file', 'file', [], 'array');    // 相当于$_FILES['file']
        $sFileName = $_file['name'] ?? '';                                    // 获取文件名
        $sFileSize = $_file['size'] ?? '';                                    // 获取大小(单位：字节)
        // todo 严禁前端传入文件保存路径，后续需要修改（需要小助手协同修改）
        $serverPathFie = request('fileserverpath', 'request'); // 文件保存路径
        $key = request('key', 'request');    //密钥
        cutil_php_log('请求参数：key：' . $key . '- FileSize:' . $sFileSize . '- FileName:' . $sFileName . '- ServerPathFie:' . $serverPathFie, 'upload_file');
        hlp_check::checkEmpty($serverPathFie);
        // 客户端主动上传和管理员要求客户端上传日志 这两种方式路径不一样，主动上传没有带前置路径
        // 如果没带就手动补一下
        if (strpos($serverPathFie, PATH_SYSTEM) === false) {
            $serverPathFie = PATH_SYSTEM . $serverPathFie;
            cutil_php_log('路径补齐：' . $serverPathFie, 'upload_file');
        }

        // infogo|文件大小|文件名
        if ($key !== dataEncrypt("infogo|$sFileSize|$sFileName")) {
            T(21101001);
        }

        $dirFile = SystemServiceProvider::isUploadDir($serverPathFie);
        if (empty($dirFile)) {
            T(21100002);
        }

        // 判断目录是否存在
        $serverDir = dirname($serverPathFie);
        path_exists($serverDir);

        // 文件上传
        if (!lib_upload::move_upload_file('file', $serverPathFie)) {
            T(21100019, ['file' => lib_upload::$files['file']['name']]);
        }

        if (API_VERSION < '1.0') {
            exit('success');
        }

        return [];
    }

    /**
     * 浪潮中英文参数，对应老交易 fast_auth
     *
     * @return mixed
     * @throws Exception
     */
    public function conf()
    {
        return ServerServiceProvider::getGlobalVarConf();
    }

    /**
     * 获取认证公钥，对应老交易 get_pubkey
     *
     * @return mixed
     * @throws Exception
     */
    public function pubkey()
    {
        getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');
        $pubKey = getRsaPubKey('net_auth', PATH_HTML . '/download/rsa/');
        return $pubKey;
    }

    /**
     * 获取版本信息，对应老交易 getclientconf
     *
     * @return mixed
     * @throws Exception
     */
    public function version()
    {
        $requestType = request('requestType', 'request');
        $returnInfo = ['msg' => L(21100018)];

        if ('mClient' === $requestType) {
            $c_path = PATH_ETC . '/version.ini';
            $returnInfo = read_inifile($c_path);
            if (!empty($returnInfo['ShowEngine'])) {
                $returnInfo['engine'] = $returnInfo['ShowEngine'];
            }
            $returnInfo['msg'] = 'ok！';
        }

        return $returnInfo;
    }

    /**
     * 获取移动端配置信息，对应老交易 getmobileconfig 废弃合并到server/info
     *
     * @return mixed
     * @throws Exception
     */
    public function mobileConfig()
    {
        $ascid = request('ascid', 'request');
        if (strlen(trim($ascid)) < 17) {
            $ascid = BaseServiceProvider::getMacAddr();
        }
        $otheruserid = request('otheruserid', 'request', 0, 'int');
        $firsturl = request('firsturl', 'request');
        $ip = getRemoteAddress();

        $returnArr = SystemServiceProvider::getMobileConfig();
        //获取nat配置信息
        $natConfig = SystemServiceProvider::getControllerConfig($ascid);
        $isVpn = VpnServiceProvider::getIsVpn($natConfig, $ip);
        // 判断 NATCONNECT为1,表示开启[强制连接]
        $isNatConnect = ($natConfig['NATCONNECT'] === '1' && $isVpn) ? 1 : 0;
        // 更新 $returnArr 数组
        $returnArr['isvpn'] = $isVpn;
        $returnArr['NATCONNECT'] = $isNatConnect;

        if (!IsUTF8($natConfig['NATCOMMENT'])) {
            $natConfig['NATCOMMENT'] = gbkToUtf8($natConfig['NATCOMMENT']);
        }
        $returnArr['NATCOMMENT'] = ($natConfig['NATCOMMENT'] && $isNatConnect) ? $natConfig['NATCOMMENT'] : '';//nat穿越时，强制连接时的提示语
        $params['servicePrefix'] = 'Base';
        $resultService = ResultServiceProvider::initResultService($params);
        $returnArr['firsturl'] = $resultService->getRedirectUrl($firsturl, $ascid);
        $returnArr['wechat'] = AuthServiceProvider::getWechatStatus($ip, $otheruserid);
        return $returnArr;
    }

    /**
     * 获取msac信息，对应老交易 getmsacdurldis
     *
     * @return mixed
     * @throws Exception
     */
    public function msacInfo()
    {
        $header = get_headers("http://www.baidu.com/");
        if (stristr($header[0], "Redirect")) {
            $isRedirect = "1";
        } else {
            $isRedirect = "0";
        }
        $res = ConfigServiceProvider::getDictValue('CLIENTCHECK', 'MSACDurlDis');
        return ["MSACDurlDis" => $res, "isRedirect" => $isRedirect];
    }

    /**
     * 获取控件安装类型，对应老交易 installonline
     *
     * @return mixed
     * @throws Exception
     */
    public function controlInfo()
    {
        $res = ConfigServiceProvider::getDictValue('CLIENTCHECK', 'ControlType');
        return $res;
    }

    /**
     * 设置当前登录管理员，对应老交易 set_top_login
     *
     * @return mixed
     * @throws Exception
     */
    public function setLogin()
    {
        $login = request('login', 'request');
        if (!$login) {  //当请求参数中没有login时直接返回空
            return false;
        }
        ConfigServiceProvider::updateDictValue('Topology', 'login', $login);
        return true;
    }

    /**
     * 获取安卓启动信息，对应老交易 startup
     *
     * @return mixed
     * @throws Exception
     */
    public function startup()
    {
        $MobileRemoteCtrl = getmoduleregist(16); //移动远程控制
        $MDM = getmoduleregist(20); //移动设备管理
        $android = ConfigServiceProvider::getDictAll('Android');
        if (empty($MobileRemoteCtrl) && empty($MDM)) {
            $android['AssistControl'] = '0';
        }
        if (empty($MDM)) {
            $android['DeviceManager'] = '0';
        }
        $returnInfo = read_inifile(PATH_ETC . 'version.ini');
        if (!empty($returnInfo['ShowEngine'])) {
            $android['ShowEngine'] = $returnInfo['ShowEngine'];
        }
        return $android;
    }

    /**
     * 获取二维码信息，对应老交易 phpdir\include\imgcode.php
     *
     * @return mixed
     * @throws Exception
     */
    public function imgcode()
    {
        $deviceid = request('deviceid', 'request', 0, 'int'); // 设备ID
        hlp_check::checkEmpty($deviceid);
        SystemServiceProvider::getValidateCode($deviceid);
        exit();
    }

    /**
     * 代理发送心跳
     *
     * @return mixed
     * @throws Exception
     */
    public function heartbeat()
    {
        $deviceId = request('deviceId', 'request', 0, 'int'); // 设备ID
        hlp_check::checkEmpty($deviceId);
        ServerServiceProvider::sendHeartbeat($deviceId);
        return [];
    }

    /**
     * 获取服务器配置
     *
     * @return array
     */
    public function config()
    {
        $data = [
            'IsInternal' => (int)IS_INTERNAL
        ];
        $data['Config'] = ServerServiceProvider::getAsmServerAddress();
        return $data;
    }

    /**
     * 无客户端浏览器指纹，用于获取header信息
     *
     * @return array
     */
    public function fingerprint()
    {
        return [
            'ACCEPT' => $_SERVER['HTTP_ACCEPT'],
            'ACCEPT_ENCODING' => $_SERVER['HTTP_ACCEPT_ENCODING'],
            'ACCEPT_LANGUAGE' => $_SERVER['HTTP_ACCEPT_LANGUAGE'],
        ];
    }
    /**
     * 移动端配置下发
     */
    public function getQrCodeImg()
    {
        $mobileIniPath = PATH_ETC . 'mobile_params.ini';
        $mobileTxtPath = PATH_HTML."/download/mobile_params.txt";
        if (!is_file($mobileTxtPath)) {
            cutil_exec_no_wait("ln -s ".$mobileIniPath." ".$mobileTxtPath);
            cutil_php_log("ln -s ".$mobileIniPath." ".$mobileTxtPath,'Server');
        }
        $path = "/download/mobile_params.txt";
        $iniPath = PATH_ETC."/access.ini";
        $aFile = parse_ini_file($iniPath);
        $urlPrefixIn = "";
        $urlPrefix = "";
        if (!empty($aFile["ServerIPv4In"]) && !empty($aFile["ServerIPv4Ex"])) {
            $urlPrefixIn = $aFile["WebProtocolIn"]."://".$aFile["ServerIPv4In"].":".$aFile["WebPortIn"].$path ;
            // $urlPrefixIn = ServerServiceProvider::getGotoPrefixUrl();
            $urlPrefix = $aFile["WebProtocolEx"]."://".$aFile["ServerIPv4Ex"].":".$aFile["WebPortEx"].$path ;
        }
        $aData = [
            "InUrl" => $urlPrefixIn,
            "OutUrl" => $urlPrefix,
        ];
        lib_qrcode::png(json_encode($aData, JSON_UNESCAPED_UNICODE), false, "L", 10, 1);
    }
    /**
     * 移动端配置下发
     * @return array
     */
    public function getQrCode()
    {
        $MobileQrCode = cutil_dict_get('ZTP', 'MobileQrCode') ?? 0;
        return [
            'OpenQrCode' => $MobileQrCode ,
            'URL' => $MobileQrCode ? '/access/server/getQrCodeImg' : ''
        ];
    }

    /**
     * 获取项目配置，原project_conf交易
     * @throws Exception
     */
    public function projectConf()
    {
        return SystemServiceProvider::getProjectConf();
    }

    /**
     * 参考服务端交易GetGrayUpdateConfig
     * 返回是否启用灰度发布配置
     * @return array
     * @throws Exception
     */
    public function grayUpdateConfig()
    {
        $iniPath = PATH_ETC . 'ClientComsList.ini';
        if (file_exists($iniPath)) {
            $returnInfo = read_inifile($iniPath);
        } else {
            $returnInfo['GrayUpdates'] = '0';
        }

        return [
            'EnableGrayUpdate' => $returnInfo['GrayUpdates'],
        ];
    }

    /**
     * 移动端管理界面普通api
     * @return true
     * @throws Exception
     */
    public function mobileManagerApi()
    {
        $params = [];
        $params['tradecode'] = request('tradecode', 'request', '', 'string');
        $params['tradetype'] = request('tradetype', 'request', '', 'string');
        $params['mod'] = request('mod', 'request', '', 'string');
        $params['login'] = request('login', 'request', '', 'string');
        $params['token'] = request('token', 'request', '', 'string');

        $param = lib_request::$requests;
        $params = array_merge($params, $param);
//      加个并发锁，1秒内禁止重复请求，入网状态会重复
        $this->isLocked(md5(json_encode($params)), 1);
        $result = lib_yar::clients('MobileManager', 'mobileManagerApi', $params);
        if (empty($result['state'])) {
            throw new Exception($result['message']);
        }
        json_print($result['data']['info']);
        return true;
    }

    /**
     * 移动端管理界面列表api
     * @return true
     * @throws Exception
     */
    public function mobileManagerList()
    {
        $params = [];
        $params['IsShowCheckBox'] = request('IsShowEditTable', 'request', '0', 'int');
        $params['IsShowEditTable'] = request('IsShowEditTable', 'request', '0', 'int');
        $params['page'] = request('page', 'request', '1', 'int');
        $params['pagesize'] = request('pagesize', 'request', '1000', 'int');
        $params['order'] = request('order', 'request', '', 'string');
        $params['ordertype'] = request('ordertype', 'request', '', 'string');
        $params['Search'] = request('Search', 'request', '', 'string');
        $params['list'] = 1; // request('list', 'request', '1', 'int');
        $params['showpage'] = request('showpage', 'request', '0', 'int');
        $params['CellClickFun'] = request('CellClickFun', 'request', '', 'string');
        $params['rowIdColumnName'] = request('rowIdColumnName', 'request', '', 'string');
        $params['parentDiv'] = request('parentDiv', 'request', '', 'string');
        $params['tradecode'] = request('tradecode', 'request', '', 'string');
        $params['tradetype'] = request('tradetype', 'request', '', 'string');
        $params['mod'] = request('mod', 'request', '', 'string');
        $params['login'] = request('login', 'request', '', 'string');
        $params['token'] = request('token', 'request', '', 'string');
        //加个并发锁，俩秒内禁止重复请求，入网状态会重复
        $this->isLocked(md5(json_encode($params)), 1);
        $param = lib_request::$requests;
        $params = array_merge($params, $param);
        $result = lib_yar::clients('MobileManager', 'mobileManagerList', $params);
        if (empty($result['state'])) {
            throw new Exception($result['message']);
        }
        json_print($result['data']['info']);
        return true;
    }

    /**
     * 移动端管理界面资源申请列表api
     * @return true
     * @throws Exception
     */
    public function backendApiList()
    {
        $params = [];
        $params['tradeCode'] = request('tradeCode', 'request', '', 'string');
        $params['mod'] = request('mod', 'request', '', 'string');
        $params['menuId'] = request('menuId', 'request', '0', 'int');
        $params['params'] = request('params', 'request', '{}', 'string');
        $params['orderBy'] = request('orderBy', 'request', '', 'string');
        $params['sort'] = request('sort', 'request', 'desc', 'string');
        $params['pageSize'] = request('pageSize', 'request', '20', 'int');
        $param = lib_request::$requests;
        $params = array_merge($params, $param);
        //加个并发锁，1秒内禁止重复请求，入网状态会重复
        $this->isLocked(md5(json_encode($params)), 1);
        $result = lib_yar::clients('backend', 'backendApiList', $params);
        if (empty($result['state'])) {
            throw new Exception($result['message']);
        }
        json_print($result['data']['info']);
        return true;
    }

    /**
     * 移动端管理界面资源申请操作api
     * @return true
     * @throws Exception
     */
    public function backendApiInfo()
    {
        $params = lib_request::$requests;
        //加个并发锁，1秒内禁止重复请求，入网状态会重复
        $this->isLocked(md5(json_encode($params)), 1);
        $result = lib_yar::clients('backend', 'backendApiInfo', $params);
        if (empty($result['state'])) {
            throw new Exception($result['message']);
        }
        json_print($result['data']['info']);
        return true;
    }

    /**
     * 纯国密,QT不支持请求国密服务器图片
     * 返回图片base64
     * @return string[]
     */
    public function getImgBase64()
    {
        //从request变量中获取参数imgUrl,然后将判断imgurl的图片是否存在,存在的话,则转为base64,然后返回
        $imgUrl = request('imgUrl', 'request', '');
        $imgPath = PATH_HTML . '/' . ltrim($imgUrl, '/');

        if (!file_exists($imgPath)) {
            return ['base64' => ''];
        }

        $imgData = file_get_contents($imgPath);
        $base64 = base64_encode($imgData);
        $imgType = pathinfo($imgUrl, PATHINFO_EXTENSION);
        $base64Img = 'data:image/' . $imgType . ';base64,' . $base64;

        return ['base64' => $base64Img];
    }
}
