<?php
/**
 * Description: 设备服务漫游代码块 只能DeviceInfoService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: DeviceInfoServiceRoamTrait.php 173445 2022-04-15 09:10:22Z renchen $
 */

namespace Services\Device\Traits;

use Common\Facades\NACServiceFacade;
use DevicePolicyVersion;
use DeviceServiceProvider;

trait DeviceInfoServiceRoamTrait
{
    /**
     * 设备由DASC漫游至ASM上，清除原DASC数据
     * @param $devID
     * @return bool
     * @throws \Exception
     */
    protected function notifyDAsc($devID):bool
    {
        $dev = \DeviceModel::getOne($devID, 'dasc');
        if (is_array($dev) && !in_array(trim($dev['DAscID']), ['','11:11:11:11:11:11'])) {
            /* 1.先将ASM的DAscID置空，表示为受ASM管控的设备 */
            \DeviceModel::DAscRoamAsmDevice($devID);
            /* 2.再将DASC上的数据清空 */
            $topic = '/cfg/file/' . trim($dev['DAscID']);
            $message = array();
            $message['type'] = 'cmd';
            $message['AscID'] = trim($dev['DAscID']);
            $message['Message'] = array('exec_cmd' => PATH_ASM . 'sbin/dasc_del_device -i ' . $dev['OrigID']);
            pub_mq_jsonmsg($topic, $message, false, 2);
            $this->writeLog("设备由DASC漫游至ASM上，清除原DASC数据，原DASCID:" . trim($dev['DAscID']) . ' origID:' . $dev['OrigID']);
            return true;
        }
        return false;
    }

    /**
     * @Description:判断漫游设备是否放通网络
     * @param int   $deviceId
     * @param array $DevInfo
     * @return bool 是否放开网络
     * @throws \Exception
     */
    protected function accessRoamDevice(int $deviceId, array $DevInfo): bool
    {
        try {
            $sceneID = 0;
            // 1. 判断是否可信，可信的话直接放开网络，否则看是否有在线记录
            if (DeviceServiceProvider::isTrustDev($deviceId)) {
                $sceneID = TRUST_SCENE_ID;
                $this->writeLog("发现是漫游设备,且是可信设备，先放通网络:" . $deviceId);
            } else {
                $scene = \NacOnLineDeviceModel::getSingle(['DeviceID' => $deviceId, 'column' => 'name']);
                if (isset($scene['SceneID'])) {
                    $sceneID = $scene['SceneID'];
                    $this->writeLog("发现是漫游设备,且有在线角色，先放通网络 DeviceID:" . $deviceId . " sceneID:" . $sceneID);
                }
            }

            // 2.如果有场景信息，则用场景的域放开网络，后续根据当前DASC的桥周期来计算是否能放通网络
            if (!empty($sceneID)) {
                $res = \TmpNetRoleModel::getSingle(['SceneID' => $sceneID, 'Type' => 1, 'column' => 'one']);
                $ipListId = $res['ID'] ?? 1;
                $netParams = [
                    'device_id'  => $deviceId,
                    'ip'         => $DevInfo['Ip'],
                    'iplistid'   => $ipListId,
                    'mac'        => $DevInfo['Mac'],
                ];
                $this->writeLog("根据场景查询到安全域id:" . $ipListId);
                NACServiceFacade::access("WebAccess:accessRoamDevice",[$netParams],'Access');
                return true;
            }
        } catch (\Exception $e) {
            $this->writeLog('accessRoamDevice 出错了，错误信息为：'.$e->getMessage().PHP_EOL.'错误行数：'.$e->getLine(), 'ERROR');
        }
        return false;
    }

    /**
     * @Description:将漫游设备变更为本地设备
     * ps：DASC上有该设备且是漫游设备，但是从DASM无法查询到该设备信息时，将漫游设备变更为本地设备
     * @param array $params
     * @return bool
     * @throws \Exception
     */
    protected function changeRoamToLocal(array $params):bool
    {
        $roamFlag = false;
        if (get_server_type() === DEVTYPE_DASC) {
            $res = \ComputerModel::getSingle(['DeviceID'=>$this->deviceId, 'column' => 'roam']);
            if (!empty($res) && (int)$res['RoamFlag'] === 1) {
                $roamFlag = true;
            }
        }

        if ($roamFlag && $this->params['isopennet'] !== 'isopen') {
            $manageIP = get_dasm_ip();
            if ($manageIP) {
                $this->writeLog("当前是DASC设备，尝试从ASM上获取设备信息,manageIP：" . $manageIP);
                $params['manageIP'] = $manageIP;
                $dasmData = $this->getDevFromDasm($params);
                if (is_array($dasmData) && empty($dasmData)) {
                    \ComputerModel::changeRoamToLocal($this->deviceId);
                }
                $this->isWalkDevice = 1;
            }
        }

        return $roamFlag;
    }

    /**
     * DASC判断为漫游设备，请求DASM上的设备条目,插入DASC
     * ps对应老的方法为：get_dev_info_by_ID，get_dev_info.
     *
     * @param $params
     *
     * @throws \Exception
     *
     * @return array|bool
     */
    protected function getDevFromDasm($params = [])
    {
        // 防止条目过多，分页获取
        $manageIP = $params['manageIP'];
        if ('' !== $manageIP) {
            $params['DAscID'] = get_ini_info(PATH_ETC . 'asc.ini', 'AscID');
            $res = \lib_yar::clients('dasm', 'devInfo', $params, $manageIP, 60 * 1000, 1);
            $this->writeLog('res值：'.var_export($res, true));

            $devLastTime = isset($params['DevLastTime']) ? (int) $params['DevLastTime'] : 0;
            if ($devLastTime > 0 && \is_array($res['data'])
                && !empty($res['data']['TDevice']) && !empty($res['data']['TDevice']['LastTime'])) {
                $online = (int) $res['data']['TDevice']['Online'];
                $lastTime = $res['data']['TDevice']['LastTime'];
                if (1 === $online && (time() - strtotime($lastTime) < $devLastTime)) {
                    $this->writeLog('自动发现设备，asm上有设备，但是该设备被其他asc管理了，不插入本地条目');

                    return $res['data']['TDevice']['OrigID'];
                }
            }
            // 这里原来会去查设备，如果在dasc上存在就更新，但是这里更新后还会被getdeviceinfo覆盖，所以这里取消更新
            // 如果漫游之后的设备信息需要以dasm上的为主，则在getdeviceinfo这个交易中处理，是漫游设备的话某些字段不更新
            if (is_array($res) && isset($res['data']['TDevice']['OrigID'])) {
                // 检查DAscID值是否一致，如果一样说明是同一个地方报上来的不是漫游设备
                // 该场景存在于：当再Dasc上删除设备，AsmSvr马上发起请求，这时设备再Dasm上还未删除
                if(isset($params['DAscID']) && $params['DAscID'] === $res['data']['DAscID']) {
                    $this->writeLog('该设备是当前DASC上报，可能是本地查询失败或本地已删除但DASM还存在才会走到漫游设备，不插入');
                    unset($res['data']['DAscID']);

                    return [];
                }

                // 检查本地是否已插入 防止重复插入
                $dev = \DeviceModel::getSingle([
                                                   'DAscID' => '11:11:11:11:11:11',
                                                   'OrigID' => $res['data']['TDevice']['OrigID'],
                                                   'column' => 'one'
                                               ]);
                $deviceID = $dev['DeviceID'] ?? 0;
                if ($deviceID > 0) {
                    $this->writeLog('漫游设备已存在，不插入');

                    return $deviceID;
                }

                return $res['data'];
            }
        }

        return [];
    }

    /**
     * 漫游设备插入DASC.
     * @param $params
     * @return array
     */
    protected function roamDevInsertDasc($params):array
    {
        try {
            $newDevID = 0;
            $dasmData = $this->getDevFromDasm($params);
            $this->writeLog("通过向ASM询问，漫游设备至本DASC，本DASC返回：" . json_encode($dasmData));
            if (empty($dasmData)) {
                T(21103006);
            }
            // 本地已经插入
            if (is_numeric($dasmData)) {
                $newDevID = (int)$dasmData;
            }

            if (is_array($dasmData) && !empty($dasmData)) {
                $insertDeviceRes = \DeviceModel::insertIntoTable($dasmData);
                $newDevID = (int)$insertDeviceRes;

                // 插入设备策略表
                if ($newDevID > 0) {
                    DevicePolicyVersion::insert([
                        'DeviceID' => $newDevID,
                    ]);
                }
            }
            if ($newDevID > 0) {
                $this->deviceId = $newDevID;
                // 更新设备信息
                $this->writeLog("通过向ASM询问，漫游设备至本DASC，本DASC存在DeviceID=" . $this->deviceId);
                if ($this->params['gettype'] === 'control') {
                    $this->updateDeviceInfo($params);
                    // yanzj 20180824 不管设备是否存在，都上报最新的ipv6地址报文
                    $this->sendipv6ControlXml();
                } else {
                    // 未装客户端漫游时，设备存在的场景更新设备IP和MAC等信息
                    $updateData = [
                        'DeviceID' => $this->deviceId,
                        'IP' => $this->params['linkIP'],
                        'Mac' => $this->mac
                    ];
                    $this->deviceOperationService->updateDevice($updateData);
                }

                // 发现是漫游上报，判断网络是否放通
                $this->accessRoamDevice($newDevID, $params);
                $this->isWalkDevice = 1;
            } else {
                // 插入漫游设备失败 抛出异常
                T(21103010);
            }
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, json_encode($params));
            if ($newDevID > 0) {
                $this->code = 1;
            }
        }

        return $this->return;
    }
}
