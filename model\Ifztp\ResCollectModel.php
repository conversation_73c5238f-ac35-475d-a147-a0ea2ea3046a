<?php

/**
 * Description: 资源收藏表
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: ResCollectModel.php 174776 2022-04-28 09:18:26Z duanyc $
 */

class ResCollectModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResCollect';
    public const PRIMARY_KEY = 'CollectID';
    protected static $columns = [
        '*'       => '*',
        'collect' => 'ResID',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        if (isset($cond['ResID'])) {
            $where .= "AND ResID = ".self::setData($cond['ResID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
