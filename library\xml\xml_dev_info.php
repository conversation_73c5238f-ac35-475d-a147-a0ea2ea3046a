<?php

/**
 * Description: 设备信息 原ParseDevInfo.php
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: xml_dev_info.php 151558 2021-07-29 04:11:02Z duanyc $
 */


/**
 * ***************************************** 解析设备信息 **********************************************
 */
class xml_dev_info extends cls_xml
{
    /**
     * 解析参数
     * @return mixed
     */
    public function parseXml()
    {
        $this->res['ComputerName'] = $this->xml->DeviceName;
        $this->res['OS'] = $this->xml->OS;
        $this->res['OSInstallTime'] = $this->xml->OSInstallTime;
        $this->res['IeVersion'] = $this->xml->IeVersion;
        $this->res['Ip'] = $this->xml->Ip;
        $this->res['Mac'] = $this->xml->Mac;
        $this->res['AllIp'] = $this->xml->AllIp;
        $this->res['AllMac'] = $this->xml->AllMac;
        $this->res['GateWay'] = $this->xml->GateWay;
        $this->res['Mask'] = $this->xml->Mark;
        $this->res['DiskId'] = $this->xml->DiskId;
        $this->res['AllIp_Ok'] = $this->xml->AllIp_Ok;
        $this->res['AllMac_Ok'] = $this->xml->AllMac_Ok;

        $this->res['CPU'] = $this->xml->CPU;
        $this->res['RealDiskId'] = $this->xml->RealDiskId;
        $this->res['DiskSize'] = $this->xml->DiskSize;
        $this->res['Memory'] = $this->xml->Memory;

        $this->res['AllIp_IPv6'] = $this->xml->AllIp_IPv6;

        /*手机新增字段属性 yancheng 2015-03-02*/

        $this->res['Runmemory'] = $this->xml->Runmemory;
        $this->res['Phonestoragespace'] = $this->xml->Phonestoragespace;
        $this->res['Phoneavailablestorage'] = $this->xml->Phoneavailablestorage;
        $this->res['Sdcardavailablestorage'] = $this->xml->Sdcardavailablestorage;
        $this->res['Imei'] = $this->xml->Imei;
        $this->res['Networksystem'] = $this->xml->Networksystem;
        $this->res['Kernelnumber'] = $this->xml->Kernelnumber;
        $this->res['Roam'] = $this->xml->Roam;
        $this->res['Phonetype'] = $this->xml->Phonetype;
        $this->res['Whetheroot'] = $this->xml->Whetheroot;
        $this->res['Charging'] = $this->xml->Charging;
        $this->res['IsInNAT'] = $this->xml->IsInNAT;
        $this->res['ProxyServer'] = $this->xml->ProxyServer;
        $this->res['AgentVersion'] = $this->xml->AgentVersion;
        $this->res['InternalAgentVersion'] = $this->xml->AgentVersion_Inner;

        $this->convertEncode();
        return $this->res;
    }

    /**
     * 解析json数据
     *
     * @param $jsonData
     *
     * @return mixed
     */
    public function parseJson($jsonData)
    {
        $this->res['ComputerName'] = $jsonData['DeviceName'] ?? '';
        $this->res['OS'] = $jsonData['OS'] ?? '';
        $this->res['OSInstallTime'] = $jsonData['OSInstallTime'] ?? '';
        $this->res['IeVersion'] = $jsonData['IeVersion'] ?? '';
        $this->res['Ip'] = $jsonData['Ip'] ?? '';
        $this->res['Mac'] = $jsonData['Mac'] ?? '';
        $this->res['AllIp'] = $jsonData['AllIp'] ?? '';
        $this->res['AllMac'] = $jsonData['AllMac'] ?? '';
        $this->res['GateWay'] = $jsonData['GateWay'] ?? '';
        $this->res['Mask'] = $jsonData['Mark'] ?? '';
        $this->res['DiskId'] = $jsonData['DiskId'] ?? '';
        $this->res['AllIp_Ok'] = $jsonData['AllIp_Ok'] ?? '';
        $this->res['AllMac_Ok'] = $jsonData['AllMac_Ok'] ?? '';

        $this->res['CPU'] = $jsonData['CPU'] ?? '';
        $this->res['RealDiskId'] = $jsonData['RealDiskId'] ?? '';
        $this->res['DiskSize'] = $jsonData['DiskSize'] ?? '';
        $this->res['Memory'] = $jsonData['Memory'] ?? '';

        /*手机新增字段属性 yancheng 2015-03-02*/
        $this->res['Runmemory'] = $jsonData['Runmemory'] ?? '';
        $this->res['Phonestoragespace'] = $jsonData['Phonestoragespace'] ?? '';
        $this->res['Phoneavailablestorage'] = $jsonData['Phoneavailablestorage'] ?? '';
        $this->res['Sdcardavailablestorage'] = $jsonData['Sdcardavailablestorage'] ?? '';
        $this->res['Imei'] = $jsonData['Imei'] ?? '';
        $this->res['Networksystem'] = $jsonData['Networksystem'] ?? '';
        $this->res['Kernelnumber'] = $jsonData['Kernelnumber'] ?? '';
        $this->res['Roam'] = $jsonData['Roam'] ?? '';
        $this->res['Phonetype'] = $jsonData['Phonetype'] ?? '';
        $this->res['Whetheroot'] = $jsonData['Whetheroot'] ?? '';
        $this->res['Charging'] = $jsonData['Charging'] ?? '';
        $this->res['IsInNAT'] = $jsonData['IsInNAT'] ?? '';
        $this->res['ProxyServer'] = $jsonData['ProxyServer'] ?? '';
        $this->res['AgentVersion'] = $jsonData['AgentVersion'] ?? '';
        $this->res['InternalAgentVersion'] = $jsonData['AgentVersion_Inner'] ?? '';
        return $this->res;
    }
}
