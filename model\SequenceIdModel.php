<?php

/**
 * Description: TSequenceID表
 * User: <EMAIL>
 * Date: 2021/08/13 15:53
 * Version: $Id: SequenceIdModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class SequenceIdModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TSequenceID';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'ID',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['Value'])) {
            $where .= "AND Value = ".self::setData($cond['Value']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}