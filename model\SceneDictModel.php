<?php

/**
 * Description: 场景信息字典表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id.
 */
class SceneDictModel extends BaseModel
{
    /**
     * 表名.
     *
     * @var string
     */
    public const TABLE_NAME = 'TSceneDict';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        'one' => 'ItemName, ItemValue,ConfigID,TSceneDict.Groups',
        'relation' => 'TSceneDict.ItemName,TSceneDict.ItemValue,TSceneDict.ConfigID,TSceneDict.Groups',
        'value' => 'ItemValue',
        'config' => 'TSceneDict.ItemValue as Config',
        '*' => '*',
    ];

    /**
     * 根据场景ID获取场景的所有配置信息
     * @param int $sceneID
     * @return array|false|mixed
     */
    public static function getAll(int $sceneID)
    {
        if (!$sceneID) {
            return false;
        }

        static $data = null;
        if (PHP_SAPI !== 'cli' && !empty($data[$sceneID])) {
            return $data[$sceneID];
        }

        self::$data = [];
        $column = self::$columns['relation'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE TSceneRelation.SceneID = ".self::setData($sceneID)." AND TSceneDict.ConfigID=TSceneRelation.ConfigID";
        $where .= " AND TSceneDict.Groups=TSceneRelation.Groups ";
        $sql = "SELECT {$column} FROM {$table['name']},TSceneRelation {$where}";
        $aData = lib_database::getAll($sql, $table['index'], false, self::$data);
        $aResult = [];

        if (!empty($aData)) {
            foreach ($aData as $row) {
                $aResult[$row['ItemName']] = $row['ItemValue'];
            }
        }

        $data[$sceneID] = $aResult;
        return $aResult;
    }

    /**
     * 根据条件获取字典表中的单个配置的值（ItemValue）
     * @param int $configId
     * @param string $itemName
     * @param string $groups
     * @return string
     */
    public static function getValue(int $configId,string $itemName,string $groups):string
    {
        $res = self::getSingle([
            'ConfigID' => $configId,
            'ItemName' => $itemName,
            'Groups' => $groups,
            'column' => 'value']);

        return $res['ItemValue'] ?? '';
    }

    /**
     * 获取配置信息
     *
     * @param int $sceneID
     * @param string $ItemName
     * @param string $Groups
     *
     * @return array|bool
     */
    public static function getOneConfig(int $sceneID, string $ItemName, string $Groups = '')
    {
        if (!$sceneID || empty($ItemName)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns['config'];
        $where = "where TSceneDict.ItemName = ".self::setData($ItemName).
            " and TSceneRelation.SceneID = ".self::setData($sceneID).
            " and TSceneRelation.ConfigID=TSceneDict.ConfigID and TSceneRelation.Groups=TSceneDict.Groups ";

        if (!empty($Groups)) {
            $where .= " and TSceneDict.Groups = '{$Groups}'";
        }

        $sql = "select {$column} from TSceneDict, TSceneRelation {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件.
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = '';

        if (isset($cond['RID'])) {
            $where .= 'AND RID = '.self::setData($cond['RID']);
        }

        if (isset($cond['ConfigID'])) {
            $where .= 'AND ConfigID = '.self::setData($cond['ConfigID']);
        }

        if (isset($cond['ItemName'])) {
            $where .= 'AND ItemName = '.self::setData($cond['ItemName']);
        }

        if (isset($cond['Groups'])) {
            $where .= 'AND TSceneDict.Groups = '.self::setData($cond['Groups']);
        }
        if (isset($cond['Groupslist'])) {
            $where .= "AND TSceneDict.Groups IN (".self::setArrayData($cond['Groupslist']).") ";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : '';
    }
}
