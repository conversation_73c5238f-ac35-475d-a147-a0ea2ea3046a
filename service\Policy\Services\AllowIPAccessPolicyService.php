<?php
/**
 * Description: 零信任指定 IP 地址可访问策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowIPAccessPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     * @return bool
     * @throws \Exception
     */
    public function check(): bool
    {
        $device = $this->device;
        foreach ($this->params['Config'] as $ip){
            $arr=explode('-',$ip);
            $r= count($arr) >1 ? FindInIP($device['IP'],$arr[0],$arr[1]):$device['IP'] == $arr[0];
            if ($r){
                return true;
            }
        }
        return false;
    }
}
