<?php

/**
 * Description: 根据设备ID和用户ID获取token
 * User: <EMAIL>
 * Date: 2022/05/16 10:32
 * Version: $Id$
 */

class DeviceTokenRedis extends BaseStringRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'DeviceToken';

    /**
     * 是否同步到ASG
     *
     * @var string
     */
    protected static $remote = false;

    /**
     * 单条
     *
     * @param string $deviceId
     *
     * @return mixed
     */
    public static function getOne($deviceId)
    {
        return self::get($deviceId);
    }

    /**
     * 单条
     *
     * @param string $deviceId
     * @param string $value
     *
     * @return mixed
     */
    public static function setOne($deviceId, $value)
    {
        $expire = $deviceId > 0 ? null : 86400;
        return self::set($value, $expire, $deviceId);
    }

    /**
     * 修改
     *
     * @param string $value
     * @param int $expire
     * @param array $keys
     *
     * @return mixed
     */
    protected static function set($value = '', $expire = 0, ...$keys)
    {
        if (empty($keys)) {
            return false;
        }

        $key = static::getKey($keys);
        $oldData = lib_redis::get(static::PREFIX, $key);
        if ($oldData === $value) {
            return false;
        }
        if (!empty($oldData)) {
            SessionRedis::deleteOne($oldData);
        }
        if (!empty(static::$remote)) {
            static::push(static::PREFIX . $key, $value, $expire);
        }
        return lib_redis::set(static::PREFIX, $key, $value, $expire);
    }

    /**
     * 删除
     *
     * @param string $token
     *
     * @return mixed
     */
    public static function deleteOne($token)
    {
        return self::del($token);
    }
}