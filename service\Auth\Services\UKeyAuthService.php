<?php
/**
 * Description: UKey认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: UKeyAuthService.php 161644 2021-11-12 13:26:30Z duanyc $
 */

namespace Services\Auth\Services;

use DeviceModel;
use Exception;
use Services\Auth\Interfaces\AuthServiceInterface;

class UKeyAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'UKey';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['userName'] = addslashes(Base64DeExt($this->params['userName']));
        $this->params['password'] = Base64DeExt($this->params['password']);
        $this->params['subject'] = Base64DeExt(request('subject', 'request'));
        $this->params['caserial'] = request('caserial', 'request');
        $this->params['certserial'] = request('certserial', 'request');

        $this->params['certbase64'] = request('certbase64', 'request');
        // 这个base64不是标准的,要处理下,才能用
        $this->params['certbase64'] = stringDecode($this->params['certbase64']);
        /* 用于根证书校验，国密校验了证书格式，需要有换行 */
        $this->params['certbase64valid'] = "-----BEGIN CERTIFICATE-----\n" . $this->params['certbase64'] . "\n-----END CERTIFICATE-----";
        /* 用于证书解析 openssl_x509_parse */
        $this->params['certbase64'] = "-----BEGIN CERTIFICATE-----" . $this->params['certbase64'] . "-----END CERTIFICATE-----";
        return $this->params;
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);
        if (!empty($userInfo['DepartID'])) {
            DeviceModel::update($this->deviceId, ['DepartID' => $userInfo['DepartID']]);
        }
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'user_name', 'password', 'subject', 'caserial', 'certserial', 'certbase64'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'userName', 'password', 'subject', 'caserial', 'certbase64', 'certbase64valid'];
    }
}
