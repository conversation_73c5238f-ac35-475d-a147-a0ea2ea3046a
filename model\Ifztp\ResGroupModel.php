<?php

/**
 * Description: 资源组表
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: ResCollectModel.php 174776 2022-04-28 09:18:26Z duanyc $
 */

class ResGroupModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResGroup';
    public const PRIMARY_KEY = 'ResID';
    protected static $columns = [
        '*'       => '*',
        'group' => 'ResID,GroupName',
    ];

    /**
     * 获取类型ID与名字映射
     *
     * @return mixed
     */
    public static function getAllName()
    {
        self::$data = [];
        $column = self::$columns['group'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $allNames = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $allNames[$row['ResID']] = $row['GroupName'];
            }
        }
        return $allNames;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['ResID'])) {
            $where .= "AND ResID = ".self::setData($cond['ResID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
