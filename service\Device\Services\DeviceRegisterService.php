<?php
/**
 * Description: 设备注册服务
 * User: renchen
 * Date: 2021/6/4 17:15
 * Version: $Id: DeviceRegisterService.php 170252 2022-03-04 01:37:58Z lihao $
 */


namespace Services\Device\Services;

use NoticeServiceProvider;
use ServerServiceProvider;
use Services\Common\Services\DeviceOperationService;
use Services\Common\Services\RoleService;

class DeviceRegisterService extends DeviceInfoService
{

    /**
     * 设备场景信息服务
     *
     * @var DeviceSceneService
     */
    protected $DeviceSceneService = null;

    /**
     * 服务器类型
     *
     * @var string
     */
    protected $serverType = null;

    /**
     * asc配置
     *
     * @var array
     */
    protected $ascConfig = null;

    /**
     * 初始化
     * @throws \Exception
     */
    public function __construct()
    {
        parent::__construct([]);
        $this->serverType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if ($this->serverType === DEVTYPE_DASC) {
            $this->ascConfig = read_inifile(PATH_ETC . "asc.ini");
        }
        $this->logFileName = 'register';
        $this->DeviceSceneService = new DeviceSceneService();
    }

    /**
     * 获取设备注册信息（即：get_device_reginfo）
     *
     * @param $params
     * @return array
     */
    public function getReginfo($params): array
    {
        $deviceType = \DeviceTypeModel::getAll(['Type' => 2], 'one');
        if (0 === strcasecmp(LANG, 'en')) {
            foreach ($deviceType as $k => $v) {
                $deviceType[$k]['TypeName'] = $v['AliasName'];
            }
        }
        $deviceExpand = \DeviceExpandModel::getSingle(['DeviceID' => $params['deviceId']]);
        $deviceInfo = \DeviceModel::getJoinComputerAndDepart($params['deviceId']);
        $deviceInfo['Location_parentID'] = $this->getParentLocation((int)$deviceInfo['LocationId']);

        // 数据整理，为方便新的前端使用数据
        $typeIDArr = array_column($deviceType, 'TypeID');
        $typeIndex = array_search($deviceInfo['Type'], $typeIDArr, true);
        $deviceInfo['TypeName'] = $typeIndex !== false ? $deviceType[$typeIndex]['TypeName'] : '';

        // LocationName
        $deviceInfo['LocationName'] = \LocationModel::getLocationNameByLocationID((int)$deviceInfo['LocationId']);

        return ['deviceType' => $deviceType, 'deviceExpand' => $deviceExpand, 'deviceInfo' => $deviceInfo];
    }

    /**
     * 提交注册信息
     *
     * @param $params
     *
     * @return mixed
     * @throws \Exception
     */
    public function submitInfo($params)
    {
        cutil_php_debug("input params:" . var_export($params, true), 'regdevsubmit');
        $sceneInfo = $this->DeviceSceneService->getSceneInfoById($params['sceneId']);
        cutil_php_debug("sceneInfo:" . var_export($sceneInfo, true), 'regdevsubmit');
        $params['IsNeedReg'] = $sceneInfo['IsNeedReg']; // IsNeedReg: 0:自动注册，1:手动注册
        $params['Registered'] = (int)$sceneInfo["IsNeedAuto"] === 1 ? 0 : 1;// 0待审核 1自动注册 IsNeedAuto: 0:自动审核，1：手功审核

        $deviceInfo = $this->regDeviceByASM($params);
        cutil_exec_no_wait(PATH_ASM . 'sbin/AsmTaskManagerClient -u ' . $params['deviceId']);
        if ($params['Registered'] === 0 && !$this->isAgent()) {
            \lib_alarm::createAlarm($deviceInfo); // 生成报警
        }
        // 异步重新更新设备角色
        \lib_yar::clients('net', 'UpdateDeviceRole', ['deviceId' => $params['deviceId']]);
        if ((int)$params['Registered'] === 1) {
            \ResultServiceProvider::isolationDeviceByCurr($params['deviceId']);
        }
        // 如果是DASC，则及时上报审核状态到DASM update by renchen 2021-1-27
        $this->noticeDasm($params);
        //注册之后通知MSEP
        NoticeServiceProvider::sendSyncStatus($params['deviceId'],'device');
        return $deviceInfo;
    }

    /**
     * 是否小助手？
     * @return bool
     */
    private function isAgent(): bool
    {
        $HTTP_REFERER = isset($_SERVER['HTTP_REFERER']) ?
            htmlspecialchars($_SERVER['HTTP_REFERER'], ENT_QUOTES, 'ISO-8859-1') : '';
        cutil_php_debug("HTTP_REFERER:" . var_export($HTTP_REFERER, true), 'regdevsubmit');

        return !(false === strpos($HTTP_REFERER, 'from=IsAgent'));
    }

    /**
     * 判断是否是二次审核
     * 如果开启了二次无需审核测返回true
     *
     * @param $MAC
     * @param $Hard
     *
     * @return bool
     */
    public function isAgainAudit($MAC, $Hard): bool
    {
        $auditCount = \DeviceAuditLogModel::getCount(['Mac' => $MAC, 'Hard' => $Hard]);
        if (!empty($auditCount)) {
            // 第二次
            $RegisterAudit = \DictModel::getAll('CLIENTCHECK');
            if ((int)$RegisterAudit['RegisterAudit'] === 1) {
                // 二次无需审核
                return true;
            }
        }
        return false;
    }

    /**
     * 获取设备位置
     * @param $params
     * return locationId;
     * @return false|int|mixed
     */
    private function getDeviceLoaction($params)
    {
        $locationId = $params['childInputPosition'] !== "" ? $params['childInputPosition'] : $params['locationId'];
        if ($params['customLocationInput'] !== "") {
            $Location = \LocationModel::getOneByLocation($params['customLocationInput']);

            if (isset($Location['LocationID']) && $Location['LocationID'] !== "") {
                $locationId = $Location['LocationID'];
            } else {
                $ldata = ['UpID' => $locationId, 'Location' => $params['customLocationInput']];
                $locationId = \LocationModel::insert($ldata);
            }
        }
        return $locationId;
    }

    /**
     * 更新注册信息
     * @param array $params
     * @param array $device
     * @param array $updateComputer
     * @throws \Exception
     */
    private function updateRegisterDeviceInfo(array $params, array $device, array $updateComputer): void
    {
        //更新设备信息
        $getlocationId = $this->getDeviceLoaction($params);

        $aInfo = array(
            'Mac' => $params['MAC'],
            'TDevice' => array(
                'LocationId' => $getlocationId,
                'EMail' => $params['email'],
                'Remark' => $params['remark'],
                'RegistIsInternal' => IS_INTERNAL ? 1 : 2,
                'LastTime' => date('Y-m-d H:i:s')
            )
        );

        // 未开启【已审核设备再次入网时不需要审核】，但角色需手工审核
        // 避免从小助手查看设备详情菜单提交后，设备需要重新审核
        // 小助手页面不更新此语句
        // 现场小助手版本新旧混用的情况下,需要添加判断$device['Registered'] = -2, 保证可以修改状态值
        if (!$this->isAgent() || (int)$device['Registered'] === -2) {
            $deviceOperationService = new DeviceOperationService();
            $result = $deviceOperationService->updateDevice($updateComputer);
            cutil_php_log("update device:" . var_export($result, true), $this->logFileName);
        }

        // 如果是ASM自动注册的话不更新设备类型，否则更新设备类型
        if ((int)$params['IsNeedReg'] === 1) {
            // 如果是管理员修改的设备类型则不更新
            if (!empty($params['deviceType'])) {
                $aInfo['TDevice']['Type'] = $params['deviceType'];
                $aInfo['TDevice']['SubType'] = \hlp_common::getSubType(OS_TYPE);
                $aInfo['TDevice']['IsHandType'] = 1;   //设备注册 用户选择的设备类型 也认为是正确的类型 不允许别的地方修改
            }
        }

        if (!empty($params['departId'])) {
            // 传递了部门ID则进行更改
            $aInfo['TDevice']['DepartID'] = $params['departId'];
            $aInfo['TAddDevice']['DepartID'] = $params['departId'];
        }

        if (trim($params['tel']) !== "") {
            $aInfo['TDevice']['Tel'] = $params['tel'];
        }

        if (trim($params['username']) !== "") {
            $aInfo['TDevice']['UserName'] = $params['username'];
            $aInfo['TAddDevice']['UserName'] = $params['username'];
        }
        $aInfo['DeviceID'] = $params['deviceId'];

        $deviceOperationService = new DeviceOperationService();
        $result = $deviceOperationService->updateDevice($aInfo);
        cutil_php_log("device register:{$params['IsNeedReg']}" . var_export($result, true), $this->logFileName);

        //添加扩展信息
        $expandCount = \DeviceExpandModel::getCount(['DeviceID' => $params['deviceId']]);
        $eparams = [];
        for ($i = 1; $i <= 10; $i++) {
            if (empty($params["requireexpand{$i}"])) {
                continue;
            }
            $eparams["Expand_{$i}"] = $params["requireexpand{$i}"];
        }
        if (empty($expandCount)) {
            // /发现扩展信息不存在 添加
            $eparams['DeviceID'] = $params['deviceId'];
            $eparams['UpdateTime'] = "now()";
            \DeviceExpandModel::insert($eparams);
        } else {
            //更新
            \DeviceExpandModel::update($params['deviceId'], $eparams);
        }
    }

    /**
     * @Description:设备迁移
     * @param $deviceId
     * @throws \Exception
     */
    public function deviceMigrate($deviceId)
    {
        if ($this->serverType === DEVTYPE_DASC) {
            cutil_php_log("deviceMigrate:{$deviceId}", $this->logFileName);
            $params = ['deviceIds' => $deviceId];
            \lib_yar::clients('distribute', 'deviceMigrate', $params);
        }
    }

    /**
     * 通过ASM设备注册
     *
     * @param $params
     * @return array|bool|mixed
     * @throws \Exception
     */
    private function regDeviceByASM(&$params)
    {
        $departCount = \DepartModel::getCount(['DepartID' => $params['departId']]);
        if (empty($departCount)) {
            // 发现部门已经不存在(或已删除)
            $params['departId'] = '0';
        }
        // 设备重注册
        if (!empty($params['deviceId'])) {
            $device = \DeviceModel::getJoinComputer($params['deviceId'], 'register');
            if (empty($device)) {
                T(21103006);
            }
            $params["MAC"] = $device["Mac"];
            $params["Hard"] = $device["Hard"];

            $relationComputer = \RelationComputerModel::getOne($params['deviceId'], 'auth');
            $ipamConfig = ServerServiceProvider::ipamConfig();
            if ($relationComputer['CutOffStopTime'] >= date("Y-m-d H:i:s") && $ipamConfig['linkage'] != 1 && $ipamConfig['dev_regulation_access_on_off'] != 1) {
                T(21120029);
            }

            // 判断是否伪造mac(如果是ipv6环境以0.0.0.0作为ip填充的，则不处理,如果获取到MAC会导致设备反复插入导致无法入网，urtracker:38412)
            if ($device["IP"] !== '0.0.0.0' && RelativelyIsFake($device["Mac"])) {
                $mac = QueryMacFromMvg($device["IP"]);
                if (IsStrHaveMac($mac)) {
                    $params["Hard"] = $params["MAC"] = $mac;
                }
            }

            // 判断是否是二次审核  $ClientCheck 这里有待改进
            if ($this->isAgainAudit($params['MAC'], $device['Hard'])) {
                $params['Registered'] = 1;
            }

            //只要是注册，就更新注册时间
            $updateComputer = array(
                'DeviceID' => $params['deviceId'],
                'TComputer' => array(
                    'Registered' => $params['Registered'],
                    'RegTime' => date('Y-m-d H:i:s'),
                ),
            );

            if (is_array($device) && (int)$device['Registered'] === -2) {
                //注册时候如果allmac为空 并且hard为mac类型更新硬盘id
                //合入通用升级包 ASMPatch_v6.0.6039.2722.R001_20230313_优化hard更新处理.exe.rar
                if( IsStrHaveMac($params['Hard']) == $params['Hard']  && $device['AllMac'] == ''){
                    $params['Hard'] = $params['MAC'];
                    cutil_php_log('new_Hard:'.$params['Hard'], $this->logFileName);
                }
                // 新设备注册时重新获取硬盘id，防止硬盘重复问题
                if ($params['newHard'] !== '') {
                    $params['Hard'] = $params['newHard'];
                }
            }
            if ($params['Hard']) {
                $updateComputer['Hard'] = $params['Hard'];
            }

            // 更改IP后重注册，修复
            if ((int)$params['Registered'] === 1) {
                // 判断授权
                $isRegistrable = \SystemServiceProvider::isRegistrable();
                cutil_php_log("isRegistrable: " . var_export($isRegistrable, true), 'regdevsubmit');
                if ((int)$device['Registered'] !== 1 && !$isRegistrable) {
                    T(21139006);
                }
                if ((int)$device['Registered'] !== 1) {
                    $this->deviceMigrate($params['deviceId']);
                }
                $this->repairRegRegister($params['deviceId']);
            }

            $this->updateRegisterDeviceInfo($params, $device, $updateComputer);
        }
        // 获取设备基本信息
        return \DeviceModel::getJoinComputerAndDepart($params['deviceId']);
    }

    /**
     * 修复重注册
     *
     * @param $deviceId
     */
    public function repairRegRegister($deviceId): void
    {
        if (empty($deviceId)) {
            return;
        }
        \ChangeIPreRegModel::delete(['DeviceID' => $deviceId]);
        $deviceList = \DeviceModel::getJoinComputer($deviceId, 'register', false);
        $cdata = [];
        foreach ($deviceList as $device) {
            if ((int)$device['Registered'] === 1) {
                $cdata[] = ['DeviceID' => $deviceId, 'IP' => $device['IP'],
                    'Mac' => $device['Mac'], 'Hard' => $device['Hard']];
            }
        }
        \ChangeIPreRegModel::insertPatch($cdata);
    }

    /**
     * 通知 DASM注册状态变化
     * @param $params
     * @return bool
     * @throws \Exception
     */
    private function noticeDasm($params): bool
    {
        // 如果是DASC，且注册状态为待审核,或者自动注册的就立即通知DASM
        if ($this->serverType === DEVTYPE_DASC) {
            $report = array();
            $report['type'] = 'mysql';
            $report['AscID'] = $this->ascConfig['AscID'];
            $report['Message'] = array();
            // 查询TComputer表的主键
            $computer = \ComputerModel::getOne($params['deviceId'], 'one');
            if (!isset($computer['ID']) || !is_numeric($computer['ID'])) {
                cutil_php_warning('根据DeviceID没有查询到TComputer表中的ID,不执行上报操作', 'regdevsubmit');
                return false;
            }
            $terms = "DAscID='" . $this->ascConfig['AscID'] . "' AND OrigID=" . $computer['ID'];
            $updateData = array(
                'action' => 'update',
                'TableName' => 'TComputer',
                'ID' => array(
                    'DeviceID' => $params['deviceId'],
                ),
                'terms' => $terms,
                'ItemNames' => array(
                    'DeviceID' => $params['deviceId'],
                    'Registered' => $params['Registered']
                ),
            );
            $report['Message'][] = $updateData;
            pub_mq_jsonmsg('/report/TComputer', $report, false, 2, $this->ascConfig['ManageIp']);
        }
        return true;
    }
}
