<?php

/**
 * Description: TEngineIpRegion表
 * User: <EMAIL>
 * Date: 2022/09/14 16:52
 * Version: $Id: EngineIpRegionModel.php 171857 2022-09-14 17:16:07Z chenpan $
 */

class EngineIpRegionModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TEngineIpRegion';
    public const PRIMARY_KEY = 'MacID';
    protected static $columns = [
        '*'    => '*',
        'IP' => 'IP',
        'device' => 'DeviceID'
    ];

    /**
     * 根据MAC获取数据
     * @param $cond
     * @param string $Column
     * @return array|bool
     */
    public static function getJoinTmpRole($cond, $Column = 'info')
    {
        if (empty($cond)) {
            return false;
        }

        self::$data = [];
        if (isset($cond['DeviceID'])) {
            $where = "WHERE DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['MAC'])) {
            $where = "WHERE TDevIpRegion.MAC = ".self::setData($cond['MAC']);
        }

        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $leftJoin = "left join TTmpNetRole on TDevIpRegion.IPListId = TTmpNetRole.ID";
        $sql = "select {$column} from {$table['name']} {$leftJoin} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }


    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['MacID'])) {
            $where .= "AND MacID = ".self::setData($cond['MacID']);
        }

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
