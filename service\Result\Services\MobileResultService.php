<?php
/**
 * Description: 移动端认证后上报结果服务
 * User: <EMAIL>
 * Date: 2021/07/22 15:53
 * Version: $Id: MobileResultService.php 157620 2021-09-23 15:45:58Z duanyc $
 */

namespace Services\Result\Services;

use Common\Facades\NACServiceFacade;
use Services\Auth\Interfaces\ResultServiceInterface;

class MobileResultService extends BaseResultService implements ResultServiceInterface
{

    /**
     * 初始化
     *
     * @param $params
     * @throws \Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 初始化上报
     * @return mixed|void
     * @throws \Exception
     */
    public function init()
    {
        $this->setDeviceInfo();
        $this->initclientCheck();
        $this->params['NoPassItemResult'] = [];
        $this->params['PassItemResult'] = [];
        $this->params['NoCheckItem'] = [];
    }

    /**
     * 初始化配置
     */
    public function initclientCheck()
    {
        $aResult = \DictModel::getAll('CLIENTCHECK'); //安检结果提示
        //由于手机端并没有PC端 安检后提示文字配置的那么多情况，但是确实共用的一套代码，但是手机端的入网成功提示确可以配置，这里优先使用手机端的配置
        $aResult['PromptWords1_en']=$aResult['mobile_Web_GoWeb_en'];
        $aResult['PromptWords1']=$aResult['mobile_Web_GoWeb'];

        //中英文版切换
        if (LANG === "en_US") {
            foreach ($aResult as $key => $value) {
                if ($aResult[$key."_en"]!="") {
                    $aResult[$key] = $aResult[$key."_en"];
                }
            }
        }
        $this->clientCheck = $aResult;
    }

    /**
     * 上报提交
     * @return array
     * @throws \Exception
     */
    public function submit()
    {
        $isNoItems = empty($this->params['itemsid']) && empty($this->params['checkres']);
        if ($this->params['isSafecheck'] == '0' || $isNoItems) { //未进行安检
            $this->authNoSafeCheck();
        } else { //进行过安检
            $this->getAndParsePolicy();  // 获取并解析规范
            $this->parseSingleItem();  // 分析并保存检查结果
            $this->judgeDeviceCheckResult(); // 判断检查结果
            $this->checkProduceAlarmInfo(); // 生成安检报警
            $this->publishCheckEvent((int)$this->params['deviceId'],'MobileResultService->submit');
        }
        $this->autoTrust();  // 自动设为可信处理
        $this->networkControl();  // 分析网络控制技术
        //重定向url
        $AccessUrl = $this->getRedirectUrl($this->params['firsturl'], $this->params['ascid']);
        if (strlen($AccessUrl) && !preg_match("/^http(s)?:\/\//i", $AccessUrl)) {
            $AccessUrl=HTTP_PROTOCOL . $AccessUrl;
        }
        $this->params['NoPassItemResult'] = $this->getNoPassItemResult();
        $vpnType = \DictModel::getOneItem("ZTP","VpnType"); // VpnType
//        $return['VpnType'] = $vpnType ?? 'proxy';
        return [
            "NoItem" => $this->params['NoPassItemResult'],
            "YesItem" => $this->params['PassItemResult'],
            "ErrItem" => $this->params['NoCheckItem'],
            "CheckResult" => $this->checkResult,
            "Other" => ["firsturl" => $AccessUrl],
            'VpnType' => $vpnType['ItemValue'] ?? 'proxy'
        ];
    }

    /**
     * 获取未通过关键安检项
     * @return array
     */
    public function getNoPassItemResult()
    {
        if (empty($this->params['NoPassItemResult'])) {
            return [];
        }
        // 分析关键检查项并排序
        $tempKeyItem=array();
        foreach ($this->params['NoPassItemResult'] as $key => $i) {
            if (in_array($i['ItemID'], $this->params['KeyItemID'])) {
                $i['Result'] = 'Key';
                $tempKeyItem[] = $i;
                unset($this->params['NoPassItemResult'][$key]);
            }
        }
        return array_merge($tempKeyItem, $this->params['NoPassItemResult']);
    }

    /**
     * 自动设为可信
     */
    public function autoTrust()
    {
        // 移动终端自动设为可信
        $trustdevmanager = \DictModel::getAll('trustdevmanager');
        if ($trustdevmanager['autotrust']=='1' && VpnIpRange($this->deviceInfo['IP'], $trustdevmanager['trustip'], '|')) {
            $rparams = ['IsTrustDev' => 1, 'CutOffStopTime' => '1970-01-01', 'AuditStopTime' => '9999-12-31'];
            \RelationComputerModel::update($this->deviceId, $rparams);
            $this->writeLog("mobile automatically trusted");
            \DeviceModel::update($this->deviceId, ['RoleID' => ROLE_ID_TRUST]);
        }
    }

    /**
     * 分析网络控制技术
     * @throws \Exception
     */
    public function networkControl()
    {
        // 只要重新做了认证安检，不过结果是否通过，都要删除TCutNetDevice表中的记录
        // 姜工说，终端都重新做人了就放过他吧 即使安检不过
        \CutNetDeviceModel::delete(['DeviceID' => $this->deviceId]);
        if ($this->checkResult['Res'] != "false") {
            $nparams = [ 'DeviceID' => $this->deviceId, 'AuthTime' => 'now()', 'AuthDevice' => $this->deviceId,
              'AuthDevicePort' => 0, 'DeviceMAC' => $this->deviceInfo['MAC'],'DeviceIP' => $this->deviceInfo['IP']];
            switch ($this->params["enforcement"]) {
                case 'tbridge':  // 透明网桥
                    $nparams['AuthType'] = 'TBRIDGE';
                    \NacAuthListModel::insert($nparams);
                    break;
                case 'rbridge':  // 策略路由
                    $nparams['AuthType'] = 'RBRIDGE';
                    \NacAuthListModel::insert($nparams);
                    break;
            }
        }

        $netParams = ['device_id' => (int)$this->deviceId];
        if ($this->params['isPhoneClient'] == '0' && $this->params['tbclientip4']) {
            $netParams['ip'] = $this->params['tbclientip4'];
            NACServiceFacade::access("WebAccess:MobileResult networkControl", [$netParams], 'Access');
            $dparams = ['DeviceID' => $this->deviceId, 'IP' => $this->params['tbclientip4']];
            $this->deviceService->updateDevice($dparams);
        } else {
            NACServiceFacade::access("WebAccess:MobileResult networkControl", [$netParams], 'Access');
        }
        $this->writeLog("WebAccess " . $this->deviceId.' '. $this->params['tbclientip4']." ###");
    }
}
