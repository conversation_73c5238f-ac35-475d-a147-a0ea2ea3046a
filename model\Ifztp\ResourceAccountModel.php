<?php

/**
 * Description: 资源的账号
 * User: <EMAIL>
 * Date: 2022/05/07 23:32
 * Version: $Id$
 */

class ResourceAccountModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResourceAccount';
    public const PRIMARY_KEY = 'ResAccountID';
    protected static $columns = [
        '*'    => '*',
        'user' => 'ResAccountID,UserName,Password',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        if (isset($cond['ResID'])) {
            $where .= "AND ResID = ".self::setData($cond['ResID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
