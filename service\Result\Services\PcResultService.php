<?php
/**
 * Description: PC认证后上报结果服务
 * User: <EMAIL>
 * Date: 2021/07/22 15:53
 * Version: $Id: PcResultService.php 158198 2021-09-28 13:02:19Z duanyc $
 */

namespace Services\Result\Services;

use Common\Facades\NACServiceFacade;
use hlp_common;
use NoticeServiceProvider;
use Services\Auth\Interfaces\ResultServiceInterface;
use Services\Common\Services\RoleService;
use Services\Device\Services\DeviceSceneService;

class PcResultService extends BaseResultService implements ResultServiceInterface
{
    /**
     * 初始化
     *
     * @param $params
     * @throws \Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 初始化上报
     * @return mixed|void
     * @throws \Exception
     */
    public function init()
    {
        $this->setDeviceInfo();
        $this->initclientCheck();
        $this->params['NoPassItemResult'] = [];
        $this->params['PassItemResult'] = [];
        $this->params['NoCheckItem'] = [];
    }

    /**
     * 初始化配置
     */
    public function initclientCheck()
    {
        $aResult = \DictModel::getAll('CLIENTCHECK'); //安检结果提示

        //中英文版切换
        if (LANG === "en_US") {
            foreach ($aResult as $key => $value) {
                if ($aResult[$key."_en"]!="") {
                    $aResult[$key] = $aResult[$key."_en"];
                }
            }
        }
        $this->clientCheck = $aResult;
    }

    /**
     * 上报提交
     * @return array
     * @throws \Exception
     */
    public function submit()
    {
        if ($this->params['auditStatus'] != '1') {
            if ($this->params['isSafecheck'] == '0') {
                // 未进行安检
                $this->authNoSafeCheck(); // 只进行身份认证未进行安检
                $this->checkAudit(); // 安检后需要审核才能入网
            } else {
                // 进行过安检
                $osType = \hlp_common::getOsTypeByOsName($this->deviceInfo['OSName']);
                $this->getAndParsePolicy($osType);  // 获取并解析规范
                $this->parseSingleItem();  // 分析并保存检查结果
                $this->judgeDeviceCheckResult(); // 判断检查结果
                $this->checkAudit(); // 安检后需要审核才能入网
                $this->checkProduceAlarmInfo(); // 生成安检报警
            }
            $this->networkControl(); // 分析网络控制技术
        } else {
            $res = trim($this->params['res']);
            $this->checkResult['Res'] = ($res !== "") ? $res : "success";
            $this->checkResult['Desc'] = L(21104011);
            if ((int)$this->params['reCheck'] === 1) {
                $this->repairTNacOnLineDeviceInfo();
            }
            $netParams = ['device_id' => $this->deviceId];
            if ($this->params['isActive'] == '0' && $this->params['tbclientip4']) {
                $dparams = ['DeviceID' => $this->deviceId, 'IP' => $this->params['tbclientip4']];
                $this->deviceService->updateDevice($dparams);
                $netParams['ip'] = $this->params['tbclientip4'];
            }
            NACServiceFacade::access("WebAccess:PcResult submit", [$netParams], 'Access');
        }
        $this->params['NoPassItemResult'] = $this->getNoPassItemResult();
        return [
            "NoItem" => $this->params['NoPassItemResult'],
            "YesItem" => $this->params['PassItemResult'],
            "ErrItem" => $this->params['NoCheckItem'],
            "CheckResult" => $this->checkResult
        ];
    }

    /**
     * 获取未通过关键安检项
     * @return array
     */
    public function getNoPassItemResult()
    {
        if (empty($this->params['NoPassItemResult'])) {
            return [];
        }
        // 分析关键检查项并排序
        $tempKeyItem=array();
        foreach ($this->params['NoPassItemResult'] as $key => $i) {
            if (in_array($i['ItemID'], $this->params['KeyItemID'])) {
                $i['Key'] = 1;
            } else {
                $i['Key'] = 0;
            }
            $tempKeyItem[] = $i;
        }
        return $tempKeyItem;
    }

    /**
     * 安检后需要审核才能入网
     */
    public function checkAudit()
    {
        //开启安检后审核功能
        if ($this->deviceInfo['Registered'] == "0" && $this->params['ControlPostion'] == "1" &&
            $this->checkResult['Res'] !== "false") {
            $this->checkResult['Desc'] = $this->clientCheck['PromptWords6'];
        }
    }

    /**
     * 分析网络控制技术
     * @throws \Exception
     */
    public function networkControl()
    {
        \CutNetDeviceModel::delete(['DeviceID' => $this->deviceId]);
        if ($this->checkResult['Res'] !== "false") {
            $nparams = [ 'DeviceID' => $this->deviceId, 'AuthTime' => 'now()', 'AuthDevice' => $this->params["bas"],
                         'AuthDevicePort' => 0, 'DeviceMAC' => $this->deviceInfo['MAC'],'DeviceIP' => $this->deviceInfo['IP']];
            switch ($this->params["enforcement"]) {
                case 'tbridge':  // 透明网桥
                    $nparams['AuthType'] = 'TBRIDGE';
                    \NacAuthListModel::insert($nparams);
                    break;
                case 'rbridge':  // 策略路由
                    $nparams['AuthType'] = 'RBRIDGE';
                    \NacAuthListModel::insert($nparams);
                    break;
            }
        }

        if ($this->deviceInfo['Registered'] != "0" || $this->params['controlPostion'] != "1") {
            // 开启安检后审核功能
            $this->repairTNacOnLineDeviceInfo();
            $netParams = ['device_id' => $this->deviceId];
            if ($this->params['isActive'] == '0' && $this->params['tbclientip4']) {
                $netParams['ip'] = $this->params['tbclientip4'];

                $dparams = ['DeviceID' => $this->deviceId, 'IP' => $this->params['tbclientip4']];
                $this->deviceService->updateDevice($dparams);
            }
            try {
                NACServiceFacade::access('WebAccess:PcResult networkControl', [$netParams], 'Access');
                $this->writeLog("WebAccess " . $this->deviceId.' '. $this->params['tbclientip4']." ###");
            }
            catch (\Exception $e) {
                $this->writeLog('WebAccess:PcResult networkControl 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误文件：' . $e->getFile(). '错误行数：' . $e->getLine(), 'ERROR');
            }
            // 如果指纹违规则马上断开网络 检查是否有指纹模块授权
            $ret_reg = getmoduleregist(8);

            if ($ret_reg != 0) {
                //从数据库中读取该设备是否违规
                $finger = \FingerViolationModel::getSingle(['MAC' => $this->deviceInfo['MAC']]);
                if (!empty($finger)) {
                    $cmd = PATH_ASM . 'sbin/set_client_one ' . $this->deviceInfo['IP'] . ' 0 ' . $this->deviceInfo['MAC'];
                    cutil_exec_no_wait($cmd);
                }
            }
        }
    }

    /**
     * 认证信息补全，防止第一次安检不通过断网删除了TNacOnLineDevice
     * 认证信息,所以在修复完成之后能入网的情况下要补全信息
     */
    public function repairTNacOnLineDeviceInfo()
    {
        if ($this->deviceId > 0 && $this->params['roleId'] > 0 && !empty($this->params['authType'])) {
            $onlineDevice = \NacOnLineDeviceModel::getOne($this->deviceId, 'name');
            if (empty($onlineDevice)) {
                $nparams = ['DeviceID' => $this->deviceId, 'AuthType' => $this->params['authType'],
                    'UserName' => $this->params['userName'], 'RoleID' => $this->params['roleId'],
                    'InsertTime' => 'now()', 'AuthTime' => 'now()', 'LastHeartTime' => 'now()'];
                \NacOnLineDeviceModel::insert($nparams);
                if (hlp_common::isOpenThirdServer()) {
                    \NacPreOnLineDeviceModel::delete(['DeviceID' => $this->deviceId]);
                    \NacPreOnLineDeviceModel::insert($nparams);
                }

                //插入在线表之后通知MSEP
                NoticeServiceProvider::sendSyncStatus((string)$this->deviceId, 'deviceToUser');
            }
        }
    }

    /**
     * 获取客户端需要的信息
     * @return array
     * @throws \Exception
     */
    public function getResultInfo()
    {
        $UKey = \DictModel::getAll("UKey"); // UKey数
        $GuestAuth = \DictModel::getAll("GUESTAUTH"); //来宾认证参数
        $ChangePassword = $this->getChangePassword();
        $type_path = PATH_ETC . "asm/asc/etc/tbridge_private.ini";
        $aEthernet = parse_initfile($type_path); //获取802.1x设置开关
        $SuportDSM = (((isset($aEthernet['8021X']) && (int)$aEthernet['8021X'] === 1)
                || (isset($aEthernet['dot1x']) && (int)$aEthernet['dot1x'] === 1))
                && $this->deviceInfo['IsTrustDev'] != '1') ? '1' : '0';

        $aNatInfo = parse_initfile(PATH_ETC . "asm/asc/etc/tbridge_comm.ini"); //读取NAT网络设置开关
        $aNatOpen = $aNatInfo['OPENNAT'] ?: 0;

        $sceneService = new DeviceSceneService();
        $sceneInfo = $sceneService->getSceneInfoById($this->params['sceneId']);
        $paramInfo = new \xml_check_param_info($sceneInfo['CheckParam']);
        $sceneParamConf = $paramInfo->parseXml();
        $return = [];
        $return['WebUrl'] = URL_BASE . "/";
        $return['CheckUrl'] = $return['WebUrl'] . ($this->params['enforcement'] === "portal" ? "portal.html#enforcement=" : "?enforcement=") .
                              $this->params['enforcement'] . "&bas=" . $this->params['bas'];
        $return['IsInstall'] = $sceneInfo['IsNeedActive'] == '2' ? '1' : '0';
        $return['DeviceID'] = $this->params['deviceId'];
        $return['PolicyID'] = $this->params['policyId'];
        $return['RoleID'] = $this->params['roleId'];
        $return['SceneID'] = $this->params['sceneId'];
        $return['DeskLinkName'] = $sceneInfo['IsAssistant'] == '1' ? $sceneParamConf['AssistantName'] : '';
        $return['IsUseMiniAuthWnd'] = $sceneInfo['IsUseMiniAuthWnd'] ?? '';
        $return['IsOpenNetworkBeforeLogon'] = $sceneInfo['IsOpenNetworkBeforeLogon'] ?? '';
        $return['AJCNotOnlineCutOffNet'] = $sceneInfo['AJCNotOnlineCutOffNet'];
        $return['IsAuthorizedAccess'] = $sceneInfo['IsAuthorizedAccess'] ?? '';
        $return['RegeditTitle'] = $sceneParamConf['ClientCheckTitle'];
        $return['LastCheckTID'] = $this->deviceInfo['LastCheckTID'];
        $return['CheckResult'] = $this->deviceInfo['CheckResult'];
        $return['CheckResultDesc'] = '';
        $DeviceCurStatus = 0;
        //上报未审核状态给小助手xiaoxj 20201013
        if ($this->deviceInfo['Registered'] == "0" && $this->clientCheck['ControlPostion'] == "1"
            && $this->deviceInfo['CheckResult'] !== "false") {
            $DeviceCurStatus = 1;
        }
        $return['DeviceCurStatus'] = $DeviceCurStatus;
        $return['LastCheckTime'] = $this->deviceInfo['LastFaultTime'];
        $return['ListeningPort'] = '36588';
        $return['CryptKey'] = 'asmkey';
        $return['AboutTitle'] = $sceneParamConf['AboutTitle'];
        $return['AboutMessage'] = str_replace('        ', '[br]', $sceneParamConf['AboutMessage']);
        $return['DelDevice'] = $sceneParamConf['DelDevice'];
        $return['CheckAgent'] = '0';
        $return['HowLongNotInsert'] = '';
        if ($UKey['UKeyType'] == '3' && $this->params['authType'] === 'UKey') {
            $LastAuthType = 'Ukey_ePass1000ND';
        } else {
            $LastAuthType = $this->params['authType'];
        }
        $return['LastAuthType'] = $LastAuthType;
        $return['AuthType'] = $this->params['authType'];
        $return['UKeyAuthCert'] = '';
        $return['UserName'] = $this->params['username'];
        $return['Password'] = '';
        $return['IsAutoAuth'] = $this->params['isAutoAuth'];
        $return['IsHideIcon'] = $sceneParamConf['HideAssistant'];
        $return['ReCheckTime'] = $sceneParamConf['ReCheck'] == '1' ? $sceneParamConf['ReCheckTime'] : '0';
        $return['Disturb'] = $sceneParamConf['Disturb'];
        $return['AuthInterval'] = min($sceneInfo['AuthInterval'], 10);
        $return['IsShowNavigation'] = $sceneParamConf['IsNavigation'];
        $return['SupportNat'] = $aNatOpen;
        $return['SuportDSM'] = $SuportDSM;
        $return['EnableSoftuseApplyFor'] = '1';
        $IsCanGetGuestCode = '0';
        // 来宾接待配置还是跟用户角色走
        $roleService = new RoleService();
        $roleCode = $roleService->getRoleInfoOne($this->params['roleId'], 'RoleCode', 'RoleCode');
        if ($this->params['isGuest'] != '1' && $GuestAuth['State'] == '1' && $GuestAuth['RoleCode'] == "1"
            && (isset($roleCode['RoleCode']) && $roleCode['RoleCode'] == "1") && $this->deviceInfo['Registered'] == "1" &&
            ($GuestAuth['NetCode'] == '1' || $GuestAuth['QrCode'] == '1')) {
            $IsCanGetGuestCode = '1';
        }
        $return['IsCanGetGuestCode'] = $IsCanGetGuestCode;
        $return['IsAutoRepair'] = $sceneInfo['AutoRepair'];
        $return['IsSafeCheck'] = $sceneInfo['IsSafeCheck'];
        $return['IsSceneChange'] = '';
        $return['IsChangePassword'] = $ChangePassword['IsChangePassword'];
        $return['PswChgUrl'] = $ChangePassword['ChangeUrl'];
        $return['IsAgentCall'] = 'Agent';
        $return['HideMenu'] = $sceneInfo['clientMenuName'];
        $return['ActiveMenu'] = $sceneInfo['ActiveMenu'];
        $return['SecurityCheckPolicy'] = $this->getAgentRole((int)$this->params['sceneId']);
        $return['BridgeType'] = $aEthernet['BRIDGE_TYPE'];
        $vpnType = \DictModel::getOneItem("ZTP", "VpnType"); // VpnType
        $return['VpnType'] = $vpnType['ItemValue'] ?? 'vpn';
        return $return;
    }

    /**
     * 获取改变密码配置
     * @return mixed
     */
    private function getChangePassword()
    {
        $ChangePassword = \DictModel::getAll("ChangePassword"); // 是否允许修改密码
        if ($ChangePassword["ChangeUrl"]!="") {
            $newChangePasswordUrl = explode("::", $ChangePassword["ChangeUrl"]);
            if (!empty($newChangePasswordUrl[1])) {
                $userType = substr($this->params['username'], 0, 1);
                if (preg_match("/^\d*$/", $userType)) {
                    //第三方链接修改密码不传用户名和，密码
                    $ChangePassword["ChangeUrl"] = str_replace("?username={username}", "", $newChangePasswordUrl["1"]) ;
                } else {
                    $ChangePassword["ChangeUrl"] =  $newChangePasswordUrl["0"];
                }
            }
        }
        return $ChangePassword;
    }

    /**
     * 获取角色
     *
     * @param int $sceneID
     *
     * @return mixed
     * @throws \Exception
     */
    private function getAgentRole(int $sceneID)
    {
        $scene = \SceneDictModel::getOneConfig($sceneID, 'ReCheckActions', 'AgentConfig');
        if (empty($scene['Config'])) {
            return '';
        }

        return (new \xml_agent_role($scene['Config']))->parseXml();
    }
}
