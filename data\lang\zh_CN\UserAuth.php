<?php
/**
 * Description: 用户认证
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: UserAuth.php 159776 2021-10-25 03:11:24Z duanyc $
 */

$GLOBALS['LANG'][21133] = [
    21133001 => '认证失败，[ {user} ]用户被锁定 {time} 分钟，请稍候再试！',
    21133002 => '认证失败,请输入正确的用户名和密码！',
    21133003 => '认证失败,本次认证必须修改密码！',
    21133004 => '认证失败，[ {user} ]登录错误，登录错误次数过多，用户被锁定 {time} 分钟，请稍候再试！',
    21133005 => '[ {user} ] 密码错误，认证失败，您还有 {time} 次机会！',
    21133006 => '认证失败,请输入正确的用户名和密码！',
    21133007  => '最小长度',
    21133008  => '位',
    21133009  => '不可为',
    21133010  => '不可包含用户名',
    21133011 => '不含重复的字母或数字',
    21133012 => '必须由数字、字母两者组合',
    21133013 => '必须由大写字母、小写字母、数字、特殊字符中三者组合',
    21133014 => '您的账户密码强度不符合要求',
    21133015 => '密码已过期，您需要修改密码才能继续使用！',
    21133016 => '密码将于 {expiredate} 到期，请及时修改！',
    21133017 => '密码复杂度校验',
    21133018 => '扫码已过期，请重新扫码！',
];
