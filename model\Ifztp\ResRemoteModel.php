<?php

/**
 * Description: 远程应用资源
 */

class ResRemoteModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResRemote';
    public const PRIMARY_KEY = 'ResID';
    protected static $columns = [
        '*' => '*',
        'serve' => 'RemotSerID,S.ResID as serResID,R.ResID',
        'list' => 'Icon,310 AS AccessTypeID,Remark,Tel,Username,ResID',
        'one' => "R.ResID,310 AS AccessTypeID,'https' AS ProxyProtocol,S.GateWayID,S.GateWayType,S.Addr,S.Port,S.RemotePort,A.App<PERSON>ath,S.Name as RemoteServiceName",
        'agent' => "S.GateWayType,S.GateWayID,310 AS AccessTypeID,'https' AS ProxyProtocol",
        'power' => '310 AS AccessTypeID,R.ResID,S.GateWayID,S.GateWayType,S.Addr as IP,S.Route',
        'guacamole' => 'R.ResID,310 AS AccessTypeID,S.Addr AS DomainName,S.Port AS RealPort',
        'health' => 'IsPassive',
    ];

    /**
     * 单条
     * @param $ID
     * @param string $Column
     * @return array|false|null
     */
    public static function getRelationOne($ID, $Column = '*')
    {
        if (empty($ID) || empty($Column)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "SELECT {$column} FROM " . "TResRemote R LEFT JOIN TRemoteService S ON R.RemotSerID = S.ID LEFT JOIN TRemoteApp A ON R.RemotAppID = A.ID" .
            "  WHERE R.ResID = " . self::setData($ID);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 多条
     *
     * @param array $cond
     * @param bool $order
     * @param int $start
     * @param int $limit
     *
     * @return mixed
     */
    public static function getRelationList($cond = [], $order = false, $start = 0, $limit = 1000)
    {
        self::$data = [];
        $where = static::getRelationWhere($cond);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $orderStr = $order ? "ORDER BY {$order}" : "";
        $groupBy = isset($cond['groupby']) ? "GROUP BY {$cond['groupby']}" : "";
        $column = isset($cond['column'], static::$columns[$cond['column']]) ? static::$columns[$cond['column']] : '*';
        $sql = "SELECT {$column} FROM " . "{$table['name']} R LEFT JOIN TRemoteService S ON R.RemotSerID = S.ID" .
            " {$where} {$groupBy}  {$orderStr} LIMIT {$start}, {$limit}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getRelationWhere($cond = []): string
    {
        $where = "";

        if (!empty($cond['ResID'])) {
            $where .= "AND R.ResID = " . self::setData($cond['ResID']);
        }

        if (!empty($cond['InResID'])) {
            $where .= "AND R.ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (!empty($cond['ResID'])) {
            $where .= "AND ResID = " . self::setData($cond['ResID']);
        }

        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }

        if (isset($cond['InRemotSerID'])) {
            $where .= "AND RemotSerID IN (" . self::setArrayData($cond['InRemotSerID']) . ")";
        }

        if (isset($cond['InRemotAppID'])) {
            $where .= "AND RemotAppID IN (" . self::setArrayData($cond['InRemotAppID']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
