<?php
/**
 * Description: 企业微信认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: WeWorkAuthService.php 170349 2022-03-04 09:55:28Z lihao $
 */

namespace Services\Auth\Services;

use AuthServiceProvider;
use DeviceModel;
use Exception;
use hlp_common;
use Services\Auth\Interfaces\AuthServiceInterface;

class WeWorkAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'WeWork';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['code'] = request('code', 'request');
        return $this->params;
    }

    /**
     * 移动端首页
     */
    public function main()
    {
        $code = request('code', 'request');
        $otherParams = hlp_common::otherAuthParams();
        $url = '/mobile/ui/wel.html?route_type=wework&weworkcode=' . $code . "&{$otherParams}";
        $url = strlen($url) > 0 ? $url : '/mobile/ui/wel.html?1=1';
        cutil_php_log('Location:' . $url . '&t=' . time(), 'wework');
        header('Location:' . $url . '&t=' . time());
        exit();
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);
        $config = AuthServiceProvider::getAuthDomainConfig('WeWork');
        if ($config['isEnableSynchronous'] == 1) {
            $dparams = ['DepartID' => $userInfo['DepartID'], 'UserName' => $userInfo['UserName']];
            DeviceModel::update($this->deviceId, $dparams);
        }
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'userName', 'password',  'code'];
    }
}
