<?php
/**
 * Description: 网络相关逻辑service
 * User: <EMAIL>
 * Date: 2021/08/03 15:53
 * Version: $Id: NetServiceProvider.php 172561 2022-04-01 09:25:22Z renchen $
 */

use Services\Common\Services\DeviceOperationService;

class NetServiceProvider extends BaseServiceProvider
{
    /**
     * 修改设备信息
     *
     * @param $deviceId
     * @param string $isActive
     * @param $tbclientip4
     *
     * @throws Exception
     */
    public static function winceAllowNet($deviceId, string $isActive, $tbclientip4)
    {
        $deviceInfo = \DeviceModel::getJoinRelationComputer($deviceId, 'relation');
        if (empty($deviceInfo) || empty($deviceInfo['DeviceID'])) {
            T(21104005);
        }
        if (self::getDeviceNetAble($deviceId)) {
            return;
        }

        //放开网络
        cutil_php_debug('wince_net:  try to allow net , deviceid :' . $deviceId, 'wince_net');
        //置为安检成功
        RelationComputerModel::update($deviceId, ['LastCheckTID' => 'null', 'CheckResult' => 'success',
            'LastFaultTime' => 'now()', 'CheckTime' => 'now()']);
        //移除强制下线
        CutNetDeviceModel::delete(['DeviceID' => $deviceId]);

        $netParams = ['device_id' => $deviceId];
        if ($isActive === '0' && !empty($tbclientip4)) {
            $netParams['ip'] = $tbclientip4;

            $deviceOperationService = new DeviceOperationService();
            $params = ['DeviceID' => $deviceId, 'IP' => $tbclientip4];
            $deviceOperationService->updateDevice($params);
        }
        \Common\Facades\NACServiceFacade::access("WebAccess:winceAllowNet", [$netParams], 'Access');

        if (!self::getDeviceNetAble($deviceId)) {
            T(21102004);
        }
    }

    /**
     * 获取当前设备是否放开
     *
     * @param $deviceId
     *
     * @return bool
     */
    public static function getDeviceNetAble($deviceId)
    {
        $res = DevIpRegionModel::getSingle(['DeviceID' => $deviceId, 'column' => 'net']);
        if (is_array($res) && isset($res['IPListId']) && $res['IPListId'] % 2 === 1) {
            return true;
        }
        return false;
    }

    /**
     * portal准入快速放开网络
     * 主要运用在装了小助手，在保活周期内的网络放开，在入网状态接口调用发起portal认证
     * @param array $params
     * @return int 0 :参数错误或者不需要快速认证 ； 1：已快速认证放开网络 ；<0 属于portal准入，但是不进行快速认证放开网络
     * @throws Exception
     */
    public static function portalFastOpenNet($params = []): int
    {
        if (!isset($params['deviceStatus'], $params['isEmergency'], $params['DeviceID'])) {
            return 0;
        }

        // 如果网络是放开的，开启了portal准入，就重新放开一下网络
        // 如果切换紧急模式的原因是关键服务异常比如：php-fpm，mysql，nginx中的一种那么无法放开网络
        if ($params['deviceStatus'] && is_file(PATH_ETC . 'portalCnf.ini')
            && get_ini_info(PATH_ETC . 'portalCnf.ini', 'EnablePortal')) {

            // 如果上次portal认证的时间小于1分钟，则在入网状态时不发起portal认证
            // 如果确实是需要认证的，走正常认证处理不通过入网状态放开网络
            $portalAuthTime = (int)cache_get_info('DeviceID_', $params['DeviceID'], 'PortalAuthTime');
            if (time() - $portalAuthTime < 60) {
                return -1;
            }

            // 如果没安装小助手，则不发起主动认证
            $installClient = DeviceModel::getOne((int)$params['DeviceID'], 'installClient');
            if (!is_array($installClient) || (int)$installClient['InstallClient'] !== 1) {
                if (!isset($params['portalRedirect']) || (int)$params['portalRedirect'] !== 1) {
                    return 0;
                }
                return -2;
            }

            // 如果设备ID跟TDevIpRegion中的ID不一致的，说明伪造IP入网，也不进行认证（其中会误杀例外终端和第三方要求放开网络的）
            // 目前portal准入暂不支持例外终端，第三方要求放开网络的在我们系统中没有设备对应的关系
            $ipArr = DevIpRegionModel::getList(['DeviceID' => $params['DeviceID'], 'column' => 'IP']);
            if (!is_array($ipArr) || !in_array($installClient['IP'], array_column($ipArr, 'IP'), true)) {
                return -3;
            }
            // 判断安全域是否有奇数
            $hasOddNumbers = array_filter($ipArr, function($item) {
                return $item['IPListId'] % 2 != 0;
            });

            if (count($hasOddNumbers) > 0) {
                // 取消从请求参数中获取，直接从设备缓存信息中取
                $baseIP = request('basIP', 'request', '');

                $netParams = [
                    'device_id' => $params['DeviceID'],
                    'ip' => $installClient['IP'],
                    'iplistid' => $params['IPListId'] ?? 1,
                    'mac' => $params['Mac'],
                    'bas_ip' => $baseIP
                ];
                \Common\Facades\NACServiceFacade::access("WebAccess:portalFastOpenNet", [$netParams], 'Access');
            }

            return 1;
        }

        return 0;
    }

    /**
     * 运维模式下放开指定设备的限制
     * @param $deviceId
     * @return mixed
     * @throws Exception
     */
    public static function makeEngineRegion($deviceId)
    {
        $deviceInfo = \DeviceModel::getOne($deviceId, 'one');
        if (!$deviceInfo) {
            T(21100002);
        }
        EngineIpRegionModel::delete(['DeviceID' => $deviceId]);
        $MacID = $deviceInfo['Mac'] . "_" . $deviceInfo['IP'] . "_" . $deviceId;
        $params = ['MacID' => $MacID, 'IP' => $deviceInfo['IP'], 'MAC' => $deviceInfo['Mac'], 'DeviceID' => $deviceId];
        EngineIpRegionModel::insert($params);
        $netParams = ['device_id' => (int)$deviceId, 'iplistid' => 3]; //场景管理升级后  3对应的是可信场景对应的安全域ID
        \Common\Facades\NACServiceFacade::access("WebAccess:makeEngineRegion", [$netParams], 'Access');
        return true;
    }

    /**
     * 获取访客入网增强指定安全域组合的TOtherRegion的ID  存在则直接返回，不存在则生成一个新的
     * @param $RegionIDs
     * @return mixed
     */
    public static function getOtherRegionID($RegionIDs)
    {
        $RegionIDArr = explode(',', $RegionIDs);
        if (!$RegionIDs || !is_array($RegionIDArr)) {
            T(21126031);
        }
        $OtherRegionInfo = \OtherRegionModel::getSingle(['RegionIDs' => $RegionIDs, "Type" => "1"]);
        if ($OtherRegionInfo && $OtherRegionInfo['ID']) {
            return $OtherRegionInfo['ID'];
        }
        $ipListID = 0;
        $noipListID = 0;
        $devInfo = read_inifile(PATH_ETC . "devinfo.ini");
        $deviceType = $devInfo['devtype'];
        $ascCfg = read_inifile(PATH_ETC . "asc.ini");
        if ($deviceType == "dasc" && $ascCfg['IsManage'] == 1) {
            $dasmIp = get_pub_dasm_ip();
            if (!empty($dasmIp)) {
                $res = lib_yar::clients("dasm_backend", "getOtherRegionId", ['ids'=>$RegionIDs], $dasmIp, 10000);
                self::log("rpc请求DASM结果 " . json_encode($res));
                if ($res) {
                    $ipListID = $res['data'][0];
                    $noipListID = $res['data'][1];
                }
            }
        }
        $IpRegionList = \IpRegionModel::getList(['RIDs' => $RegionIDArr, 'column' => 'one']);
        $OtherRegion = ["SceneID" => "0", "Type" => "1", "RegionIDs" => $RegionIDs, "IP" => "", "Port" => "", "IPv6" => "", "DomainList" => ""];
        foreach ($IpRegionList as $IpRegion) {
            $OtherRegion['IP'] .= "," . $IpRegion['IP'];
            $OtherRegion['IPv6'] .= "," . $IpRegion['IPv6'];
            $OtherRegion['Port'] .= "," . $IpRegion['Port'];
            $OtherRegion['DomainList'] .= "," . $IpRegion['DomainList'];
        }
        $ipListID && $OtherRegion['ID'] = $ipListID;
        OtherRegionModel::delete(['ID'=>$ipListID]);
        $ipListID = OtherRegionModel::insert($OtherRegion);
        $GuestNoRegion = ["SceneID" => "0", "Type" => "0", "RegionIDs" => $RegionIDs, "IP" => ",0.0.0.0-0.0.0.0", "Port" => ",", "IPv6" => ",::-::", "DomainList" => ","];
        $noipListID && $GuestNoRegion['ID'] = $noipListID;
        OtherRegionModel::delete(['ID'=>$noipListID]);
        $noipListID = OtherRegionModel::insert($GuestNoRegion);  //桥模块需要插入一个Type为0的安全域
        // 陈帅要求修改
        $regionIpList = array_column($IpRegionList, 'IP');
        foreach ($regionIpList as $regionIpstr) {
            $regionIpArr = explode('-', $regionIpstr);
            $cmd = PATH_ASM . "sbin/set_server_area add {$ipListID} {$regionIpArr[0]} {$regionIpArr[1]}";
            cutil_exec_wait($cmd);  //可以刷新桥模块配置
        }
        $cmd = PATH_ASM . "sbin/set_server_area add {$noipListID} 0.0.0.0 0.0.0.0";
        cutil_exec_wait($cmd);  //可以刷新桥模块配置
        sleep(2); //等待2秒后 再返回
        return $ipListID;
    }

    /**
     * 处理离线回话/设备 TODO 建议后面移植到nac-service
     * @param $deviceIds
     * @return bool
     */
    public static function offlineDevice($deviceIds)
    {
        if (empty($deviceIds)) {
            return false;
        }
        $deviceIds = is_array($deviceIds) ? $deviceIds : explode(',', $deviceIds);
        $list = RelationComputerModel::getList(['DeviceIds' => $deviceIds, 'column' => 'authId']);
        if (empty($list)) {
            self::log("device not exist: " . json_encode($deviceIds));
            return false;
        }
        $authIds = [];
        foreach ($list as $row) {
            $authIds[] = $row['LastAuthID'];
        }
        NacAuthLogModel::updatePatch(['RIDs' => $authIds], ['OffLineTime' => 'now()']);
        NacOnLineDeviceModel::delete(['DeviceIds' => $deviceIds]);
        $events = [];
        foreach ($deviceIds as $deviceId) {
            $events[] = ['DeviceID' => $deviceId, 'EventType' => 'UserSessionTimeout', 'Source' => 'AsmCacheKickUser'];
        }
        DeviceCycleEventModel::insertPatch($events);
        lib_redis::mDel('DeviceID_', $deviceIds, 'TNacOnLineDevice.LastHeartTime');
        return true;
    }

}
