<?php
/**
 * Description: 客户端相关：安装文件生成
 * User: <EMAIL>
 * Date: 2024/12/20 15:53
 * Version: $Id: NacClientServiceProvider.php 153494 2021-08-19 01:25:07Z duanyc $
 */


class NacClientServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'NacClient';

    /**
     * 生成客户端安装文件
     * @param $params
     * @throws Exception
     */
    public static function makeClientInstallFile($params)
    {
        $agentPromotion = $params['agentPromotion'];
        $winClientNameStr = $params['winClientNameStr'];
        $protocol = $params['protocol'];
        $serverIp = $params['serverIp'];
        $serverPort = $params['serverPort'];
        $SpecialInstall = 0;
        if (file_exists(PATH_HTML . "/download/clientzip/IsSetup_Agent.zip.exe")) {
            $SpecialInstall = 1;
        }
        self::initPushClientInstallFile($agentPromotion, $winClientNameStr);
        // 但是如果升级过程或者其他情况下特殊安装包丢失的情况，恢复到正常下载包,将此判断提前，否则如果正常包也不存在的情况无法生成正常软连
        if (!empty($SpecialInstall) && !file_exists(PATH_HTML . "/download/IsSetup_Special_Agent.exe")) {
            // 如果发现特殊安装包不存在，则需要把对应的已经生成的特殊安装包软链接删除，否则会在入网界面点击安装下载对应小助手会报错误
            unset($SpecialInstall);
            unlink(PATH_HTML . "/download/clientzip/{$winClientNameStr}_IsSetup_Agent.zip.exe");
            unlink(PATH_HTML . "/download/clientzip/{$winClientNameStr}_IsSetup_Agent.zip.exe.xml");
        }
        //判断是否是特殊安装包安装
        if (!empty($SpecialInstall)) {
            $IP_Agent_Name = "clientzip/{$winClientNameStr}_IsSetup_Agent.zip.exe";
            $IP_IsSetup_Agent_URL = PATH_HTML . "/download/{$IP_Agent_Name}";
            $IsSetup_Agent_URL = PATH_HTML . "/download/IsSetup_Special_Agent.exe";
        } else {
            $IP_Agent_Name = "{$winClientNameStr}_IsSetup_Agent.exe";
            $IP_IsSetup_Agent_URL = PATH_HTML . "/download/{$IP_Agent_Name}";
            $IsSetup_Agent_URL = PATH_HTML . "/download/IsSetup_Agent.exe";
        }

        // bugId: 15309 1698R001点击小助手特殊安装包生成之后升级至3746LTS02sp1，入网认证点击下载小助手跳转至身份认证页面
        if (!file_exists($IP_IsSetup_Agent_URL) && file_exists($IsSetup_Agent_URL)) {
            cutil_exec_wait("ln -s $IsSetup_Agent_URL  $IP_IsSetup_Agent_URL");
            cutil_exec_wait(PATH_HTML . "/download/MakeMd5Xml.sh {$IP_Agent_Name}");
        }

        $oldWidgetNameArr = [
            'IsSetup_Widget.exe',
            'IsSetup_Agent.exe',
        ];
        foreach ($oldWidgetNameArr as $agentName) {
            self::createClientInstallFile($serverIp . '_' . $serverPort . '_' . $protocol, $agentName);
        }

        $agentNameArr = [
            'IsaHelpLinuxAgent_uos_aarch64.deb',
            'IsaHelpLinuxAgent_uos_x86_64.deb',
            'IsaHelpLinuxAgent_uos_mips64.deb',
            'IsaHelpLinuxAgent_uos_loongarch64.deb',
            'IsaHelpLinuxAgent_Kylin_aarch64.deb',
            'IsaHelpLinuxAgent_Kylin_x86_64.deb',
            'IsaHelpLinuxAgent_Kylin_mips64.deb',
            'IsaHelpLinuxAgent_Kylin_loongarch64.deb'
        ];
        foreach ($agentNameArr as $agentName) {
            self::createClientInstallFile($winClientNameStr, $agentName);

            // bugID:4620,R001客户端升级依赖此老的文件名称
            self::createClientInstallFile($serverIp, $agentName);
            self::createClientInstallFile($serverIp, str_replace('IsaHelpLinuxAgent', 'IsaHelpLinuxAgent_Dot1x', $agentName), $agentName);
        }
    }

    /**
     * 复制客户端推送安装包客户端/控件安装文件
     *
     * @param $agentPromotion
     * @param $winClientNameStr
     *
     * @throws Exception
     */
    private static function initPushClientInstallFile($agentPromotion, $winClientNameStr): void
    {
        // 客户端推送
        if (!empty($agentPromotion)) {
            $IP_IsSetup_Agent_URL = PATH_HTML . "/download/clientPush/{$winClientNameStr}_IsSetup_Agent_Engine.exe";
            $IsSetup_Agent_URL = PATH_HTML . "/download/IsSetup_Agent_Engine.exe";
            if (!file_exists($IP_IsSetup_Agent_URL)) {
                cutil_exec_wait("ln -s $IsSetup_Agent_URL  $IP_IsSetup_Agent_URL");
                cutil_exec_wait(PATH_HTML . "/download/MakeMd5Xml.sh clientPush/{$winClientNameStr}_IsSetup_Agent_Engine.exe");
            }
        }
    }

    /**
     * 创建安装包文件的软链接
     *
     * @param $serverip   string 前缀
     * @param $agentName  string 软链名
     * @param $sourceFile string 源文件
     * @throws Exception
     */
    private static function createClientInstallFile($serverip, $agentName, $sourceFile = '')
    {
        $sourceFile = $sourceFile ?: $agentName;
        $IsSetup_URL = PATH_HTML . "/download/" . $sourceFile;
        $agent_name = $serverip . "_" . $agentName;
        $IP_IsSetup_URL = PATH_HTML . "/download/" . $agent_name;
        if (!file_exists($IP_IsSetup_URL) && file_exists($IsSetup_URL)) {
            cutil_exec_wait("ln -s $IsSetup_URL $IP_IsSetup_URL");
            cutil_exec_wait(PATH_HTML . "/download/MakeMd5Xml.sh " . $agent_name);
        }
    }

    /**
     * 判断是否存在客户端软软链接，特殊安装包和普通安装包只要有一个存在就返回true
     * @param $winClientNameStr
     * @return bool
     */
    private static function isExistClientInstallLink($winClientNameStr, $specialInstall)
    {
        if (!$specialInstall) {
            if (file_exists(PATH_HTML . "/download/{$winClientNameStr}_IsSetup_Agent.exe")) {
                return true;
            }
        } else {
            if (file_exists(PATH_HTML . "/download/clientzip/{$winClientNameStr}_IsSetup_Agent.zip.exe")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成客户端安装文件
     * @param $data
     */
    public static function noticeMakeClientInstallFile($data)
    {
        $key = md5(json_encode($data));
        $cache = MessageRedis::getOne(0, $key);
        if (!empty($cache)) {
            if (!self::isExistClientInstallLink($data['winClientNameStr'], $data['specialInstall'])) {
                cutil_php_log('存在小助手客户端软连接的redis信息，但是实际上没有安装文件，可能是软连接没生成或者被删除: ' . var_export($data, true), self::$logFile);
                self::makeClientInstallFile($data);
            }
            return;
        }

        $data['run_func'] = 'makeClientInstallFile';
        lib_queue::addJob('COMMON_RUN', $data);
        MessageRedis::setOne(0, $key, json_encode($data));
    }
}
