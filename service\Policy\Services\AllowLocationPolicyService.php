<?php
/**
 * Description: 零信任指定位置策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;


use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowLocationPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     * @return bool
     */
    public function check(): bool
    {
        $ip = $this->getClientIp(true);
        $aCity = \lib_ipregion::memorySearch($ip);
        if (isset($aCity['region'])) {
            $arr = explode("|", $aCity['region']);
            $city = isset($arr[3]) && $arr[3] != "0" ? $arr[3] : "";
            $providers = isset($arr[4]) && $arr[4] != "0" ? $arr[4] : "";
            if ($providers == '内网IP') {
                return true;
            }
            foreach ($this->params['Config'] as $value) {
                if ($value == $city) {
                    return true;
                }
            }
        }
        return false;
    }
}
