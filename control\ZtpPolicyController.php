<?php
/**
 * Description: 零信任策略
 * User: <EMAIL>
 * Date: 2022/05/05 23:32
 * Version: $Id$
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";
use Services\Common\Services\DESService;

class ZtpPolicyController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['resourceCheck' => true];

    /**
     * 资源策略检查 原交易：resClient
     * @throws Exception
     */
    public function resourceCheck()
    {
        try {
            $access_token = request('access_token', 'request');
            $url_str = DESService::desEcbDecrypt(stringDecode($access_token));
            parse_str($url_str, $data);
            $ResID = (int)($data['resId'] ?? 0);
            $Client = (int)($data['client'] ?? 0);
            $Token = $data['token'] ?? '';
            $AddAuth = $data['addAuth'] ?? '';
            $OsType = $data['os_type'] ?? '';
            $ClickID = $data['ClickID'] ?? '';
            //独立运行远程应用处理
            $winRunRemote = (int)($data['winRunRemote'] ?? 0);
            if ($winRunRemote) {
                define('FORBID_GOTO_URL', 1);
            }
            $Session = LoginServiceProvider::getSessionInfo($Token, 'policy');
            if (empty($Session)) {
                PolicyServiceProvider::log("Error Session: {$Token}");
                return gotoErrorUrl("0x1012");
            }
            $Session['OsType'] = $OsType;
            $Session['Client'] = $Client;
            $Resource = ResourceServiceProvider::getResource($ResID, 'agent');
            if (empty($Resource)) {
                PolicyServiceProvider::log("Error Resource: {$ResID}");
                return gotoErrorUrl("0x1015");
            }
            // 代理处理
            $Resource['AddAuth'] = $AddAuth;
            $Resource['ClickID'] = $ClickID;
            AgentServiceProvider::agentHandle($Session, $Resource);
            PolicyServiceProvider::log("User Access Resource: {$Session['Uuid']}：{$ResID}");
        } catch (Exception $e) {
            $DeviceID = $Session['DeviceID'] ?? '';
            $message = $e->getMessage();
            PolicyServiceProvider::log("Error: " . $message);
            LoginServiceProvider::setUserErrMessage("0x0018", $message, $DeviceID);
            AgentServiceProvider::agentHandleFail($Session, $Resource);
            return gotoErrorUrl("0x0018", $DeviceID, $OsType);
        }
    }

    /**
     * 上报策略检查结果
     * @throws Exception
     */
    public function checkResult(): array
    {
        $_SERVER['HTTP_WEB_TOKEN'] = request('Token', 'request');
        $DeviceID = request('DeviceID', 'request', 0, 'int');
        $ItemID = request('ItemID', 'request', 0, 'int');
        $CheckResult = request('CheckResult', 'request');
        $CheckResult = json_decode(gbkToUtf8(stripslashes($CheckResult)), true);
        hlp_check::checkEmpty($ItemID);
        hlp_check::checkEmpty($DeviceID);
        hlp_check::checkEmpty($CheckResult);
        $Session = LoginServiceProvider::checkLogin($DeviceID);
        try {
            PolicyServiceProvider::reportPolicyResult($Session['Token'], $ItemID, $CheckResult);
        } catch (Exception $e) {
            PolicyServiceProvider::log("checkResult Error: " . $e->getMessage());
        }
        return [];
    }

    /**
     * 客户端获取IP资源列表
     * @throws Exception
     */
    public function getIpResList(): array
    {
        $_SERVER['HTTP_WEB_TOKEN'] = request('Token', 'request');
        $DeviceID = request('DeviceID', 'request', 0, 'int');
        hlp_check::checkEmpty($DeviceID);
        $Session = LoginServiceProvider::checkLogin($DeviceID);
        $list = PolicyServiceProvider::getIpResList($Session['Token']);
        return ['list' => $list];
    }
}
