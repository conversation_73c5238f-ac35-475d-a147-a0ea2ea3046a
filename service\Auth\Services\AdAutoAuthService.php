<?php
/**
 * Description: AD域单点登录认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: AdAutoAuthService.php 169023 2022-02-18 01:08:20Z huyf $
 */

namespace Services\Auth\Services;

use Exception;
use Services\Auth\Interfaces\AuthServiceInterface;

class AdAutoAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['userName'] = addslashes(Base64DeExt($this->params['userName']));
        $this->params['domainName'] = Base64DeExt($this->params['domainName']);

        if (empty($this->params['userName'])) {
            T(21100002);
        }

        $this->params['authType'] = 'ADAutoLogin';
        return $this->params;
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'user_name', 'domain_name', 'fakeIsInDomain'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'userName', 'domainName'];
    }
}
