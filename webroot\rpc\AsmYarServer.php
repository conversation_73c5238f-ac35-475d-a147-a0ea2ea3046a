<?php
/**
 * Description: yar父类
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: AsmYarServer.php 157495 2021-09-22 15:16:46Z renchen $
 */

//abstract class
//protected required for subclass
abstract class AsmYarServer
{
    /**
     * 指定服务
     * @var string
     */
    protected $_server = '';
    /**
     * 密钥
     * @var mixed
     */
    private $_app_key;
    private $_sign;
    private $_time;
    private $_signType;

    /**
     * rpc控制层日志
     * @var string
     */
    public static $logFile = 'yar_server';

    /**
     * cls_rpc constructor.
     */
    public function __construct()
    {
        $this->_app_key = $GLOBALS['CONFIG']['yar_server'][$this->_server]['key'];
        $this->_sign = request('sign', 'request');
        $this->_time = request('time', 'request', 0, 'int');
        $this->_signType = request('signType', 'request', 0, 'int');
    }

    /**
     * 根据参数生成签名
     * @param array $rpc_params
     * @return string
     */
    private function _makeSign($rpc_params)
    {
        $values = array_values($rpc_params);
        $val = ($this->_signType === 1) ? json_encode($values[0]) : serialize($values);
        $str = sha1($this->_time . $val . $this->_time);
        $key = sha1($this->_time . sha1(sha1($this->_time . $this->_app_key) . $this->_time) . $this->_time);
        $sign = '';
        for ($i = 0; $i < 40; $i++) {
            $sign .= $str[$i] . $key[39 - $i];
        }
        return substr(sha1(strrev(sha1($sign))), 5, 32);
    }

    /**
     * 检查参数签名
     * 在rpc的url通过get方式或调用方法的前2个参数传递校验参数
     * @param array $args
     * @return void
     * @throws Exception
     */
    private function _checkSign(&$args)
    {
        if (empty($this->_sign) && empty($this->_time)) {
            $this->_sign = isset($args[0]) && is_string($args[0]) ? $args[0] : '';
            $this->_time = isset($args[1]) && is_numeric($args[1]) ? (int)$args[1] : 0;
            $args = array_slice($args, 2);
        }
        $sign = $this->_makeSign($args);
        if (empty($this->_sign) || empty($this->_time) || $this->_sign !== $sign) {
            $this->log([$args, $this->_sign, $this->_time, $sign, $this->_app_key]);
            header('HTTP/1.1 403 File Not Exist', true, 403);
            json_print(['errcode' => '403', 'errmsg' => 'Yar File Not Exist']);
        }
    }

    /**
     * 检查数据签名并调用相应方法
     * @param string $fun
     * @param array $args
     * @return bool|mixed
     * @throws Exception
     */
    public function __call(string $fun, array $args)
    {
        $return = array('state' => true, 'message' => '', 'code' => 0);
        try {
            $this->_checkSign($args);
            $this->log([$fun, $args]);
            $result = call_user_func_array(array($this, $fun), $args);
            $return['message'] = $result['message'] ?? '';
            $return['code'] = $result['code'] ?? 0;
            $return['data'] = $result['data'] ?? $result;
        } catch (Exception $e) {
            $return['state'] = false;
            $return['message'] = $e->getMessage();
            $return['code'] = $e->getCode();
            $previous = $e->getPrevious();
            if ($previous) {
                $return['allMessage'] = $previous->getMessage();
            }
        }

        return $return;
    }

    /**
     * 写日志.
     *
     * @param string|array $content 日志内容
     * @param string $logType 日志级别
     *                        "DEBUG" ‐ debug信息、细粒度信息事件
     *                        "INFO" ‐ 重要事件、强调应用程序的运行过程
     *                        "NOTICE" ‐ 一般重要性事件、执行过程中较INFO级别更为重要的信息
     *                        "WARNING" ‐ 出现了非错误性的异常信息、潜在异常信息、需要关注并且需要修复
     *                        "ERROR" ‐ 运行时出现的错误、不必要立即进行修复、不影响整个逻辑的运行、需要记录并做检测
     *                        "CRITICAL" ‐ 紧急情况、需要立刻进行修复、程序组件不可用
     *                        "ALERT" ‐ 必须立即采取行动的紧急事件、需要立即通知相关人员紧急修复
     *                        "EMERGENCY" ‐ 系统不可用
     */
    protected function log($content, $logType = 'INFO'): void
    {
        cutil_php_log($content, static::$logFile, $logType);
    }
}
