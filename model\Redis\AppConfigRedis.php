<?php

/**
 * Description: 网关配置
 * User: <EMAIL>
 * Date: 2022/05/27 10:32
 * Version: $Id$
 */

class AppConfigRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'appConfigs';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'appnum,appcfg,terminalcfg,noauthappcfg';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['appnum'],
        'appcfg' => ['appcfg'],
        'terminalcfg' => ['terminalcfg'],
        'noauthappcfg' => ['noauthappcfg']
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['appcfg' => true, 'noauthappcfg' => true, 'terminalcfg' => true];

    /**
     * 发布内容定义
     * @var string
     */
    protected static $publish = '';

    /**
     * 单条
     *
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($column = 'one')
    {
        return self::get($column);
    }

    /**
     * 修改
     *
     * @param array $key_values
     * @param array $keys
     *
     * @return boolean
     */
    protected static function set($key_values = [], ...$keys)
    {
        if (empty($key_values)) {
            return false;
        }

        $expire = null;
        if (isset($key_values['LifeTime'])) {
            $expire = $key_values['LifeTime'];
        }
        $isVersion = false; // 是否更新版本号
        if (isset($key_values['Version'])) {
            $isVersion = true;
            unset($key_values['Version']);
        }
        $columns = explode(',', static::$localColumns);
        $vals = [];
        foreach ($columns as $column) {
            if (isset($key_values[$column])) {
                $vals[$column] = isset(static::$jsonColumns[$column]) ?
                    json_encode($key_values[$column]) : (string)$key_values[$column];
            }
        }
        $key = static::getKey($keys);
        // 过滤掉不变的数据
        $fields = array_keys($vals);
        if ($isVersion) {
            $fields[] = 'Version';
        }
        $oldData = lib_redis::getHash(static::PREFIX, $key, $fields);
        if (!empty($oldData)) {
            foreach ($vals as $column => $val) {
                if (isset($oldData[$column]) && $column !== 'LifeTime' && $val === $oldData[$column]) {
                    unset($vals[$column]);
                }
            }
        }
        if (empty($vals)) {
            return false;
        }
        if ($isVersion) {
            $vals['Version'] = !empty($oldData['Version']) ? $oldData['Version'] + 1 : 1;
        }
        $result = lib_redis::hMSetEx(static::PREFIX . $key, $vals, $expire);
        if ($result) {
            if (!empty(static::$publish)) {
                cutil_php_log(['publish', static::PREFIX . static::TABLE_NAME, static::$publish], "model_" . static::TABLE_NAME);
                lib_redis::publish(static::PREFIX . static::TABLE_NAME, static::$publish);
            } else {
                $publishData = lib_redis::getHash(static::PREFIX, $key, null);
                cutil_php_log(['publish', $publishData], "model_" . static::TABLE_NAME);
                lib_redis::publish(self::PREFIX . self::TABLE_NAME, json_encode($publishData));
            }
        }
        return $result;
    }

    /**
     * 单条
     *
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($data)
    {
        $data['Version'] = true; // 表示更新版本号
        return self::set($data);
    }
}
