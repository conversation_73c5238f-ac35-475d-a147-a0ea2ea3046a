<?php

/**
 * Description: 规范TNacPolicyList表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: NacPolicyListModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class NacPolicyListModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacPolicyList';
    public const PRIMARY_KEY = 'PolicyID';
    protected static $columns = [
        'one' => 'PolicyID',
        'info' => 'PolicyID,ChangeTime,InsertTime,PolicyBody, State, PolicyTradecode',
        'policy' => 'PolicyID, Type, PolicyName, PolicyBody, PolicyObject, PolicyTradecode, ChangeTime, ChangeUser, ' .
              ' State, StartDate, EndDate, CreateUser, TargetTime, IsPrivate, Remark, InsertTime',
        'senior' => "JSON_UNQUOTE(PolicyConfig->'$.AdvancedConfig.Ctrl.IsPrompt') as IsPrompt ,JSON_UNQUOTE(PolicyConfig->'$.AdvancedConfig.Ctrl.FastAntiVirus') as FastAntiVirus ,JSON_UNQUOTE(PolicyConfig->'$.AdvancedConfig.Ctrl.FastCheck') as FastCheck ,JSON_UNQUOTE(PolicyConfig->'$.AdvancedConfig.Ctrl.SafetyMonitor') as SafetyMonitor",
        'senior_kdb' => "PolicyConfig::json->'$.AdvancedConfig'->'$.Ctrl'->>'$.IsPrompt' AS IsPrompt,PolicyConfig::json->'$.AdvancedConfig'->'$.Ctrl'->>'$.FastAntiVirus' AS FastAntiVirus,PolicyConfig::json->'$.AdvancedConfig'->'$.Ctrl'->>'$.FastCheck' AS FastCheck,PolicyConfig::json->'$.AdvancedConfig'->'$.Ctrl'->>'$.SafetyMonitor' AS SafetyMonitor",

        'ztp' => 'PolicyConfig,PolicyBody,PolicyObject,State',
        'item' => 'PolicyID,ItemID,ChangeTime,InsertTime',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['PolicyID'])) {
            $where .= "AND PolicyID = ".self::setData($cond['PolicyID']);
        }

        if (isset($cond['ItemID'])) {
            $where .= "AND ItemID = ".self::setData($cond['ItemID']);
        }

        if (isset($cond['PolicyType'])) {
            $where .= "AND PolicyType = ".self::setData($cond['PolicyType']);
        }

        if (!empty($cond['InPolicyID'])) {
            $where .= "AND PolicyID IN (" . self::setArrayData($cond['InPolicyID']) . ")";
        }

        if (isset($cond['State'])) {
            $where .= "AND State = ".self::setData($cond['State']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
