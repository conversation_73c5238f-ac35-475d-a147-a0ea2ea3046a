<?php

/**
 * Description: TPrivateSet表
 * User: <EMAIL>
 * Date: 2021/06/18 10:02
 * Version: $Id: PrivateSetModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class PrivateSetModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TPrivateSet';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'info' => 'ProjectMod, isShow'
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }

        if (isset($cond['isShow'])) {
            $where .= "AND isShow = ".self::setData($cond['isShow']);
        }

        if (isset($cond['ProjectMod'])) {
            $where .= "AND ProjectMod = ".self::setData($cond['ProjectMod']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
