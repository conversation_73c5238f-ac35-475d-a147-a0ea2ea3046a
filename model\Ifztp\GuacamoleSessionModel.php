<?php

/**
 * Description: guacamole会话
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: ResCollectModel.php 174776 2022-04-28 09:18:26Z duanyc $
 */

class GuacamoleSessionModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGuacamoleSession';
    public const PRIMARY_KEY = 'GuacSessID';
    protected static $columns = [
        '*'     => '*',
        'heart' => 'GuacSessID,DateStart',
        'list'  => 'UserID,Token,ResID,GuacSessID,IsFinished,TunnelID,Protocol,DesAddr,DesPort,ResTag',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['GuacSessID'])) {
            $where .= "AND GuacSessID = ".self::setData($cond['GuacSessID']);
        }

        if (isset($cond['TunnelID'])) {
            $where .= "AND TunnelID = ".self::setData($cond['TunnelID']);
        }

        if (isset($cond['InTunnelID'])) {
            $where .= "AND TunnelID IN (" . self::setArrayData($cond['InTunnelID']) . ")";
        }

        if (isset($cond['IsFinished'])) {
            $where .= "AND IsFinished = ".self::setData($cond['IsFinished']);
        }

        if (isset($cond['LtUpdateTime'])) {
            $where .= "AND UpdateTime < ".self::setData($cond['LtUpdateTime']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
