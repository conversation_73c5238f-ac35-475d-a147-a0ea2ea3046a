<?php
/**
 * Description: 补丁相关操作
 * User: renchen
 * Date: 2021/8/3 21:51
 * Version: $Id: PatchService.php 157413 2021-09-22 08:11:28Z renchen $.
 */

namespace Services\Patch\Services;

use Services\Common\Services\CommonService;

class PatchService extends CommonService
{
    public function __construct($params)
    {
        parent::__construct();
        $this->logFileName = '/tmp/logs/Patch';
    }

    /**
     * 获取补丁服务器的IP和端口.
     * ps: 原：get_patchserver_ipport.
     */
    public function getPatchServerInfo(): array
    {
        try {
            $devType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
            if ('dasc' === $devType) {
                if (file_exists(PATH_ETC . 'ascpatch.ini')) {
                    $patchArr = read_inifile(PATH_ETC . 'ascpatch.ini');
                    if ('Server' === $patchArr['repairfrom']) {
                        $this->return['data']['IP'] = $patchArr['server_ip'];
                        $this->return['data']['Port'] = $patchArr['server_port'];
                        $this->return['data']['ServerStatus'] = 1;
                    }
                }
            } else {
                $result = \DictModel::getList(['Type' => 'PatchUpdate', 'ConditionPatchServer' => true, 'column' => 'one']);
                foreach ($result as $item) {
                    if (1 !== (int) $item['ItemValue'] && strtolower($item['ItemName']) === strtolower('ServerStatus')) {
                        $this->return['data'] = [];

                        break;
                    }
                    if (strtolower($item['ItemName']) === strtolower('PatchServerIP')) {
                        $this->return['data']['IP'] = $item['ItemValue'];
                    }
                    if (strtolower($item['ItemName']) === strtolower('PatchServerPort')) {
                        $this->return['data']['Port'] = $item['ItemValue'];
                    }
                    if (strtolower($item['ItemName']) === strtolower('ServerStatus')) {
                        $this->return['data']['ServerStatus'] = $item['ItemValue'];
                    }
                }
            }
        } catch (\Exception $e) {
            $this->recordErrorMessage($e);
        }

        return $this->return;
    }

    /**
     * 获取最后的下载补丁
     * ps: 对应老交易 make_belong_times(BelongTimes).
     *
     * @param int $deviceID
     * @return array
     */
    public function getBelongTimes(int $deviceID):array
    {
        try {
            $result = \UpdateInstallLogModel::getSingle(['DeviceID' => $deviceID, 'column' => 'maxID']);
            $lastID = $result['LastID'] ?? 0;
            $this->return['data'] = $deviceID.'_'.($lastID['LastID'] + 1);
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, '设备ID'.$deviceID);
        }

        return $this->return;
    }

    /**
     * 获取补丁详情
     * ps：对应老交易 get_patch_info
     * @param $params
     * @return array
     */
    public function getPathDetail($params):array
    {
        try {
            if ($params['isLinuxPatch'] !== 1) {
                $result = \UpdateDetailModel::getDetail($params);
            } else {
                $result = \LinuxPatchBaseModel::getDetail($params);
            }
            $map = [
                1 => L(21100024),
                2 => L(21100025),
                3 => L(21100026),
            ];
            foreach ($result as $key => &$value) {
                if ($params['isLinuxPatch'] === 1) {
                    $value['UpdateType'] = $map[(int)$value['UpdateType']] ?? '--';
                }
                $value = str_replace(array("\0", "\r", "\n", "\r\n"), array("", "<br/>", "<br/>", "<br/>"), $value);
            }
            unset($value);
            $this->return['data'] = $result;

        } catch (\Exception $e) {
            $this->recordErrorMessage($e, '相关参数：'.var_export($params,true));
        }

        return $this->return;
    }

    /**
     * 插入补丁安装记录.
     * ps: 对应老交易：update_install_log
     * @param $params array
     * @return array
     */
    public function insertInstallLog(array $params):array
    {
        try {
            $this->return['data'] = \UpdateInstallLogModel::insert([
                'DeviceID' => $params['DeviceID'],
                'BelongTimes' => $params['BelongTimes'],
                'RevisionID' => $params['InstallResult']
            ]);
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, '相关参数：'.var_export($params,true));
        }

        return $this->return;
    }

    /**
     * 获取指定设备需要安装的补丁信息
     * ps: 对应老交易：get_device_repair_patch
     * @param $params array
     * @return array
     */
    public function deviceRepairPatch(array $params): array
    {
        try {
            $ServerAddr = getServerAddr();
            $ServerPort = getHttpPort();
            $devType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
            if ($devType === 'dasc') {
                if (file_exists(PATH_ETC . 'ascpatch.ini')) {
                    $patchArr = read_inifile(PATH_ETC . 'ascpatch.ini');
                    if ($patchArr['repairfrom'] === 'Server') {
                        $ServerAddr = $patchArr['server_ip'];
                        $ServerPort = $patchArr['server_port'];
                    }
                }
            } else {
                $AscDownload = \ConfigServiceProvider::getDictOne('PatchUpdate', 'AscDownload');
                $ServerStatus = \ConfigServiceProvider::getDictOne('PatchUpdate', 'ServerStatus');
                if ((int)$AscDownload === 1) {
                    $device = \DeviceModel::getOne($params['deviceid'], 'asc');
                    if (is_array($device)) {
                        $aResult = \DevASCInfoModel::getSingle(['AscID' => $device['AscID'], 'column' => 'info']);
                        if ($aResult && $aResult['IP'] !== '127.0.0.1') {
                            //用于asc下发补丁,不用TDevASCInfo.AscPortASC监听的端口
                            $ServerAddr = $aResult['IP'];
                            $ServerPort = $aResult['AscPort'];
                        }
                    }
                } else if ((int)$ServerStatus === 1) {
                    $ServerAddr = \ConfigServiceProvider::getDictOne('PatchUpdate', 'PatchServerIP');
                    $ServerPort = \ConfigServiceProvider::getDictOne('PatchUpdate', 'PatchServerPort');
                }
            }

            if (OS_TYPE === OSTYPE_LINUX) {
                $aGetInfo = \DevicePatchCheck::getLinuxDevicePatchCheck(["DeviceID" => $params['deviceid']]);
            } else {
                $aGetInfo = \DevicePatchCheck::getDevicePatchCheck(["DeviceID" => $params['deviceid']]);
            }

            $return = [];
            $return['PatchDirOnServer'] = HTTP_PROTOCOL.$ServerAddr.":".$ServerPort."/download/patch/";
            foreach ($aGetInfo as $item) {
                $UpdateList =[];
                $UpdateList['RevisionId'] = $item['RevisionID'];
                $UpdateList['KB'] = $item['KBIDS'];
                $UpdateList['DownloadUrl'] = $item["DownURL"];
                $UpdateList['Args'] = $item['InstallCommand_Arguments'];
                $return['UpdateList']['Update'][] = $UpdateList;
            }
            $this->return['data'] = $return;
        } catch (\Exception $e) {
            $this->recordErrorMessage($e);
        }
        return $this->return;
    }
}
