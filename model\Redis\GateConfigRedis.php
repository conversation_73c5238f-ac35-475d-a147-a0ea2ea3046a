<?php

/**
 * Description: 网关配置 读数据
 * User: <EMAIL>
 * Date: 2022/06/06 10:32
 * Version: $Id$
 */

class GateConfigRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'gateConfig';

    /**
     * 本地网关和控制中心共用数据的字段
     * TerminalHeartbeat: RDP的心跳间隔
     * ExpireTime：
     * RsaPrivateKeyPkcs8Der：
     * MyiP：
     * Gateways：
     * RdpPort：
     * AddJsUrl：
     * RsaPublicKey：
     * RdpAddr：
     * ErrorUrl：
     * ErrorUrlIn：
     * ClipboardPaste：
     * RsaPrivateKey：
     * ClipboardCopy：
     * Version：版本号
     * @var string
     */
    protected static $localColumns = 'AccessAddress,AccessPort,GwID,MyiP,RdpAddr,RdpPort,ErrorUrl,ErrorUrlIn,RouteCmd,'.
                                     'AddJsUrl,RsaPrivateKey,ControlUrl,ControlUrlIn,State,IsEnable,AsmRootPath,'.
                                     'RsaPublicKey,RsaPrivateKeyPkcs8Der,Gateways,ExpireTime,ClipboardCopy,ClipboardPaste,TerminalHeartbeat,Watermark,GwVirState,disVirIp,RecoveVirIp,RecoveHours,ProxyPort,PriorityProx,VirtualIP,InternalIPs';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'    => ['AccessAddress', 'AccessPort' ,'VirtualIP'],
        'control'    => ['ControlUrl', 'ControlUrlIn'],
        'gateway'    => ['RouteCmd','ProxyPort','VirtualIP'],
        'watermark'    => ['Watermark'],
        'err'    => ['ErrorUrl'],
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['Gateways' => true];

    /**
     * 单条
     *
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($column = 'one')
    {
        return self::get($column);
    }

    /**
     * 修改
     *
     * @param array $key_values
     * @param array $keys
     *
     * @return boolean
     */
    protected static function set($key_values = [], ...$keys)
    {
        if (empty($key_values)) {
            return false;
        }

        $expire = null;
        if (isset($key_values['LifeTime'])) {
            $expire = $key_values['LifeTime'];
        }
        $isVersion = false; // 是否更新版本号
        if (isset($key_values['Version'])) {
            $isVersion = true;
            unset($key_values['Version']);
        }
        $columns = explode(',', static::$localColumns);
        $vals = [];
        foreach ($columns as $column) {
            if (isset($key_values[$column])) {
                $vals[$column] = isset(static::$jsonColumns[$column]) ?
                    json_encode($key_values[$column]) : (string)$key_values[$column];
            }
        }
        $key = static::getKey($keys);
        // 过滤掉不变的数据
        $fields = array_keys($vals);
        if ($isVersion) {
            $fields[] = 'Version';
        }
        $oldData = lib_redis::getHash(static::PREFIX, $key, $fields);
        if (!empty($oldData)) {
            foreach ($vals as $column => $val) {
                if (isset($oldData[$column]) && $column !== 'LifeTime' && $val === $oldData[$column]) {
                    unset($vals[$column]);
                }
            }
        }
        if (empty($vals)) {
            return false;
        }
        if ($isVersion) {
            $vals['Version'] = !empty($oldData['Version']) ? $oldData['Version'] + 1 : 1;
        }
        $result = lib_redis::hMSetEx(static::PREFIX . $key, $vals, $expire);
        if ($result) {
            $publishData = lib_redis::getHash(static::PREFIX, $key, null);
            cutil_php_log(['publish', $publishData], "model_" . static::TABLE_NAME);
            lib_redis::publish(self::PREFIX . self::TABLE_NAME, json_encode($publishData));
        }
        return $result;
    }

    /**
     * 单条
     *
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($data)
    {
        $data['Version'] = true; // 表示更新版本号
        return self::set($data);
    }

    /**
     * 是否改变
     *
     * @param array $params
     * @param array $oldParams
     * @param string $column
     *
     * @return mixed
     */
    public static function isChange($params, $oldParams, $column)
    {
        if (isset($params[$column])) {
            if (!isset($oldParams[$column]) || $oldParams[$column] !== $params[$column]) {
                return true;
            }
        }
        return false;
    }
}
