<?php
/**
 * Description: 短信认证
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: SmsAuth.php 149450 2021-07-08 10:13:29Z duanyc $
 */

$GLOBALS['LANG'][21131] = [
    21131001 => '验证码不合法或已过期！',
    21131002 => '请输入正确的手机号码！',
    21131003 => '非网内手机号,请联系管理员!',
    21131004 => '短信发送过频！',
    21131005 => '手机号错误，非当前管理员手机号!',
    21131006 => '短信发送过频！',
    21131007 => '手机短信验证码（来宾码）[{pw}]由[{user}]自动生成，创建时间为 {nowTime}',
    21131008 => '"设备ID:{deviceid},用户ID:{userid},手机号:{phone},验证码:{code},操作时间:{time}"',
    21131009 => '您的短信验证码是',
    21131010 => '分钟内有效.',
    21131011 => '发送失败，账户关联手机号不正确，或短信服务异常，请联系管理员！',
    21131012 => '第三方短信网关地址有误，请联系管理员!',
    21131013 => '您的短信验证码是:',
    21131014 => '短信发送成功，请注意查收！',
    21131015 => '邮件发送成功，请注意查收！',
    21131016 => '手机验证码认证，自动生成用户[{user}]，创建时间为 {nowTime}',
    21131017 => '您的邮箱验证码是',
    21131018 => '非预约手机号禁止接入,请联系您的接待人!',
];
