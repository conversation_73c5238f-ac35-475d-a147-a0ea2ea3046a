<?php

/**
 * Description: 安检信息 原basic.class.php
 * User: <EMAIL>
 * Date: 2021/07/23 15:53
 * Version: $Id: xml_safe_check_policy.php 154440 2021-08-27 07:51:34Z duanyc $
 */


/**
 * ***************************************** 解析安检信息 **********************************************
 */
class xml_safe_check_policy extends cls_xml
{
    /**
     * 解析安检报文
     * @return array
     */
    public function parseXml()
    {
        if (is_array($this->res)) { //防止重复添加
            return $this->res;
        }
        foreach ($this->xml->CheckItems->Item as $item) {
            $aRow ['ItemId'] = $item->ItemID;
            $aRow['IsAutoRepair'] = $item->IsAutoRepair;
            $aRow ['Key'] = $item->Key;
            $aRow ['CheckPeriod'] = $item->CheckPeriod;
            $aRow ['ItemIndexOf'] = $item->ItemIndexOf;
            $aRow ['InsideName'] = ( string )$item->InsideName;
            $this->res ["ItemData"] [] = $aRow;
        }
        $this->res ['RepairConf'] = $this->xml->RepairConf;
        $this->convertEncode();
        return $this->res;
    }

    /**
     * 获取策略的所有关键检查项
     * @return array
     */
    public function getKeyItemID()
    {
        $aKey = [];
        $aPolidy = $this->parseXml();
        foreach ($aPolidy["ItemData"] as $row) {
            if ($row ['Key'] == "Yes") {
                $aKey [] = $row ['ItemId'];
            }
        }
        return $aKey;
    }

    /**
     * 获取自动修复项
     * @return array
     */
    public function getAutoRepairItemName()
    {
        $aPolidy = $this->parseXml();
        $AutoRepairItemName = array();
        foreach ($aPolidy ["ItemData"] as $row) {
            if ($row ['IsAutoRepair'] == "1") {
                $AutoRepairItemName [] = $row ['InsideName'];
            }
        }
        return $AutoRepairItemName;
    }
}
