<?php

/**
 * Description: dasm相关 ，必须为protected，父类__call方法会包装数据
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: DasmController.php 166749 2022-01-10 02:31:22Z duanyc $
 */

require PATH_ROOT . "/webroot/rpc/AsmYarServer.php";

class DasmController extends AsmYarServer
{
    /**
     * 指定服务
     * @var string
     */
    protected $_server = 'dasm';

    /**
     * 获取dasm信息，对应老交易 get_dasm_dev_info，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function setDevInfo($params)
    {
        if (empty($params['DeviceID'])) {
            T(21100002);
        }
        isset($params['ip']) ? hlp_check::checkIP($params['ip']) : false;
        isset($params['mac']) ? hlp_check::checkMAC($params['mac']) : false;
        $data = [];
        $data['DeviceID'] = $params['DeviceID'];
        if ($params['ip']) {
            $data['IP'] = $params['ip'];
        }
        if ($params['mac']) {
            $data['Mac'] = $params['mac'];
        }
        return DeviceServiceProvider::updateDevInfo($data);
    }

    /**
     * 获取dasm信息，对应老交易 get_dasm_dev_info，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function devInfo($params)
    {
        $dasmDevService = DeviceServiceProvider::initDasmDevService();

        return $dasmDevService->getDevInfoFromDasm($params);
    }

    /**
     * 从DASC上报信息ASM，ASM查询设备信息后返回， DASC再插入本地数据库，对应老交易 get_dasm_switch_info，必须为protected
     * ps: 查阅相关代码之后，未发现有调用的地方，暂时不重构 2021-08-16
     * @param array $params
     * @return array
     */
    protected function switchInfo(array $params): array
    {
        $dasmDevService = DeviceServiceProvider::initDasmDevService();

        return $dasmDevService->getSwitchInfoFromDasm($params);
    }

    /**
     * 从DASC上报信息ASM，ASM查询设备信息后返回， DASC再插入本地数据库，对应老交易 get_dasm_usb_info，必须为protected
     *
     * @param $params
     * @return mixed
     */
    protected function usbInfo($params)
    {
        $dasmDevService = DeviceServiceProvider::initDasmDevService();

        return $dasmDevService->getUsbInfoFromDasm($params);
    }

    /**
     * 获取用户绑定设备信息，对应老交易 get_dasmserver_info，必须为protected
     *
     * @param array $params
     * @return mixed
     * @throws Exception
     */
    protected function deviceBindUser(array $params)
    {
        $dasmDevService = DeviceServiceProvider::initDasmDevService();
        $params['deviceId'] = $dasmDevService->getDevOrigID($params);
        $params['servicePrefix'] = 'Base';
        $authService = AuthServiceProvider::initAuthService($params);
        $authService->checkDeviceBindUser($params['authType'], $params['userID']);
        return [];
    }

    /**
     * 检查是否指纹违规.
     * ps: 对应老交易：get_dasmserver_info 中 action 为：FingerIllegal
     * @param array $params
     * @return array
     */
    protected function checkFingerIllegal(array $params): array
    {
        $dasmDevService = DeviceServiceProvider::initDasmDevService();
        $result = $dasmDevService->checkFingerIllegal($params);
        if ($result['code'] === 1 && $result['data']['count']) {
            return array('status' => 'n', 'info' => 'not ok');
        }
        return array('status' => 'y', 'info' => 'ok');
    }

    /**
     * 更新上报来宾自助申请的
     * @param array $params
     * @return array
     * @throws Exception
     */
    protected function updateValidTime(array $params): array
    {
        $params['servicePrefix'] = 'SelfGuest';
        $selfService = AuthServiceProvider::initAuthService($params);
        $res = $selfService->updateValidTime(true);
        return ['res' => $res];
    }

    /**
     * 启用/暂停 客户端分发功能
     * @param array $params
     * @return false|int
     * @throws Exception
     */
    protected function toggleClientProxy(array $params)
    {
        if ($params['switch'] === true) {
            $conf = "proxy_pass {$params['url']};";
        } else {
            $conf = "default_type    text/plain;" . PHP_EOL . "return 403 '当前未启用外置服务器下载客户端功能'; ";
        }
        $content = <<<EOF
location ^~ /client/ {
     $conf
}
location ^~ /backend/api/clientDistribute {
     proxy_pass {$params['host']}/backend/api/clientDistribute;
}
location ^~ /backend/storage/ {
     proxy_pass {$params['host']}/backend/storage/;
}
EOF;
        $path = PATH_ETC . 'nginx/module/client.conf';
        cutil_exec_wait("chmod 777 ".$path);
        $result = file_put_contents($path, $content);
        $cmd = PATH_USR . 'sbin/nginx -s reload';
        cutil_exec_no_wait($cmd);
        return $result;
    }
}
