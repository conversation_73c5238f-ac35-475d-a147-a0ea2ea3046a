<?php
/**
 * Description: 远程应用
 */

class RemoteServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'remote';

    /**
     * 通过用户ID获取远程应用用户数据
     *
     * @param $user_id
     * @param $winRunRemote
     *
     * @return array|bool
     * @throws Exception
     */
    public static function getRemoteUserInfo($user_id, $winRunRemote = false)
    {
        $WinRunRemote = (int)$winRunRemote;
        $cond = ['UserID' => $user_id, 'WinRunRemote' => $WinRunRemote, 'column' => 'info'];
        $remote_user_info = RemoteUserModel::getSingle($cond);
        if (empty($remote_user_info)) {
            $auth_user_info = AuthUserModel::getSingle(['ID' => $user_id, 'column' => 'ztpuser']);
            $user_name = str_replace(array('@', '*'), array('_', '-'), $auth_user_info['UserName']);
            if (mb_strlen($user_name) > 18) {
                $user_name = mb_substr($user_name, 0, 14) . '_' . randomStr(3);
            }
            $user_name .= "_" . ($winRunRemote ? 'w' : 'b');
            $remote_user_info = [
                'UserID' => $user_id,
                'RemoteUserName' => $user_name,
                'RemotePwd' => aesEncrypt('Re7@'.randomStr(32)),
                'WinRunRemote' => $WinRunRemote,
            ];
            RemoteUserModel::insert($remote_user_info);
        }
        $remote_user_info['RemotePwd'] = aesDecrypt($remote_user_info['RemotePwd']);
        return $remote_user_info;
    }

    /**
     * 创建远程用户账号并返回用户数据
     * @param $userId
     * @param $Resource
     * @param $winRunRemote
     * @return array
     * @throws Exception
     */
    public static function createRemoteUser($userId, $Resource, $winRunRemote): array
    {
        $remote_user = self::getRemoteUserInfo($userId, $winRunRemote);
        //远程应用网关信息
        $gate_info = GatewayServiceProvider::getGateGroupHostIpInfo($Resource);
        //调用rpc通过网关创建远程服务器用户
        $params['remote_user_name'] = $remote_user['RemoteUserName'];
        $params['remote_user_pwd'] = $remote_user['RemotePwd'];
        $params['remote_ser_ip'] = $Resource['Addr'];
        $params['remote_ser_port'] = $Resource['RemotePort'];
        $res_data = [];
//        $res = lib_yar::clients('backend', 'createRemoteSerUser', $params, $gate_info['IP']);
        lib_redis::del('ASM_', 'RemoteSerUser:' . $params['remote_user_name']); // 先删除
        WorkerUdpSend("MessageToGw", "createRemoteSerUser", $params, [$gate_info['IP']]);

        $move_num = 0;
        $remoteSerUserRes = '';
        do {
            sleep(1);
            $remoteSerUserRes = lib_redis::get('ASM_', 'RemoteSerUser:' . $params['remote_user_name']);
            if (!empty($remoteSerUserRes)) {
                break;
            }
            $move_num++;
        } while ($move_num < 3);

        $res = json_decode($remoteSerUserRes, true);
        if (isset($res['status']) && $res['status']) {
            $res_data['remoteSerIP'] = $Resource['Addr'];
            $res_data['remoteSerPort'] = $Resource['Port'];
            $res_data['remoteUserName'] = $remote_user['RemoteUserName'];
            $res_data['remoteUserPwd'] = $remote_user['RemotePwd'];
            $res_data['remoteAppPath'] = $Resource['AppPath'];
            $res_data['remoteCopy'] = $Resource['IsCopy'];
            $res_data['watermark']['switch'] = $Resource['Watermark'] ?? false;
            if ($res_data['watermark']['switch']) {
                $session['UserName'] = $remote_user['RemoteUserName'];
                $cfgStr = WatermarkServiceProvider::getWatermarkContent($session);
                $res_data['watermark']['info'] = $cfgStr;
            }
            $res_data['redirectPrinters'] = "1";
            $res_data['redirectDrives'] = "0";
            $res_data['redirectPorts'] = "1";
        } else {
            self::log("create remote service user:" . var_export($res, true));
            T(21148041);
        }
        return $res_data;
    }
}
