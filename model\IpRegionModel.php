<?php

/**
 * Description: 安全域
 * User: <EMAIL>
 * Date: 2022/10/25 10:32
 * Version: $Id: NetRoleModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class IpRegionModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TIpRegion';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        'one' => 'RID,RegionName,IP,IPv6,Port,DomainList',
        '*'   => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['RID'])) {
            $where .= "AND RID = ".self::setData($cond['RID']);
        }
        if (isset($cond['RIDs'])) {
            $where .= "AND RID IN (" . self::setArrayData($cond['RIDs']) . ")";
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
