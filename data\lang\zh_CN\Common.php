<?php
/**
 * Description: 公共提示
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: Common.php 170672 2022-03-09 06:23:04Z lihao $
 */

$GLOBALS['LANG'][21100] = [
    21100000 => '操作失败',
    21100001 => '队列不存在！',
    21100002 => '参数错误！',
    21100003 => '插入失败！',
    21100004 => '未配置缓存！',
    21100005 => '请求方式不正确！',
    21100006 => '访问被拒绝: {message}！',
    21100007 => '终端时间不正确！',
    21100008 => '请求失败！',
    21100009 => '参数只能为单词！',
    21100010 => '手机号格式不正确！',
    21100011 => '{columnName}由字母、数字、中文以及-_*@.组成且长度不超过50位',
    21100012 => '{columnName}格式错误',
    21100013 => '{columnName}不可输入\',",/,~,\,<,>,^,&,+等字符,且不能超过500的字符',
    21100014 => 'ID格式不对',
    21100015 => '日期格式不正确！',
    21100016 => '不可输入\',",~,/,\,<,>,^,&,+,=,{,}等字符,且不能超过1000的字符',
    21100017 => '不可输入\',",/,~,\,<,>,^,&,+等字符,且不能超过500的字符',
    21100018 => '参数错误或ini文件不存在！',
    21100019 => '文件[{file}]上传失败！',
    21100020 => 'IP格式不合法！',
    21100021 => 'MAC格式不合法！',
    21100022 => '操作太频繁！',
    21100023 => '参数中包含非法字符，请仔细检查！',
    21100024 => '严重',
    21100025 => '重要',
    21100026 => '中等',
];
