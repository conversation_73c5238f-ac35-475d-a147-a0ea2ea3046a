<?php
/**
 * Description: AD域和Ldap公共服务
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: AdLdap.php 147146 2021-06-17 02:04:51Z duanyc $
 */

$GLOBALS['LANG'][21106] = [
    21106001 => 'The parameter is illegal!',
    21106002 => 'Failed to connect to the server, please contact the administrator!',
    21106003 => 'Error logging in to the domain server! Please check if the server is normal.',
    21106004 => 'Authentication failed, maybe the user name does not exist or the password is wrong!',
    ******** => 'Authentication failed, please enter the correct user name and password!',
    ******** => 'The domain server configuration is wrong, please check carefully!',
    ******** => 'The user name does not exist or the password is wrong, please re-enter!',
    ******** => 'Failed to connect to the AD domain server, please contact the administrator!',
    ******** => 'AD domain server authentication failed, account or password is wrong!',
    ******** => 'AD domain server authentication failed, this user does not exist!',
    ******** => 'AD domain server authentication failed, the certificate or password is wrong!',
    ******** => 'AD domain server authentication failed, this login is forbidden!',
    ******** => 'AD domain server authentication failed and the password expired!',
    ******** => 'The AD domain server authentication failed and the account is unavailable!',
    ******** => 'AD domain server authentication failed and the account expired!',
    ******** => 'The AD domain server authentication failed, the user must reset the password!',
    ******** => 'AD domain server authentication failed, account login is restricted!',
    ******** => 'AD domain user',
    ******** => 'LDAP domain user',
    ******** => 'Authentication failed, the user name or password may be wrong!',
    ******** => 'Authentication failed, username or password cannot be empty!',
    ******** => 'Error connecting to LDAP domain server!',
    ******** => 'There is no configuration of the AD domain files that need to be synchronized, and nothing is done!',
    ******** => 'There is no AD domain that needs to be synchronized, and nothing is done!',
    ******** => 'There is no configuration file for the LDAP server configuration that needs to be synchronized, and nothing is done!',
    ******** => 'There is no LDAP server configuration that needs to be synchronized, and nothing is done!',
    ******** => "Sorry, your PHP version is not compatible with LDAP paging function",
    ******** => 'The AD domain server is connected successfully!',
    ******** => 'The connection to the AD domain server failed. The connection to the AD domain may fail or the user name does not exist or the password is wrong!',
    21106030 => 'The connection to the AD domain server failed, please contact the administrator!',
    21106031 => 'The connection to the AD domain server fails. The connection to the AD domain may fail or the user name does not exist, the password is wrong, or the certificate is wrong!',
    21106032 => "There is a limit on the number of server queries, the server is limited to {count}",
    21106033 => "Server search filter option configuration error",
    21106034 => "Role mapping failed",
    21106035 => "Configuration problem, no entry in the role mapping list",
    21106036 => 'LDAP authentication failed, maybe the user name does not exist or the password is wrong!',
    21106037 => 'There is no domain server that needs to be synchronized',
    21106038 => 'Cannot get server configuration information',
    21106039 => 'Domain server configuration error',
    21106040 => 'Failed to connect to the domain server, please contact the administrator!',
    21106041 => 'User authentication failed, maybe the user name does not exist or the password is wrong!',
    21106042 => "The existing PHP version is not compatible with the LDAP paging function",
    21106043 => 'Cannot find domain user information',
    21106044 => 'Cannot query the information of the domain OU department',
    21106045 => 'Failed to extract OU field from user information',
    ******** => 'Server SearchFilter configuration error',
    ******** => "The username entered cannot be empty",
    ******** => "Cannot get server configuration information",
    ******** => 'Cannot find domain user information',
    ******** => "The calling method is incorrect, please call the initFromUser or initFromServer method to complete the initialization",
    ******** => "AD domain authentication failed",
    ******** => "LDAP authentication failed",
    ******** => "Used to synchronize account fields in AD domain ",
    ******** => " Information",
    ******** => "Your role does not have permission, please contact the administrator!",
];
