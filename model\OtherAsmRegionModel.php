<?php

/**
 * Description: TOtherAsmRegion表
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: OtherAsmRegionModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class OtherAsmRegionModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TOtherAsmRegion';
    public const PRIMARY_KEY = 'MacKey';
    protected static $columns = [
        'all' => 'IP,IPListId,DeviceID,AsmIP',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['AsmIP'])) {
            $where .= "AND AsmIP = ".self::setData($cond['AsmIP']);
        }

        if (isset($cond['MAC'])) {
            $where .= "AND MAC = ".self::setData($cond['MAC']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
