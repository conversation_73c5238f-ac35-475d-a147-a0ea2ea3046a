<?php

/**
 * Description: 用户信息
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: UserInfoRedis.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class UserInfoRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'UserInfo';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'Uuid,UserName,TrueNames,Sessions,AccoutStatus,Calculate,LifeTime,ZtpUser';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = 'Uuid,UserName,TrueNames,Sessions,AccoutStatus,LifeTime,ZtpUser';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['Sessions', 'TrueNames'],
        'status' => ['ZtpUser'],
        'ZtpUser' => ['ZtpUser', 'UserName', 'TrueNames']
    ];

    /**
     * 单条
     *
     * @param string $userID
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($userID, $column = 'one')
    {
        return self::get($column, $userID);
    }

    /**
     * 单条
     *
     * @param string $userID
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($userID, $data)
    {
        return self::set($data, $userID);
    }
}
