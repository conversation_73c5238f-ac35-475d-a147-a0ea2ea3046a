<?php
/**
 * Description: 工具
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: hlp_tool.php 147146 2021-06-17 02:04:51Z duanyc $
 */

/**
 * 接口测试函数
 *
 * <AUTHOR>
 * @version $Id: hlp_test.php 147146 2021-06-17 02:04:51Z duanyc $
 */
class hlp_test
{
    /**
     * 命令行参数
     * @var array
     */
    private static $flag;

    /**
     * 数据
     * @var array
     */
    private static $data;

    /**
     * 设置文字背景
     *
     * @param $msg
     * @param $color
     * @param $end
     * @return string
     */
    private static function setMessageColor($msg, $color, $end = true)
    {
        return "\033[{$color}m{$msg}" . ($end ? "\e[0m" : "");
    }

    /**
     * 检查维护基本数据
     * 使用账号dyc: 111，设备ID为1
     * @throws Exception
     */
    public static function checkData()
    {
        $deviceInfo = DeviceModel::getOne(1);
        if (empty($deviceInfo)) {
            throw new Exception("device ID: 1 not exist, please add device!");
        }
        $userInfo = \AuthUserModel::getOneByUserName('User', 'dyc', 'auth');
        $password = dataEncrypt('111');
        if (empty($userInfo) || $userInfo['auth_password'] !== $password) {
            throw new Exception("user dyc not exist, please add user username 'dyc', password is '111'!");
        }
    }

    /**
     * 接口测试检查
     */
    public static function testCheck()
    {
        try {
            $flag = hlp_common::getCmdFlag();
            self::$flag = $flag;
            if (empty($flag['test'])) {
                echo self::setMessageColor(PHP_EOL . "开始接口测试：", 42);
            }
            if (empty($flag) || !isset($flag['test'])) {
                self::help($flag);
            }
            self::checkData();
            if (!empty($flag['ct'])) {
                if (!empty($flag['ac'])) {
                    self::testAction($flag['ct'], $flag['ac']);
                    exit(PHP_EOL);
                }
                if (strpos($flag['ct'], ',') === false) {
                    self::testControl($flag['ct']);
                } else {
                    $cts = explode(',', $flag['ct']);
                    foreach ($cts as $ct) {
                        self::testCmdControl($ct);
                    }
                }
                exit(PHP_EOL);
            }
            $files = scandir(PATH_CONTROL);
            foreach ($files as $fileName) {
                if (strpos($fileName, 'Controller.php') !== false) {
                    $control = str_replace('Controller.php', '', $fileName);
                    $control = strtolower(substr($control, 0, 1)) . substr($control, 1);
                    self::testCmdControl($control);
                }
            }
        } catch (Exception $e) {
            echo self::setMessageColor(PHP_EOL . "错误码: " . $e->getCode() . ", 错误信息: " .PHP_EOL. $e->getMessage(), 41);
        }

        exit(PHP_EOL);
    }

    /**
     * 命令行控制器所有方法测试
     *
     * @param $control
     * @throws Exception
     */
    public static function testCmdControl($control)
    {
        $other = "";
        foreach (self::$flag as $c => $v) {
            if (!in_array($c, ['test', 'ct'])) {
                $other .= "-{$c} {$v}";
            }
        }
        $cmd = "php " . PATH_ROOT . "/webroot/access/index.php -test 1 -ct {$control} {$other}";
        echo self::setMessageColor(PHP_EOL . "开始{$control}控制器接口测试---------------------------------------", 42);
        cutil_shell_exec($cmd);
    }

    /**
     * 控制器所有方法测试
     *
     * @param $control
     * @throws Exception
     */
    public static function testControl($control)
    {
        $controllerName = hlp_common::firstUpper($control) . 'Controller';
        $path = PATH_CONTROL . '/' . $controllerName . '.php';

        if (is_file($path)) {
            require_once($path);
        } else {
            throw new Exception("{$controllerName} is not exists!");
        }

        $class = new ReflectionClass($controllerName);
        $methods = $class->getMethods(ReflectionMethod::IS_PUBLIC);
        if (empty($methods)) {
            throw new Exception("{$controllerName} not exist methods!");
        }
        foreach ($methods as $method) {
            $action = $method->getName();
            if ($action === '__construct') {
                continue;
            }
            self::testMethod($controllerName, $control, $action);
        }
    }

    /**
     * 控制器所有方法测试
     *
     * @param $control
     * @param $action
     * @throws Exception
     */
    public static function testAction($control, $action)
    {
        $controllerName = hlp_common::firstUpper($control) . 'Controller';
        $path = PATH_CONTROL . '/' . $controllerName . '.php';

        if (is_file($path)) {
            require_once($path);
        } else {
            throw new Exception("{$controllerName} is not exists!");
        }

        if (method_exists($controllerName, $action) === true) {
            self::testMethod($controllerName, $control, $action);
            return;
        }
        throw new Exception("{$controllerName} Method {$action}() is not exists!");
    }

    /**
     * 控制器所有方法测试
     *
     * @param $controllerName
     * @param $control
     * @param $action
     * @throws Exception
     */
    public static function testMethod($controllerName, $control, $action)
    {
        $tradecode = "[$control/$action]";
        echo PHP_EOL . "Test $controllerName: $action";
        if (!file_exists(PATH_SYSTEM . "/data/test")) {
            throw new Exception("please delopy https://git.infogo.tech/ASM-Web/dsq/src/branch/master/bindir.");
        }
        if (!isset(self::$data[$control])) {
            $file = PATH_SYSTEM . "/data/test/{$control}.json";
            if (!file_exists($file)) {
                throw new Exception("{$tradecode} test data {$file} not exist.");
            }
            $content = file_get_contents($file);
            self::$data[$control] = json_decode($content, true);
        }
        if (empty(self::$data[$control])) {
            throw new Exception("{$tradecode} test data not exist controller.");
        }
        if (empty(self::$data[$control][$action])) {
            throw new Exception("{$tradecode} test data not exist method.");
        }
        $testData = self::$data[$control][$action];
        $message = "";
        $in = isset(self::$flag['in']) ? iconv('GBK', 'UTF-8', self::$flag['in']) : '';
        $ig = isset(self::$flag['ig']) ? explode(',', self::$flag['ig']) : [];
        foreach ($testData as $index => $tdata) {
            if (!isset($tdata['request']) || !isset($tdata['reponse'])) {
                throw new Exception("{$tradecode} test data format error.");
            }
            if (!empty($in) && $in !== $index) {
                continue;
            }
            if (!empty($ig) && in_array($index, $ig)) {
                continue;
            }
            try {
                $ip = self::$flag['ip'] ?? '127.0.0.1';
                $url = "http://{$ip}/access/1.0/windows/json/{$control}/{$action}";
                $otherParams = [];
                if (isset($tdata['header'])) {
                    $otherParams['header'] = $tdata['header'];
                }
                if (isset(self::$flag['token'])) {
                    $otherParams['header'][] = "WEB-TOKEN: " . self::$flag['token'];
                } else {
                    $otherParams['header'][] = "WEB-TOKEN: " . DeviceTokenRedis::getOne(1);
                }
                $data = curl($url, 'POST', $tdata['request'], 1, 60, $otherParams);
                $code = $tdata['status'] ?? 200;
                if ($data['code'] !== $code) {
                    $message .= "{$tradecode}:{$index} request status code: {$data['code']}." . PHP_EOL;
                }
                $success = true;
                $result = json_decode($data['data'], true);
                foreach ($tdata['reponse'] as $column => $value) {
                    if (isset($result[$column])) {
                        if ($value !== $result[$column]) {
                            $message .= "{$tradecode}:{$index} reponse {$column} expect: {$value}, reality: {$result[$column]}." . PHP_EOL;
                            $success = false;
                        }
                    } elseif (!isset($result['data'][$column])) {
                        $message .= "{$tradecode}:{$index} reponse {$column} not exist." . PHP_EOL;
                        $success = false;
                    } elseif ($result['data'][$column] !== $value && $value !== 'require') {
                        $message .= "{$tradecode}:{$index} reponse {$column} expect: {$value}, reality: {$result['data'][$column]}." . PHP_EOL;
                        $success = false;
                    }
                }
                if (isset(self::$flag['debug']) || !$success) {
                    echo PHP_EOL . "request {$url} input:" . PHP_EOL;
                    var_export($tdata['request']);
                    echo PHP_EOL . "reponse:" . PHP_EOL;
                    var_export($result ?: $data);
                }
            } catch (Exception $e) {
                echo PHP_EOL . "request {$url}:" . PHP_EOL;
                throw new Exception("{$tradecode} request" . " 错误码: " . $e->getCode() . ", 错误信息: ". $e->getMessage());
            }
            if (!empty($message)) {
                throw new Exception($message);
            } else {
                echo self::setMessageColor(PHP_EOL . "{$tradecode}:{$index} 接口数据测试通过!", 42);
            }
        }
    }

    /**
     * 帮助
     * @param $flag[name] string 指定名称
     */
    public static function help($flag)
    {
        $msg = PHP_EOL . "php " . PATH_ROOT . "/webroot/access/index.php -test";
        $str = <<<EOF
    -test 表示开启测试【必选项】
    -debug 表示调试，会打印详细输入数据，返回数据。【可选项】
    -ct [控制器名] 表示测试的控制器【可选项】
    -ac [方法名] 表示测试的控制器的方法【可选项】
    -ip [服务器IP] 表示测试指定服务器，否则测试本地127.0.0.1【可选项】
    -in [用例索引] 表示测试指定索引的数据，如Localhost【可选项】
EOF;
        $msg .= PHP_EOL . $str .PHP_EOL;
        $msg .= PHP_EOL . "php " . PATH_ROOT . "/webroot/access/index.php -test -ip [*************]";
        $msg .= PHP_EOL . "php " . PATH_ROOT . "/webroot/access/index.php -test -ct [auth]";
        $msg .= PHP_EOL . "php " . PATH_ROOT . "/webroot/access/index.php -test -ct [auth] -ac [index]";
        $msg .= PHP_EOL . "php " . PATH_ROOT . "/webroot/access/index.php -test -ct [auth] -ac [index] -debug";
        echo self::setMessageColor($msg, 42) . PHP_EOL;
        exit();
    }
}
