<?php
/**
 * Description: 零信任允许安装软件策略项服务
 * User: <EMAIL>
 * Date: 2022/05/12 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowSoftPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 获取策略结果
     * @return mixed
     * @throws \Exception
     */
    protected function getPolicyResult()
    {
        static $cache = null;
        $token = $this->params['session']['Token'];
        $policyId = $this->params['PolicyID'];
        if (PHP_SAPI !== 'cli' && isset($cache[$token][$policyId])) {
            return $cache[$token][$policyId];
        }
        $userPolicy = \UserPolicyRedis::getOne($this->params['session']['Token'], $this->params['PolicyID']);
        if (empty($userPolicy)) {
            T(21148012, ['params' => $this->params]);
        }
        $cache[$token][$policyId] = $userPolicy;
        return $userPolicy;
    }

    /**
     * 检查是否通过
     * @return bool
     * @throws \Exception
     */
    public function check(): bool
    {
        // iOS不能检查，则不检查该策略
        if ($this->params['session']['OsType'] === OSTYPE_IOS) {
            return true;
        }
        // 未安装客户端且不要求安装客户端，则不检查客户端策略
        if (empty($this->device['InstallClient']) && empty($this->params['InstallClient'])) {
            return true;
        }
        try {
            $userPolicy = $this->getPolicyResult();
        } catch (\Exception $e) {
            $this->reason = $e->getMessage();
            return false;
        }
        if (empty($userPolicy["{$this->params['column']}Result"])) {
            $this->reason = $userPolicy["{$this->params['column']}Reason"];
        }
        $result = $userPolicy["{$this->params['column']}Result"] ?? '0';
        return !empty($result);
    }
}
