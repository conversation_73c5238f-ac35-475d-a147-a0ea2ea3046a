<?php

/**
 * Description: 设备TNacPreOnLineDevice表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: NacPreOnLineDeviceModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class NacPreOnLineDeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacPreOnLineDevice';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        'name' => 'DeviceID,UserName',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
