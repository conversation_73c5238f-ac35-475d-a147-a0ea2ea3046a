<?php

/**
 * Description: TFingerViolation 指纹违规记录表
 * User: <EMAIL>
 * Date: 2021/05/27 10:32
 * Version: $Id
 */

class FingerViolationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TFingerViolation';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        'one'  => 'IP,MAC',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if(isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if(isset($cond['MAC'])) {
            $where .= "AND MAC = ".self::setData($cond['MAC']);
        }

        if(isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
