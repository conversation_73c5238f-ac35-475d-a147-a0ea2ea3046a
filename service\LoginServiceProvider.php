<?php
/**
 * Description: 登录相关逻辑service
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: LoginServiceProvider.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class LoginServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'login';

    /**
     * 误差时间
     */
    public const MISTAKE_TIME = 180;

    /**
     * 获取LoginToken即当前会话的token
     *
     * @return mixed
     */
    public static function getLoginToken()
    {
        return $_SERVER['HTTP_WEB_TOKEN'] ?? '';
    }

    /**
     * 生成LoginToken
     *
     * @param $userId
     *
     * @return mixed
     * @throws Exception
     */
    public static function genLoginToken($userId)
    {
        $key = $userId . microtime(true) . random_int(100000, 999999);
        return md5($key);
    }

    /**
     * 获取时间戳
     * @return false|float
     */
    public static function getTimestamp()
    {
        return round(microtime(true) * 1000);
    }

    /**
     * 获取或分配token
     * @param $data
     * @param $params
     * @return mixed|string
     * @throws Exception
     */
    public static function getToken($data, $params = [])
    {
        $userId = $data['UserID'] ?? '';
        /*$token = self::getLoginToken();
        if (isset($params['autoAuth']) && $params['autoAuth'] === "1") {  //autoAuth=1为客户端自动认证调用net_auth的情况 使用Redis里设备对应的token
            $token = DeviceTokenRedis::getOne($data['DeviceID']) ?: '';
        }
        $session = SessionRedis::getOne($token);
        $Uuid = $session['Uuid'] ?? '';
        if (empty($token) || empty($Uuid)) {
            $token = self::genLoginToken($userId);
        }
        if ($Uuid !== $userId) {
            UserAddAuthRedis::deleteOne($token);
            $token = self::genLoginToken($userId);
        }*/
        return self::genLoginToken($userId);
    }

    /**
     * 登录成功后登录态维护
     *
     * @param $data
     * @param $userInfo
     * @return void
     * @throws Exception
     */
    public static function setSession($data, $userInfo)
    {
        $userId = $data['UserID'] ?? '';
        $token = $data['Token'];
        // 优先用用户表的角色
        if (!isset($data["DepartID"]) || $data["DepartID"] !== $userInfo['DepartID']) {
            $departInfo = DepartModel::getOne($userInfo['DepartID'], 'one');
            $data['AllDepartID'] = $departInfo['AllDepartID'];
            $data['DepartName'] = $departInfo['DepartName'];
        }
        $UserType = ($data['AuthType'] === AUTH_TYPE_GUEST) ? 'guest' : 'staff';
        $DepartIDs = str_replace('/', ',', $data['AllDepartID']);
        //增加记录授权组id
        $authorizationIds = $data['AuthorizationGroup'] ?? '';
        $Expire = $data['AuthInterval'] * 60 + self::MISTAKE_TIME;
        $sdp = parse_initfile(PATH_ETC . 'sdp.ini');

        $data = [
            'Uuid' => $userId,
            'AccountID' => $data['AccountID'] ?? '0',
            'DeviceID' => $data['DeviceID'],
            'DevName' => $data['DevName'],
            'DevInfo' => $userInfo['DevInfo'] ?? '',
            'SceneID' => $data['SceneID'],
            'RoleID' => $userInfo['RoleID'] ?? '0',
            'DepartIDs' => $DepartIDs,
            'DepartName' => $data['DepartName'] ?? '',
            'Type' => $userInfo['Type'],
            'UserType' => $UserType,
            'LifeTime' => $Expire,
            'UserName' => $data['UserName'] ?? '',
            'TrueNames' => $data['TrueNames'] ?? '',
            'PolicyID' => $data['PolicyID'],
            'LastTime' => $data['TokenTimestamp'],
            'SessionStatus' => $userId ? '2' : '1',
            'ConnectIp' => getRemoteAddress(),
            'IP' => $userInfo['IP'] ?? '',
            'OsType' => OS_TYPE,
            'Client' => IS_CLIENT,
            'Mac' => $userInfo['Mac'] ?? '',
            'SelectMode' => $sdp['selectMode'] ?? '',
            'AuthorizationIDs' => $authorizationIds,
            'IsInternal' => IS_INTERNAL ? 1 : 0,
            'AuthTime' => time(),
        ];
        SessionRedis::setOne($token, $data);
        DeviceTokenRedis::setOne($data['DeviceID'], $token);
    }

    /**
     * 增加session生命期限
     * @param $Token
     * @return array
     * @throws Exception
     */
    public static function addSessionLifeTime($Token)
    {
        $Session = SessionRedis::getOne($Token, 'online');
        if (empty($Session['Uuid'])) {
            self::log("{$Token} had expire.");
            return [];
        }
        $deviceRedis = DeviceIDRedis::getOne($Session['DeviceID']);
        if ($deviceRedis['LastHeartTime'] !== $Session['LastHeartTime']) {
            $sessionData = [];
            $sessionData['LastHeartTime'] = $deviceRedis['LastHeartTime'];
            $sessionData['LifeTime'] = $Session['LifeTime'];
            SessionRedis::setOne($Token, $sessionData);
        }
        return ['res' => true];
    }

    /**
     * 更新所有在线时间
     */
    public static function updateSessionLifeTime()
    {
        $list = NacOnLineDeviceModel::getAllOnlineAuthInterval();
        if (empty($list)) {
            return;
        }
        foreach ($list as $device) {
            if (empty($device['Token'])) {
                continue;
            }
            $UpdateLifeTime = $device['AuthInterval'] * 60 + self::MISTAKE_TIME;
            SessionRedis::setOne($device['Token'], ['UpdateLifeTime' => $UpdateLifeTime]);
        }
    }

    /**
     * 登录成功后登录态维护
     *
     * @param $token
     *
     * @return void
     * @throws Exception
     */
    public static function syncSession($token): void
    {
        if (empty($token)) {
            return;
        }
        SessionRedis::handPush($token);
    }

    /**
     * 记录其他用户日志
     *
     * @param $Session
     * @param $LogTypeID
     * @param $Content
     * @param array $Data
     *
     * @return bool|int
     */
    public static function addOtherUserLog($Session, $LogTypeID, $Content, $Data = [])
    {
        if (empty($Session) || empty($LogTypeID) || empty($Content)) {
            return false;
        }

        $params = ['UserID' => $Session['Uuid'] ?? 0, 'AccountID' => $Session['AccountID'] ?? 0,
            'AccessToken' => $Session['Token'] ?? '', 'LogTypeID' => $LogTypeID, 'IP' => $_SERVER["REMOTE_ADDR"],
            'Content' => $Content, 'LoginName' => $Session['UserName'], 'User_Name' => $Session['UserName'],
            'True_Name' => $Session['TrueNames'] ?? ''];
        if (isset($Data['Status'])) {
            $params['Status'] = $Data['Status'];
        }
        $params['Client'] = SystemServiceProvider::getClientTypeId();
        $params['ClientInfo'] = $Data['DevInfo'] ?? SystemServiceProvider::getDevInfo($Session['DevInfo']);
        $params['UPDATETIME'] = 'now()';
        return UserLogModel::insert($params);
    }

    /**
     * 退出登录
     */
    public static function logout()
    {
        $token = self::getLoginToken();
        if (!empty($token)) {
            $session = SessionRedis::getOne($token, 'user');
            if (!empty($session)) {
                $content = L(********, ['UserName' => $session['UserName'], 'Time' => date('Y-m-d H:i:s')]);
                self::addOtherUserLog($session, 59, $content);
            }
            lib_yar::clients('backend', 'revokOauthToken', $session['Uuid']);
        }
    }

    /**
     * 下线处理
     *
     * @param $DeviceId
     * @param $Token
     *
     * @return bool
     */
    public static function cutoff($DeviceId, $Token): bool
    {
        if (empty($DeviceId) || empty($Token)) {
            return false;
        }

        DeviceTokenRedis::deleteOne($DeviceId);
        SessionRedis::deleteOne($Token);
        UserAddAuthRedis::deleteOne($Token);
        return true;
    }

    /**
     * 检查登录状态
     *
     * @param $deviceId
     * @param $check
     * @param $ztpNocheck
     * @return mixed
     * @throws Exception
     */
    public static function checkLogin($deviceId, $check = true, $ztpNocheck = false)
    {
        $token = self::getLoginToken();
        if (empty($token) && !$ztpNocheck) {
            hlp_common::setHeader(428, 'No Login');
            T(21120054);
        }
        $session = SessionRedis::getOne($token, 'user');
        if (empty($session) || !isset($session['Uuid'])) {
            if ($ztpNocheck) {
                return [];
            }
            hlp_common::setHeader(428, 'Login Err');
            T(21120055);
        }
        if ($check && (string)$deviceId !== $session['DeviceID']) {
            self::log("checkLogin:{$deviceId}:{$session['DeviceID']}");
            hlp_common::setHeader(428, 'Login DErr');
            T(21120055);
        }
        if ($check && empty($session['Uuid'])) {
            T(21120061);
        }
        return $session;
    }

    /**
     * 是否已登录
     *
     * @param $deviceId
     * @param $column
     *
     * @return bool|mixed
     */
    public static function isLogin($deviceId, $column = 'user')
    {
        try {
            $token = self::getLoginToken();
            if (empty($token)) {
                T(21120054);
            }
            $session = SessionRedis::getOne($token, $column);
            if (empty($session) || !isset($session['Uuid'])) {
                T(21120055);
            }
            if ((string)$deviceId !== $session['DeviceID']) {
                T(21120055);
            }
            return $session;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * 从ZTP_Cookie获取检查是否登录
     *
     * @param $ZTPCookie
     *
     * @return mixed
     * @throws Exception
     */
    public static function checkCookieLogin($ZTPCookie)
    {
        $ControlUrl = GatewayServiceProvider::getControlUrlPrefix();
        $errData = ['ControlUrl' => $ControlUrl];
        if (empty($ZTPCookie)) {
            hlp_common::setHeader(428, 'No Login');
            T(21120054, [], $errData);
        }
        $UserInfo = @json_decode(pubDncrypt($ZTPCookie), true);
        if (empty($UserInfo) || empty($UserInfo['SessionID'])) {
            hlp_common::setHeader(428, 'No Login2');
            T(21120054, [], $errData);
        }
        $Token = $UserInfo['SessionID'];
        $session = SessionRedis::getOne($Token, 'user');
        if (empty($session) || !isset($session['Uuid'])) {
            hlp_common::setHeader(428, 'Login Err');
            T(21120055, [], $errData);
        }
        if ($UserInfo['DeviceID'] !== $session['DeviceID'] || $UserInfo['Uuid'] !== $session['Uuid']) {
            hlp_common::setHeader(428, 'Login DErr');
            T(21120055, [], $errData);
        }
        if (empty($session['Uuid'])) {
            T(21120061, [], $errData);
        }
        return [$session, $UserInfo];
    }

    /**
     * 设置登录cookie
     *
     * @param $data
     *
     * @return mixed
     */
    public static function setCookieLogin($data)
    {
        $aInfo = [];
        $aInfo['DeviceID'] = (string)$data['DeviceID'];
        $aInfo['Uuid'] = $data['UserID'];
        $aInfo['SessionID'] = $data['Token'];
        $json = json_encode($aInfo);
        $Cookies = pubEncrypt($json);
        //缓存太久有问题先搞俩分钟吧
        $Expire = time() + $data['AuthInterval'] * 2;
        setcookie('ZTP_Cookie', $Cookies ?? '', $Expire, "/");
        return SessionRedis::getOne($data['Token'], 'user');
    }

    /**
     * 获取登录信息
     *
     * @param $token
     * @param string $column
     *
     * @return bool|mixed
     */
    public static function getSessionInfo($token, $column)
    {
        if (empty($token)) {
            return false;
        }
        $session = SessionRedis::getOne($token, $column);
        if (empty($session)) {
            return false;
        }
        return $session;
    }

    /**
     * 是否需要强化认证
     *
     * @param $ResID
     * @param $checkPolicy
     * @param $token
     *
     * @return bool|string
     */
    public static function isNeedAddAuth($ResID, $checkPolicy = true, $token = '')
    {
        $dict = DictModel::getOneItem('User', 'AddAuthType');
        $AddAuthType = $dict['ItemValue'] ?? '';
        if ($AddAuthType === 'Close') {
            return false;
        }
        // 移动端暂不支持企业微信认证，访问资源时触发企业微信强化认证应直接访问资源成功
        if (IS_MOBILE && $AddAuthType === 'WeWork') {
            return false;
        }
        if (!$checkPolicy) {
            return true;
        }
        $addAuth = false;
        $PolicyID = PolicyServiceProvider::getPolicyID($ResID);
        if (!empty($PolicyID)) {
            // 查询资源的策略是否要求附加认证
            $Policy = PolicyServiceProvider::getPolicy($PolicyID);
            if (empty($Policy)) {
                return false;
            }
            $session = SessionRedis::getOne($token, 'policy');
            $inEffect = PolicyServiceProvider::checkInEffect($session, $Policy['PolicyConfig']);
            // 策略生效
            if (!empty($inEffect)) {
                $PolicyObject = json_decode($Policy['PolicyObject'], true);
                $addAuth = !empty($PolicyObject['RequireAuth']) ? $AddAuthType : false;
            }
        }
        return $addAuth;
    }

    /**
     * 检查强化认证（原附加认证）
     *
     * @param $userId
     * @param $token
     * @param $resInfo
     *
     * @return bool
     * @throws Exception
     */
    public static function checkAddAuth($userId, $token, &$resInfo): bool
    {
        $ResID = $resInfo['ResID'];
        $addAuth = self::isNeedAddAuth($ResID, true, $token);
        self::log("checkAddAuth:{$addAuth}");
        $resInfo['AddAuth'] = $addAuth;
        // 策略不要求附加认证
        if (empty($addAuth)) {
            return false;
        }

        $addAuthData = UserAddAuthRedis::getOne($token);
        if (empty($addAuthData['Status'])) {
            $data = self::getAddAuthParams($userId, $addAuth);
            $data['AddAuthType'] = $addAuth;
            T(21148004, [], $data);
        }
        return true;
    }

    /**
     * 检查强化认证开启状态
     *
     * @param $authType
     *
     * @throws Exception
     */
    public static function checkAddAuthType($authType): void
    {
        $dict = DictModel::getOneItem('User', 'AddAuthType');
        $AddAuthType = $dict['ItemValue'] ?? '';
        if ($AddAuthType === 'Close') {
            T(21148030);
        }
        if ($authType !== $AddAuthType) {
            self::log("checkAddAuthType {$authType}：{$AddAuthType}");
            T(21148031);
        }
    }

    /**
     * 网关判断是否强化认证
     *
     * @param $token
     *
     * @return bool
     */
    public static function isAddAuth($token): bool
    {
        $addAuthData = UserAddAuthRedis::getOne($token);
        if (empty($addAuthData)) {
            return false;
        }
        return !empty($addAuthData['Status']);
    }

    /**
     * 要求附加认证是否符合
     *
     * @param $require
     * @param $token
     *
     * @return bool
     */
    public static function getAddAuthStatus($require, $token): bool
    {
        if (empty($require)) {
            return true;
        }
        $addAuthData = UserAddAuthRedis::getOne($token);
        return !empty($addAuthData['Status']);
    }

    /**
     * 获取附加认证需要的参数
     *
     * @param $userId
     * @param string $addAuthType User|UKey|Mobile|Email|Finger|DingTalk|WeWork|FeiShu
     *
     * @return array
     */
    public static function getAddAuthParams($userId, $addAuthType): array
    {
        switch ($addAuthType) {
            case 'Mobile':
                $Userinfo = AuthUserModel::getOne($userId, 'userinfo');
                return ['Tel' => $Userinfo['Tel'] ?? ''];
                break;
            case 'Mailbox':
                $Userinfo = AuthUserModel::getOne($userId, 'userinfo');
                return ['Email' => $Userinfo['Email'] ?? ''];
                break;
            case 'DingTalk':
                $dict = AuthServiceProvider::getAuthDomainConfig('DingTalk');
                return ['AppID' => $dict['appIDScanner'] ?? ''];
                break;
            case 'WeWork':
                $dict = AuthServiceProvider::getAuthDomainConfig('WeWork');
                return ['AgentID' => $dict['agentid'] ?? '', 'CorpID' => $dict['corpId'] ?? ''];
                break;
            case 'FeiShu':
                $dict = AuthServiceProvider::getAuthDomainConfig('FeiShu');
                return ['AppID' => $dict['appid'] ?? ''];
                break;
            case 'User':
            case 'AdDomain':
            case 'LDAP':
            case 'Radius':
            case 'WebAuth':
            case 'Email':
                $Account = AccountModel::getSingle(['user_id' => $userId, 'auth_type' => $addAuthType]);
                return ['UserName' => $Account['auth_name'] ?? ''];
                break;
        }
        return [];
    }

    /**
     * 获取用户日志列表
     *
     * @param $cond
     * @param $start
     * @param $limit
     *
     * @return array
     */
    public static function getUserLogList($cond, $start = 0, $limit = 100)
    {
        $AllTypeNames = UserLogTypeModel::getAllName();
        $cond['column'] = 'list';
        $List = UserLogModel::getList($cond, 'ID DESC', $start, $limit);
        if (!empty($List)) {
            foreach ($List as $Key => $Val) {
                $List[$Key]['LogTypeName'] = $AllTypeNames[$Val['LogTypeID']] ?? '';
            }
        }
        return $List;
    }

    /**
     * 获取用户日志数量
     *
     * @param $Cond
     *
     * @return mixed
     */
    public static function getUserLogCount($Cond)
    {
        return UserLogModel::getCount($Cond);
    }

    /**
     * 获取用户信息
     *
     * @param $UserID
     *
     * @return mixed
     */
    public static function getUserInfo($UserID)
    {
        if (empty($UserID)) {
            return [];
        }
        return AuthUserModel::getOne($UserID, 'userinfo');
    }

    /**
     * 设置登录后的缓存数据
     *
     * @param $UserID
     * @param $Token
     * @param $UserInfo
     */
    public static function setLoginUserInfo($UserID, $Token, $UserInfo): void
    {
        if (empty($UserInfo)) {
            return;
        }
        $RedisUserInfo = UserInfoRedis::getOne($UserID, 'one');
        if (!empty($RedisUserInfo['Sessions'])) {
            $Sessions = json_decode($RedisUserInfo['Sessions'], true);
            $SessionKeys = array_keys($Sessions);
            $existKeys = lib_redis::mexists(SessionRedis::SESSION_KEY, $SessionKeys);
            foreach ($existKeys as $key => $exist) {
                if (!$exist) {
                    unset($Sessions[$key]);
                }
            }
            $Sessions[$Token] = 1;
        } else {
            $Sessions = [$Token => 1];
        }
        // 用户信息最长7天过期
        $data = ['Uuid' => $UserID, 'ZtpUser' => $UserInfo['ZtpUser'], 'UserName' => $UserInfo['UserName'], 'TrueNames' => $UserInfo['TrueNames'] ?? '',
            'AccoutStatus' => '0', 'Calculate' => '0', 'LifeTime' => 7 * 86400];
        $data['Sessions'] = json_encode($Sessions);
        UserInfoRedis::setOne($UserID, $data);
    }

    /**
     * 下线用户与设备无关的回话记录
     * @param $UserID
     * @return bool
     */
    public static function offlineUserInfo($UserID)
    {
        if (empty($UserID)) {
            return false;
        }
        $RedisUserInfo = UserInfoRedis::getOne($UserID, 'one');
        if (!empty($RedisUserInfo['Sessions'])) {
            $Sessions = json_decode($RedisUserInfo['Sessions'], true);
            foreach ($Sessions as $Token => $State) {
                $Session = SessionRedis::getOne($Token, 'online');
                if (!empty($Session) && $Session['DeviceID'] < 0) {
                    self::cutoff($Session['DeviceID'], $Token);
                }
            }
        }
        return true;
    }

    /**
     * 检查设备是否上报结果(安检)，认证结果是否完成，检查设备是否注册并审核
     *
     * @param $DeviceID
     *
     * @throws Exception
     */
    public static function checkResult($DeviceID): void
    {
        if ($DeviceID < 0) {
            return;
        }
        $onlineDevice = NacOnLineDeviceModel::getOne($DeviceID, 'name');
        $guestOnlineDevice = GuestOnLineDeviceModel::getSingle(['DeviceID' => $DeviceID]);  //查询来宾在线表是否存在
        if (empty($onlineDevice) && empty($guestOnlineDevice)) {
            hlp_common::setHeader(428, 'No Online');
            T(21120054);
        }
        $registered = DeviceServiceProvider::getDevRegistered($DeviceID);
        if ((int)$registered !== 1) {
            T(21105003);
        }
        if (empty($guestOnlineDevice)) {  //如果不是来宾访客入网则校验检查时间
            $rcomputer = RelationComputerModel::getSingle(['DeviceID' => $DeviceID, 'column' => 'check']);
            if (empty($rcomputer) || $rcomputer['CheckTime'] === DEFAULT_TIME) {
                T(21104019);
            }
        }
    }

    /**
     * 设置用户错误信息
     *
     * @param $error_code
     * @param $message
     * @param $deviceId
     *
     * @return mixed
     */
    public static function setUserErrMessage($error_code, $message, $deviceId)
    {
        if (empty($deviceId)) {
            return false;
        }
        return MessageRedis::setOne($deviceId, $error_code, $message);
    }

    /**
     * 获取用户错误信息
     *
     * @param $error_code
     * @param $deviceId
     */
    public static function getUserErrMessage($error_code, $deviceId)
    {
        if (empty($deviceId)) {
            return L(21148040);
        }
        return MessageRedis::getOne($deviceId, $error_code);
    }

    /**
     * 获取要同步到网关的数据
     *
     * @param $GwIps
     *
     * @return array
     */
    public static function getSyncUserInfo($GwIps): array
    {
        if (empty($GwIps)) {
            return [];
        }
        $list = [];
        foreach ($GwIps as $gwIp) {
            SessionRedis::getPushAll($gwIp, $list, 10);
            UserInfoRedis::getPushAll($gwIp, $list, 10);
            UserResourceRedis::getPushAll($gwIp, $list, 10);
            UserAddAuthRedis::getPushAll($gwIp, $list, 10);
        }
        return $list;
    }

    /**
     *
     * 更新所有在线用户对应的ASG_UserInfo这个key对应的过期时间
     *
     */
    public static function updateUserInfoLifeTime()
    {
        $cond = ['column' => 'user', 'groupby' => 'Token'];
        $list = NacOnLineDeviceModel::getList($cond, false, 0, 50000);
        $size = 500;
        $start = 0;
        if (!empty($list)) {
            foreach ($list as $row) {
                //用户ID不存在则不处理
                if (empty($row['UserID'])) {
                    continue;
                }
                //通过用户ID找到对应的ASG_UserInfo是否存在，如果存在，则查询session是否存在，多端登录只要有一个session在就续期7天
                $RedisUserInfo = UserInfoRedis::getOne($row['UserID'], 'one');
                if (!empty($RedisUserInfo['Sessions'])) {
                    //只要有一个存在，就在当前的日期上往后续期7天
                    $data = ['LifeTime' => 7 * 86400];
                    UserInfoRedis::setOne($row['UserID'], $data);
                }

                $start++;

                if ($start % $size === 0) {
                    lib_database::closeMysql();
                }
            }
        }

    }
}
