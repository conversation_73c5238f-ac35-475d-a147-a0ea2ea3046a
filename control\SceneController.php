<?php
/**
 * Description: 准入场景化相关接口
 * User: renchen
 * Date: 2022/7/19 14:17.
 */
!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL.'/BaseController.php';
class SceneController extends BaseController
{
    /**
     * 根据设备ID获取设备场景信息
     * @throws Exception
     *
     * @return array
     */
    public function getDeviceScene(): array
    {
        $deviceId = request('deviceId', 'post', 0, 'int');
        $UserType = request('userType', 'post', '', 'string');
        if ($deviceId <= 0) {
            T(21100002);
        }
        if ($UserType && !in_array($UserType,['1','2'])) {
            T(21100002);
        }

        $deviceSceneService = DeviceServiceProvider::initDeviceSceneService();
        $sceneInfo = $deviceSceneService->getDevSceneInfo($deviceId, $UserType);
        if (1 !== $sceneInfo['code']) {
            T($sceneInfo['code']);
        }
        AuthServiceProvider::checkLoginScene($deviceId, $sceneInfo['data']);
        return [
            'errcode' => '0',
            'errmsg' => 'ok',
            'data' => $sceneInfo['data'],
        ];
    }
}
