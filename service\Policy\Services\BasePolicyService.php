<?php
/**
 * Description: 零信任策略项服务
 * User: <EMAIL>
 * Date: 2022/05/12 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;


class BasePolicyService
{
    /**
     * 请求参数数组,和处理后的数据.
     *
     * @var array
     */
    protected $params;
    protected $device;
    protected $reason; // 不通过原因

    /**
     * 初始化
     * @param $params
     * @throws \Exception
     */
    public function __construct($params)
    {
        $this->params = $params;
        static $devices = null;
        if (empty($devices[$this->params['DeviceID']])) {
            if (empty($this->params['DeviceID'])) {
                T(21103006);
            }

            //如果是微应用等假设备ID，且绑定了策略，此处设备信息部分直接为空，跳过去
            if (intval($this->params['DeviceID']) <= -1) {
                $device = [];
            } else {
                $device = \DeviceModel::getJoinComputer($this->params['DeviceID'], 'policy');
                if (empty($device)) {
                    T(21103006);
                }
            }

            $devices[$this->params['DeviceID']] = $device;
        }
        $this->device = $devices[$this->params['DeviceID']];
    }

    /**
     * redis 有序集合 实现 多少分钟内 变动 少于多少次的判断
     * @param $cacheKey
     * @param $initNum
     * @param $expire
     * @return bool
     */
    function _limit($cacheKey, $initNum, $expire): bool
    {
        //redis有序集合实现的限速
        $now = time();
        $value = $cacheKey . time();
        $prefix = 'ASG_';
        \lib_redis::zAdd($prefix, $cacheKey, $now, $value); //全局限速的  值一定要保证不一致
        \lib_redis::expire($prefix, $cacheKey, $expire);
        $countEndTime = $now - $expire;
        $zremBeginTime = $now - $expire - $expire;
        $nownum = \lib_redis::zCount($prefix, $cacheKey, $countEndTime, $now); //获取最近一段时间的次数
        \lib_redis::ZREMRANGEBYSCORE($prefix, $cacheKey, $zremBeginTime, $countEndTime); //获取最近一段时间的次数
        if ($nownum >= $initNum) {
            //在指定时间范围内请求次数超过设定值
            \lib_redis::del($prefix, $cacheKey);
            return false;
        }
        return true;
    }

    /**
     * 获取用户的IP
     * @return mixed
     */
    public function getClientIp($location = false)
    {
        if ($this->device['InstallClient']) {
            return $location ? $this->device['GateIP'] : $this->device['IP'];
        }
        $ip = getRemoteAddress();
        if ($ip === '127.0.0.1') {
            return $this->params['session']['ConnectIp'];
        }
        return $ip;
    }

    /**
     * 获取原因
     * @return mixed
     */
    public function getReason()
    {
        return $this->reason;
    }
}
