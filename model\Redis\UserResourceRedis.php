<?php

/**
 * Description: 用户资源的策略结果
 * User: <EMAIL>
 * Date: 2022/05/16 10:32
 * Version: $Id$
 */

class UserResourceRedis extends BaseRedis
{
    /**
     * 是否记录日志
     *
     * @var string
     */
    public const IS_LOG = false;

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'UserResource';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'Result,Reason,LifeTime,VirIpResult,VirIpReason,ErrCode';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = 'Result,LifeTime';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => '',
    ];

    /**
     * 单条
     *
     * @param string $token
     * @param string $resId
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($token, $resId, $column = 'one')
    {
        return self::get($column, $token, $resId);
    }

    /**
     * 单条
     *
     * @param string $token
     * @param string $resId
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($token, $resId, $data)
    {
        $data['LifeTime'] = 360;
        return self::set($data, $token, $resId);
    }
}
