<?php

/**
 * Description: 路由配置
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: inc_route.php 170935 2022-03-13 12:44:24Z huyf $
 */

/* 访问控制 */
!defined('IN_INIT') && exit('Access Denied');
/* 初始化全局配置变量 */
$GLOBALS['ROUTE'] = array();

$GLOBALS['ROUTE']['routes'] = array(
    'access/([\d\.]+)/(ios|android|harmonyos|windows|mac|linux|server)/(object|json)/([a-z_]+)(/([a-z0-9_]+))?' =>
        'api_version=$1&os_type=$2&source_type=$3&ct=$4&ac=$6',
    'access/getotheruser' => 'api_version=1.0&os_type=windows&source_type=json&ct=auth&ac=other',
    'access/qr' => 'api_version=1.0&os_type=windows&source_type=json&ct=scan&ac=short',
    'access/qrcode' => 'api_version=1.0&os_type=windows&source_type=json&ct=scan&ac=qrcode',
    'access/errmsg' => 'api_version=1.0&os_type=windows&source_type=json&ct=ztpGateway&ac=errmsg',
    'access/resourceCheck' => 'api_version=1.0&source_type=json&ct=ztpPolicy&ac=resourceCheck',
    'access/sdpErr' => 'api_version=1.0&os_type=windows&source_type=json&ct=ztpGateway&ac=sdpErr',
    'access/rpc/([a-z_]+)' => 'api_version=1.0&yarrpc=1&ct=$1',
    'access/([a-z_]+)/([a-z0-9_]+)' => 'api_version=1.0&os_type=windows&source_type=json&ct=$1&ac=$2',
    '.*' => array('ct' => 'index', 'ac' => 'index')
);

/* 老方式的映射 key统一使用小写 */
$GLOBALS['ROUTE']['map'] = [
    'cutoff_net'                  => ['control' => 'net', 'action' => 'cutoff'],
    'wince_net'                   => ['control' => 'net', 'action' => 'winceAccess'],
    'get_server_lang'             => ['control' => 'server', 'action' => 'lang'],
    'get_server_info'             => ['control' => 'server', 'action' => 'info'],
    'writelog'                    => ['control' => 'server', 'action' => 'writelog'],
    'sendipv6xml'                 => ['control' => 'server', 'action' => 'ipv6xml'],
    'get_server_time'             => ['control' => 'server', 'action' => 'time'],
    '/update/upload_file.php'     => ['control' => 'server', 'action' => 'upload'],
    'authorization'               => ['control' => 'server', 'action' => 'authorization'],
    'fast_auth'                   => ['control' => 'server', 'action' => 'conf'],
    'get_pubkey'                  => ['control' => 'server', 'action' => 'pubkey'],
    'getclientconf'               => ['control' => 'server', 'action' => 'version'],
    'getmobileconfig'             => ['control' => 'server', 'action' => 'mobileConfig'],
    'getmsacdurldis'              => ['control' => 'server', 'action' => 'msacInfo'],
    'installonline'               => ['control' => 'server', 'action' => 'controlInfo'],
    'set_top_login'               => ['control' => 'server', 'action' => 'setLogin'],
    'startup'                     => ['control' => 'server', 'action' => 'startup'],
    'get_client_ipv6'             => ['control' => 'server', 'action' => 'ipv6'],
    'traderuntime'                => ['control' => 'server', 'action' => 'runtime'],
    'project_conf'                => ['control' => 'server', 'action' => 'projectConf'],
    'selfguestsubmit'             => ['control' => 'guest', 'action' => 'self'],
    'get_guest_netcode'           => ['control' => 'guest', 'action' => 'netcode'],
    'get_guest_imgcode'           => ['control' => 'guest', 'action' => 'imgcode'],
    'get_user_imgcode'            => ['control' => 'scan', 'action' => 'index'],
    'check_scaned'                => ['control' => 'scan', 'action' => 'guest'],
    'login'                       => ['control' => 'scan', 'action' => 'login'],
    '/asmtools/qrcode/qrcode.php' => ['control' => 'scan', 'action' => 'qrcode'],
    'net_auth'                    => ['control' => 'auth', 'action' => 'index'],
    'get_authflag'                => ['control' => 'auth', 'action' => 'flag'],
    'get_roleinfo'                => ['control' => 'auth', 'action' => 'roleinfo'],
    '/a/getotheruser_int.php'     => ['control' => 'auth', 'action' => 'other'],
    'get_userflag'                => ['control' => 'auth', 'action' => 'config'],
    'auto_auth'                   => ['control' => 'auth', 'action' => 'auto'],
    'getdeviceinfoprocess'        => ['control' => 'device', 'action' => 'infoprocess'],
    'get_device_reginfo'          => ['control' => 'device', 'action' => 'reginfo'],
    'get_device_online'           => ['control' => 'device', 'action' => 'online'],
    'dev_info'                    => ['control' => 'device', 'action' => 'info'],
    'getdevicename'               => ['control' => 'device', 'action' => 'name'],
    'check_device_illegal'        => ['control' => 'device', 'action' => 'illegal'],
    'uninstall_assistant'         => ['control' => 'device', 'action' => 'uninstall'],
    'get_device_isreg'            => ['control' => 'device', 'action' => 'isReg'],
    'changepass'                  => ['control' => 'user', 'action' => 'changePass'],
    'm_changepass'                => ['control' => 'user', 'action' => 'mChangePass'],
    'get_device_location'         => ['control' => 'user', 'action' => 'location'],
    'get_domainconfig'            => ['control' => 'user', 'action' => 'domainConfig'],
    'getcheckpolicy'              => ['control' => 'check', 'action' => 'policy'],
    'getcheckitem'                => ['control' => 'check', 'action' => 'item'],
    'mobileresult'                => ['control' => 'check', 'action' => 'mobileResult'],
    'policycheckitem'             => ['control' => 'check', 'action' => 'auto'],
    'get_dingtalk_user'           => ['control' => 'dingtalk', 'action' => 'user'],
    '/a/getotheruser_int/dingtalk/dingtalk.php'  => ['control' => 'dingtalk', 'action' => 'jump'],
    'get_wework_user'             => ['control' => 'wework', 'action' => 'user'],
    '/a/getotheruser_int/wework/wework.php'  => ['control' => 'wework', 'action' => 'jump'],
    'get_feishu_user'             => ['control' => 'feishu', 'action' => 'user'],
    '/a/getotheruser_int/feishu/feishu.php'  => ['control' => 'feishu', 'action' => 'jump'],
    'regdevsubmit'                => ['control' => 'register', 'action' => 'submit'],
    'send_sms'                    => ['control' => 'sms', 'action' => 'send'],
    'getvpninfo'                  => ['control' => 'vpn', 'action' => 'check'],
    'vpnupdevinfo'                => ['control' => 'vpn', 'action' => 'upDevinfo'],
    'get_resource_auth_config'    => ['control' => 'resource', 'action' => 'config'],
    'get_resource_auth_user'      => ['control' => 'resource', 'action' => 'user'],
    'get_senior_qrcode'           => ['control' => 'resource', 'action' => 'qrcode'],
    'resource_auth'               => ['control' => 'resource', 'action' => 'auth'],
    'get_asc_ipport'              => ['control' => 'asc', 'action' => 'ipport'],
    'make_belong_times'           => ['control' => 'patch', 'action' => 'belongTimes'],
    'get_patchserver_ipport'      => ['control' => 'patch', 'action' => 'serverInfo'],
    'get_patch_info'              => ['control' => 'patch', 'action' => 'detail'],
    'update_install_log'          => ['control' => 'patch', 'action' => 'installLog'],
    'get_dasm_dev_info'           => ['control' => 'distributed', 'action' => 'devInfo'],
    'get_dasm_usb_info'           => ['control' => 'distributed', 'action' => 'usbInfo'],
    'get_dasm_switch_info'        => ['control' => 'distributed', 'action' => 'switchInfo'],
    'import_dasm_dev_info'        => ['control' => 'distributed', 'action' => 'importDevFromDasm'],
    'ssocheckloginstate'          => ['control' => 'sso', 'action' => 'checkLoginState'],
    'ssoauth'                     => ['control' => 'sso', 'action' => 'auth'],
    'get_device_repair_patch'     => ['control' => 'patch', 'action' => 'deviceRepairPatch'],
    'alarm_alert'                 => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'mobile_changeleftmenu'       => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'alarmnow'                    => ['control' => 'server', 'action' => 'backendApiList'],
    'devicefind'                  => ['control' => 'server', 'action' => 'mobileManagerList'],
    'deviceinfotip'               => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'asclist'                     => ['control' => 'server', 'action' => 'mobileManagerList'],
    'asc_set_access'              => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'dynamiccode'                 => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'get_asc_set'                 => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'device_audit_sub'            => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'device_audit'                => ['control' => 'server', 'action' => 'mobileManagerList'],
    'resource_apply_list'         => ['control' => 'server', 'action' => 'backendApiList'],
    'doubleha'                    => ['control' => 'server', 'action' => 'mobileManagerApi'],
    'deviceconfig'                => ['control' => 'server', 'action' => 'mobileManagerApi'],
];

/* 老方式ajaxResult的兼容
{
    "status": "y",
    "info": "短信发送成功，请注意查收！"
}
*/
$GLOBALS['ROUTE']['json'] = [
    'login'              => true,
    'get_device_online'  => true,
    'send_sms'           => true,
];

/* 老方式phpdir/trade.php的兼容
*/
$GLOBALS['ROUTE']['oldJson'] = [
    'project_conf'              => true,
];

/* 老方式msg/code格式的兼容
__res = {
    "length": 3,
    "status": "0",
    "code": "-1",
    "msg": "不在NAT地址列表！"
}
*/
$GLOBALS['ROUTE']['msgcode'] = [
    'check_device_illegal' => true,
    'getvpninfo'           => true,
    'vpnupdevinfo'         => true,
];

/**
 * csrf白名单
 */
$GLOBALS['ROUTE']['csrfWhiteList'] = [
    'ScanController'     => ['qrcode' => true, 'index' => true, 'short' => true],
    'ServerController'   => ['imgcode' => true, 'start' => true, 'getQrCode' =>true, 'getQrCodeImg' =>true],
    'AuthController'     => ['other' => true],
    'DingtalkController' => ['user' => true, 'jump' => true, 'auth' => true],
    'FeishuController'   => ['user' => true, 'jump' => true],
    'WeworkController'   => ['user' => true, 'jump' => true],
    'GuestController'    => ['imgcode' => true],
    'SsoController'      => ['auth' => true],
    'SdkController'      => ['getConfig' => true],
    'ZtpGatewayController' => ['sdpErr' => true],
    'DeviceController' => ['envStatus' => true],
    'GatewayController' => ['setCookies' => true, 'getWatermark' => true, 'getForm' => true],
    'ZtpPolicyController' => ['resourceCheck' => true],
    'OTPController' => ['qrcode' => true, 'iosQrcode' => true, 'androidQrcode' => true, 'cancelActivation' => true],
    'SceneController' => ['getDeviceScene' => true],
    'MsepController' => ['deviceStatus' => true, 'virusChange' => true],
];

/**
 *  校验登录状态
 */
$GLOBALS['ROUTE']['CheckLogin'] = [
    'depart' => ['list' => true, 'tree' => true, 'default' => true],
];

/**
 * csrf时间戳严格校验
 */
$GLOBALS['ROUTE']['CheckSignTime'] = [
    'net' => ['cutoff' => true],
    'device' => ['uninstall' => true],
];
