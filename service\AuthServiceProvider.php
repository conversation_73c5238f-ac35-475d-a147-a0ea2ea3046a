<?php
/**
 * Description: 认证接口暴露
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: AuthServiceProvider.php 175145 2022-05-06 07:53:42Z huyf $
 */

use Services\Auth\Services;
use Services\Device\Services\DeviceSceneService;

class AuthServiceProvider extends BaseServiceProvider
{
    /**
     * 初始化认证服务
     *
     * @param $params
     *
     * @return Object|Services\AdAutoAuthService|Services\DingTalkAuthService|Services\FingerAuthService|Services\GuestAuthService|Services\MacAuthService|Services\MobileAuthService|Services\NoAuthService|Services\SelfGuestAuthService|Services\SmsAuthService|Services\UKeyAuthService|Services\UserAuthService|Services\WechatAuthService|Services\WeWorkAuthService|Services\FeiShuAuthService|Services\SsoAuthService
     * @throws Exception
     */
    public static function initAuthService($params)
    {
        $object = null;
        $getType = $params['servicePrefix'];
        $serviceClass = '\\Services\\Auth\\Services\\' . $getType . 'AuthService';
        try {
            $object = (new ReflectionClass($serviceClass))->newInstance($params);
        } catch (ReflectionException $e) {
            self::log('initAuthService:' . $e->getMessage());
            T(21120010);
        }
        return $object;
    }

    /**
     * 按照传入的顺序验证多个认证服务
     *
     * @param $authServers
     * @param $authParams
     * @param $authService Object|Services\BaseAuthService
     *
     * @return bool|object|array
     * @throws Exception
     */
    public static function validateRpcAuthServer($authServers, $authParams, $authService)
    {
        if (isset($authParams['password'])) {
            $authParams['password'] = Base64EnExt($authParams['password']);
        }
        $authParams['RequestIp'] = getRemoteAddress();
        $authParams['isMobile'] = IS_MOBILE;
        $data = ['authServers' => $authServers, 'authParams' => $authParams];
        $result = lib_yar::clients('auth', 'index', $data, '', 60000, 1);
        cutil_php_log(['RpcResult' => $result], 'net_auth');
        if (!empty($result['state'])) {
            $data = $result['data'];
            if (!isset($data['UserInfo'], $data['BusinessInfo'])) {
                T(21100002);
            }
            return $authService->getAuthInfo($data['UserInfo'], $data['BusinessInfo']);
        } else {
            $allMessage = $result['allMessage'] ?? '';
            throw new Exception($result['message'], $result['code'], new Exception($allMessage));
        }
    }

    /**
     * 获取认证结果
     * @param $session
     * @return mixed
     * @throws Exception
     */
    public static function getAuthResult($session)
    {
        if (!isset($session['Uuid'])) {
            T(21100002);
        }
        if (!empty($session['Uuid'])) {
            $UserInfo = AuthUserModel::getOne($session['Uuid'], 'auth');
        } else {
            $UserInfo = ['RoleID' => 0];
        }
        if (empty($UserInfo)) {
            T(21120061);
        }
        $onlineDevice = NacOnLineDeviceModel::getOne($session['DeviceID'], 'user');
        if (empty($onlineDevice)) {
            T(21126023);
        }

        $BusinessInfo = [
            'SendMessage' => '',
            'SyncToDevice' => false,
            'FactorAuth' => false,
            'AuthType' => $onlineDevice['AuthType'],
            'BeforeAuthType' => $onlineDevice['AuthType'],
            'UserName' => $onlineDevice['UserName']
        ];
        $params = [];
        $params['servicePrefix'] = 'Base';
        $authService = AuthServiceProvider::initAuthService($params);
        $return = $authService->getAuthInfo($UserInfo, $BusinessInfo);
        $return['Token'] = $session['Token'];
        return $return;
    }

    /**
     * rpc回调passport
     *
     * @param $params
     *
     * @return bool|object|array
     * @throws Exception
     */
    public static function callbackRpcAuthServer($params)
    {
        if (!isset($params['servicePrefix'])) {
            T(21120010);
        }
        $result = lib_yar::clients('auth', 'callback', $params);
        cutil_php_log(['RpcResult' => $result], 'net_auth');
        if (!empty($result['state'])) {
            return $result['data'];
        } else {
            $allMessage = $result['allMessage'] ?? '';
            throw new Exception($result['message'], $result['code'], new Exception($allMessage));
        }
    }

    /**
     * 解析敲门参数为认证参数
     *
     * @param $AuthData
     * @param $deviceId
     *
     * @return array
     * @throws Exception
     */
    public static function parseFwknopData($AuthData, $deviceId): array
    {
        $Data = hlp_common::parseFwknopData($AuthData);
        $type = $Data[0] ?? '';
        $servicePrefix = $GLOBALS['AUTH_CONFIG']['TypeService'][$type] ?? $type;
        $authService = self::initAuthService(['servicePrefix' => $servicePrefix, 'deviceId' => '']);
        return $authService->getFwknopData($Data);
    }

    /**
     * 获取信息
     *
     * @param $routeType
     * @param $managerIp
     * @return mixed
     * @throws Exception
     */
    public static function getOtherUserInfo($routeType, $managerIp)
    {
        include_config('auth');
        $otherServices = $GLOBALS['AUTH_CONFIG']['OtherService'];
        if (!isset($otherServices[$routeType])) {
            return lib_otheruser::getInfo($routeType, $managerIp);
        }

        $servicePrefix = $otherServices[$routeType];
        $authService = self::initAuthService(['servicePrefix' => $servicePrefix]);
        return $authService->main();
    }

    /**
     * 根据用户ID获取用户名
     *
     * @param $userId
     *
     * @return bool
     */
    public static function getUserNameById($userId)
    {
        if (empty($userId)) {
            return false;
        }

        $user = AuthUserModel::getOne($userId, 'base');

        if (empty($user)) {
            return false;
        }

        return $user['UserName'];
    }

    /**
     * 根据场景ID获取信息
     *
     * @param $sceneID
     *
     * @return array
     */
    public static function getSceneInfoBySceneID($sceneID): array
    {
        $sceneService = new DeviceSceneService();
        $sceneInfo = $sceneService->getSceneInfoById($sceneID);
        $sceneInfo['CheckPatch'] = $sceneService->patchIsInPolicy((int)$sceneInfo['PolicyID']);
        return $sceneInfo;
    }

    /**
     * 根据设备ID获取场景以及相关信息（需要查询设备相关数据进行场景匹配）
     *
     * @param $deviceID
     *
     * @return array
     */
    public static function getSceneInfoByDeviceID($deviceID, $userType = ''): array
    {
        $sceneService = new DeviceSceneService();
        return $sceneService->getDevSceneInfo($deviceID, $userType);
    }

    /**
     * 获取设备在线信息
     *
     * @param $deviceid
     *
     * @return array
     * @throws Exception
     */
    public static function getOnlineInfo($deviceid): array
    {
        $onlineDevice = NacOnLineDeviceModel::getOne($deviceid, 'user');
        if (empty($onlineDevice['SceneID'])) {
            T(21120036);
        }
        $SceneData = self::getSceneInfoBySceneID($onlineDevice['SceneID']);
        $onlineDevice = array_merge($onlineDevice, $SceneData);
        //小助手保活时间
        $AuthInterval = $SceneData['AuthInterval'];
        $aIntTime = strtotime($onlineDevice['LastHeartTime']);
        $aNowTime = time();
        $aHowLongNoHeart = $aNowTime - $aIntTime;
        if ($aHowLongNoHeart > ($AuthInterval * 60)) {
            T(21120035);
        }
        return $onlineDevice;
    }

    /**
     * 已自动审核的设备需要重新审核则返回未注册的状态
     *
     * @param int $sceneID 当前场景id
     * @param int $deviceId 设备id
     *
     * @return int  1:需要 0:不需要
     */
    public static function getIsAgainReg(int $sceneID, int $deviceId): int
    {
        $sceneInfo = self::getSceneInfoBySceneID($sceneID);
        $ClientCheckInfo = DictModel::getAll("CLIENTCHECK");
        $devIsAgainReg = DeviceAuditLogModel::getJoinComputer($deviceId);
        $devIsAgainReg = !empty($devIsAgainReg) ? 0 : 1;
        if ((int)$ClientCheckInfo['reAudit'] === 1 && (int)$sceneInfo['IsNeedAuto'] === 1 && $devIsAgainReg) {
            return 1;
        }
        return 0;
    }

    /**
     * 获取用户
     *
     * @param $userType
     * @param $userName
     * @param $Column string 查询字段
     *
     * @return array|bool
     */
    public static function getUserByUserName($userType, $userName, $Column = 'one')
    {
        if (empty($userName)) {
            return false;
        }

        return AuthUserModel::getOneByUserName($userType, $userName, $Column);
    }


    /**
     * 获取用户
     *
     * @param $deviceId
     *
     * @return string  userType 1,2,'' 三种情况
     */
    public static function getDeviceUserType($deviceId)
    {
        $userType = '';
        if (empty($deviceId)) {
            return $userType;
        }
        $scene = RelationComputerModel::getOne($deviceId, 'scene');
        if ($scene['SceneID']) {
            $sceneInfo = SceneModel::getOne($scene['SceneID'], 'info');
            $userType = $sceneInfo['UserType'] ?? '';
        }
        return $userType;
    }

    /**
     * 是否必须修改密码
     *
     * @param $username
     *
     * @return bool
     */
    public static function isMustChangePasswrod($username)
    {
        if (empty($username)) {
            return false;
        }

        $userinfo = self::getUserByUserName('User', $username, 'auth');

        if (!empty($userinfo)) {
            return $userinfo['MustChange'];
        }

        return false;
    }

    /**
     * 获取微信状态
     *
     * @param $ip
     * @param $otheruserid
     *
     * @return string c关闭 o开启 a自动认证
     */
    public static function getWechatStatus($ip, $otheruserid)
    {
        //获取微信配置
        $wechatconfig = DictModel::getAll("WeChatConfig");
        $wechat = "c";//c 关闭 o开启 a自动认证
        //是否开启微信推广
        if ($wechatconfig['openwechat'] == '1') {
            if (VpnIpRange($ip, $wechatconfig['clientips'], '|@@|')) {
                $wechat = 'o';
            }
            if ($otheruserid) {
                // 是否从微信接口进入 识别当天之内的id，防止没有通过微信接口非法进入页面
                $res = OtherUserModel::getSingle(['ID' => $otheruserid, 'LikeLastUpTime' => date("Y-m-d")]);
                if ($res['subscribe'] == '1') {
                    $wechat = 'a';
                }
            }
        }
        return $wechat;
    }

    /**
     * 策略变更通知
     *
     * @param $deviceIds
     */
    public static function tacticsChangeSel($deviceIds): void
    {
        if (empty($deviceIds)) {
            return;
        }
        $dataList = NacOnLineDeviceModel::getRolePolicyList($deviceIds);
        $deviceIds = is_array($deviceIds) ? $deviceIds : [$deviceIds];
        if (!empty($dataList)) {
            $insertParams = [];
            foreach ($dataList as $row) {
                $PolicyID = $row['PolicyID'] ?? 0;
                $insertParams[] = ['DeviceID' => $row['DeviceID'], 'RoleID' => $row['RoleID'], 'PolicyID' => $PolicyID];
            }
            PolicyChangeModel::delete(['InDeviceID' => $deviceIds]);
            PolicyChangeModel::insertPatch($insertParams);
        }
    }

    /**
     * 认证后，同步账号信息到设备
     *
     * @param $deviceId int
     * @param $userinfo array
     *
     */
    public static function syncDeviceInfo($deviceId, $userinfo)
    {
        if (empty($deviceId)) {
            return;
        }

        $dparams = ['UserName' => $userinfo['UserName'], 'Tel' => $userinfo['Tel'], 'EMail' => $userinfo['EMail']];
        DeviceModel::update($deviceId, $dparams);
    }

    /**
     * 获取钉钉/飞书/企业微信 服务器配置
     * @param $type
     * @param string $ip
     * @return array|mixed
     */
    public static function getAuthDomainConfig($type, $ip = '')
    {
        $ip = empty($ip) ? getRemoteAddress() : $ip;
        $ServerConfig = AuthDomainModel::getAll(['Type' => $type]);
        foreach ($ServerConfig as $item) {
            $serverArr = json_decode($item['ServerConfig'], true);
            $authIpList = explode('|', $item['IPSegment']);
            if (isset($serverArr['version']) && $serverArr['version'] == 'v2') {
                // 简化钉钉参数
                $serverArr['appIDScanner'] = $serverArr['appkey'];
            }
            foreach ($authIpList as $ips) {
                $ip_arr = explode('-', $ips);
                $live = FindInIP($ip, $ip_arr[0], $ip_arr[1]);
                if ($live === 1) {
                    $serverArr['ServerID'] = (int)$item['ID'];
                    return $serverArr;
                }
            }
        }
        return [];
    }

    /**
     * 检查场景是否变化
     *
     * @param $deviceId
     * @param $sceneInfo
     */
    public static function checkLoginScene($deviceId, $sceneInfo): void
    {
        if (empty($sceneInfo['SceneID'])) {
            return;
        }
        $session = LoginServiceProvider::isLogin($deviceId, 'scene');
        if (empty($session) || (int)$session['SceneID'] === (int)$sceneInfo['SceneID']) {
            return;
        }
        $sceneService = new DeviceSceneService();
        $sceneData = $sceneService->getSceneInfoById((int)$sceneInfo['SceneID']);
        $sessionData = [
            'PolicyID' => $sceneData['PolicyID'],
            'LifeTime' => $sceneData['AuthInterval'] * 60 + LoginServiceProvider::MISTAKE_TIME,
            'SceneID' => $sceneData['SceneID']
        ];
        SessionRedis::setOne($session['Token'], $sessionData);
        self::changeGuestRegion($deviceId,$sceneData);

    }

    /**
     * 处理来宾入网，非审核非接待并且审核状态为已审核的安全域修改
     * @param $deviceId
     * @param $sceneInfo
     * @return void
     */
    public static function changeGuestRegion($deviceId, $sceneInfo)
    {
        try {
            // 新获取到的场景为来宾场景时才进行操作
            if ($sceneInfo['UserType'] == "2") {
                $guestOnLineData = GuestOnLineDeviceModel::getSingle(['DeviceID' => $deviceId]);
                if (!empty($guestOnLineData)) {
                    $cond = [
                        'UserID' => $guestOnLineData['UserID'],
                        'DeviceID' => $deviceId,
                        'GreetType' => 'empty',
                        'AuditType' => '',
                        'Status' => 1,
                    ];
                    $applyData = GuestSelfApplyModel::getSingle($cond);
                    if (!empty($applyData)) {
                        $passIpRegion = $sceneInfo['PassIpRegion'];
                        //     修改安全域
                        $ipListID = NetServiceProvider::getOtherRegionID($passIpRegion);

                        $guestOnlineParams = [
                            'IplistID' => $ipListID,
                            'RegionIDs' => $passIpRegion
                        ];
                        GuestOnLineDeviceModel::updateById('', $deviceId, $guestOnlineParams);
                        self::log("更新设备ID" . $deviceId . "的安全域为" . $passIpRegion . "_" . $ipListID);
                        // 调用set_client_one安全域才能生效
//                        $deviceInfo = DeviceModel::getJoinComputer($deviceId, 'gateInfo');
//
//                        $aResult = DevASCInfoModel::getAll();
//                        foreach ($aResult as $v) {
//                            $cmd = PATH_ASM . "sbin/set_client_one {$deviceInfo['GateIP']} {$ipListID} {$deviceInfo['MAC']} ";
//                            cutil_exec_no_wait($cmd, 10, $v['LinkIP']);
//                        }
                    }
                }
            }
        } catch (Exception $exception) {
            self::log($exception->getMessage());
        }
    }
    /**
     * 校验双因子账号是否与当前登录用户绑定
     * @param $data
     * @param $params
     * @throws Exception
     */
    public static function checkfactorAuth($data, $params)
    {
        if (in_array($params['factorType'], ['DingTalk', 'WeWork', 'FeiShu'], true)) {
            if (!empty($params['userid']) && $params['userid'] != $data['UserID']) {
                T(21120060);
            }
        }
    }

    /**
     * 根据设备ID批量回收用户会话
     * @param $deviceIds
     * @param $remark
     * @return bool
     */
    public static function recoveryUserByDeviceIds($deviceIds, $remark)
    {
        $list = NacOnLineDeviceModel::getList(['DeviceIds' => $deviceIds]);
        if (empty($list)) {
            return false;
        }
        $historyList = [];
        foreach ($list as $onlineDevice) {
            $historyList[] = self::getOnlineUserHistory($onlineDevice);
            self::recoveryToken($onlineDevice['Token'], $remark, $onlineDevice['DeviceID']);
            LoginServiceProvider::cutoff($onlineDevice['DeviceID'], $onlineDevice['Token']);
        }
        NacOnLineUserHistoryModel::insertPatch($historyList);
        return NetServiceProvider::offlineDevice($deviceIds);
    }

    /**
     * 获取要插入的数据
     * @param $onlineDevice
     * @return mixed
     */
    public static function getOnlineUserHistory($onlineDevice)
    {
        $onlineHistory = $onlineDevice;
        $session = SessionRedis::getOne($onlineDevice['Token'], 'hisotry');
        $onlineHistory['TrueNames'] = $session['TrueNames'] ?? '';
        $onlineHistory['DepartName'] = $session['DepartName'] ?? '';
        $onlineHistory['IP'] = $session['IP'] ?? '';
        $onlineHistory['GateIP'] = $session['ConnectIp'] ?? '';
        $onlineHistory['Mac'] = $session['Mac'] ?? '';
        $onlineHistory['UserType'] = $session['Type'] ?? '';
        $OsType = $session['OsType'] ?? null;
        $Client = $session['Client'] ?? null;
        if (!empty($session)) {
            $onlineHistory['Client'] = SystemServiceProvider::getClientTypeId($OsType, $Client);
        } else {
            $onlineHistory['Client'] = 0;
        }
        unset($onlineHistory['InsertTime'], $onlineHistory['InsertTime']);
        $onlineHistory['OfflineTime'] = 'now()';
        return $onlineHistory;
    }

    /**
     * 通过token回收
     * @param $token
     * @param $remark
     * @return bool
     */
    public static function recoveryUserByToken($token, $remark)
    {
        $onlineDevice = NacOnLineDeviceModel::getSingle(['Token' => $token]);
        return self::recoveryUser($onlineDevice, $remark);
    }

    /**
     * 回收用户信息
     * @param $onlineDevice
     * @param $remark
     * @return bool
     */
    public static function recoveryUser($onlineDevice, $remark)
    {
        $token = $onlineDevice['Token'] ?? '';
        // 用户已离线
        if (empty($token)) {
            self::log("{$token} is had offline.");
            return false;
        }
        $userHistory = self::getOnlineUserHistory($onlineDevice);
        NacOnLineUserHistoryModel::insert($userHistory);
        self::recoveryToken($token, $remark, $onlineDevice['DeviceID']);
        LoginServiceProvider::cutoff($onlineDevice['DeviceID'], $onlineDevice['Token']);
        return true;
    }

    /**
     * 回收其他信息
     * @param $token
     * @param $remark
     * @param int $deviceId
     */
    public static function recoveryToken($token, $remark, $deviceId = 0)
    {
        if (!getmoduleregist('11')) {
            return;
        }
        self::log("recoveryToken {$token}");
        ModuleOnlineRedis::delColumn($token, ['WebOnline', 'Users', 'Proxy']);
        OnlineZtpUserRedis::delColumn('Users', $token);
        if ($remark != 'adminRecoveIp') {
            VirPoolServiceProvider::delOneVirIpOnLine(['Token'=>$token,'flag'=>$remark]);
        }
        //解决注销后，再立刻登录，由于有消息延迟，导致登录后路由被清除的问题
        if (!in_array($remark,["reAuthRecovery",'Cutoff']) && !empty($deviceId)) {
            //不是认证的时候回收的则通知客户端
            NoticeServiceProvider::noticeTokenRecovery($deviceId);
        }
    }

    /**
     * 设置session状态
     * @param $token
     * @param string $status
     */
    public static function setSessionStatus($token, $status = SESSION_STATUS_OK)
    {
        SessionRedis::setOne($token, ["SessionStatus" => $status]);
    }

    /**
     * 使用登录token获取用户信息
     * @param $token
     * @return false|mixed
     */
    public static function getUserInfoByToken($token)
    {
        if (empty($token)) return false;
        $onlineInfo = NacOnLineDeviceModel::getSingle(['Token' => $token]);
        if ($onlineInfo) {
            $uid = $onlineInfo['UserID'];
            return AuthUserModel::getOne($uid, 'sdc');
        }
        return false;
    }

    /**
     * 校验是否需要资源配置的  可信设备要求认证
     * @param $deviceId
     * @return bool
     * @throws Exception
     */
    public static function checkTrustDevResourceIsNeedAuth($deviceId): bool
    {
        $configData = ResourceServiceProvider::getConfig();
        if ($configData && (int)$configData['trustNoAuth'] === 1) {
            $isTrustInfo = RelationComputerModel::getOne($deviceId, 'isTrust');
            if (!empty($isTrustInfo) && (int)$isTrustInfo['IsTrustDev'] === 1) {
                return false;
            }
        }
        return true;
    }
}
