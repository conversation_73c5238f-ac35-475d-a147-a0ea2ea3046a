<?php
/**
 * Description: 相关的redis key 需要续期的逻辑可以处理在这里
 * User: <EMAIL>
 * Date: 2022/7/19 17:07
 * Version: $Id$
 */

/* 定义关键常量 */
define('PATH_CUR', __DIR__);

/** 初始化 */
require PATH_CUR . '/../init.php';


class CacheRedisUpdateExpired
{
    /**
     * 执行
     * @throws Exception
     */
    public function handle(): void
    {
        echo 'CacheRedisUpdateExpired start:' . PHP_EOL;
        //循环所有在线用户，对应的userinfo key 是否续期
        LoginServiceProvider::updateUserInfoLifeTime();
        cutil_php_log("CacheRedisUpdateExpired handle finish!", 'cronb');
    }
}

try {
    $obj = new CacheRedisUpdateExpired();
    $obj->handle();
} catch (Exception $e) {
    echo "code: " . $e->getCode() . ", message: " . $e->getMessage();
}
