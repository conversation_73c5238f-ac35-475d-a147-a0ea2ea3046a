<?php
/**
 * Description: 认证公共
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: Auth.php 163065 2021-11-26 03:30:34Z duanyc $
 */


$GLOBALS['LANG'][21120] = [
    21120001 => 'Authentication failed, user has expired!',
    21120002 => 'The current authenticated user has been deactivated, please contact the administrator in time!',
    21120003 => 'The device does not exist. Please refresh the page and try again!',
    21120004 => 'The authentication is successful!',
    21120005 => 'Authentication failed, username or password cannot be empty!',
    21120006 => "\"设备ID:{DeviceID},认证方式：{AuthType},用户名:{UserName},角色ID:'0',认证IP:{IP},认证MAC:{MAC},交换机IP:{SwitchIP},交换机端口:{SwitchPort},认证码:{AuthCode},认证是否成功:失败,认证失败原因:{FailReason}\"",                                   // 上报第三方，不翻译
    21120007 => 'Authentication failed, the device is bound to other users, and user [{userName}] is not allowed to authenticate!',
    21120008 => 'Authentication failed, user [{userName}] is bound to other devices, and authentication on this device is not allowed!',
    21120009 => "\"设备ID:{DeviceID},认证方式：{AuthType},用户名:{UserName},角色ID:{RoleID},备注:{Remark},认证IP:{IP},认证MAC:{MAC},交换机IP:{SwitchIP},交换机端口:{SwitchPort},认证码:{AuthCode}\"",                                                        // 上报第三方，不翻译
    21120010 => 'The authentication method does not exist!',
    21120011 => 'The interface does not exist!',
    21120012 => 'The interface has not yet been implemented!',
    21120013 => "The interface business has not yet been implemented!",
    21120014 => 'System user',
    21120015 => 'AD domain user',
    21120016 => 'LDAP user',
    ******** => 'Radius users',
    ******** => 'UKey users',
    ******** => 'Mac address user',
    ******** => 'Email users',
    ******** => 'Guest user',
    ******** => 'Username Password (Web)',
    ******** => 'Fingerprint authentication',
    ******** => 'SMS authentication',
    ******** => 'Dingding users',
    ******** => 'Enterprise WeChat users',
    ******** => 'SMS',
    ******** => 'Failed to get the character ID!',
    ******** => 'The device is quarantined!',
    ******** => 'The account has been used on {num} devices. Continued authentication will cause the terminal {name} [{ip}] to exit the authentication and lose access to the network.',
    ******** => "User [{username}] has been disconnected from another computer [{ip}]!",
    ******** => 'User [{user}] has logged in on another computer [{ip}], and your computer will be disconnected from the Internet later!',
    ******** => 'Please enter verification code!',
    ******** => 'Incorrect verification code!',
    ******** => 'The assistant keep-alive time has expired or there is no authentication',
    ******** => 'The information of this device on the server side has been updated, please re-authenticate and check the security!',
    ******** => 'According to the administrator\'s policy, mobile devices must pass the mobile web authentication, please open the browser to authenticate, thank you for your cooperation!',
    ******** => 'According to the administrator\'s policy, the device is not allowed to access the network, please use other methods to authenticate, thank you for your cooperation!',
    21120040 => 'According to the administrator policy, mobile devices must pass the mobile client authentication, please download the client for authentication, thank you for your cooperation!',
    21120041 => 'Not allowed authentication method!',
    21120042 => 'Flybook user',
    21120043 => 'Two factor authentication',
    21120044 => 'The verification code has not been entered for a long time, please re authenticate!',
    21120045 => 'Verification code is not entered for two factor authentication!',
    21120046 => 'The device [{curip}] has been offline on another computer [{IP}]. Your computer will be disconnected later!',
    21120047 => 'Ad domain server',
    21120048 => 'Email server',
    21120049 => 'LDAP server',
    ******** => 'RADIUS Server',
    ******** => 'Local server',
    ******** => 'Third party server',
    ******** => 'The device type is wrong. Please contact the administrator to confirm whether the type of this machine in the system is correct.',
    ******** => 'Not logged in!',
    ******** => 'Login expiration!',
    ******** => 'Binding failed, please click to join the network now and try again.',
    ******** => 'Binding failed, please log in again.',
    ******** => 'The binding failed, the current account has been bound.',
    ******** => 'The binding failed, the current account has been bound, please log in again.',
    ******** => 'The account of this authentication type is inconsistent with the account bound to the current user, and the two-factor authentication fails, please try again!',
    ******** => 'User does not exist!',
    ******** => 'Two factor authentication succeeded!',
    ******** => 'Guest access authentication failed. It is not within the validity period!',
    ******** => 'User [{user}] logs out your computer [{ip}], your computer will be disconnected later!',
    ******** => 'The current device visitor authentication scenario is unavailable',
    ******** => 'The host device [{ip}] goes offline to the guest device',
    ******** => 'The device disables guest',
];
