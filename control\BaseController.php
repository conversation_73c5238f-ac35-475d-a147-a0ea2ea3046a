<?php

/**
 * Description: 父控制器
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: BaseController.php 147146 2021-06-17 02:04:51Z duanyc $
 */
!defined('IN_INIT') && exit('Access Denied');

class BaseController
{
    // 成功错误信息
    public $errmsg = '';

    /**
     * 方法
     * @var string
     */
    public $method = '';

    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = [];

    /**
     * 公共判断
     * @throws Exception
     */
    public function __construct()
    {
        $this->method = $GLOBALS['AC'];
        // 默认限制使用POST提交
        if (empty($this->whiteList[$this->method])) {
            if (lib_request::$method != 'POST') {
                T(21100005);
            }
        }
    }

    /**
     * 增加一个redis并发锁
     * @throws Exception
     */
    protected function isLocked(string $key, int $expire = 1)
    {
        $this->method = $GLOBALS['AC'];
        //为避免极端情况下会出现锁设置过期时间失效，带个精确到分的时间放key后面
        $key = md5($key . ":" . $this->method . ":" . date("YmdHi"));
        //判断当前请求是否存在
        if (!\lib_redis::setnx('LOCK_', $key, time(), $expire)) {
            T(21100006, ['message' => L(21100022)]);
        }
    }

    /**
     * 获取session字段
     *
     * @param string $key
     * @param string $default
     * @return string
     */
    protected function getSessionData($key = null, $default = ''): string
    {
        $token = LoginServiceProvider::getLoginToken();
        if (!empty($token)) {
            $session = SessionRedis::getOne($token, 'cookie');
            if (empty($key)) {
                return $session;
            }
            return $session[$key] ?? $default;
        }
        return $default;
    }

    /**
     * 更新维护对应内外网到session中
     * @param  $deviceId
     * @return bool
     */
    protected function updateIsInternal($deviceId = 0): bool
    {
        // 先通过头部获取token
        $token = LoginServiceProvider::getLoginToken();
        if (empty($token)) {
            // 通过设备ID获取到对应的token
            $token = DeviceTokenRedis::getOne($deviceId);
        }
        if (!empty($token)) {
            // 通过设备ID查询Session，如果存在则判断对应的内外网
            $session = SessionRedis::getOne($token, 'policy');
            if (!isset($session['Uuid'])){
                return true;
            }

            if ((isset($session['Client']) && $session['Client'] == 0)  || !isset($session['Client'])){
                $deviceInfo =  DeviceModel::getOne($deviceId,'installClient');
                if ($deviceInfo['InstallClient'] == 1){
                    SessionRedis::setOne($token, ['Client' => $deviceInfo['InstallClient']]);
                }
            }

            $isInternal = IS_INTERNAL ? 1 : 0;
            if (empty($session['IsInternal']) || $session['IsInternal'] != $isInternal) {
                //只有在session 中有用户的时候才计入redis
                if (!empty($session['Uuid'])) {
                    $isChang = SessionRedis::setOne($token, ['IsInternal' => IS_INTERNAL ? 1 : 0]);
                    if (!empty($isChang)) {
                        // 内外网切换时，触发一次权限计算
                        $powerService = ResourceServiceProvider::initResourceService('Power');
                        $powerService->setUserPower($token, 'updateIsInternal');
                        // 清除ASM:Sdp_Ini缓存
                        lib_redis::del("ASM:", "Sdp_Ini");
                    }
                }
            }
        }
        return true;
    }

}
