<?php

/**
 * Description: IP资源表
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: ResIPListModel.php 174806 2022-04-28 12:32:16Z duanyc $
 */

class ResIPListModel extends ResConfigListModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResIPList';
    public const PRIMARY_KEY = 'ResID';
    protected static $columns = [
        '*'       => '*',
        'list' => 'Icon,IP,Remark,Tel,Username,ResID,GateWayID,GateWayType,ControllType',
        'gateway' => 'DISTINCT GateWayID,GateWayType',
        'push' => 'IP,ResID,GateWayID,GateWayType',
        'power' => 'ResID,GateWayType,GateWayID,IP,Route',
        'group' => 'ResID,GateWayID,GateWayType',
        'client' => 'IP,ResID',
        'notice' => 'ResID',
        'one'  => 'ResID,IP,Route,Icon,Remark,ResMode,GateWayID,GateWayType,Agreement',
    ];

    /**
     * 插入
     *
     * @param array $key_values
     *
     * @return bool|int
     */
    public static function duplicate($key_values = [])
    {
        if (empty($key_values) || empty($key_values['ResID']) || empty($key_values['IP'])) {
            return false;
        }

        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $result = lib_database::duplicate($key_values, $table['name'], $table['index']);
        cutil_php_log(json_encode(['model_insert', $table, $key_values]), "model_{$table['name']}");

        if ($result) {
            return lib_database::insertId();
        }

        return $result;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }

        if (isset($cond['GateWayType'])) {
            $where .= "AND GateWayType = ".self::setData($cond['GateWayType']);
        }

        if (isset($cond['GateWayID'])) {
            $where .= "AND GateWayID = ".self::setData($cond['GateWayID']);
        }

        if (!empty($cond['NotInResID'])) {
            $where .= "AND ResID NOT IN (" . self::setArrayData($cond['NotInResID']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

}
