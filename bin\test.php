<?php
/**
 * Description: 测试
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: test.php 164922 2021-12-15 06:34:09Z duanyc $
 */

/* 定义关键常量 */
define('PATH_CUR', __DIR__);

/** 初始化 */
require PATH_CUR . '/../init.php';
try {
    //WorkerUdpSend('MessageToGw', 'getGwconfig', []);
    //WorkerUdpSend('MessageToGw', 'getResconfig', []);
    //WorkerUdpSend('MessageToCo', 'getPolicyconfig', []);
    //$resList = ResIPListModel::getList(['column' => 'list']);
    //WorkerUdpSend('MessageToGw', 'syncIpRes', ['ResList' => $resList]);
    //$res = PolicyServiceProvider::getAllPolicyGateway();
    //var_dump($res);
    $result = getmoduleregist(12);
    var_dump($result);
} catch (Exception $e) {
    var_export($e->getMessage());
}
