<?php

/**
 * Description: Msep相关接口
 * User: <EMAIL>
 * Date: 2024/04/08 10:32
 */

use Services\MsepServiceProvider;

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class MsepController extends BaseController
{
    protected $SDK;

    public function __construct()
    {
        parent::__construct();
        $cfgPath = PATH_ETC . 'csrf.ini';
        $cfgarr = @parse_ini_file($cfgPath);
        $enableCsrf = $cfgarr['enable'] ?? 0;
        if ($enableCsrf) {
            $config = ConfigServiceProvider::getDictAll("MSEP");
            if ($config['State'] == 0) { //检查是否联动
                T(21100002);
            }
            if ($config['Ip'] != $_SERVER['REMOTE_ADDR']) { //检查请求IP是否来自MSEP
                T(21100002);
            }
        }
        $this->SDK = new cls_msep();
    }

    /**
     * @description MSEP/TAM 安装状态上报接口
     * @return array
     * @throws Exception
     */
    public function deviceStatus()
    {
        $sign = request('Sign');
        $ts = request('Time');
        $deviceID = request('DeviceID');
        $MsepClient = request('MsepClient');
        $TamClient = request('TamClient');
        $verify = $this->SDK->verifySign($ts, $deviceID, $sign);
        if (!$verify) {
            T(21100002);
        }
        MsepServiceProvider::setupChange($deviceID, $MsepClient, $TamClient);
        return [];
    }

    /**
     * @description 杀毒扫描变动接口
     * @return array
     * @throws Exception
     */
    public function virusChange()
    {
        $sign = request('Sign');
        $ts = request('Time');
        $deviceID = request('DeviceID');
        $verify = $this->SDK->verifySign($ts, $deviceID, $sign);
        if (!$verify) {
            cutil_php_log("{$ts} MSEP call virusChange sign verify fail , deviceIds:$deviceID" . ",sign:" . $sign, 'msep');
            T(21100002);
        }
        MsepServiceProvider::virusChange($deviceID);
        return [];
    }
}
