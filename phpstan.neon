includes:
    - phpstan-baseline.neon

parameters:

    paths:
        - bin
        - control
        - helper
        - library
        - service

    # The level 8 is the highest level
    level: 5

    ignoreErrors:
        - '#Constant .* not found.#'
        - '#Parameter .* of function L expects string, int given.#'
        - '#Access to an undefined property Services.*.#'
        - '#Call to an undefined method.*.#'
        - '#Parameter .* of static method .* expects string, int given.#'
        - '#Call to an undefined static method .*.#'
        - '#Call to static method .*.#'
        - '#.* expects int, string given.#'
        - '#Right side of && is always true#'

    excludePaths:
        - service/*/Test/*
        - library/phpqrcode/*

    tmpDir: /tmp/phpstan/access-api

    checkMissingIterableValueType: false
    treatPhpDocTypesAsCertain: false
    reportUnmatchedIgnoredErrors: false
