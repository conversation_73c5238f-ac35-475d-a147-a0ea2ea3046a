<?php
/**
 * Description: 零信任允许操作系统策略项服务
 * User: <EMAIL>
 * Date: 2022/05/12 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowOSPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 获取设备类型
     * @return string
     * @throws \Exception
     */
    public function getOsType(): string
    {
        return $this->params['session']['OsType'] ?? '';
    }

    /**
     * 检查是否通过
     * @return bool
     * @throws \Exception
     */
    public function check(): bool
    {
        $OsType = $this->getOsType();
        return in_array($OsType, $this->params['Config']);
    }
}
