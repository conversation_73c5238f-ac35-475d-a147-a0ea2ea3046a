<?php
/**
 * Description: 零信任资源
 * User: <EMAIL>
 * Date: 2022/03/4 15:53
 * Version: $Id: ZtpResourceController.php 175039 2022-05-05 07:33:41Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/ZtpController.php";

class ZtpResourceController extends ZtpController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['diagnose' => true, 'getApplyResList' => true, 'getApplyResLogList' => true];

    public $nocheckZtpUser = ['ztpUserExceed' => true];
    public $nocheck = ['ztpUserExceed' => true];

    /**
     * 资源列表
     *
     * @return mixed
     * @throws Exception
     */
    public function list()
    {
        if (!getmoduleregist(11)) {
            T(21148036);
        }
        $ListType = request('listType', 'request', 0, 'int');
        $queryStr = request('queryStr', 'request', '', 'string');
        LoginServiceProvider::checkResult($this->deviceId);
        $ztpUserExceed = hlp_common::ztpUserExceed($this->session['Token']);
        // 只获取历史记录
        if ($ListType === 1) {
            $historyResLists = ResourceServiceProvider::getUserHistoryList($this->session, false, $queryStr);
            return ['historyResLists' => $historyResLists, 'ztpUserExceed' => $ztpUserExceed];
        }
        // 只获取收藏记录
        if ($ListType === 2) {
            $collectLists = ResourceServiceProvider::getUserCollectList($this->session, false, $queryStr);
            return ['collectLists' => $collectLists, 'ztpUserExceed' => $ztpUserExceed];
        }
        //只获取可以申请的资源列表
        $resApplyList = ResourceServiceProvider::getUserApplyResourceList($this->session['Token'], $queryStr, 0, 0);
        if ($ListType === 3) {
            return ['applyLists' => $resApplyList, 'ztpUserExceed' => $ztpUserExceed];
        }
        $ResList = [];
        $return = ResourceServiceProvider::getUserResourceList($this->session['Token'], $ResList, $queryStr);
        $return['historyResLists'] = ResourceServiceProvider::getUserHistoryList($this->session, $ResList, $queryStr);
        $return['collectLists'] = ResourceServiceProvider::getUserCollectList($this->session, $ResList, $queryStr);
        $return['applyLists'] = $resApplyList;
        $return['urlPrefix'] = URL_BASE;
        $return['ztpUserExceed'] = $ztpUserExceed;
        $return['IsInternal'] = $this->session['IsInternal'];
        return $return;
    }

    /**
     * 检查资源状态
     *
     * @return mixed
     * @throws Exception
     */
    public function checkStatus()
    {
        $ResID = request('resId', 'request', 0, 'int');
        hlp_check::checkEmpty($ResID);
        $Resource = $this->checkResource($ResID);
        if (isset($Resource['AccessTypeID']) && $Resource['AccessTypeID'] == ACCESS_TYPE_CUSTOM_APP && (!IS_CLIENT || OS_TYPE !== OSTYPE_WINDOWS)) {
            T(21148052);
        }
        $this->errmsg = L(21148005);
        return [];
    }

    /**
     * 获取资源访问地址
     *
     * @return mixed
     * @throws Exception
     */
    public function accessUrl()
    {
        $ResID = request('resId', 'request', 0, 'int');
        $firsturl = request('firsturl', 'request');
        hlp_check::checkEmpty($ResID);
        //避免绕过checkStatus检查，获取到accessUrl
        $this->checkResourcePolicy($ResID);
        $Resource = $this->checkResource($ResID);
        if (isset($Resource['AccessTypeID']) && $Resource['AccessTypeID'] == ACCESS_TYPE_CUSTOM_APP && (!IS_CLIENT || OS_TYPE !== OSTYPE_WINDOWS)) {
            T(21148052);
        }
        $winRunRemote = ResourceServiceProvider::isWinRunRemote($Resource);
        $accessUrl = AgentServiceProvider::getAccessUrl($this->session['LifeTime'], $this->session['Token'], $Resource, $firsturl);
        $resData = array();
        $resData['winRunRemote'] = $winRunRemote;
        $resData['accessUrl'] = $accessUrl;
        $resData['resType'] = $Resource['ResType'];
        if (isset($Resource['AccessTypeID']) && $Resource['AccessTypeID'] == ACCESS_TYPE_CUSTOM_APP) {
            $resData['GrantType'] = $Resource['GrantType'];
            $resData['Content'] = Base64EnExt($Resource['Content']);
        } elseif ($Resource['ResType'] == ResourceServiceProvider::RES_TYPE_REMOTE_APP) {
            $remoteInfo = RemoteServiceProvider::createRemoteUser($this->userId, $Resource, $winRunRemote);
            $resData = array_merge($resData, $remoteInfo);
        }
        return $resData;
    }

    /**
     * 记录资源访问日志
     *
     * @return mixed
     * @throws Exception
     */
    public function recordLog()
    {
        $ResID = request('resId', 'request', 0, 'int');
        hlp_check::checkEmpty($ResID);
        $Resource = ResourceServiceProvider::getResource($ResID);
        if (empty($Resource)) {
            T(21148002);
        }
        $Res = ResourceServiceProvider::addAccessLog($this->userId, $ResID ,$Resource, $this->session);
        if (empty($Res)) {
            T(21148007);
        }
        return [];
    }


    /**
     * 资源收藏
     *
     * @return mixed
     * @throws Exception
     */
    public function collect()
    {
        $ResID = request('resId', 'request', 0, 'int');
        hlp_check::checkEmpty($ResID);
        $collect = ResourceServiceProvider::getUserCollect($this->userId, $ResID);
        if (!empty($collect)) {
            T(21148008);
        }
        $res = ResourceServiceProvider::collectResource($this->userId, $ResID);
        if (empty($res)) {
            T(********);
        }
        return [];
    }

    /**
     * 取消收藏资源
     *
     * @return mixed
     * @throws Exception
     */
    public function cancelCollect()
    {
        $ResID = request('resId', 'request', 0, 'int');
        hlp_check::checkEmpty($ResID);
        $collect = ResourceServiceProvider::getUserCollect($this->userId, $ResID);
        if (empty($collect)) {
            T(********);
        }
        $res = ResourceServiceProvider::cancelCollectResource($this->userId, $ResID);
        if (empty($res)) {
            T(********);
        }
        return [];
    }

    /**
     * 获取资源的账号（表单代填方式)
     *
     * @return mixed
     * @throws Exception
     */
    public function getAccount()
    {
        $ResID = request('resId', 'request', 0, 'int');
        hlp_check::checkEmpty($ResID);
        return AgentServiceProvider::getResourceAccount($this->userId, $ResID);
    }

    /**
     * 保存资源的账号（表单代填方式)
     *
     * @return mixed
     * @throws Exception
     */
    public function saveAccount()
    {
        $ResID = request('resId', 'request', 0, 'int');
        $UserName = request('username', 'request', '', 'string');
        $Password = request('password', 'request');
        hlp_check::checkEmpty($ResID);
        $RUserName = Base64DeExt($UserName);
        $RPassword = Base64DeExt($Password);
        if (empty($UserName) || empty($Password) || empty($RUserName) || empty($RPassword)) {
            T(********);
        }
        $res = AgentServiceProvider::saveResourceAccount($this->userId, $ResID, $RUserName, $RPassword);
        if (empty($res)) {
            T(********);
        }
        return [];
    }

    /**
     * 资源诊断
     * @return mixed
     * @throws Exception
     */
    public function diagnose()
    {
        $address = request('address', 'request');
        hlp_check::checkEmpty($address);
        $data = [];
        $powerService = ResourceServiceProvider::initResourceService('Power');
        $resCheckData = $powerService->checkResPrivilege($this->session, $address);
        $data['ResPrivilege'] = $resCheckData['returnArr'] ?? '';

        //根据权限返回资源详情获取对应的网关信息
        $resData = $resCheckData['resData'] ?? [];
        $agentService = ResourceServiceProvider::initResourceService('Agent');
        $data['ResConnection'] = $agentService->checkResConnection($address, $resData);
        $data['ResName'] = ['ResID' => $resData['ResID'] ?? '', 'ResName' => $resData['ResName'] ?? ''];
        return ['ASM' => $data];
    }

    /**
     * 资源策略检查给前端调用
     * @return mixed
     * @throws Exception
     */
    public function policyCheck()
    {
        try {
            $ResID = request('resId', 'request', 0, 'int');
            $Session = LoginServiceProvider::getSessionInfo($this->session['Token'], 'policy');
            $Resource = ResourceServiceProvider::getResource($ResID);
            if (empty($Resource)) {
                T(21148002);
            }
            $PolicyID = PolicyServiceProvider::getPolicyID($ResID);
            if (!empty($PolicyID)) {
                // 检查资源的策略
                PolicyServiceProvider::checkPolicy($Session, $PolicyID);
            } else {
                PolicyServiceProvider::checkNoPolicy($Session, [$ResID]);
            }
        } catch (Exception $e) {
            $message = $e->getMessage();
            PolicyServiceProvider::log("Error: " . $message);
            LoginServiceProvider::setUserErrMessage("0x0018", $message, $this->deviceId);
            throw $e;
        }
        return [];
    }

    /**
     * 客户端获取可以资源申请列表
     * @params groupResID:资源组ID，queryStr：搜索字符串
     * @return array
     * @throws Exception
     */
    public function getApplyResList(): array
    {
        $groupResID = request('groupResID', 'request', 0, 'int');
        $queryStr = request('queryStr', 'request', '', 'string');
        $isReturnGroup = request('isReturnGroup', 'request', 0, 'int');
        //查询当前状态为非下架，开启允许申请的资源列表
        return ResourceServiceProvider::getUserApplyResourceList($this->session['Token'], $queryStr, $groupResID, $isReturnGroup);
    }

    /**
     * 客户端获取用户资源申请日志记录列表
     *
     * @return array
     * @throws Exception
     */
    public function getApplyResLogList(): array
    {
        $start = request('start', 'request', 0, 'int');
        $limit = request('limit', 'request', 20, 'int');
        //查询当前用户申请记录
        return ResourceServiceProvider::getResApplyList($this->userId, -1, $start, $limit);
    }

    /**
     * 客户端资源申请
     *
     * @return mixed
     * @throws Exception
     */
    public function apply()
    {
        //验证申请内容，字段是否超出最大等
        $resId = request('resId', 'request', 0, 'int');
        $remark = request('remark', 'request', '', 'string');
        $applyDays = request('apply_days', 'request', 0, 'int');
        return ResourceServiceProvider::apply($this->session['Token'], $resId, $remark, $applyDays);
    }


    /**返回是否超过零信任授权点数
     * @return bool
     * @throws Exception
     */
    public function ztpUserExceed()
    {
        if (!$this->session['Token']){
            return true;
        }
        $redisData = SessionRedis::getOne($this->session['Token'], 'ztpUserExceed');
        if (isset($redisData['ztpUserExceed'])) {
            return (bool)$redisData['ztpUserExceed'];
        }
        return hlp_common::ztpUserExceed($this->session['Token']);
    }
}
