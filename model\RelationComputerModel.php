<?php

/**
 * Description: 设备TRelationComputer表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: RelationComputerModel.php 170669 2022-03-09 06:14:05Z lihao $
 */

class RelationComputerModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TRelationComputer';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        'one'       => 'Mac,DeviceID',
        'auth'      => 'LastAuthID, CutOffStopTime, LastFaultTime, CheckResult,SceneID,ForbidGuest',
        'isTrust'   => "IsTrustDev,TrustStopTime",
        'safecheck' => "CheckResult,LastFaultTime",
        'check'     => "CheckTime",
        'scene'    => 'SceneID',
        'authId'    => 'LastAuthID',
        '*'         => '*',
    ];

    /**
     * 查询绑定关系
     *
     * @param $DeviceID
     *
     * @return array|bool
     */
    public static function getBindUser($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $where = "on TDevice.DeviceID=TRelationComputer.DeviceID WHERE TDevice.DeviceID = " . self::setData($DeviceID);
        $sql = "SELECT TRelationComputer.IsBindUser FROM TDevice left join TRelationComputer {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 查询设备最后认证方式
     *
     * @param $DeviceID
     *
     * @return array|bool
     */
    public static function getLastAuthType($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $where = " WHERE TRelationComputer.DeviceID = " . self::setData($DeviceID);
        $sql = "SELECT TNacAuthLog.AuthType FROM TRelationComputer " .
            " left join TNacAuthLog on TRelationComputer.LastAuthID = TNacAuthLog.RID " .
            $where;
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = " . self::setData($cond['DeviceID']);
        }

        if (isset($cond['DeviceIds'])) {
            $where .= "AND DeviceID IN (" . self::setArrayData($cond['DeviceIds']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
