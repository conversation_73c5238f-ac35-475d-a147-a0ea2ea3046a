<?php
/**
 * Description: 配置服务暴露
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: ConfigServiceProvider.php 172396 2022-03-30 12:41:23Z huyf $
 */
class ConfigServiceProvider extends BaseServiceProvider
{
    /**
     * 获取配置信息
     *
     * @param $Type
     * @param $ItemName
     *
     * @return mixed
     * @throws Exception
     */
    public static function getDictOne($Type, $ItemName)
    {
        if (empty($Type) || empty($ItemName)) {
            return false;
        }
        $result = DictModel::getOneItem($Type, $ItemName);
        if (empty($result)) {
            return false;
        }
        return $result['ItemValue'];
    }

    /**
     * 获取字典值
     *
     * @param string $type 对应字典表中的字段
     * @param string $itemname 对应字典表中的字段
     * @param string $defval 查询失败情况下默认返回的字符
     *
     * @return string 字典表中的键值
     */
    public static function getDictValue($type, $itemname, $defval = '')
    {
        $get_data = DictModel::getOneItem($type, $itemname);
        if (empty($get_data)) {
            return $defval;
        }
        if (array_key_exists('ItemValue', $get_data)) {
            return $get_data['ItemValue'];
        } else {
            return $get_data;
        }
    }

    /**
     * 修改配置数据
     *
     * @param string $Type
     * @param string $ItemName
     * @param string $ItemValue
     *
     * @return bool
     */
    public static function updateDictValue(string $Type, string $ItemName, string $ItemValue)
    {
        if (empty($Type) || empty($ItemName)) {
            return false;
        }

        return DictModel::updateItem($Type, $ItemName, $ItemValue);
    }

    /**
     * 保存配置数据
     *
     * @param string $Type
     * @param string $ItemName
     * @param string $ItemValue
     *
     * @return bool
     */
    public static function saveDict(string $Type, string $ItemName, string $ItemValue)
    {
        if (empty($Type) || empty($ItemName)) {
            return false;
        }
        $dict = DictModel::getOneItem($Type, $ItemName);
        if (!empty($dict)) {
            return DictModel::updateItem($Type, $ItemName, $ItemValue);
        }
        $data = ['Type' => $Type, 'ItemName' => $ItemName, 'ItemValue' => $ItemValue];
        return DictModel::insert($data);
    }

    /**
     * 检查是否有更新
     * @return bool
     */
    public static function isNeedUpdateDictCache()
    {
        $cache = DictHashRedis::getOne('Config', ['LastTime']);
        if (!empty($cache['LastTime'])) {
            $cond = ['column' => 'list'];
            $cond['Time'] = $cache['LastTime'];
            $count = DictModel::getCount($cond);
            if (!empty($count)) {
                return true;
            }
        }
        return true;
    }

    /**
     * 更细缓存
     * @param bool $type
     * @return bool
     * @throws Exception
     */
    public static function updateDictCache($type = false)
    {
        $cache = DictHashRedis::getOne('Config', ['LastTime']);
        $cond = ['column' => 'list'];
        if (!empty($type)) {
            $cond['Type'] = $type;
        } elseif (!empty($cache['LastTime'])) {
            $cond['Time'] = $cache['LastTime'];
            $count = DictHashRedis::getCount();
            if ($count < 90) {
                unset($cond['Time']);
            }
        }
        $list = DictModel::getList($cond);
        $dictList = [];
        if (!empty($list)) {
            foreach ($list as $row) {
                if (!isset($dictList[$row['Type']])) {
                    $dictList[$row['Type']] = [];
                }
                $dictList[$row['Type']][$row['ItemName']] = $row['ItemValue'];
            }
            foreach ($dictList as $type => $data) {
                DictHashRedis::setOne($type, $data);
            }
            DictHashRedis::setOne('Config', ['LastTime' => date('Y-m-d H:i:s')]);
        }
        DM('D_101', 'all');
        return true;
    }

    /**
     * 重设置为全量
     */
    public static function resetDictCache()
    {
        DictHashRedis::deleteOne('Config');
    }

    /**
     * 获取配置信息
     *
     * @param $Type
     *
     * @return mixed
     * @throws Exception
     */
    public static function getDictAll($Type)
    {
        if (empty($Type)) {
            return false;
        }
        return DictModel::getAll($Type);
    }

    /**
     * 存在异常IP
     *
     * @param $ip
     * @param $CLIENTCHECK
     *
     * @return int
     */
    public static function existExceptionIp($ip, $CLIENTCHECK)
    {
        $ipexist_list = 0;
        if ($ip != "" && $CLIENTCHECK['FastAuth'] == 0) {
            $ipstr = $CLIENTCHECK['exceptionip'];
            if ($ipstr != "") {
                $ipstr_arr1 = explode(",", $ipstr);
                foreach ($ipstr_arr1 as $str1) {
                    $iparr0 = explode(":", $str1);
                    $iparr = explode("-", $iparr0[0]);
                    $start = ipToNum($iparr[0]);
                    $Current = ipToNum($ip);
                    $stop = ipToNum($iparr[1]);
                    if ($Current >= $start && $Current <= $stop) {
                        $ipexist_list = 1;
                    }
                }
            }
        }
        return $ipexist_list;
    }

    /**
     * 获取注册配置
     * @param $isGuest
     * @return array
     */
    public static function getRegisterConfig($isGuest)
    {
        $config = [
            'RequireDep'          => 'tree', // 部门名称项
            'RequireUser'         => 'text', // 使用人项
            'RequireTel'          => 'text', // 联系电话项
            'RequireMail'         => 'text', // 电子邮箱项
            'RequireDevType'      => 'select', // 设备类型
            'RequirePos'          => 'tree', // 计算机所在地项
            'RequireExpand_1'     => '', // 定制字段1
            'RequireExpand_2'     => '', // 定制字段2
            'RequireExpand_3'     => '', // 定制字段3
            'RequireExpand_4'     => '', // 定制字段4
            'RequireExpand_5'     => '', // 定制字段5
            'RequireExpand_6'     => '', // 定制字段6
            'RequireExpand_7'     => '', // 定制字段7
            'RequireExpand_8'     => '', // 定制字段8
            'RequireExpand_9'     => '', // 定制字段9
            'RequireExpand_10'    => '', // 定制字段10
            'RequireRem'          => 'textarea', // 备注项
        ];
        $data = [];
        $dataEn = [];
        $dictResult = DictModel::getAll('CLIENTCHECK');
        foreach ($config as $column => $InputType) {
            $val = $dictResult[$column] ?? '';
            $valEn = $dictResult["{$column}_en"] ?? $val;
            $row = self::getRegisterConfigOne($isGuest, $column, $val, $InputType);
            if (!empty($row)) {
                $data[] = $row;
            }
            $rowEn = self::getRegisterConfigOne($isGuest, $column, $valEn, $InputType, $val);
            if (!empty($rowEn)) {
                $dataEn[] = $rowEn;
            }
        }
        return ['zh' => $data, 'en' => $dataEn];
    }

    /**
     * 获取注册配置
     * @param $isGuest
     * @param $column
     * @param $val
     * @param $InputType
     * @param $default
     * @return array|bool
     */
    private static function getRegisterConfigOne($isGuest, $column, $val, $InputType, $default = '')
    {
        $data = ['Column' => $column];
        $defaultArr = explode('|', $default);
        $valArr = explode('|', $val);
        if (strpos($column, 'RequireExpand') !== false) {
            $data['Select'] = $valArr[0];
            $data['Title'] = $valArr[1];
            $data['InputType'] = $valArr[2] ?? ($defaultArr[2] ?? '');
            $data['InputValue'] = $valArr[3] ?? ($defaultArr[3] ?? '');
            $data['Remark'] = $valArr[4] ?? ($defaultArr[4] ?? '');
            $data['Rule'] = $valArr[5] ?? ($defaultArr[5] ?? '');
        } else {
            $data['Select'] = $valArr[0];
            $data['Title'] = $valArr[1];
            $data['InputType'] = $InputType;
            $data['InputValue'] = '';
            $data['Remark'] = $valArr[2];
            $data['Rule'] = $valArr[3];
        }
        if (in_array($data['InputType'], ['select', 'checkbox', 'radio'])) {
            $data['InputValue'] = explode('####', $data['InputValue']);
        }
        // 过滤掉隐藏的
        if ($data['Select'] != '0' && $data['Select'] != '1') {
            return false;
        }
        return $data;
    }

    /*
 * 查询是否开启802.1x功能开关
 * return int
 * 0 未开启 ;1.已开启
 * */
    public static function getDot1x()
    {
        $dot1x = 0;
        $path = PATH_ETC . "/asm/asc/etc/tbridge_private.ini";
        if (!file_exists($path)) {
            return $dot1x;
        }
        $tb = read_inifile($path);
        return $tb['dot1x'] ?? $dot1x;
    }
}
