<?php

/**
 * Description: 零信任-下发后-控制中心本地执行（ASG网关下发给ASM控制中心）
 * TradeType为MessageToCo（同时支持MessageToCo和MessageToGw的也定义在该类）
 * User: <EMAIL>
 * Date: 2022/03/22 15:53
 * Version: $Id: CenterController.php 172377 2022-03-30 10:02:11Z duanyc $
 */

use Services\MsepServiceProvider;

require PATH_ROOT . "/webroot/rpc/AsmYarServer.php";

class ZtpCenterController extends AsmYarServer
{
    /**
     * 指定服务
     * @var string
     */
    protected $_server = 'ztpCenter';

    /**
     * 获取资源用户名密码配置
     * @param $params
     * @return array
     */
    protected function getResUserPassword($params)
    {
        $data = AgentServiceProvider::getResourceAccount($params['UserId'], $params['ResId']);
        return [
            'UserName' => $data['UserName'] ?? '',
            'Password' => $data['Password'] ?? ''
        ];
    }

    /**
     * 回收用户信息 goyar调用时，方法首字母必须大写
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function RecoveryUser($params)
    {
        if (empty($params['token']) && empty($params['deviceIds'])) {
            self::log('RecoveryUser token params is empty.');
            return false;
        }
        if (empty($params['remark'])) {
            self::log('RecoveryUser remark params is empty.');
            return false;
        }
        if (!empty($params['deviceIds'])) {
            return AuthServiceProvider::recoveryUserByDeviceIds($params['deviceIds'], $params['remark']);
        }
        return AuthServiceProvider::recoveryUserByToken($params['token'], $params['remark']);
    }

    /**
     * 资源记住密钥证书、密码接口
     * @param $params
     * @return array
     */
    protected function saveRememberPasswordCer($params)
    {
        if ($params['Password'] === 'noUpdate' || $params['Cer'] === 'noUpdate') {
            $info = $this->getRememberPasswordCer(['ResId' => $params['ResId'], 'UserId' => $params['UserId']]);
            if ($params['Password'] === 'noUpdate') {
                $params['Password'] = $info['password'] ?? '';
            }
            if ($params['Cer'] === 'noUpdate') {
                $params['Cer'] = $info['cer'] ?? '';
            }
        }
        GatewayServiceProvider::saveRememberPasswordCer($params);
        return ['code' => 0, 'msg' => 'success'];
    }

    /**
     * 获取记住密钥证书、密码配置
     * @param $params
     * @return array
     */
    protected function getRememberPasswordCer($params)
    {
        $data = RememberPasswordCerModel::getSingle(['ResId' => $params['ResId'], 'UserId' => $params['UserId']]);
        return [
            'account' => $data['Account'] ?? '',
            'password' => $data['Password'] ?? '',
            'cer' => $data['Cer'] ?? '',
            'cerName' => $data['CerName'] ?? '',
            'isRememberAccount' => $data['IsRememberAccount'] ?? '',
            'isRememberPassword' => $data['IsRememberPassword'] ?? '',
            'isRememberCer' => $data['IsRememberCer'] ?? '',
            'AutoLogin' => $data['AutoLogin'] ?? 0,
        ];
    }

    /**
     * 资源记住密码接口
     * @param $params
     * @return array
     */
    protected function saveRememberPassword($params)
    {
        if ($params['Password'] === 'noUpdate') {
            $info = $this->getRememberPassword(['ResId' => $params['ResId'], 'UserId' => $params['UserId']]);
            $params['Password'] = $info['password'] ?? '';
        }
        if (empty($params['Account']) && empty($params['Password'])) { //为空就清除记住密码
            GatewayServiceProvider::removeRememberPassword(['ResId' => $params['ResId'], 'UserId' => $params['UserId']]);
        }else{
            GatewayServiceProvider::saveRememberPassword($params);
        }
        return ['code' => 0, 'msg' => 'success'];
    }


    /**
     * 回收多条在线虚拟IP记录
     * @param $params
     * @return array
     */
    protected function delVirIpOnLine($params)
    {
        return VirPoolServiceProvider::delVirIpOnLine($params);
    }

    /**
     * 回收一条在线虚拟IP记录
     * @param $params
     * @return array
     */
    protected function delOneVirIpOnLine($params)
    {
        return VirPoolServiceProvider::delOneVirIpOnLine($params);
    }

    /**
     * 生成所有用户限速配置
     * @param $params
     * @return array
     */
    protected function setAllUserSpLimit($params)
    {
        $data = UserSpLimitServiceProvider::setAllUserSpLimit();
        return true;
    }

    /**
     * 获取记住密码配置
     * @param $params
     * @return array
     */
    protected function getRememberPassword($params)
    {
        $data = RememberPasswordModel::getSingle(['ResId' => $params['ResId'], 'UserId' => $params['UserId']]);
        return [
            'account' => $data['Account'] ?? '',
            'password' => $data['Password'] ?? '',
            'AutoLogin' => $data['AutoLogin'] ?? 0,
        ];
    }

    /**
     * 获取远程应用的密码
     *
     * @param $params
     *
     * @return array
     * @throws Exception
     */
    protected function getRemotePassword($params): array
    {
        $data = RemoteServiceProvider::getRemoteUserInfo($params['UserId']);
        return [
            'account' => $data['RemoteUserName'] ?? '',
            'password' => $data['RemotePwd'] ?? '',
            'AutoLogin' => 1,
        ];
    }

    /**
     * 同步用户态，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function syncTerminalUser($params)
    {
        $Token = $params['Token'] ?? '';
        if (empty($Token)) {
            $this->log('Token is empty.');
            return [];
        }
        return LoginServiceProvider::addSessionLifeTime($Token);
    }

    /**
     * @param $params
     * @return bool
     */
    protected function updateLifeTime($params)
    {
        LoginServiceProvider::updateSessionLifeTime();
        return true;
    }

    /**
     *  向控制中心要求下发网关配置数据，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getGwconfig($params)
    {
        $list = GatewayServiceProvider::getGatewayData($params);
        return ['AsgArrayList' => $list];
    }

    /**
     *  向控制中心要求下发下发网关组的网关配置数据到网关，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getGwgroup($params)
    {
        if (!isset($params['GroupID'])) {
            return false;
        }
        $list = GatewayGroupServiceProvider::getGatewayData($params['GroupID']);
        return ['AsgArrayList' => $list];
    }

    /**
     *  向控制中心要求下发普通资源（Web应用和主机服务器）配置数据，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getResconfig($params)
    {
        $GwIps = $params['GWInfo'] ?? [];
        $list = AppServiceProvider::getAppConfigs($GwIps);
        return ['AsgArrayList' => [$list]];
    }

    /**
     * 向控制中心要求下发资源（访问域）配置数据，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getIpResconfig($params)
    {
        $GwIps = $params['GWInfo'] ?? [];
        $list = AppServiceProvider::getVpnConfigs($GwIps);
        ResourceServiceProvider::makeRedisResource($params);
        return ['AsgArrayList' => [$list]];
    }

    /**
     *  通知控制中心计算策略对应的配置数据，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getPolicyconfig($params)
    {
        $PolicyIDs = $params['PolicyIDs'] ?? [];
        if (!empty($PolicyIDs)) {
            // 暂无用
            return true;
        }
        return true;
    }

    /**
     *  同步所有用户信息redis的session和userinfo，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getAllUserinfo($params)
    {
        $GwIps = $params['GWInfo'] ?? [];
        if (empty($GwIps)) {
            return false;
        }
        $list = LoginServiceProvider::getSyncUserInfo($GwIps);
        return ['AsgArrayList' => $list];
    }

    /**
     * 获取关联的用户ID，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function getRalationUserIds($params)
    {
        if (!isset($params['ObjectType'])) {
            return false;
        }
        $return = ['UserIds' => []];
        switch ($params['ObjectType']) {
            case 5: // 策略关联的资源
                $ResIds = $params['ResIds'] ?? [];
                $return['UserIds'] = NoticeServiceProvider::getUserIdsByResIds($ResIds);
        }
        return $return;
    }

    /**
     * 设置用户的权限，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function setUserPower($params)
    {
        if (!isset($params['ObjectType'])) {
            return false;
        }
        switch ($params['ObjectType']) {
            case 0: // 用户
                if (isset($params['UserIDs'])) {
                    NoticeServiceProvider::noticeUpdateManyUserPower($params['UserIDs']);
                } else {
                    NoticeServiceProvider::noticeUpdateUserPower($params['UserID']);
                }
                break;
            case 1: // 角色
                $RoleIds = is_array($params['RoleID']) ? $params['RoleID'] : [$params['RoleID']];
                NoticeServiceProvider::noticeUpdateRolePower($RoleIds);
                break;
            case 2: // 部门
                $DepartIds = is_array($params['DepartID']) ? $params['DepartID'] : [$params['DepartID']];
                NoticeServiceProvider::noticeUpdateDepartPower($DepartIds);
                break;
            case 3: // 设备
                $DeviceIds = is_array($params['DeviceID']) ? $params['DeviceID'] : [$params['DeviceID']];
                NoticeServiceProvider::noticeUpdateDevicePower($DeviceIds);
                break;
            case 4: // 资源或者授权组
                if (isset($params['AuthorizationID']) && $params['AuthorizationID'] > 0) {
                    NoticeServiceProvider::noticeUpdateAuthorizationGroupPower($params['AuthorizationID']);
                } else {
                    NoticeServiceProvider::noticeUpdateResourcePower($params['ResID']);
                }
                break;
            case 5: // 策略关联的资源
                $ChangeResIds = $params['ChangeResIds'] ?? [];
                $AllResIds = $params['AllResIds'] ?? [];
                NoticeServiceProvider::noticeUpdatePolicyPower($ChangeResIds);
                NoticeServiceProvider::noticeUpdatePolicyChangePower($AllResIds);
                break;
            case 6: // 网关
                $GwID = $params['GwID'] ?? 0;
                $GroupID = $params['GroupID'] ?? 0;
                NoticeServiceProvider::noticeUpdateGateway($GwID, $GroupID);
                break;
            case 7: // 全局配置变更
                NoticeServiceProvider::noticeUpdateConfig();
                break;
        }
        return true;
    }

    /**
     * 网关向控制中心上报数据信息并存储在对应的数据库表中，原ReportInfo::dataReported，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function dataReported($params)
    {
        return [];
    }

    /**
     * 网关向控制中心上报ip变动信息，原ReportInfo::ipchange，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function ipchange($params)
    {
        return [];
    }

    /**
     * 网关上报策略触发事件，原ReportInfo::policyEvents，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function policyEvents($params)
    {
        return [];
    }

    /**
     * 网关向控制中心上报资源健康检查的结果，原ReportInfo::healthcheck，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function healthcheck($params)
    {
        if (empty($params['data']) || empty($params['GwIp'])) {
            return false;
        }
        return AppServiceProvider::reportHealthcheck($params['GwIp'], $params['data']);
    }

    /**
     * 网关向控制中心发送命令，原ReportInfo::coudpcmd，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function coudpcmd($params)
    {
        return [];
    }

    /**
     * 提供管理平台下发静默杀毒接口
     * @param $params
     * @return array
     */
    protected function virusScan($params)
    {
        if (empty($params['deviceIds'])) {
            return ['code' => 1, 'msg' => '非法参数'];
        }
        cutil_php_log("管理平台下发杀毒检查:" . var_export($params['deviceIds'], true), 'msep');
        MsepServiceProvider::virusDetection($params['deviceIds']);
        return ['code' => 0, 'msg' => 'success'];
    }

    /**
     * 控制中心停用策略，删除策略时候调用自动恢复
     *
     * @param $params
     * @return array|false
     * @throws Exception
     */
    protected function recoveryDynamicPolicy($params)
    {
        //支持单个和多个动态策略的删除后自动恢复触发
        if (empty($params['DPolicyID'])) {
            return false;
        }
        $recoverReason = $params['Reason'] ?? '策略变更';
        $operateUser = $params['OperateUser'] ?? 'System';
        $dPolicyIDs = is_array($params['DPolicyID']) ? $params['DPolicyID'] : [$params['DPolicyID']];
        //实例化对应的类
        $service = PolicyServiceProvider::getPolicyService('DynamicTactic', $params);
        foreach ($dPolicyIDs as $dPolicyID) {
            $service->recoveryDynamicPolicy($dPolicyID, 0, 2, $operateUser, $recoverReason);
        }
        return [];
    }
}
