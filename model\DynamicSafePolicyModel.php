<?php

/**
 * Description: 动态策略主表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id
 */

class DynamicSafePolicyModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDynamicSafePolicy';
    public const PRIMARY_KEY = 'DPolicyID';
    protected static $columns = [
        'info' => 'DPolicyID,PolicyName,Status',
        '*'   => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DPolicyID'])) {
            $where .= "AND DPolicyID IN (".self::setArrayData($cond['DPolicyID']).") ";
        }

        if (isset($cond['PolicyName'])) {
            $where .= "AND PolicyName = ".self::setData($cond['PolicyName']);
        }

        if (isset($cond['Status'])) {
            $where .= "AND Status = ".self::setData($cond['Status']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
