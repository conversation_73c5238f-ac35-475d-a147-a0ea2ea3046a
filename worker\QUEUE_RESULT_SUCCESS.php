<?php

/**
 * Description: 上报入网状态结果成功队列
 * User: <EMAIL>
 * Date: 2021/09/13 15:53
 * Version: $Id: QUEUE_AUTH_SUCCESS.php 156563 2021-09-13 15:04:16Z duanyc $
 */

use Services\MsepServiceProvider;

if (!defined('IN_ACCESS')) {
    exit('Access Denied!');
}

if (PHP_SAPI != 'cli' || !isset($this) || !$this instanceof cls_queue) {
    exit();
}

/**
 * 任务主函数
 *
 * @param $params
 */
if (!function_exists('QUEUE_RESULT_SUCCESS')) {
    /**
     * 安检成功处理
     *
     * @param $params
     *
     * @return bool
     */
    function QUEUE_RESULT_SUCCESS($params)
    {
        cutil_php_log("SAFECHECK_SUCCESS:" . var_export($params, true), 'submitResult');
        if (empty($params['deviceId'])) {
            return false;
        }
        if ($params['is_client']) {
            // 有客户端的情况下,才需要下发通知
            AuthServiceProvider::tacticsChangeSel($params['deviceId']);
        }
        try {
            $isNeedVir = VpnServiceProvider::checkIsVirIp();
            if ($isNeedVir) {
                cutil_php_log("start set virip {$params['Token']}", 'submitResult');
                VirPoolServiceProvider::ApplyVirIpForToken($params['Token']);
            }
            $reg = VpnServiceProvider::checkIsNeedProxy($params);
            if ($reg) {
                cutil_php_log("start set vpn SpLimit {$params['Token']}", 'submitResult');
                UserSpLimitServiceProvider::setOneUserSpLimit($params['Token']);
            }
            AuthServiceProvider::setSessionStatus($params['Token']);
            //增加策略配置快速杀毒下发逻辑
            if (!empty($params['policyId']) && !empty($params['osType']) && $params['osType'] == OSTYPE_WINDOWS && $params['is_client']) {
                //增加判断策略是否开启快速下发杀毒开关,默认不开启
                $isFastAntiVirus = 0;
                $policyInfo = \NacPolicyListModel::getOne($params['policyId'], 'senior');
                !empty($policyInfo) && $isFastAntiVirus = $policyInfo['FastAntiVirus'] ?? 0;
                cutil_php_log(OSTYPE_WINDOWS . "SAFECHECK_SUCCESS:policyInfo" . var_export($policyInfo, true), 'submitResult');
                if ($isFastAntiVirus == 1) {
                    MsepServiceProvider::virusDetection($params['deviceId']);
                }
            }
        } catch (\Exception $exception) {
            cutil_php_log("SAFECHECK_SUCCESS Err:" . $exception->getTraceAsString(), 'submitResult');
        }

        return true;
    }
}

QUEUE_RESULT_SUCCESS($this->params);
