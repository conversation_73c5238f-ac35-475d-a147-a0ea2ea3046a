<?php
/**
 * Description: 认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: BaseAuthService.php 171965 2022-03-25 07:04:18Z chenpan $
 */

namespace Services\Auth\Services;

use AuthUserModel;
use Exception;
use LoginServiceProvider;
use NacAuthLogModel;
use RelationComputerModel;
use ResourceAuthLogModel;
use Services\Auth\Traits\BaseAuthServiceAfterTrait;
use Services\Auth\Traits\BaseAuthServiceBeforeTrait;
use Services\Auth\Traits\BaseAuthServiceExecuteTrait;
use Services\Common\Services\CommonService;
use Services\Common\Services\DepartService;
use Services\Common\Services\DeviceOperationService;
use Services\Device\Services\DeviceSceneService;
use SessionRedis;
use UserLogModel;

class BaseAuthService extends CommonService
{
    // 入网前代码块
    use BaseAuthServiceBeforeTrait;

    // 入网中代码块
    use BaseAuthServiceExecuteTrait;

    // 入网后代码块
    use BaseAuthServiceAfterTrait;
    /**
     * 请求参数数组,和处理后的数据.
     *
     * @var array
     */
    protected $params;

    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = '';

    /**
     * 是否已识别用户，已识别该用户类型，则存储用户信息，用于给用户提示，优先该认证方式
     * @var array
     */
    protected $userInfo = [];

    /**
     * 认证方式名字
     * @var string
     */
    protected $userTypeName = '';

    /**
     * 设备id.
     *
     * @var int
     */
    protected $deviceId;

    /**
     * 设备信息.
     *
     * @var array
     */
    protected $deviceInfo;

    /**
     * 终端设备在线信息
     *
     * @var array
     */
    protected $onlineDeviceInfo;

    /**
     * 异常
     *
     * @var Exception
     */
    protected $exception = null;

    /**
     * 场景公共服务
     *
     * @var DeviceSceneService
     */
    protected $sceneService = null;

    /**
     * 部门公共服务
     *
     * @var DepartService
     */
    protected $departService = null;

    /**
     * 设备id统一管理服务对象.
     *
     * @var DeviceOperationService
     */
    public $deviceOperationService;
    /**
     * 初始化
     *
     * @param $params
     */
    public function __construct($params)
    {
        parent::__construct();
        $this->logFileName = 'net_auth';
        $this->params = $params;
        $this->params['factorAuth'] = false;
        $this->params['authType'] = $this->userType;
        $this->deviceId = $params['deviceId'] ?? null;
        $this->sceneService = new DeviceSceneService();
        $this->departService = new DepartService();
        $this->deviceOperationService = new DeviceOperationService();
    }

    /**
     * 更新双因子认证记录
     * @throws Exception
     */
    public function updateTwoFactorAuthLog()
    {
        // 802.1x时，认证记录由服务端维护
        if ($this->params['callfrom'] == AUTH_FROM_8021X && $this->params['isRecord'] == 0) {
            return;
        }
        $cache = cache_get_info('twoFactor', $this->deviceId);
        if (empty($cache)) {
            T(21120044);
        }
        $UserLogId = $cache['UserLogId'];
        unset($cache['UserLogId']);
        $checkCode = empty($this->params['checkCode']) ? "" : $this->params['checkCode'];
        $successAuthLog = false;
        if (!empty($cache['RID'])) {
            $lcond = ['IsSuccess' => 0, 'RID' => $cache['RID'], 'column' => 'twoFactor'];
            $successAuthLog = NacAuthLogModel::getSingle($lcond);
        }
        $cache['AuthCode'] = $checkCode;
        $content = L(21148025, ['UserName' => $cache['UserName'], 'Time' => date('Y-m-d H:i:s')]) . L(21120062);
        if (!empty($successAuthLog)) {
            $cache['IsSuccess'] = 1;
            $cache['FailReason'] = '';
            NacAuthLogModel::update($cache['RID'], $cache);
            UserLogModel::update($UserLogId, ['Status' => '0', 'Content' => $content]);
        } else {
            unset($cache['RID']);
            NacAuthLogModel::insert($cache);
            $logTypeId = IS_CLIENT ? 27 : 26;
            $Data = ['DevInfo' => $this->deviceInfo['DevInfo']];
            $params['UserLogId'] = LoginServiceProvider::addOtherUserLog($cache, $logTypeId, $content, $Data);
        }
    }

    /**
     * TNacAuthLog中是否有未闭合的重复的认证记录,如果有就不插入(解决历史IP使用记录中有大量重复)
     *
     * @return bool
     */
    public function isHaveRepeatAuthLog()
    {
        //双因子认证中使用
        if ($this->params['isRecord'] == 0) {
            return true;
        }
        if (strlen($this->deviceId) <= 0) {
            return true;
        }
        // 更新最后在线离线时间
        $lastAuthID = $this->deviceInfo['LastAuthID'];
        if (!empty($lastAuthID)) {
            $authLog = NacAuthLogModel::getOne($lastAuthID, 'offline');
            if (empty($authLog['OffLineTime'])) {
                NacAuthLogModel::update($lastAuthID, ['OffLineTime' => 'now()']);
            }
        }
        return false;
    }

    /**
     * 记录认证成功记录
     *
     * @param $authType
     * @param $data
     * @param $remark
     * @param $userId
     * @param $flag
     * @return mixed
     * @throws Exception
     */
    public function recordAuthSucLog($authType, &$data, $remark = '', $userId = false, $flag = 1)
    {
        // 来宾认证不记录日志
        if ($authType === 'Guest') {
            // 更新上一次认证方式为来宾or员工
            RelationComputerModel::update($this->deviceId, ['LastUserType'=>2]);
            return false;
        }
        RelationComputerModel::update($this->deviceId, ['LastUserType' => 1]);
        $checkCode = empty($this->params['checkCode']) ? "" : $this->params['checkCode'];
        $this->params['userName'] = $this->formatUsername(trim($this->params['userName']));
        $params = [
            'DeviceID' => $this->deviceId,
            'AuthType' => $authType,
            'subAuthType' => $this->params['subType'],
            'UserName' => trim($this->params['userName']),
            'RoleID' => $data['RoleID'],
            'Remark' => $remark,
            'InsertTime' => 'now()',
            'UpdateTime' => 'now()',
            'IP' => $this->deviceInfo['IP'],
            'MAC' => $this->deviceInfo['Mac'],
            'SwitchIP' => $this->deviceInfo['SwitchIp'] ?? '',
            'SwitchPort' => $this->deviceInfo['SwitchPort'] ?? '',
            'AuthCode' => $checkCode
        ];
        if (!$flag && $this->params['factorAuth']) {
            $params['IsSuccess'] = STRING_FALSE;
            $params['FailReason'] = L(21120045);
        }
        if (!empty($this->params['UID'])) {
            $params['UID'] = $this->params['UID'];
        } elseif (!empty($userId)) {
            $params['UID'] = $userId;
        }
        $lastLogid = 0;
        if (!$this->isHaveRepeatAuthLog()) {
            $lastLogid = NacAuthLogModel::insert($params);
            cutil_php_log($params, $this->logFileName);
            $log_alert = L(21120009, $params);
            check_log_forward('UserAuthLog', $log_alert);
            $rparams = ['LastAuthID' => $lastLogid,'LastUserType' => 1];
            RelationComputerModel::update($this->deviceId, $rparams);
            cutil_php_log($rparams, $this->logFileName);
            cutil_php_log('AuthDeviceID is ' . $this->deviceId, $this->logFileName);

            // 记录访问日志
            $session = SessionRedis::getOne($data['Token'], 'user');
            $content = L(
                empty($params['FailReason']) ? 21148025 : 21148027,
                ['UserName' => $params['UserName'], 'Time' => date('Y-m-d H:i:s')]
            );
            $logTypeId = IS_CLIENT ? 27 : 26;
            $Status = empty($params['FailReason']) ? '0' : '1';
            $content .= $params['FailReason'] ?? '';
            $Data = ['Status' => $Status, 'DevInfo' => $this->deviceInfo['DevInfo']];
            $params['UserLogId'] = LoginServiceProvider::addOtherUserLog($session, $logTypeId, $content, $Data);
            //刷新认证时间
            if (!empty($session['Uuid'])) {
                AuthUserModel::update($session['Uuid'], ['AuthTime' => 'now()']);
                $data["AuthTime"] = date('Y-m-d H:i:s');
            }
        }
        if ($this->params['factorAuth']) {
            unset($params['IsSuccess'], $params['FailReason']);
            $params['RID'] = $lastLogid;
            cache_set_info('twoFactor', $this->deviceId, $params, 600);
        }
        return $lastLogid;
    }

    /**
     * 记录认证失败记录
     *
     * @param $authType
     * @param $message
     *
     * @return mixed|void
     * @throws Exception
     */
    public function recordAuthErrLog($authType, $message)
    {
        // 不记录认证失败记录
        if (!$this->params['isRecordErr'] || $authType === 'Guest') {
            return;
        }

        $checkCode = empty($this->params['checkCode']) ? "" : $this->params['checkCode'];
        $this->params['userName'] = $this->formatUsername(trim($this->params['userName']));
        $params = [
            'DeviceID' => $this->deviceId,
            'AuthType' => $authType,
            'UserName' => trim($this->params['userName']),
            'RoleID' => 0,
            'OffLineTime' => 'now()',
            'InsertTime' => 'now()',
            'UpdateTime' => 'now()',
            'IP' => $this->deviceInfo['IP'],
            'MAC' => $this->deviceInfo['Mac'],
            'SwitchIP' => $this->deviceInfo['SwitchIp'] ?? '',
            'SwitchPort' => $this->deviceInfo['SwitchPort'],
            'AuthCode' => $checkCode,
            'IsSuccess' => STRING_FALSE,
            'FailReason' => $message
        ];

        $isUpdate = $this->twoFactorAuthErrLog($params);
        if (!$isUpdate) {
            NacAuthLogModel::insert($params);
            $userInfo = AuthUserModel::getOneByUserName($this->userType, $params['UserName'], 'base');
            $content = L(21148027, ['UserName' => $params['UserName'], 'Time' => date('Y-m-d H:i:s')]);
            $content .= $message;
            $session = ['Uuid' => $userInfo['ID'] ?? 0, 'UserName' => $params['UserName'], 'DeviceID' => $this->deviceId];
            $logTypeID = IS_CLIENT ? 27 : 26;
            $Data = ['Status' => '1', 'DevInfo' => $this->deviceInfo['DevInfo']];
            LoginServiceProvider::addOtherUserLog($session, $logTypeID, $content, $Data);
        }

        $log_alert = L(21120006, $params);
        cutil_php_log($params, $this->logFileName);
        check_log_forward('UserAuthLog', $log_alert);
    }

    /**
     * 双因子认证记录
     *
     * @param $params
     *
     * @return bool
     */
    public function twoFactorAuthErrLog(&$params)
    {
        if ($this->params['twoAuthType'] == 'TwoFactor') {
            $params['FailReason'] = L(21120043) . ": {$params['FailReason']}";
            $cache = cache_get_info('twoFactor', $this->deviceId);
            if (empty($cache['RID'])) {
                return false;
            }
            $lcond = ['DeviceID' => $this->deviceId, 'RID' => $cache['RID'], 'column' => 'twoFactor'];
            $successAuthLog = NacAuthLogModel::getSingle($lcond);
            if (!empty($successAuthLog)) {
                $UserLogId = $cache['UserLogId'];
                $content = L(21148027, ['UserName' => $cache['UserName'], 'Time' => date('Y-m-d H:i:s')]);
                UserLogModel::update($UserLogId, ['Content' => $content . $params['FailReason']]);
                NacAuthLogModel::update($successAuthLog['RID'], $params);
                $cache['RID'] = '';
                cache_set_info('twoFactor', $this->deviceId, $cache, 600);
                return true;
            }
        }
        return false;
    }

    /**
     * 高级动态记录认证失败记录
     *
     * @param $AuthResult 1表示成功日志，0表示失败日志
     * @param $authType
     * @param $message
     *
     * @return mixed|void
     * @throws Exception
     */
    public function recordResourceAuthLog($AuthResult, $authType, $message = '')
    {
        $params = [
            'DeviceID' => $this->deviceId,
            'AuthType' => $authType,
            'UserName' => trim($this->params['userName']),
            'DevName' => $this->deviceInfo['DevName'],
            'ResourceAddr' => $this->params['resourceAddr'],
            'AuthResult' => $AuthResult,
            'IsQrLogin' => $this->params['isQrLogin']
        ];
        if ($AuthResult == '1') {
            $params['Remark'] = $message;
        } else {
            $params['FailReason'] = $message;
        }
        ResourceAuthLogModel::insert($params);
        cutil_php_log($params, $this->logFileName);
    }

    /**
     * @return Exception
     */
    public function getException()
    {
        return $this->exception;
    }

    /**
     * @return array
     */
    public function getParams(): array
    {
        return $this->params;
    }

    /**
     * @return array
     */
    public function getUserInfo()
    {
        return $this->userInfo;
    }

    /**
     * 更新设备缓存信息
     */
    public function updateDeviceCacheAndLastTime()
    {
        $this->deviceOperationService->updateDeviceCacheAndLastTime([
            'TDevice.Online' => 1,
            'DeviceID' => $this->deviceId,
            'TNacOnLineDevice.LastHeartTime' => func_time_getNow(),
            'TDevice.LastTime' => func_time_getNow(),
            'TDevice.LastChangeTime' => func_time_getNow()
        ]);
    }

    /**
     * 根据场景ID获取场景配置详情
     * @param $SceneID
     * @param $newMobile
     * @return array
     */
    public function getSceneInfoById($SceneID, $newMobile)
    {
        return $this->sceneService->getSceneInfoById($SceneID, $newMobile);
    }

    /**
     * 获取指定场景指定配置
     * @param $SceneID
     * @param $ItemName
     * @param $Groups
     * @return array|bool
     */
    public function getSceneInfoOne($SceneID, $ItemName, $Groups)
    {
        return $this->sceneService->getSceneInfoOne($SceneID, $ItemName, $Groups);
    }

    /**
     * 获取敲门认证的参数
     *
     * @param $Data
     *
     * @return mixed
     * @throws Exception
     */
    public function getFwknopData($Data)
    {
        $params = [];
        $columns = $this->getFwknopColumns();
        // type=User方式有很多种，新加参数不能严格要求
        if ($Data[0] !== 'User' && count($Data) < count($columns)) {
            T(21148022);
        }
        foreach ($columns as $index => $column) {
            $params[$columns[$index]] = $Data[$index] ?? '';
        }
        return $params;
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type'];
    }

    /**
     * 获取认证的参数
     *
     * @return mixed
     * @throws Exception
     */
    public function getAuthParams()
    {
        $params = [];
        $columns = $this->getAuthColumns();
        foreach ($columns as $column) {
            $params[$column] = $this->params[$column] ?? '';
        }
        return $params;
    }

    /**
     * 获取认证的字段
     * @return array
     */
    public function getAuthColumns()
    {
        return ['authType'];
    }

    /**
     * 强化认证后处理
     */
    public function addAuthAfter(): void
    {
    }

    /**
     * 敲门后处理
     */
    public function fwknopAfter(): void
    {
    }

    /**
     * 处理username
     * @param $username
     * @return string
     */
    public function formatUsername($username)
    {
        $username = str_replace(" ", " ", $username);
        if (IsUTF8($username)) {
            $userNameBak = $username;
            $username = iconv("UTF-8", "GBK", trim($username));
            $username = (str_replace(" ", "", $username) !== $username) ? str_replace(" ", " ", $username) : $userNameBak;
        }
        return $username;
    }
}
