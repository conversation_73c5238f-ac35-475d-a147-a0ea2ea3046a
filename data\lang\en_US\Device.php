<?php
/**
 * Description: 设备
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: Device.php 166287 2021-12-31 04:22:05Z duanyc $
 */

$GLOBALS['LANG'][21103] = [
    21103001 => 'Parameter error',
    21103002 => 'IP address format error',
    21103003 => 'MAC address format error',
    21103004 => 'The received message format is wrong',
    21103005 => 'Socket connection creation failed',
    21103006 => 'The device does not exist. Refresh the page or restart the client and try again!',
    21103007 => 'The real customer service IP address was not obtained',
    21103008 => "DeviceId, mac, ip are not passed parameters, please check the passed parameters",
    21103009 => 'The ip or mac format is illegal!',
    21103010 => 'Roaming device insertion failed',
    21103011 => "The device is not online, please try again after authentication!",
    21103012 => 'The system has not been authorized or the authorization has expired, or the number of registered devices has exceeded the maximum number supported by the device model, please contact your dealer!',
    21103013 => 'Device creation failed!',
    21103014 => 'Device IP cannot be empty!',
    21103015 => 'The current device does not support device import!',
    21103016 => 'Small assistant automatic authentication reporting device',
    21103017 => 'The device scenario information fails to be queried',
    21103018 => 'The administrator did not open guest privileges',
];
