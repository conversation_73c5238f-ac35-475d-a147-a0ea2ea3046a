<?php
/**
 * Description: VPN相关逻辑service
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: VpnServiceProvider.php 153494 2021-08-19 01:25:07Z duanyc $
 */

use Services\Common\Services\DeviceOperationService;

class VpnServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'vpn';

    /**
     * 判断当前ip是否在一个IP范围之内 如果在返回true反之false
     *
     * @param $ip
     * @param $iprange
     *
     * @return bool
     */
    public static function isVpnIpRange($ip, $iprange)
    {
        if ($ip != "" && $iprange != "") {
            $ipstr_arr1 = explode(",", $iprange);
            foreach ($ipstr_arr1 as $str1) {
                $iparr = explode("-", $str1);
                $start = ipToNum($iparr[0]);
                $stop = ipToNum($iparr[1]);
                $nowip = ipToNum($ip);
                if ($nowip >= $start && $nowip <= $stop) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检查vpn的ip范围
     *
     * @param $deviceId
     * @param $ip
     *
     * @throws Exception
     */
    public static function checkVpnIpRange($deviceId, $ip)
    {
        // 判断是否开启VPN
        $c_path = PATH_ETC . "asm/asc/etc/tbridge_comm.ini";
        $aData = DevASCInfoModel::getJoinDevice($deviceId);
        if ($aData['IsUseRemote'] == '1') {
            $c_path = PATH_ETC . "asm/tbridge_comm.ini";
        }
        $bfg = read_inifile($c_path);
        if ($bfg['OPENNAT'] != '1') {
            T(21140002);
        }
        if ($bfg['NATFORCE'] != '1') {
            T(21140003);
        }
        // 是否在nat IP范围之内
        if ($bfg['NATIP'] == "") {
            T(21140004);
        }
        if (!self::isVpnIpRange($ip, $bfg['NATIP'])) {
            T(21140004);
        }
    }

    /**
     * 修改设备信息
     *
     * @param $deviceId
     * @param $ip
     *
     * @throws Exception
     */
    public static function updateDeviceInfo($deviceId, $ip)
    {
        if (empty($deviceId) || empty($ip)) {
            return;
        }

        $params = ['DeviceID' => $deviceId, 'IP' => $ip,];
        $operationService = new DeviceOperationService();
        $result = $operationService->updateDevice($params);
        self::log($result);

        if ($result['code'] !== 1) {
            self::log($result['message']);
            throw new Exception($result['message'], $result['code']);
        }
    }

    /**
     * 获取是否vpn
     *
     * @param $bfg
     * @param $ip
     *
     * @return bool
     */
    public static function  getIsVpn($bfg, $ip)
    {
        // 是否在nat IP范围之内 判断是否开启VPN
        if ($bfg['OPENNAT'] == '1' && $bfg['NATFORCE'] == '1' && $bfg['NATIP'] != "" && VpnIpRange($ip, $bfg['NATIP'])) {
            return true;
        }
        return false;
    }
    /**
     * 检查vpn资源的策略
     *
     * @param $Token
     */
    public static function checkVpnPolicy($Token): void
    {
        $Session = SessionRedis::getOne($Token, 'policy');
        setRemoteAddress($Session['ConnectIp']);
        if (!empty($Session['vpnIpList'])) {
            $ipResIds = explode(',', $Session['vpnIpList']);
            ResourceServiceProvider::patchCheckPolicy($Session, $ipResIds);
            self::log("checkVpnPolicy: {$Token}: {$Session['vpnIpList']}");
        }
    }

    /**
     * 通知网关VPN
     *
     * @param $Token
     * @param $IpListGate
     * @param $vpnIpRangleAll
     * @param $vpnRouteListAll
     * @return array
     */
    public static function noticeGatewayVpn($Token, $IpListGate, $vpnIpRangleAll, $vpnRouteListAll): array
    {
        if (empty($IpListGate)) {
            return [];
        }
        try {
            $IpListGate = is_array($IpListGate) ? $IpListGate : json_decode($IpListGate, true);
            $cond = ['InGatewayID' => $IpListGate, 'column' => 'group'];
            $gatewayList = GateWayModel::getList($cond);
            if (empty($gatewayList)) {
                self::log('noticeGatewayVpn gatewayList empty!');
                return [];
            }
            $AsgList = [];
            $key = SessionRedis::PREFIX . SessionRedis::getKey([$Token]);
            $vpnIpRangleAll = json_decode($vpnIpRangleAll, true);
            $vpnRouteListAll = json_decode($vpnRouteListAll, true);
            $gateways = [];
            foreach ($gatewayList as $gateway) {
                $tmp = [];
                $tmp['vpnIpRangle'] = implode('##', $vpnIpRangleAll[$gateway['ID']] ?? []);
                $tmp['vpnRouteList'] = implode(', ', $vpnRouteListAll[$gateway['ID']] ?? []);
                $AsgList[$gateway['IP']] = [$key  => $tmp];
                $gateways[$gateway['ID']] = $gateway;
            }
            $data = ['AsgList' => $AsgList];
            WorkerUdpSend('MessageToGw', 'setHashRedis', $data);
            self::log(['noticeGatewayVpn', $Token, $IpListGate]);
            return $gateways;
        } catch (Exception $e) {
            self::log(['noticeGatewayVpn', $Token, $e->getMessage()]);
            return [];
        }
    }

    /**
     * 获取组装后的分网关vpn范围数据
     *
     * @param $vpnIpRangleAll
     *
     * @return array|mixed
     */
    public static function getVpnIpRangleAll($vpnIpRangleAll)
    {
        $vpnIpRangleAll = json_decode($vpnIpRangleAll, true);
        if (empty($vpnIpRangleAll)) {
            return [];
        }
        foreach ($vpnIpRangleAll as $key => $val) {
            $vpnIpRangleAll[$key] = json_encode($val);
        }
        return $vpnIpRangleAll;
    }

    /**
     * 通知客户端重连vpn
     *
     * @param $Token
     * @param $oldSession
     * @param $newSession
     *
     */
    public static function noticeClientChange($oldSession, $newSession): void
    {
        try {
            $vpnIpRangleAll = self::getVpnIpRangleAll($newSession['vpnIpRangleAll']);
            $ovpnIpRangleAll = self::getVpnIpRangleAll($oldSession['vpnIpRangleAll']);
            $delDiffData = array_diff_key($ovpnIpRangleAll, $vpnIpRangleAll);
            $addDiffData = array_diff_key($vpnIpRangleAll, $ovpnIpRangleAll);
            if (!empty($delDiffData) || !empty($addDiffData)) {
                NoticeServiceProvider::noticeZtpConfigChange($oldSession['DeviceID']);
            } else {
                $diffData = array_diff_assoc($vpnIpRangleAll, $ovpnIpRangleAll);
                if (empty($diffData)) {
                    return;
                }
                NoticeServiceProvider::noticeZtpConfigChange($oldSession['DeviceID']);
            }
        } catch (Exception $e) {
            self::log("noticeClientChange error:" . $e->getMessage());
        }
    }

    /**
     * IP变动,放开网络
     * @param int $deviceId
     * @param string $ip
     * @return void
     * @throws Exception
     */
    public static function vpnIpRangeOpenNet(int $deviceId, string $ip)
    {
        try {
            $netStatus = DeviceServiceProvider::ipRangeOpenNet($deviceId, $ip, 'vpnIpRangeOpenNet');
            $msg = $netStatus === 1 ? 'IP变动,放开网络:' : 'IP变动,未放开网络不处理';
            self::log($msg . $deviceId . ' ' . $ip);
        } catch (Exception $e) {
            self::log('vpnIpRangeOpenNet error:' . $e->getMessage());
        }
    }

    /**
     * 检查心跳
     */
    public static function checkHeart(): void
    {
        $cond = ['IsFinished' => '0', 'column' => 'heart', 'groupby' => 'AuthToken'];
        $list = GWUserLoginLogModel::getList($cond, false, 0, 10000);
        if (!empty($list)) {
            foreach ($list as $row) {
                if (empty($row['AuthToken'])) {
                    continue;
                }
                ResourceServiceProvider::setUserPower($row['AuthToken']);
            }
        }
    }
    /**
     * 校验是否需要下发虚拟IP和限速规则
     * @param array $params
     * @return false|true
     *
     * **/
    public static function checkIsNeedProxy($params)
    {
        // 是否是客户端调用
        if ($params['is_client']) {
            // 是否拥有vpn授权
            $reg = getmoduleregist('14');
            if ($reg) {
                $vpnType = DictModel::getOneItem('ZTP', 'VpnType');
                // 是否是新的vpn:proxy
                if ($vpnType['ItemValue'] == 'proxy') {
                    return true;
                }
            }
        }
        return false;
    }
    /**
     * 校验是否需要下发虚拟IP
     * @return false|true
     *
     * **/
    public static function checkIsVirIp()
    {
        // 20240416 增加新需求,web代理资源/vpn资源均可以使用虚拟IP
        // 当前 仅判断 ztp授权
        $reg = getmoduleregist('11');
        if ($reg) {
            return true;
        }
        return false;
    }
}
