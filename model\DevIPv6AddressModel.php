<?php

/**
 * Description: 设备ipv6地址信息表
 * User: <EMAIL>
 * Date: 2021/05/25 10:32
 * Version: $Id
 */

class DevIPv6AddressModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevIPv6Address';
    public const PRIMARY_KEY = 'ipv6_address_bin';
    protected static $columns = [
        'one'  => 'mac_address,device_id',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if(isset($cond['DeviceID'])) {
            $where .= "AND device_id = ".self::setData($cond['DeviceID']);
        }

        if(isset($cond['ipv6_address_bin'])) {
            $where .= "AND ipv6_address_bin = ".self::setData($cond['ipv6_address_bin']);
        }

        if (isset($cond['ipv6_address'])) {
            $where .= "AND ipv6_address = " . self::setData($cond['ipv6_address']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
