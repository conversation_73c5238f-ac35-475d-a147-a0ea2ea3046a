<?php

/**
 * Description: 访客入网在线设备表
 * User: <EMAIL>
 * Date: 2022/10/15 10:32
 * Version: $Id: GuestRelationModel.php 166879 2022-01-11 04:50:15Z duanyc $
 */

class GuestOnLineDeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGuestOnLineDevice';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'ID',
        '*' => '*',
        'device' => 'DeviceID AS device_id',
    ];

    /**
     * 获取来宾放开网络表 单个来宾在线管理信息列表
     *
     * @param array $cond
     *
     * @return bool|mixed
     */
    public static function getSingleInfoList($cond = [])
    {
        if (!isset($cond['AccessType'], $cond['GreetUserID'])) {
            return false;
        }
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "SELECT B.Name,B.Tel,B.Unit,A.InsertTime,A.ExpirateTime,A.DeviceID FROM TGuestOnLineDevice A left join TGuestSelfApply B on A.UserID = B.UserID
                  LEFT JOIN TGuestRelation C on A.UserID = C.UserID 
                WHERE	B.Status != -1 and A.AccessType in (" . self::setArrayData($cond['AccessType']) . ") AND A.GreetUserID = " . self::setData($cond['GreetUserID']);
        if ($cond['GreetUserID'] == 0){
            $sql .=' AND C.DeviceId ='.self::setData($cond['DeviceId']);
        }
        debug([$sql,self::$data]);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";
        if (isset($cond['UserID'])) {
            $where .= "AND UserID = " . self::setData($cond['UserID']);
        }
        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = " . self::setData($cond['DeviceID']);
        }
        if (isset($cond['RegionIDs'])) {
            $where .= "AND RegionIDs = " . self::setData($cond['RegionIDs']);
        }
        if (isset($cond['IplistID'])) {
            $where .= "AND IplistID = " . self::setData($cond['IplistID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * 更新来宾在线记录
     * @param $userId
     * @param $DeviceID
     * @param $key_values
     * @return bool|int|Object
     */
    public static function updateById($userId = "", $DeviceID, $key_values = [])
    {
        if (empty($DeviceID) || empty($key_values)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = empty($userId) ? " DeviceID = " . self::setData($DeviceID) : " UserID = " . self::setData($userId) . " and DeviceID = " . self::setData($DeviceID);
        cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $key_values]), "model_{$table['name']}");
        return lib_database::update($key_values, $where, $table['name'], $table['index'], self::$data);
    }
}
