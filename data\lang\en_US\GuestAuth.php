<?php
/**
 * Description: 来宾认证
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: GuestAuth.php 165610 2021-12-23 06:13:11Z duanyc $
 */

$GLOBALS['LANG'][21126] = [
    21126001 => 'Non-allowed guest authentication type!',
    21126002 => 'The Internet code must not be empty!',
    21126003 => 'The guest code is illegal or invalid!',
    21126004 => 'The guest QR code is invalid or not scanned!',
    21126005 => 'Authentication failed, the administrator has not created a guest user, please contact the administrator!',
    21126006 => 'Failed to obtain the role, please try to authenticate and apply again, thank you!',
    21126007 => 'Guest role',
    21126008 => 'Turn on automatic authentication',
    21126009 => 'The device does not exist or has been deleted, please register the device!',
    21126010 => 'The user [{user}] generates the guest code [{pw}] on the device [IP:{ip}], and the creation time is {nowTime}',
    21126011 => 'The administrator has set that the device is not allowed to apply for a guest internet code, please contact the administrator!',
    21126012 => 'QR code has expired',
    21126013 => 'The scanning device is illegal, please check the key configuration!',
    21126014 => 'Failed to synchronize the authentication status, please use the APP scan function to try again!',
    21126015 => 'Your device is not connected to the network or does not comply with the regulations, and cannot receive guests!',
    21126016 => 'You don\'t have the permission to receive guests at present. You can\'t receive guests!',
    21126017 => 'Guest successfully connected to the network',
    21126018 => 'Non-allowed guest authentication type!',
    21126019 => 'The QR code has expired, please refresh the page to get the QR code again',
    21126020 => 'Encountered an unknown error, please try to get the QR code again',
    21126021 => 'User does not exist, please register',
    21126022 => 'Guest code generation failed, please contact technical staff',
    21126023 => 'The device is not online or does not exist or has been deleted, please register the device!',
    21126024 => 'In the current scenario, the device cannot assign visitor access codes. Contact the administrator!',
    21126025 => 'The scanned device does not exist or has been deleted!',
    21126026 => 'The guest authentication type is not allowed. Please refresh to obtain a new authentication method!',
    21126027 => 'The user [{user}] generates the team guest code [{pw}] on the device [IP:{ip}], and the creation time is {nowTime}',
    21126028 => 'The maximum number of people that can be received has been reached!',
    21126029 => 'The team is inconsistent with the corresponding application user, please check!',
    21126030 => 'Unable to find the information corresponding to the reception user!',
    21126031 => 'Visitor security domain query error, network release failed!',
    21126032 => 'Cancellation failed. The record is not within the valid period. Please refresh the page and try again',
    21126033 => 'Cancellation failed. The record does not exist. Please refresh the page and try again',
    21126034 => 'Visitor has been connected. Please go to the [Single Visitor] or [Team Visitor] tab for management.',
    21126035 => 'The administrator has not enabled the reception team function. Please contact the administrator!',
    21126036 => 'Username can not be empty!',
    21126037 => 'Visitor security domain cannot be empty!',
    21126038 => 'The administrator has not enabled the visitor reservation function. Please contact the administrator!',
    21126039 => 'User [{user}] uses a third-party business interface to generate guest internet access codes [{pw}]',
    21126040 => 'The currently assigned network domain is not within the range of scene assignable and accessible network domains. Please contact the administrator!'
];
