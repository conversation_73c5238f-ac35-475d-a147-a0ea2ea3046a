<?php

/**
 * Description: 设备TQRcodeuser表
 * User: <EMAIL>
 * Date: 2021/06/09 18:52
 * Version: $Id: QRcodeUserModel.php 160346 2021-10-29 11:54:36Z duanyc $
 */

class QRcodeUserModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TQRcodeuser';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'one'  => 'ID,Token,UserID,DeviceID'
    ];

    /**
     * 根据Token获取用户数据
     * @param $Token
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByToken($Token, $Column = 'one')
    {
        if (empty($Token)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Token = ".self::setData($Token);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Token'])) {
            $where .= "AND Token = ".self::setData($cond['Token']);
        }

        if (isset($cond['LikeToken'])) {
            $LikeToken = "%{$cond['LikeToken']}%";
            $where .= "AND Token like ".self::setData($LikeToken);
        }

        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
