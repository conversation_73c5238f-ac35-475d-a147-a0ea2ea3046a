<?php

/**
 * Description: 报警相关
 * User: <EMAIL>
 * Date: 2021/06/25 8:53
 * Version: $Id: lib_alarm.php 156825 2021-09-15 10:08:13Z duanyc $
 */
!defined('IN_INIT') && exit('Access Denied');

class lib_alarm
{

    /**
     * @var mixed|string
     */
    private static $wsNoticeUrl = 'http://127.0.0.1:50005/access/ws/refresh';

    /**
     * 待审核报警 原CreateAlarm方法
     * PC和手机共用
     *
     * @param $aDeviceInfo
     *
     * @throws Exception
     */
    public static function createAlarm($aDeviceInfo)
    {
        $desc = L(21105001);
        cutil_exec_wait(PATH_ASM . 'sbin/warnner -l 4 -s 308 -d ' . $aDeviceInfo['DeviceID'] . ' -i ' . $aDeviceInfo['IP'] . " -x '<WARN><USERNAME>" . $aDeviceInfo['UserName'] . "</USERNAME></WARN>'" . ' -n "' . $aDeviceInfo['DevName'] . '" "' . "[" . $aDeviceInfo['DevName'] . "][" . $aDeviceInfo['IP'] . "][" . $aDeviceInfo['UserName'] . "]：&nbsp;&nbsp;" . $desc . '"');
    }

    /**
     * 注册账号报警
     *
     * @param $aDeviceInfo
     *
     * @throws Exception
     */
    public static function createRegisterAlarm($aDeviceInfo)
    {
        //产生告警
        $warnnerArr = array(
            'userName' => $aDeviceInfo['UserName'],
            'trueNames' => $aDeviceInfo['TrueNames']
        );
        $alarmInfo = " [{$aDeviceInfo['DevName']}][{$aDeviceInfo['IP']}]" . L(21137015, $warnnerArr);
        $cmd = PATH_ASM . "sbin/warnner -l 1 -s 300 -d  {$aDeviceInfo['DeviceID']}  -i \"{$aDeviceInfo['IP']}\"  -x '<WARN><USERNAME>" . $aDeviceInfo['UserName'] . "</USERNAME></WARN>' -n \"{$aDeviceInfo['DevName']}\"  \"{$alarmInfo}\"   ";
        cutil_exec_wait($cmd);//报警
    }

    /**
     * 生成给终端发送信息通知
     * @param array $msgSenderInfo
     * @return bool
     * @throws Exception
     */
    public static function createMsgSender(array $msgSenderInfo, $noticeWsInfo = []): bool
    {
        if (empty($msgSenderInfo['DeviceId']) || empty($msgSenderInfo['Content'])) {
            return false;
        }
        $cmd = "msgsender -d {$msgSenderInfo['DeviceId']} -c '{$msgSenderInfo['Content']}'";
        if (!empty($msgSenderInfo['IsNull']) && $msgSenderInfo['IsNull'] === true) {
            $cmd .= " >/dev/null &";
        }
        cutil_exec_no_wait($cmd);//报警
        //判断是否ws通知，默认不通知
        if (!empty($noticeWsInfo)) {
            // 插入user_notice表
            $lastId = UserNoticeModel::insert($noticeWsInfo);
            if ($lastId > 0) {
                try {
                    // 调用发送接口触发通知
                    $res = curl(self::$wsNoticeUrl, "POST", '['.$lastId.']', 10, 3);
                    if ($res['errcode'] != 0) {
                        return false;
                    }
                } catch (\Exception $e) {
                    return false;
                }
            }
        }
        return true;
    }
}
