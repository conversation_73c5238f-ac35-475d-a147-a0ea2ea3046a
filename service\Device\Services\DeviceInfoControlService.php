<?php
/**
 * Description: 设备信息控制方式服务
 * User: <EMAIL>
 * Date: 2021/07/2 15:53
 * Version: $Id: DeviceInfoControlService.php 167484 2022-01-17 09:32:45Z renchen $
 */

namespace Services\Device\Services;

use DeviceModel;
use DeviceServiceProvider;
use Exception;
use HardToDevIDModel;
use NacOnLineDeviceModel;
use Srv\Nacservice\V1\Device;

class DeviceInfoControlService extends DeviceInfoService
{
    /**
     * 是否Net设备
     *
     * @var int
     */
    public $isNetDevice = 0;

    /**
     * 判断设备是否存在.
     *
     * @return mixed
     * @throws Exception
     */
    public function isDeviceExist()
    {
        $result = $this->isDeviceExistByControl();

        if ($result === false) {
            $this->writeLog('通过['.$this->params['gettype'].']方式，没有获取到设备信息');
        }
        return $result;
    }

    /**
     * 获取设备信息
     * @return mixed
     * @throws Exception
     */
    private function getDevInfo()
    {
        if (!empty($this->params['dev_json'])) {
            $xml = new \xml_dev_info("<?xml version='1.0' encoding='gbk'?><ASM></ASM>");
            $aDevInfo = $xml->parseJson(json_decode($this->params['dev_json'], true));
        } else {
            $xml = new \xml_dev_info($this->params['dev_xml']);
            $aDevInfo = $xml->parseXml();
        }
        return $aDevInfo;
    }

    /**
     * 通过小助手上报的信息判断设备是否存在.
     *
     * @return bool|mixed
     * @throws Exception
     */
    private function isDeviceExistByControl()
    {
        try {
            if ('' !== $this->params['dev_xml'] || !empty($this->params['dev_json'])) {
                $aDevInfo = $this->getDevInfo();
                if (IS_INTERNAL && $aDevInfo['Ip'] !== $this->params['linkIP']) {
                    $this->isNetDevice = 1;
                }
                $aDevInfo['InstallClient'] = '1';
                // 控件不更新浏览器信息
                unset($aDevInfo['IeVersion']);
                $this->writeLog('小助手上报的设备信息:'.var_export($aDevInfo, true));
                if (!$this->checkAgentVersion($aDevInfo['AgentVersion'] ?? '') && FORCED_UPGRADE) {
                    $deviceId = (int)$this->params['deviceid'];
                    if ($deviceId === 0) {
                        $res = DeviceModel::getSingle(['IP' => $aDevInfo['Ip'], 'InstallClient' => 1, 'column' => 'one']);
                        $deviceId = !empty($res) ? (int)$res['DeviceID'] : 0;
                    }
                    // 如果客户端版本没有升级，且开启了强制升级才能使用，则发消息通知客户端
                    $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>ShowUserOneMsg</WhatToDo><Msg>".L(21101015)."</Msg></ASM>";
                    $msgSenderInfo = ['DeviceId' => $deviceId, 'Content' => $content, 'IsNull' => true];
                    \lib_alarm::createMsgSender($msgSenderInfo);
                    // 2722.R001 mac 802.1x认证后如果没返回结果给他会自杀永远无法升级，这里对其进行白名单处理，后续场景不对也没办法(升级后可以拿到正确的)
                    if (strtolower(OS_TYPE) !== OSTYPE_MAC) {
                        T(21101015);
                    }
                }
                // 如果mac为空或者mac是伪造，则通过交换机ip获取mac，主要处理苹果客户端返回假mac和小助手上报空mac问题
                if (trim($aDevInfo['Mac']) === '' || RelativelyIsFake($aDevInfo['Mac'])) {
                    // 如果通过交换机获取不到，还是会返回伪造mac
                    $msg = $aDevInfo['Mac'] ? '小助手上报Mac为伪造mac,' : '小助手上报Mac为空,';
                    $aDevInfo['Mac'] = $this->getMacByIp($aDevInfo['Ip'], [
                        "isOnlyGetDevInfo" => !empty($aDevInfo['Mac']) && strtolower(OS_TYPE) === OSTYPE_WINDOWS
                    ]) ?: $aDevInfo['Mac'];
                    $this->writeLog($msg."通过GetDevInfo获取mac为：[{$aDevInfo['Mac']}]");
                }

                // 查找设备是否存在
                $getDeviceIDRes = $this->deviceOperationService->getDeviceID($aDevInfo['DiskId'], $aDevInfo['Mac'], $aDevInfo['Ip']);
                $resDeviceID = $getDeviceIDRes['code'] === 1 ? $getDeviceIDRes['data']['DeviceID'] : 0;

                // 查找设备出错
                if (!is_numeric($resDeviceID) || (int)$resDeviceID < 0) {
                    $this->writeLog("control调用getDeviceID服务查询出错：{$aDevInfo['DiskId']}, {$aDevInfo['Mac']}, {$aDevInfo['Ip']}", 'ERROR');
                    return false;
                }

                $SessionDeviceID = $this->params['SessionDeviceID'] ?? '';
                // 外网设备合并
                if (!IS_INTERNAL && $this->deviceId != 0 && $resDeviceID != 0 && $resDeviceID != $this->deviceId) {
                    $resDeviceID = $this->getFirstDeviceID($this->deviceId, $resDeviceID, $aDevInfo['Ip'], $aDevInfo['DiskId']);
                // 内网设备合并
                } elseif (IS_INTERNAL && !empty($SessionDeviceID) && $resDeviceID != 0 && $resDeviceID != $SessionDeviceID) {
                    if(!$aDevInfo['IsInNAT']){
                        $this->writeLog("设备非nat环境，触发合并");
                        $resDeviceID = $this->getFirstDeviceID($SessionDeviceID, $resDeviceID, $aDevInfo['Ip'], $aDevInfo['DiskId']);
                    }
                }

                if ((int)$resDeviceID > 0) {
                    if ($this->isNetDevice && IS_MOBILE) {
                        $this->deviceId = $this->getNetDeviceID($resDeviceID);
                    } else {
                        $this->deviceId = (int)$resDeviceID;
                    }
                    $this->mac = $aDevInfo['Mac'];

                    //判断是否漫游设备,如果是则把数据漫游到本地
                    $this->changeRoamToLocal($aDevInfo);

                    //更新设备信息
                    $result = DeviceModel::getDevHand($this->deviceId);
                    if ($result['IsHandType'] !== 1 && strlen($result['Type']) >= 5 && '0' == $result['IsUserDef']) {
                        $this->updateDeviceType($this->deviceId, $this->params['deviceType'], '');
                    }
                    $this->writeLog("通过控件判断设备[{$aDevInfo['Ip']}]存在,DeviceID=".$this->deviceId);
                    $this->updateDeviceInfo($aDevInfo);
                    // yanzj 20180824 不管设备是否存在，都上报最新的ipv6地址报文
                    $this->sendIpv6ControlXml();

                    return true;
                }

                if (0 === (int)$resDeviceID) {
                    $this->writeLog('通过控件判断设备不存在：'.var_export($getDeviceIDRes, true));
                    $temMac = '';

                    if (get_server_type() === 'dasc' && get_dasc_status() /*&& $this->aQuery['roamflag'] == '1'*/) { /* 如果当时设备是DASC, 并且和ASM联动完成，尝试从ASM获取ASM信息 20191126 李湘要求去掉漫游标记 asc升级dasc*/
                        $manageIP = get_dasm_ip();
                        if ($manageIP) {
                            $this->writeLog("当前是DASC设备，尝试从ASM上获取设备信息,manageIP：" . $manageIP);
                            $this->params['manageIP'] = $manageIP;
                            $aDevInfo['manageIP'] = $manageIP;
                            $result = $this->roamDevInsertDasc($aDevInfo);
                            if ($result['code'] === 1) {
                                return true;
                            }
                        }
                    }

                    // 判断此设备id的mac是否为伪造
                    if ($this->deviceId) {
                        $res = DeviceModel::getOneByType($this->deviceId, 101);
                        $temMac = $res['Mac'];
                        $this->writeLog("通过设备ID:[{$this->deviceId}]查得MAC：[".$temMac.']');
                    }

                    // 不存在设备id或者查到的mac不是伪造mac，则插入新设备否则更新
                    if (!$this->deviceId || !RelativelyIsFake($temMac)) {
                        if (!$this->insertDeviceByIpGetMac($aDevInfo['Ip'], $aDevInfo['Mac'], $aDevInfo['DiskId'], $aDevInfo['DevBootTime'] ?? '')) {
                            return false;
                        }
                        $this->isNewDevice = 1;
                        $this->writeLog("不是伪造mac控件插入设备[{$aDevInfo['Ip']}]：DeviceID=".$this->deviceId);
                    }

                    $this->updateDeviceInfo($aDevInfo);
                    $this->sendIpv6ControlXml();

                    return true;
                }
            }
        } catch (Exception $e) {
            $this->recordErrorMessage($e);
            // 如果是客户端版本过低，返回错误码21101015
            if ($e->getCode() == 21101015) {
                T(21101015);
            }
        }

        return false;
    }

    /**
     * 获取合并后的设备ID
     * @param $iDevID
     * @param $DBDeviceID
     * @param $ip
     * @param $pHard
     * @return int
     * @throws Exception
     */
    private function getFirstDeviceID($iDevID, $DBDeviceID, $ip, $pHard)
    {
        $this->writeLog("合并设备获取 getFirstDeviceID：{$iDevID}: {$DBDeviceID}: {$ip}: {$pHard}: " . IS_INTERNAL);
        if (IS_INTERNAL) {
            $Result = NacOnLineDeviceModel::getCorrectDeviceID([$iDevID, $DBDeviceID]);
        } else {
            $Result = DeviceModel::getCorrectDeviceID([$iDevID, $DBDeviceID]);
        }
        $FirstDeviceIDValue = $Result['DeviceID'] ?? -1;
        if ($FirstDeviceIDValue > 0 && $FirstDeviceIDValue != $DBDeviceID) {
            $iDevID = (int)$FirstDeviceIDValue;
            $this->writeLog("修改设备ID为早期设备ID[{$iDevID}]");
            // 废弃客户端MAC地址对应的设备
            $this->clearDeviceInfo($DBDeviceID);
            // 修改硬盘ID与设备ID映射表指向最新的设备ID
            // 这里合并需要先删除HardToDevID相同的deviceid条目，然后再插入，bug:56064
            HardToDevIDModel::delete(['DeviceID' => $DBDeviceID]);
            HardToDevIDModel::insert(['DeviceID' => $DBDeviceID, 'MacHard' => $pHard]);
            // 外网设备拉起设备合并后放开网络
            $netStatus = DeviceServiceProvider::ipRangeOpenNet((int)$iDevID, $ip, 'getFirstDeviceID');
            $msg = $netStatus === 1 ? '设备合并IP变动,放开网络:' : '设备合并IP变动,未放开网络不处理';
            $this->writeLog($msg.$iDevID.' '.$ip);
        } elseif ($FirstDeviceIDValue == -1) {
            $iDevID = (int)$DBDeviceID;
        } else {
            // 废弃传入的设备
            $this->clearDeviceInfo($iDevID);
            $iDevID = (int)$DBDeviceID;
        }
        $this->writeLog("合并设备后的设备ID getFirstDeviceID：{$iDevID}");
        return $iDevID;
    }

    /**
     * 获取net环境下的设备ID
     *
     * @param $resDeviceID
     *
     * @return int
     * @throws Exception
     */
    private function getNetDeviceID($resDeviceID): int
    {
        $mac = MakeMacByIP($this->params['linkIP'], 4);
        $getDeviceIDRes = $this->deviceOperationService->getDeviceID($mac, $mac, $this->params['linkIP']);
        $resNetDeviceID = $getDeviceIDRes['code'] === 1 ? $getDeviceIDRes['data']['DeviceID'] : 0;
        $resNetMac = $getDeviceIDRes['code'] === 1 ? $getDeviceIDRes['data']['MAC'] : '';
        if ($resDeviceID && $resNetDeviceID && (int)$resNetDeviceID !== (int)$resDeviceID && RelativelyIsFake($resNetMac)) {
            $this->clearDeviceInfo($resNetDeviceID);
        }
        return (int)$resDeviceID;
    }
}
