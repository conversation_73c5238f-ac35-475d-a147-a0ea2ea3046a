<?php
/**
 * Description: 短信认证
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: SmsAuth.php 149450 2021-07-08 10:13:29Z duanyc $
 */

$GLOBALS['LANG'][21131] = [
    21131001 => 'The verification code is illegal or has expired!',
    21131002 => 'Please enter the correct mobile phone number!',
    21131003 => 'For non-network phone numbers, please contact the administrator!',
    21131004 => 'SMS is sent too frequently!',
    21131005 => 'The phone number is wrong, not the current administrator\'s phone number!',
    ******** => 'SMS is sent too frequently!',
    ******** => 'SMS verification code (guest code) [{pw}] is automatically generated by [{user}], and the creation time is {nowTime}',
    ******** => '"Device ID: {deviceid}, user ID: {userid}, mobile phone number: {phone}, verification code: {code}, operation time: {time}"',
    ******** => 'Your SMS verification code is',
    ******** => 'Effective within minutes.',
    ******** => 'Sending failed, incorrect phone number associated with account or abnormal SMS service. Please contact the administrator!',
    ******** => 'The third-party SMS gateway address is incorrect, please contact the administrator!',
    ******** => 'Your SMS verification code is:',
    ******** => 'The text message was sent successfully, please pay attention to check it!',
    ******** => 'The email was sent successfully. Please check it!',
    ******** => 'Mobile phone verification code authentication. The user [{user}] is automatically generated. The creation time is {nowtime}',
    ******** => 'Your email verification code is',
    ******** => 'Access to non reserved mobile phone numbers is prohibited. Please contact your receptionist!',
];
