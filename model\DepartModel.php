<?php

/**
 * Description: 部门表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: DepartModel.php 174717 2022-04-28 02:41:13Z duanyc $
 */

class DepartModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDepart';
    public const PRIMARY_KEY = 'DepartID';
    protected static $columns = [
        'one' => 'DepartID,DepartName,UpID,AllDepartID,ZtpDepart',
        'all' => 'DepartID,UpID,IPSegment',
        'name' => 'AllDepartName',
        'depart' => "DepartID, UpID,AllDepartName,IPSegment,(length(AllDepartID)-length(replace(AllDepartID,'/',''))) as departlevel",
        'child' => "DepartID,UpID,DepartName,(length(AllDepartID)-length(replace(AllDepartID,'/',''))) as departlevel",
        'tree' => 'UpID, DepartID,AllDepartID, DepartName,AllDepartName,IPSegment',
        'max' => 'max(DepartID) as MaxID',
        '*' => '*',
    ];

    /**
     * 单条:存在部门ID为0的记录需要查询,覆盖父类方法
     *
     * @param string $ID
     * @param string $Column
     *
     * @return mixed
     */
    public static function getOne($ID, $Column = '*')
    {
        if (empty($Column)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $primaryKey = static::PRIMARY_KEY;
        $where = "WHERE {$primaryKey} = " . self::setData($ID);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 单条根据$OtherSysID查询
     *
     * @param string $OtherSysID
     * @param string $Column
     *
     * @return mixed
     */
    public static function getOneByOtherSysID($OtherSysID, $Column = '*')
    {
        if (empty($OtherSysID)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE OtherSysID = " . self::setData($OtherSysID);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 所有条目
     *
     * @return mixed
     */
    public static function getAll()
    {
        $column = self::$columns['all'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']}";
        return lib_database::getAll($sql, $table['index']);
    }

    /**
     * 设置部门全路径和ID等数据
     *
     * @param $key_values
     */
    public static function setAllDepart(&$key_values)
    {
        $UpID = $key_values['UpID'];
        $UpDepart = self::getOne($UpID);
        if (!empty($UpDepart)) {
            $key_values['AllDepartName'] = $UpDepart['AllDepartName'] . "/" . $key_values['DepartName'];
            $key_values['AllDepartID'] = $UpDepart['AllDepartID'] . "/" . $key_values['DepartID'];
            if ($UpDepart['ZtpDepart'] && getmoduleregist(11)) {
                $key_values['ZtpDepart'] = 1;
            }
        }
        if (!isset($key_values['OrderIndex'])) {
            $key_values['OrderIndex'] = $key_values['DepartID'];
        }
    }

    /**
     * @Description: 传入一个部门ID，计算出该部门的全路径并返回
     * User: <EMAIL>
     * Date: 2023/8/4 9:31
     * @param $departId
     * @param string $allDepartName
     * @param string $allDepartId
     * @return array  /部门a/b/c/
     */
    public static function getAllDepart($departId, string $allDepartName = '', string $allDepartId = ''): array
    {
        //查询上级ID
        $departInfo = self::getOne($departId);
        if (!empty($departInfo)) {
            //拼接id与名称
            $allDepartName = $allDepartName != '' ? $departInfo['DepartName'] . "/" . $allDepartName : $departInfo['DepartName'];
            $allDepartId = $allDepartId != '' ? $departInfo['DepartID'] . "/" . $allDepartId : $departInfo['DepartID'];
            //如果是顶级则返回
            if ($departInfo['UpID'] < 0) {
                return [$allDepartName, $allDepartId];
            }
            //上级不为空继续
            [$allDepartName, $allDepartId] = self::getAllDepart($departInfo['UpID'], $allDepartName, $allDepartId);
        }
        return [$allDepartName, $allDepartId];
    }

    /**
     * 插入
     *
     * @param array $key_values
     *
     * @return int
     */
    public static function insert($key_values = [])
    {
        if (empty($key_values)) {
            return false;
        }
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $dasmIp = get_pub_dasm_ip();
        $key_values['UpdateTime'] = date('Y-m-d H:i:s');

        $departId = 0;
        if (!empty($dasmIp)) {
            try {
                $result = lib_yar::clients('duser', 'getDepartId', [], $dasmIp);
                if (!empty($result['state'])) {
                    $departId = $key_values['DepartID'] = $result['data']['departId'];
                    if (isset($key_values['UpID']) && isset($key_values['DepartName'])) {
                        self::setAllDepart($key_values);
                    }
                    single_table_pub(static::TABLE_NAME, $key_values, 'insert');
                }
            } catch (Exception $e) {
                cutil_php_log(json_encode(['model_insert', $e->getMessage()]), "model_{$table['name']}");
            }
        }

        $result = lib_database::duplicate($key_values, $table['name'], $table['index']);
        cutil_php_log(json_encode(['model_insert', $table, $key_values]), "model_{$table['name']}");

        if ($result) {
            return $departId ?: lib_database::insertId();
        }

        return $result;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['UpID'])) {
            $where .= "AND UpID = " . self::setData($cond['UpID']);
        }

        if (isset($cond['GteUpID'])) {
            $where .= "AND UpID >= " . self::setData($cond['GteUpID']);
        }

        if (isset($cond['DepartID'])) {
            $where .= "AND DepartID = " . self::setData($cond['DepartID']);
        }

        if (isset($cond['DepartName'])) {
            $where .= "AND DepartName = " . self::setData($cond['DepartName']);
        }

        if (isset($cond['ZtpDepart'])) {
            $where .= "AND ZtpDepart = " . self::setData($cond['ZtpDepart']);
        }

        if (!empty($cond['Keyword'])) {
            $DepartName = "%{$cond['Keyword']}%";
            $where .= "AND DepartName LIKE " . self::setData($DepartName);
        }

        if (!empty($cond['OtherSysID'])) {
            $OtherSysID = "%{$cond['OtherSysID']}%";
            $where .= "AND OtherSysID LIKE " . self::setData($OtherSysID);
        }

        if (isset($cond['DepartIDIn'])) {
            $where .= " AND DepartID IN " . self::setData($cond['DepartIDIn']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
