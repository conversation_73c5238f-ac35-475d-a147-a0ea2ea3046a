<?php
/**
 * Description: 基本配置
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: inc_config.php 163009 2021-11-25 15:12:19Z duanyc $
 */
/* 初始化全局配置变量 */
$GLOBALS['CONFIG'] = array();

$cacheInfo = parse_ini_file(PATH_ETC . 'AsmCache.ini');

//redis配置
$GLOBALS['CONFIG']['redis_config'] = array();
$GLOBALS['CONFIG']['redis_config']['password'] = $cacheInfo['password'];
$GLOBALS['CONFIG']['redis'] = array();
$GLOBALS['CONFIG']['redis'][0] = array('host' => $cacheInfo['host'], 'port' => '6379', 'timeout' => 3);//主

//队列配置
$GLOBALS['CONFIG']['queue'] = array();
$GLOBALS['CONFIG']['queue']['AUTH_SUCCESS'] = array('name' => '认证成功处理', 'timeout' => 1440, 'num' => 1);
$GLOBALS['CONFIG']['queue']['RESULT_SUCCESS'] = array('name' => '上报入网状态结果成功处理', 'timeout' => 1440, 'num' => 1);
$GLOBALS['CONFIG']['queue']['COMMON_RUN'] = array('name' => '通用队列处理', 'timeout' => 1440, 'num' => 1);
//yar配置
$GLOBALS['CONFIG']['yar_server'] = array();
$GLOBALS['CONFIG']['yar_server']['net'] = array(
    'url' => '/api/Rpc/?tradecode=Net',
    'key' => 'EeYYum38FxeGRkxmp966FFOV',
    'gbk' => true
);
$GLOBALS['CONFIG']['yar_server']['MobileManager'] = array(
    'url' => '/api/Rpc/?tradecode=MobileManager',
    'key' => 'EeYYum38FxeGRkxmp966FFOV'
);
$GLOBALS['CONFIG']['yar_server']['safecheck'] = array(
    'url' => '/api/Rpc/?tradecode=Check',
    'key' => 'EeYYum38FxeGRkxmp966FFOV',
    'gbk' => true
);
$GLOBALS['CONFIG']['yar_server']['distribute'] = array(
    'url' => '/api/Rpc/?tradecode=Distribute',
    'key' => 'EeYYum38FxeGRkxmp966FFOV',
    'gbk' => true
);
$GLOBALS['CONFIG']['yar_server']['dasm'] = array(
    'url' => '/access/rpc/dasm',
    'key' => 'EeYYum38FxeGRkxmp966DASM',
);
$GLOBALS['CONFIG']['yar_server']['auth'] = array(
    'url' => '/passport/rpc/auth',
    'key' => 'xemp9366DAEeYYxum8GRkMFS',
);
$GLOBALS['CONFIG']['yar_server']['duser'] = array(
    'url' => '/access/rpc/duser',
    'key' => 'EeYYum38FxeGRkxmp966DASM',
);
$GLOBALS['CONFIG']['yar_server']['ztpCenter'] = array(
    'url' => '/access/rpc/ztpCenter',
    'key' => 'um38FxeGRkEexmp966DASMYY',
);
$GLOBALS['CONFIG']['yar_server']['ztpGateway'] = array(
    'url' => '/access/rpc/ztpGateway',
    'key' => '96RkEexmp6DASMYYum38FxeG',
);
$GLOBALS['CONFIG']['yar_server']['alarm'] = array(
    'url' => '/backend/api/rpcserve/Alarm',
    'key' => 'mp96638FxeGRkxDASMEeYYum',
);
$GLOBALS['CONFIG']['yar_server']['backend'] = array(
    'url' => '/backend/api/rpcserve/Backend',
    'key' => 'kEeYYumpm38Fxx66FFOV9eGR',
);
$GLOBALS['CONFIG']['yar_server']['dasm_backend'] = array(
    'url' => '/backend/api/rpcserve/Backend',
    'key' => 'kEeYYumpm38Fxx66FFOV9eGR',
);
$GLOBALS['CONFIG']['yar_server']['policy'] = array(
    'url' => '/api/Rpc/?tradecode=Policy',
    'key' => 'EeYYum38FxeGRkxmp966FFOV'
);
// 语言配置
$GLOBALS['CONFIG']['LANG'] = array(
    'zh' => 'zh_CN',
    'en' => 'en_US',
);
// 语言映射
$GLOBALS['CONFIG']['LANG_MAP'] = array(
    'zh_CN' => 'zh',
    'en_US' => 'en',
);
// 抛出异常后的数据暂存
$GLOBALS['CONFIG']['EXCEPTION_DATA'] = [];
// 多个原因数据保存
$GLOBALS['CONFIG']['ALL_MESSAGE'] = null;
