<?php
/**
 * Description: 零信任网关
 * User: <EMAIL>
 * Date: 2022/05/05 23:32
 * Version: $Id: ZtpGatewayController.php 175039 2022-05-05 07:33:41Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

use Services\Common\Services\DESService;

class ZtpGatewayController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['setCookies' => true, 'sdpErr' => true, 'getWatermark' => true, 'getForm' => true];

    /**
     * 设置cookies 网关上请求 /access/gateway/setCookies
     *
     * @return mixed
     * @throws Exception
     */
    public function setCookies()
    {
        try {
            $params = [];
            $params['access_token'] = request('access_token', 'request');
            $url_str = DESService::desEcbDecrypt(stringDecode($params['access_token']));
            parse_str($url_str, $data);
            $params['ResID'] = $data['resId'] ?? '';
            $params['AccessTypeID'] = $data['AccessTypeID'] ?? '';
            $params['Token'] = $data['token'] ?? '';
            $params['RedictUrl'] = urldecode($data['redictUrl'] ?? '');
            $params['CreateTime'] = $data['createTime'] ?? '';
            $params['ExpireTime'] = (int)($data['expireTime'] ?? 0);
            $params['AppHost'] = $data['appHost'] ?? '';
            $params['AddAuth'] = $data['addAuth'] ?? '';
            $params['cookieDomain'] = $data['cookieDomain'] ?? '';
            // 拦截只能客户端访问类型资源
            if ((int)($params['AccessTypeID']) === ACCESS_TYPE_CUSTOM_APP) {
                gotoErrorUrl("0x1016");
            }
            return AgentServiceProvider::setCookies($params);
        } catch (Exception $e) {
            AgentServiceProvider::log("Error CreateTime: {$params['CreateTime']}");
            gotoErrorUrl("0x1006");
            return false;
        }
    }


    /**
     * 表单代填接口
     * @return array|false[]
     * @throws Exception
     */
    public function getForm()
    {
        $ZTPCookie = request('ZTP_Cookie', 'cookie');
        [$session, $userInfo] = LoginServiceProvider::checkCookieLogin($ZTPCookie);
        $url = request('url', 'get');
        return ResourceServiceProvider::getFormFillContent(getServerIp(), $url, $userInfo);
    }

    /**
     * 水印开关接口 网关上提供接口 /access/gateway/getWatermark
     * @return array|false[]
     * @throws Exception
     */
    public function getWatermark()
    {
        $ZTPCookie = request('ZTP_Cookie', 'cookie');
        [$session, $userInfo] = LoginServiceProvider::checkCookieLogin($ZTPCookie);

        $switch = WatermarkServiceProvider::isOpenWatermark($userInfo);
        if ($switch) {
            return WatermarkServiceProvider::getWatermarkContent($session);
        }
        return ['switch' => false];
    }

    /**
     * 记住用户密码接口 网关上提供接口 /access/gateway/rememberPassword
     * @return array
     * @throws Exception
     */
    public function rememberPassword()
    {
        $resId = request('ResID', 'request');
        $ZTPCookie = request('ZTP_Cookie', 'cookie');
        [$session, $userInfo] = LoginServiceProvider::checkCookieLogin($ZTPCookie);
        $passwordType = request('passwordType') ?? '';
        switch ($passwordType) {
            case 'passwordCer':
                $data = [
                    'ResId' => $userInfo['ResID'],
                    'UserId' => $userInfo['Uuid'],
                    'Account' => base64_decode(request('account')),
                    'Cer' => base64_decode(request('cer')),
                    'CerName' => request('cerName'),
                    'Password' => base64_decode(request('password')),
                    'IsRememberAccount' => request('remAccount'),
                    'IsRememberPassword' => request('remPassword'),
                    'IsRememberCer' => request('remCer'),
                    'AutoLogin' => request('AutoLogin'),
                ];
                lib_yar::clients('ztpCenter', 'saveRememberPasswordCer', $data);
                break;
            default:
                $data = [
                    'ResId' => $resId ?: $userInfo['ResID'],
                    'UserId' => $userInfo['Uuid'],
                    'Account' => base64_decode(request('account')),
                    'Password' => base64_decode(request('password')),
                    'AutoLogin' => request('AutoLogin'),
                ];
                lib_yar::clients('ztpCenter', 'saveRememberPassword', $data);
        }
        return ['code' => 0, 'msg' => 'success'];
    }

    /**
     * 获取资源信息 网关上提供接口 /access/gateway/resInfo
     *
     * @return mixed
     * @throws Exception
     */
    public function resInfo()
    {
        $key = request('key', 'request');
        $data = json_decode(doXorDecrypt(Base64DeExt($key), 'rdpkey'), true);
        if (empty($data['resId'])) {
            T(********);
        }
        $ZTPCookie = request('ZTP_Cookie', 'cookie');
        list($session, $userInfo) = LoginServiceProvider::checkCookieLogin($ZTPCookie);
        $ResID = request('resId', 'request', 0, 'int');
        hlp_check::checkEmpty($ResID);
        if ((int)($data['resId']) !== $ResID) {
            T(********);
        }
        if (!empty($data['AddAuth']) && !LoginServiceProvider::isAddAuth($session['Token'])) {
            T(21148004);
        }
        $AuthAppID = isset($session['AuthAppID']) ? json_decode($session['AuthAppID'], true) : [];
        if (!in_array($ResID, $AuthAppID, false)) {
            AgentServiceProvider::log("ResId: {$ResID}:" . var_export($AuthAppID, true));
            T(********);
        }
        $resInfo = AppServiceProvider::getAppConfigResInfo($ResID);
        if (empty($resInfo)) {
            AgentServiceProvider::log("ResId: {$ResID}:" . var_export($resInfo, true));
            T(********);
        }
        hlp_match::checkAccessType($resInfo['APPType']);
        if ($resInfo['APPType'] === ACCESS_TYPE_RDP && $resInfo['RealUrl'] === get_client_ip()) {
            T(********);
        }
        GatewayServiceProvider::checkGatewayPolicy($session['Token'], $ResID);
        if (!empty($resInfo['OtherField']) && !empty($resInfo['OtherField']['remitSwitch'])) {
            $Account = ['AutoLogin' => 1, 'RemitSwitch' => 1];
            $Account['account'] = $Account['password'] = '';
        } else {
            $Account = ResourceServiceProvider::getResAccountInfo($session['Uuid'], $data['resId'], $resInfo['APPType'], ($resInfo['APPSubType'] ?? ''));
        }
        $server = explode(':', getServerAddr());
        $Data = ['ResID' => $ResID, 'ProxyProtocol' => 'https'];
        $Data['AccessTypeID'] = $resInfo['APPType'];
        $Data['Uuid'] = $session['Uuid'];
        $Data['UserName'] = $session['UserName'];
        $Data['ProxyHost'] = $server[0];
        $Data['ProxyPort'] = $server[1] ?? '443';
        $Data['ControlUrl'] = GatewayServiceProvider::getControlUrlPrefix();
        $Data['account'] = $Account['account'] ?? '';
        $Data['password'] = aesEncrypt($Account['password'] ?? '');
        $Data['RemitSwitch'] = $Account['RemitSwitch'] ?? 0;
        $Data['passwordLength'] = strlen($Account['password']);
        $Data['AutoLogin'] = $Account['AutoLogin'] ?? 0;
        $Data['GrantType'] = $resInfo['APPSubType'];
        $Data['cer'] = aesEncrypt($Account['cer'] ?? '');
        $Data['cerLength'] = strlen($Account['cer']);
        $Data['cerName'] = $Account['cerName'] ?? '';
        $Data['rememberCerInfo']['remAccount'] = $Account['isRememberAccount'] ?? '';
        $Data['rememberCerInfo']['remPassword'] = $Account['isRememberPassword'] ?? '';
        $Data['rememberCerInfo']['remCer'] = $Account['isRememberCer'] ?? '';
        return $Data;
    }

    /**
     * 用户不通过原因 网关和控制中心的403页面都可能调用
     *
     * @return mixed
     * @throws Exception
     */
    public function errmsg(): array
    {
        $deviceId = request('deviceid', 'request');
        $resId = request('resId', 'request');
        $token = request('token', 'request');
        $error_code = request('error_code', 'request');
        if ($error_code === '0x0019' || $error_code === '0x0020') {
            $message = L(********);
        } elseif (!empty($resId) && !empty($token) && $error_code === '0x0018') {
            $message = ResourceServiceProvider::getErrMessage($token, $resId);
        } else {
            $message = LoginServiceProvider::getUserErrMessage($error_code, $deviceId);
        }
        $clientMsg = L(********);
        $needClient = strpos($message, $clientMsg) !== false;
        return ['message' => $message, 'needClient' => $needClient];
    }

    /**
     * 添加guacamole会话 接口运行在控制中心，但是在网关上调用
     *
     * @return mixed
     * @throws Exception
     */
    public function addSession()
    {
        $columns = ['RemoteAddr', 'TunnelID', 'DateStart', 'Protocol', 'ResName', 'ResID', 'DeviceID', 'DevInfo',
            'UserID', 'UserName', 'TrueName', 'Token', 'IsFinished', 'DesAddr', 'DesPort', 'LoginFrom', 'ResTag', 'GateWayIp'];
        $params = [];
        foreach ($columns as $column) {
            $params[$column] = request($column, 'request');
        }
        //添加设备名称
        if (!empty($params['DeviceID'])) {
            $devInfo = DeviceServiceProvider::getOneInfo($params['DeviceID']);
            $params['DevName'] = $devInfo['DevName'] ?? '';
        }
        if (!empty($params['DevInfo'])) {
            $params['DevInfo'] = SystemServiceProvider::getDevInfo(false, false, $params['DevInfo']);
        }
        $res = GuacamoleServiceProvider::saveSession($params);
        return ['res' => $res];
    }

    /**
     * 修改guacamole会话 接口运行在控制中心，但是在网关上调用
     *
     * @return mixed
     * @throws Exception
     */
    public function editSession()
    {
        $columns = ['FinishReason', 'TunnelID', 'IsFinished', 'DateEnd'];
        $params = [];
        foreach ($columns as $column) {
            $val = request($column, 'request', false);
            if (!empty($val)) {
                $params[$column] = $val;
            }
        }
        hlp_check::checkEmpty($params['TunnelID']);
        $cond = ['TunnelID' => $params['TunnelID']];
        //添加连接时长逻辑
        $findInfo = GuacamoleServiceProvider::getInfo($cond);
        if (!empty($findInfo['DateStart'])) {
            $params['LinkLen'] = strtotime($params['DateEnd']) - strtotime($findInfo['DateStart']);
        }

        $res = GuacamoleServiceProvider::saveSession($params, $cond);
        return ['res' => $res];
    }

    /**
     * guacamole心跳 接口运行在控制中心，但是在网关上调用
     *
     * @return mixed
     * @throws Exception
     */
    public function guacHeart()
    {
        $TunnelIds = request('TunnelIDs', 'request');
        hlp_check::checkEmpty($TunnelIds);
        $TunnelIds = explode(',', $TunnelIds);
        $list = GuacamoleServiceProvider::updateHeart($TunnelIds);
        return ['list' => $list];
    }

    /**
     * sdp_err错误中转处理 接口运行在控制中心，但是在网关上调用
     *
     * @return void
     * @throws Exception
     */
    public function sdpErr(): void
    {
        // 1表示前端页面跳过来，否则从网关服务跳过来。
        $from = request('from', 'request');
        // 网关跳过来，并且不是控制器
        if (empty($from)) {
            $this->gatewayErr();
        }
        $errcode = request('errcode', 'request');
        $deviceid = request('deviceid', 'request');
        $referToken = request('referToken', 'request');
        $token = request('sessionid', 'request');
        $resid = request('resid', 'request');

        // 控制器映射地址/域名
        [$urlPrefix, $isContrl] = GatewayServiceProvider::getHostInfo();
        $osType = request('osType', 'request');
        $indexUrl = hlp_common::isMobile($osType) ? "mobile/ui/wel.html" : "access/ui/index.html";
        $refer = lib_redis::get('ASM_Refer:', $referToken);
        $resInfo = AgentServiceProvider::getResourceByRefer($refer);
        if (empty($resInfo)) {
            // 资源不存在,没有再判断一次是否为依赖资源的子资源，所以不会跳回到认证页面会直接提示没有权限
            $params = "error_code={$errcode}&deviceid={$deviceid}&resId={$resid}&token={$token}";
            gotoUrl("{$urlPrefix}/{$indexUrl}#/access/error?{$params}");
        }
        $resId = $resInfo['ResID'] ?? '';
        $session = LoginServiceProvider::getSessionInfo($token, 'user');
        //此处加个缓存判断，如果workman同步数据已经异常，不能无限重试，试3次就显示错误
        $isNeedStop = false;
        if (in_array($errcode, ['0x0012', '0x0018'])) {
            $retryKey = $errcode . '_' . $deviceid . "_" . $token;
            //判断当前请求下发重试是否已重试过
            if (!\lib_redis::setnx('ASG_ReTryTimes:', $retryKey, 1, 60)) {
                // 增加计数器，并检查是否超过限制
                $count = lib_redis::incr('ASG_ReTryTimes:', $retryKey);
            } else {
                $count = 1;
            }
            // 计数器，检查是否超过限制
            if ($count > 2) {
                $isNeedStop = true;
            }
        }
        // 未强化认证，未登录，未携带cookie都需要走流程，否则直接跳错误页面展示。
        if (!in_array($errcode, ['0x0010', '0x0012', '0x0013', '0x0018'], true) || $isNeedStop) {
            // 其他错误
            $params = "error_code={$errcode}&deviceid={$deviceid}&resId={$resid}&token={$token}";
            gotoUrl("{$urlPrefix}/{$indexUrl}#/access/error?{$params}");
        }
        if (!empty($session['Uuid'])) {
            $resInfo['OS_TYPE'] = $osType;
            $resInfo['IS_CLIENT'] = STRING_FALSE;
            $resInfo['referToken'] = $referToken;
            $url = AgentServiceProvider::getResourceUrl($token, $urlPrefix, $session, $resInfo);
            if (!empty($url)) {
                cutil_php_log($url, 'ZtpGateway');
                gotoUrl($url);
            }
            // 资源平台路由地址
            $sourceUrl = hlp_common::isMobile($osType) ? "/access/resource" : "/source/list";
            gotoUrl("{$urlPrefix}/{$indexUrl}?resId={$resId}&resurl=" . urlencode($refer) . "#{$sourceUrl}");
        } else {
            gotoUrl("{$urlPrefix}/{$indexUrl}?report_code={$errcode}&resId={$resId}&resurl=" . urlencode($refer));
        }
    }

    /**
     * 从网关跳到控制器
     * @throws Exception
     */
    private function gatewayErr(): void
    {
        $errcode = request('errcode', 'request');
        $deviceid = request('deviceid', 'request');
        $refer = request('refer', 'request');
        $token = request('sessionid', 'request');
        $resid = request('resid', 'request');
        $uuid = request('uuid', 'request');
        $queryHost = request('host', 'request');
        $controlUrlPrefix = GatewayServiceProvider::getControlUrlPrefix();

        if (in_array($errcode, ['0x0010', '0x0012', '0x0013', '0x0018'], true)) {
            $singleLoginUrl = AppServiceProvider::getSingleLoginUrl($refer);
            if (!empty($singleLoginUrl)) {
                gotoUrl($singleLoginUrl);
            }
            switch ($errcode) {
                case '0x0012':
                    // 网关session不存在触发同步
                    $session = LoginServiceProvider::getSessionInfo($token, 'one');
                    if (!empty($session['Uuid'])) {
                        LoginServiceProvider::syncSession($token);
                        sleep(1); // 同步后睡1秒钟，再继续
                        // gotoErrorUrl("0x1018");
                    }
                    break;
                case '0x0018':
                    // 网关用户资源策略不存在触发同步
                    $resourcePolicyInfo = ResourceServiceProvider::getUserResourcePolicy($token, $resid);
                    if (empty($resourcePolicyInfo['Result'])) {
                        ResourceServiceProvider::syncUserResource($token, $resid);
                        sleep(1); // 同步后睡1秒钟，再继续
                    }
                    break;
                default :
                    break;
            }
        }
        // 中转到控制器的sdpErr交易
        $md5 = md5($refer);
        lib_redis::set('ASM_Refer:', $md5, $refer, 3600);
        $middleControlUrl = "{$controlUrlPrefix}/mobile/ui/ztpindex.html?report_code={$errcode}&host={$queryHost}" .
            "&Uuid={$uuid}&token={$token}&deviceid={$deviceid}&resid={$resid}&referToken={$md5}";
        gotoUrl($middleControlUrl);
    }

    /**
     * 根据token获取网关列表
     * @return false|true|array
     * **/
    public function getGateway()
    {
        $token = LoginServiceProvider::getLoginToken();
        $aData = GatewayServiceProvider::getGateway($token);
        return $aData;
    }


    /**
     * 根据token获取IP资源对应的网关ID
     * @return false|true|array
     * **/
    public function getResGateway()
    {
        $token = LoginServiceProvider::getLoginToken();
        $resId = request('ResID', 'request');
        $aData = GatewayServiceProvider::getResGateway($token, $resId);
        return $aData;
    }

    /**
     * 上报日志，对应老交易 writelog
     *
     * @return array
     * @throws Exception
     */
    public function writelog(): array
    {
        $params = [];
        $params['content'] = request('content', 'request');
        hlp_check::checkEmpty($params['content']);
        $params['clientIP'] = getRemoteAddress();
        cutil_php_log(var_export(Base64DeExt($params['content']), true), "activecall_" . $params['clientIP']);
        return ['clientIP' => $params['clientIP']];
    }

}
