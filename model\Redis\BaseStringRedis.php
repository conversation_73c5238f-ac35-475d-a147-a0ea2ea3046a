<?php

/**
 * Description: redis string 基本类 本地网关和控制中心共用数据、控制中心若不是双机时，下发到其他网关的字段会过滤。
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: BaseRedis.php 175039 2022-05-05 07:33:41Z duanyc $
 */
class BaseStringRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASG_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = '';

    /**
     * 是否同步到ASG
     *
     * @var string
     */
    protected static $remote = false;

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => 'LifeTime',
    ];

    /**
     * 获取key
     *
     * @param array $keys
     *
     * @return mixed
     */
    protected static function getKey($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }

        return static::TABLE_NAME . ":" . implode('_', $keys);
    }

    /**
     * 单条
     *
     * @param array $keys
     *
     * @return mixed
     */
    protected static function get( ...$keys)
    {
        if (empty($keys)) {
            return false;
        }

        $key = static::getKey($keys);
        return lib_redis::get(static::PREFIX, $key);
    }

    /**
     * 修改
     *
     * @param string $value
     * @param int $expire
     * @param array $keys
     *
     * @return mixed
     */
    protected static function set($value = '', $expire = 0, ...$keys)
    {
        if (empty($keys)) {
            return false;
        }

        $key = static::getKey($keys);
        $oldData = lib_redis::get(static::PREFIX, $key);
        if ($oldData === $value) {
            return false;
        }
        if (!empty(static::$remote)) {
            static::push(static::PREFIX . $key, $value, $expire);
        }
        cutil_php_log(['set', $key, $value], "model_" . static::TABLE_NAME);
        return lib_redis::set(static::PREFIX, $key, $value, $expire);
    }

    /**
     * 删除
     *
     * @param array $keys
     *
     * @return boolean
     */
    public static function del(...$keys)
    {
        if (empty($keys)) {
            return false;
        }

        $key = static::getKey($keys);
        if (!empty(static::$remote)) {
            static::pushDel(static::PREFIX . $key);
        }
        cutil_php_log(['del', $key], "model_" . static::TABLE_NAME);
        return lib_redis::del(static::PREFIX, $key);
    }

    /**
     * 下发数据到网关
     *
     * @param $key
     * @param string $value
     * @param int $expire
     */
    protected static function push($key, $value = '', $expire = 86400): void
    {
        try {
            WorkerUdpSend('MessageToGw', 'setStringRedis', [$key => $value, 'LifeTime' => $expire]);
            cutil_php_log(['push', $key, $value], "model_" . static::TABLE_NAME);
        } catch (Exception $e) {
            cutil_php_log(['push', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }

    /**
     * 下发删除数据到网关
     *
     * @param $key
     */
    protected static function pushDel($key): void
    {
        try {
            WorkerUdpSend('MessageToGw', 'delStringRedis', [$key]);
            cutil_php_log(['pushDel', $key], "model_" . static::TABLE_NAME);
        } catch (Exception $e) {
            cutil_php_log(['pushDel', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }
}
