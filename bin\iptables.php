<?php

/**
 * Description: 防火墙相关脚本，查看帮助文档：php /var/www/access.asm.com/bin/iptables.php -a help
 * User: <EMAIL>
 * Date: 2020/11/29 23:32
 * Version: $Id: install.php 164888 2021-12-15 01:48:08Z duanyc $
 */
/* 定义关键常量 */
define('PATH_CUR', __DIR__);

/** 初始化 */
require PATH_CUR . '/../init.php';

class Iptables
{
    /**
     * 添加到白名单
     * @param array $flag 参数：
     * ip *************
     * ip ***********-*************
     * @throws Exception
     */
    public static function addWhitelist($flag): void
    {
        if (!isset($flag['ip'])) {
            throw new Exception("ip params no exist!");
        }
        $currIp = $flag['ip'] ?? '';
        self::checkParams($currIp);
        $filepath = PATH_ETC . 'iptables.ini';
        $iptables = get_ini_info($filepath);
        $iprange = $iptables['system_trust_iprange_enable'] ?? '';
        $status=false;
        $nowIpRange=explode(',',$iprange);
        $newIpRange=array_unique($nowIpRange);
        if (count($nowIpRange)!=count($newIpRange)){
            $status=true;
        }
        if (strpos($iprange, $currIp) === false || ($status)) {
            $iprange .= !empty($iprange) ? ",{$currIp}" : $currIp;
            if ($status) {
                $iprange=array_merge($newIpRange,$nowIpRange);
                $iprange=array_unique($iprange);
                $iprange=implode(',',$iprange);
            }
            $iptables['system_trust_iprange_enable'] = $iprange;
            ConfigServiceProvider::saveDict('AccessContorl', 'system_sdp', $iprange);
            $trust_iprange = $iptables['trust_iprange_enable'] ?? '';
            ConfigServiceProvider::saveDict('AccessContorl', 'sdp', $trust_iprange);
            write_localfile($filepath, array_to_inistr($iptables));
            cutil_exec_wait(PATH_HTML . "/bin/MakeOpenIP.php");
            echo "write: {$iprange}" . PHP_EOL;
        }
    }

    /**
     * 取消管理删除system_trust_iprange_enable的当前ip
     */
    public static function delWhitelist($flag): void
    {
        if (!isset($flag['ip'])) {
            throw new Exception("ip params no exist!");
        }
        $currIp = $flag['ip'] ?? '';
        self::checkParams($currIp);
        $filepath = PATH_ETC . 'iptables.ini';
        $iptables = get_ini_info($filepath);
        $iprange = $iptables['system_trust_iprange_enable'] ?? '';
        $iprangeArray = [];
        if (strpos($iprange, $currIp) !== false) {
            $iprangeArray = explode(',', $iprange);
            foreach ($iprangeArray as $k => $v) {
                if ($v == $currIp) {
                    unset($iprangeArray[$k]);
                }
            }
            $iprange = implode(',', $iprangeArray);
            $iptables['system_trust_iprange_enable'] = $iprange;
            write_localfile($filepath, array_to_inistr($iptables));
            echo "write: {$iprange}" . PHP_EOL;
        }
    }

    /**
     * 检查参数
     *
     * @param $currIp
     *
     * @return bool
     * @throws Exception
     */
    private static function checkParams($currIp): bool
    {
        if (strpos($currIp, ',') !== false){
            $currIps=explode(',',$currIp);
        }else{
            $currIps = explode('-', $currIp);
            if (count($currIps) > 2) {
                throw new Exception("ip param format not right!");
            }
        }
        foreach($currIps as $ip) {
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                throw new Exception("ip param not right!");
            }
        }
        return true;
    }

    /**
     * 切换为紧急模式后隐身模式关闭
     * @param array $flag 参数：
     * @throws Exception
     */
    public static function emergency($flag): void
    {
        $path = PATH_ETC . "sdp.ini";
        $sdpInfo = get_ini_info($path);
        if ((int)$sdpInfo['RunMode'] === 2) {
            cutil_php_log("change emergency {$sdpInfo['RunMode']}", 'iptables');
            $sdpInfo['RunMode'] = '0';
            $sdpInfo['ENABLE_SDP'] = 'n';
            write_localfile($path, arrayToIniString($sdpInfo));
            lib_yar::clients('policy', 'pushAuthPolicy', []);
            // 通知网关更新
            $gwData = [
                'ChangeRunMode' => true,
                'ChangeTrustIprange' => false
            ];
            WorkerUdpSend('MessageToGw', 'getGwconfig', $gwData);
        }
    }

    /**
     * 测试
     * @param array $flag 参数：
     * @throws Exception
     */
    public static function test($flag): void
    {
        echo 'test:' . time() . PHP_EOL;
    }
}

try {
    $serv = new cls_cmdserv(new Iptables());
    $serv->handle();
} catch (Exception $e) {
    echo "code: " . $e->getCode() . ", message: ". $e->getMessage();
}
