<?php

/**
 * Description: 用户日志类型
 * User: <EMAIL>
 * Date: 2022/05/06 23:32
 * Version: $Id$
 */

class UserLogTypeModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUserLogType';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'allname' => 'ID,Name'
    ];

    /**
     * 获取类型ID与名字映射
     *
     * @return mixed
     */
    public static function getAllName()
    {
        self::$data = [];
        $column = self::$columns['allname'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $allNames = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $allNames[$row['ID']] = $row['Name'];
            }
        }
        return $allNames;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
