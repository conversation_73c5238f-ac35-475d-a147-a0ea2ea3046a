<?php

/**
 * Description: 设备相关
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: DeviceController.php 172561 2022-04-01 09:25:22Z renchen $.
 */
!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . '/BaseController.php';


class DeviceController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['infoprocess' => true, 'envStatus' => true];

    /**
     * 请求方式
     * @var array
     */
    public static $requestType = array(
        'cookie' => 'Cookie',
        'ipgetmac' => 'IpGetMac',
        'control' => 'Control',
    );

    /**
     * 获取设备环境状态
     *
     * @return mixed
     */
    public function envStatus()
    {
        return [
            'IsInternal' => (int)IS_INTERNAL
        ];
    }

    /**
     * 参数校验.
     * @return mixed
     * @throws Exception
     */
    public function checkParams()
    {
        // 通过何种方式获取设备信息
        $params['gettype'] = request('gettype', 'request');
        if (!isset(self::$requestType[$params['gettype']])) {
            T(21100002);
        }

        // 将类型转为大驼峰方式，方便service中的函数调用
        $params['getAccessType'] = self::$requestType[$params['gettype']];
        $params['deviceid'] = request('deviceid', 'request', 0, 'int');
        $params['mac'] = request('Mac', 'request');
        // 防止URL传mac=undefined的情况，用$_COOKIE去接收mac信息
        if ($params['gettype'] === 'cookie') {
            $params['mac'] = request('Mac', 'cookie');
        }

        // 用于在小助手模式下,设备信息变动,小助手主动调用web接口,放开网络 当前仅用于小助手调用(isopen)  zhangkb 20140331
        $params['isopennet'] = request('isopennet', 'request');
        // dev_xml参数是否加密
        $params['encode'] = request('encode', 'request', 0, 'int');
        $params['dev_xml'] = request('dev_xml', 'request');
        $params['dev_json'] = request('dev_json', 'request');

        // 设备的信息xml
        if (1 === $params['encode']) {
            $params['dev_xml'] = addslashes(Base64DeExt($params['dev_xml']));
        } elseif (2 === $params['encode']) {
            $params['dev_xml'] = utf8ToGbk($params['dev_xml']);
        }

        // 设备管理器ID
        $params['license'] = request('license', 'request');
        // 是否是来宾
        $params['is_guest'] = request('is_guest', 'request', 0, 'int');
        // 是否是手机
        $params['is_mobile'] = request('is_mobile', 'request', 0, 'int');
        $params['PClient'] = $params['is_mobile'];

        // 通过环境监测(js)获取到的数据(系统)browser
        $params['OS'] = request('os_platform', 'request');
        // 通过环境监测(js)获取到浏览器信息
        $params['browser'] = request('browser', 'request');
        // 尝试从portal认证的URL参数中获取得到,确认参数是个MAC
        $params['wlanusermac'] = request('wlanusermac', 'request');
        $this->checkOtherParams($params);
        return $params;
    }

    /**
     * 检查其他参数
     *
     * @param $params
     *
     * @throws Exception
     */
    public function checkOtherParams(&$params)
    {
        // 重定向后地址栏里面的ASCID
        $params['UrlAscid'] = request('UrlAscid', 'request');
        // 重定向后地址栏里面的ASWID哨兵ID
        $params['UrlAswid'] = request('UrlAswid', 'request');

        $params['ascid'] = ('' !== $params['UrlAscid'] && 'undefined' !== $params['UrlAscid']) ? $params['UrlAscid'] : '';
        $params['aswid'] = ('' !== $params['UrlAswid'] && 'undefined' !== $params['UrlAswid']) ? $params['UrlAswid'] : '';
        $params['ascid'] = IsStrHaveMac($params['ascid']) ?: '';
        $params['aswid'] = IsStrHaveMac($params['aswid']) ?: '';

        // 伪造hard
        $diskId = DeviceServiceProvider::getLastDeviceDiskID();
        $params['fakeHard'] = request('fakeHard', 'request', $diskId);
        // 新手机改版标记 2015-03-17 yancheng add
        $params['newMobile'] = request('newMobile', 'request', 0, 'int');
        // 监测php每个步骤运行时长
        $params['subphpruntime'] = array();
        $params['forbid'] = request('forbid', 'request');
        $params['firstUrl'] = request('firstUrl', 'request');
        $params['OSInstallTime'] = request('OSInstallTime', 'request');
        // 链接IP 用于GateIP
        $params['linkIP'] = getRemoteAddress();
        if ('' === $params['linkIP']) {
            T(21103007);
        }

        // 插入设备的初始值
        $params['Registered'] = -2;
        // 获取ASM设备标志ID
        $params['licenseInfo'] = ConfigServiceProvider::getDictAll('License');
        // 客户端是否是IPV6地址
        $params['ipVer'] = strpos($params['linkIP'], ':') !== false ? 6 : 4;

        // 微信认证id
        $params['wechatid'] = request('wechatid', 'request', 0, 'int');
        // 返回数据类型
        $params['data_type'] = request('data_type', 'request');
        // 小助手漫游，从一台dasc切换到另外一台
        $params['roamflag'] = request('roamflag', 'request', 0, 'int');

        // 指纹信息
        $params['BrowserFingerprint'] = request('BrowserFingerprint', 'request');
        // portal准入basIP
        $params['basIP'] = request('basIP', 'request');
        if ($params['basIP'] !== 'clean' && !filter_var($params['basIP'], FILTER_VALIDATE_IP)) {
            $params['basIP'] = '';
        }
    }

    /**
     * 设备信息上报及获取，对应老交易 getdeviceinfoprocess.
     *
     * @return mixed
     * @throws Exception
     *
     */
    public function infoprocess()
    {
        $params = $this->checkParams();
        // 获取当前回话的有效设备ID
        $params['Token'] = LoginServiceProvider::getLoginToken();
        $params['SessionDeviceID'] = $this->getSessionData('DeviceID');
        $deviceInfoService = DeviceServiceProvider::initDeviceInfoService($params);
        // 记录原始请求信息
        $deviceInfoService->writeLog('all params：' . var_export(lib_request::$requests, true));
        $deviceInfoService->writeLog('get params：' . var_export($params, true));
        include_function('device');
        $PhoneInfo = getDeviceType('control' === $deviceInfoService->params['gettype'] ? $deviceInfoService->params['dev_xml'] : '');
        $deviceInfoService->params['phoneinfo'] = $PhoneInfo;
        $deviceInfoService->writeLog("PhoneInfo" . var_export($PhoneInfo, true), 'DEBUG');
        $deviceInfoService->writeLog($deviceInfoService->params['dev_xml']);

        // 获取设备类型
        $deviceInfoService->params['deviceType'] = $deviceInfoService->getDeviceTypeId($PhoneInfo['OSName']);
        $deviceInfoService->params['deviceSubType'] = $deviceInfoService->getDeviceSubTypeId($PhoneInfo['OSName']);

        // 记录初始参数
        $content = PHP_EOL . ($deviceInfoService->params['PClient'] ? 'PClient' : 'PC') . '进入页面参数:';
        $content .= 'type=' . $deviceInfoService->params['gettype'] . '##mac=' . $deviceInfoService->params['mac'];
        $content .= '##deviceid=' . $deviceInfoService->params['deviceid'] . '##ascid=';
        $content .= $deviceInfoService->params['UrlAscid'] . '##aswid=' . $deviceInfoService->params['UrlAswid'];
        $deviceInfoService->writeLog($content);
        $return = false;
        $deviceInfoService->setDeviceInfo();
        // 设备存在更新设备，不存在则插入设备
        if ($deviceInfoService->isDeviceExist()) {
            // 更新设备在线信息
            // $deviceInfoService->updateDeviceOnline();
            // 更新设备缓存信息
            $deviceInfoService->deviceOperationService->updateDeviceCacheAndLastTime([
                'TDevice.Online' => 1,
                'DeviceID' => $deviceInfoService->deviceId,
                'gettype' => $deviceInfoService->params['gettype'],
                'basIP' => $deviceInfoService->params['basIP'] ?? '',
            ]);
            // 更新设备计算机名称，操作系统名称信息
            $deviceInfoService->updateDeviceComputerNameAndOsName();
            // 如果存在Mac重复的设备，告知MVG取最新的一条更新在线时间等（重构新增业务）
            $deviceInfoService->checkRepeatDevice();
            // 执行完所有流程返回设备信息
            $return = $deviceInfoService->returnDeviceInfo();
            // 这里return返回数据，用老的ajax.php访问会返回数据包了一层
            if (!empty($return['DeviceID'])) {
                $BrowserDeviceID = $params['SessionDeviceID'] ?: $return['DeviceID'];
                $return['BrowserDeviceID'] = IS_INTERNAL ? $return['DeviceID'] : $BrowserDeviceID;
            }
        }

        // 更新内外网状态
        $this->updateIsInternal($return['DeviceID'] ?? ($deviceInfoService->deviceId ?? 0));

        if (empty($return)) {
            if (API_VERSION < '1.0') {
                return $return;
            }
            T(21103013);
        }

        return $return;
    }

    /**
     * 获取设备注册、编辑状态，对应老交易 dev_info
     * 这里之前是调用getdeviceinfoprocess交易中的ReturnDeviceInfo()
     * 重构之后禁止这样调用，所以这里重写这个操作
     * 只给小助手菜单“查看设备详情”用.
     *
     * @return array
     * @throws Exception
     *
     */
    public function info()
    {
        $params = [];
        $deviceId = request('deviceId', 'request', 0, 'int');
        $params['form'] = request('form', 'request');
        $params['newMobile'] = 0;
        $params['is_mobile'] = 0;
        $params['wechatid'] = 0;
        $params['getAccessType'] = 'Control';
        $params['basIP'] = request('basIP', 'request'); // 用于小助手传递basIP,Portal放开网络
        $params['deviceid'] = $deviceId;
        hlp_check::checkEmpty($deviceId);
        $deviceInfoService = DeviceServiceProvider::initDeviceInfoService($params);
        $deviceInfoService->writeLog(var_export($params, true));
        $devInfo = $deviceInfoService->returnDeviceInfo();
        if ($devInfo) {
            $deviceInfoService->setPortalServer();
            // 这是原有业务逻辑
            //如果是小助手跳转过来进行注册
            if ($params['form'] !== "AgentReg") {
                // 让设备重新填写注册信息
                $devInfo['Registered'] = -1;
            }
            // 标记为编辑用户信息
            $devInfo['editDevInfo'] = 1;
        } else {
            T(21103006);
        }
        // 更新内外网状态
        $this->updateIsInternal($devInfo['DeviceID'] ?? ($deviceInfoService->deviceId ?? 0));
        return $devInfo;
    }

    /**
     * 获取注册信息，对应老交易 get_device_reginfo.
     *
     * @throws Exception
     */
    public function reginfo(): array
    {
        $params = [];
        $params['deviceId'] = request('DeviceID', 'request', 0, 'int');
        $params['isGuest'] = request('IsGuest', 'request', 0, 'int');
        $service = DeviceServiceProvider::initDeviceRegisterService();
        $return = $service->getReginfo($params);
        $return['regConfig'] = ConfigServiceProvider::getRegisterConfig($params['isGuest']);
        return $return;
    }

    /**
     * 获取指定设备id是否被注册，对应老交易 get_device_isreg.
     *
     * @return array
     * @throws Exception
     *
     */
    public function isReg(): array
    {
        $DeviceID = request('deviceid', 'request', 0, 'int');
        if (!$DeviceID) {
            return [];
        }
        $registered = DeviceServiceProvider::getDevRegistered($DeviceID);

        return ['Registered' => $registered];
    }

    /**
     * 获取设备网络状态
     * @return array|int[]
     * @throws Exception
     */
    public function netStatus(): ?array
    {
        $serviceStatus = DeviceServiceProvider::initDeviceStatusService();
        $return = [];
        $deviceId = 0;
        $mac = '';
        if (isset(lib_request::$requests["deviceId"])) {
            $deviceId = request('deviceId', 'request', 0, 'int');
            if ($deviceId <= 0) {
                T(21100012, ['columnName' => 'deviceId']);
            }
            $return = $serviceStatus->getByDeviceId($deviceId);
        } elseif (isset(lib_request::$requests["mac"])) {
            $mac = IsStrHaveMac(request('mac', 'request', ''));
            if ($mac === false) {
                T(21100012, ['columnName' => 'mac']);
            }
            $return = $serviceStatus->getByMac($mac);
        } elseif (isset(lib_request::$requests["ip"])) {
            $ip = request('ip', 'request', '');
            if (!filter_var($ip, FILTER_VALIDATE_IP)) {
                T(21100012, ['columnName' => 'ip']);
            }
            $return = $serviceStatus->getByIp($ip);
        } else {
            T(21103008);
        }
        $others['DeviceID'] = $deviceId;
        $others['Mac'] = $mac;
        $others['portalRedirect'] = request('portalRedirect', 'request', 0, 'int');
        // 如果网络是放开的，且不是紧急模式，开启了portal准入，就重新放开一下网络 Add by RC 2021/10/26
        // 背景：1. 可信设备重定向 2. 保活期设备重新接入
        $params = array_merge($return, $others);
        $ret = NetServiceProvider::portalFastOpenNet($params);
        if ($ret < -1) {
            // 修改入网状态，让用户有机会入网（只在portal准入且桥那边已放开的情况修改）
            // portalFastOpenNet返回值说明
            // 返回值 0：未启用portal准入，或者入网状态查询为未入网，不会进入到这个逻辑
            // 返回值-1: 一分钟内访问入网状态，不修改入网状态的值，认为已入网不发起主动认证，不会进入到这个逻辑
            // 返回值-2: 获取入网状态超过1分钟，且未安装小助手，即使桥认为已入网，这里修改为未入网，不发起主动认证
            // 返回值-3：获取入网状态超过1分钟，安装了小助手，且IP变更了，这里修改为未入网，不发起主动认证
            $return['deviceStatus'] = 0;
        }
        return $return;
    }

    /**
     * 获取入网信息
     * @return array|int[]
     * @throws Exception
     */
    public function netInfo(): ?array
    {
        $return = [];
        $return['ZtpModule'] = getmoduleregist(11);
        return $return;
    }

    /**
     * 获取在线设备列表 对应老接口 get_device_online
     * @return array|int[]
     * @throws Exception
     */
    public function online(): ?array
    {
        $params = [];
        $params['DeviceID'] = request('DeviceID', 'request', 0, 'int');
        $params['UserName'] = request('UserName', 'request', '');
        $params['AuthType'] = request('AuthType', 'request', 'User');

        $errorField = [];
        //禅道bugID=1231，去掉校验 todo 后续统一走用户ID查询
        /*if (!empty($params['UserName'])) {
            $checkResult = hlp_check::checkRegex('username', $params['UserName']);
            if (!$checkResult['state']) {
                $errorField[] = 'UserName';
            }
        } else {
            $params['UserName'] = '';
        }*/

        if (!empty($params['AuthType'])) {
            $checkResult = hlp_check::checkUserType($params['AuthType'], true);
            if (!$checkResult['state']) {
                $errorField[] = 'AuthType';
            }
        } else {
            $params['AuthType'] = '';
        }

        if (!empty($errorField)) {
            T(21100012, ['columnName' => implode(',', $errorField)]);
        }
        $session = LoginServiceProvider::getSessionInfo(LoginServiceProvider::getLoginToken(), 'user');
        $return = DeviceServiceProvider::getDeviceOnlineList($params['DeviceID'], $params['UserName'], $params['AuthType'], $session['Uuid']);
        return $return;
    }

    /**
     * 检测设备是否设备隔离、ip/mac违规、指纹违规，对应老交易 check_device_illegal
     *
     * @return mixed
     * @throws Exception
     */
    public function illegal()
    {
        $params = [];
        $params['ip'] = request('ip', 'request');
        $params['mac'] = request('mac', 'request');
        $params['hard'] = request('hard', 'request');
        hlp_check::checkEmpty($params['ip']);
        hlp_check::checkEmpty($params['mac']);
        if (empty($params['hard'])) {
            $params['hard'] = $params['mac'];
        }
        if (!filter_var($params['ip'], FILTER_VALIDATE_IP) || !IsStrHaveMac($params['mac'])) {
            T(21103009);
        }
        $deviceid = DeviceServiceProvider::queryDeviceID($params['hard'], $params['mac'], $params['ip']);
        $devInfo = DeviceServiceProvider::getDevInfo($deviceid);
        $deviceService = DeviceServiceProvider::initDeviceInfoService();
        $devInfo['ipMacBindIllegal'] = $deviceService->hasIpMacBindIllegal($params['mac']);
        $devInfo['fingerIllegal'] = $deviceService->hasfingerIllegal($deviceid);
        $devInfo['sysAuthStatus'] = 1;
        if ((int)$devInfo['Registered'] != 1) {
            $DevNumIn = SystemServiceProvider::getDevNumIn();
            $TimeIn = SystemServiceProvider::getTimeIn();
            if ($DevNumIn === 0) {
                $devInfo['sysAuthStatus'] = 0;
            } elseif ($TimeIn === 0) {
                $devInfo['sysAuthStatus'] = 2;
            }
        }

        // 返回审核到期提醒配置
        $aClientCheck = ConfigServiceProvider::getDictAll('CLIENTCHECK');
        $devInfo['AuditCallState'] = isset($aClientCheck['IsAuditCall']) ? $aClientCheck['IsAuditCall'] : 0;
        $devInfo['AuditCallDay'] = isset($aClientCheck['AuditCallDay']) ? $aClientCheck['AuditCallDay'] : 0;
        $devInfo['AuditCallContent'] = isset($aClientCheck['AuditCallContent']) ? $aClientCheck['AuditCallContent'] : "";
        $devInfo['AuditStopTime'] = strtotime($devInfo['AuditStopTime']);
        unset($devInfo['TrustStopTime'], $devInfo['CutOffStopTime']);
        $devInfo['deviceID'] = $deviceid;
        return $devInfo;
    }

    /**
     * 获取设备名，对应老交易 getdevicename
     *
     * @return mixed
     * @throws Exception
     */
    public function name()
    {
        $ip = getRemoteAddress();
        $devname = "";
        if (strlen($ip)) {
            // 通过提前获取 0	WORKGROUP\SUNNY-PC	00:24:8C:4B:FA:1C
            $res = cutil_shell_exec(PATH_ASM . "sbin/NetBiosScan  -a " . $ip . " -d 0");
            preg_match("/\t(.*)\t/i", $res, $out);
            $devname = $out[1] ?? '';
        }
        file_put_contents(PATH_HTML . "/a/getDeviceName.txt", $devname);
        return true;
    }

    /**
     * 是否注册设备相关信息，对应老交易，无用交易暂不处理 is_regdev
     *
     * @return mixed
     * @throws Exception
     */
    public function regdev()
    {
        return ['res' => 1];
    }

    /**
     * 获取设备ID
     *
     * @return mixed
     * @throws Exception
     */
    public function deviceId()
    {
        $ip = request('ip', 'request');
        $mac = request('mac', 'request');
        $hard = request('hard', 'request');
        $deviceId = DeviceServiceProvider::queryDeviceID($hard, $mac, $ip);
        return ['DeviceID' => $deviceId];
    }

    /**
     * 卸载小助手上报信息，对应老交易 uninstall_assistant
     *
     * @return mixed
     * @throws Exception
     */
    public function uninstall()
    {
        $message = 'OK';
        $deviceId = request('deviceid', 'request', 0, 'int');
        $type = request('type', 'request', 0, 'int');
        hlp_check::checkEmpty($deviceId);
        if ($type === 1) {
            $params = ['device_id' => $deviceId, 'return' => 1];
            $result = lib_yar::clients('net', 'deviceDelete', $params, '127.0.0.1', 30000);
            if (empty($result['state'])) {
                T(21100000);
            }
        } else {
            DeviceServiceProvider::uninstall($deviceId);
        }
        return $message;
    }

    /**
     * 获取设备当前在线表对应的用户ID
     * @return mixed
     * @throws Exception
     */
    public function userId()
    {
        $deviceId = request('deviceId', 'request', 0, 'int');
        $onlineDevice = NacOnLineDeviceModel::getOne($deviceId, 'user');
        if (empty($onlineDevice['UserID'])) {
            return ['UserID' => 0];  //查询不到对应的用户ID时  返回0  此时不展示管理入口
        }
        $userId = $onlineDevice['UserID'];
        return ['UserID' => $userId];
    }

    /**
     * 重启小助手
     * @return string[]
     * @throws Exception
     */
    public function restartAgent()
    {
        $deviceId = request('deviceId', 'request', 0, 'int');
        cutil_exec_wait("msgsender -d '$deviceId' -c '<ASM><TradeCode>SvrMsgToAss</TradeCode><AgentID>$deviceId</AgentID><WhatToDo>Restart</WhatToDo><Msg>小组手版本太低，请重启小组手升级</Msg></ASM>' >/dev/null &");
        return ['msg' => '重启消息发送成功'];

    }
}
