<?php
/**
 * Description: 页面方法
 * User: <EMAIL>
 * Date: 2022/05/06 23:32
 * Version: $Id$
 */

!defined('IN_INIT') && exit('Access Denied');

class hlp_page
{
    /**
     * 显示Oauth2页面
     *
     * @param $RedictUrl
     */
    public static function showOauth2($RedictUrl): void
    {
        echo <<<STR
<!DOCTYPE html>
<html>
<body>
<script language="javascript" type="text/javascript">
    var rect_url = '{$RedictUrl}';
    var pattern = /amp;/g;
    rect_url=rect_url.replace(pattern, '');
    window.location.href=rect_url;
</script>
</body>
</html>
STR;
        exit(0);
    }

    /**
     * 显示表单代填页面
     *
     * @param $data
     */
    public static function showForm($data): void
    {
        echo <<<STR
<!DOCTYPE html>
<html>
<body>
<script></script>
<script>
    var form = document.createElement('form');
    var action_url = '{$data['action']}';
    var pattern = /amp;/g;
    form.action = action_url.replace(pattern, '');
    form.method = 'post';
    var input1 = document.createElement('input');
    var input2 = document.createElement('input');
    input1.type = 'hidden';
    input1.name = '{$data['loginName']}';
    input1.id = '{$data['loginName']}';
    input1.value = '{$data['nameValue']}';
    input2.type = 'password';
    input2.name = '{$data['loginPwd']}';
    input2.id = '{$data['loginPwd']}';
    input2.value = '{$data['pwdValue']}';
    form.appendChild(input2);
    form.appendChild(input1);
    document.body.appendChild(form);
    form.submit();
    setTimeout(function () {
        var homepage = '{$data["home"]}';
        console.log(homepage);
        if(homepage!=""){
            window.location.href = homepage;
        }
    }, 2000);
</script>
</body>
</html>
STR;
        exit(0);
    }

    /**
     * 显示普通资源页面
     *
     * @param $RedictUrl
     */
    public static function showCommon($RedictUrl): void
    {
        echo <<<STR
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
</head>
<body>
<script>
    var href = '{$RedictUrl}';
    var pattern = /amp;/g;
    href = href.replace(pattern, '');
    open_window(href);
    function open_window(link){
        var arg ='\u003cscript\u003etop.location.replace("'+link+'")\u003c/script\u003e';
        var iframe = document.createElement('iframe');
        iframe.src='javascript:window.name;';
        iframe.name=arg;
        document.body.appendChild(iframe);
    }
</script>
</body>
STR;
        exit(0);
    }

    /**
     * 获取ping测试的代码
     *
     * @param $baseUrl
     * @param $formUrl
     * @param $codeName
     *
     * @return string
     */
    private static function getPingTestCode($baseUrl, $formUrl, $codeName): string
    {
        return <<<STR
        // 测试服务器
        function ping_test(ip, code){
            var img = new Image();
            var start = new Date().getTime();
            var flag = false;
            var isCloseWifi = true;
            var hasFinish = false;
            img.onload = function() {
                if ( !hasFinish ) {
                    flag = true;
                    hasFinish = true;
                    window.location.href = "{$baseUrl}{$formUrl}&{$codeName}="+code;
                }
            };
            img.onerror = function() {
                if ( !hasFinish ) {
                    if ( !isCloseWifi ) {
                        flag = true;
                        window.location.href = "{$baseUrl}{$formUrl}&{$codeName}="+code;
                    }
                    hasFinish = true;
                }
            };
            setTimeout(function(){
                isCloseWifi = false;
            },2);
            img.src = 'http://' + ip + '/' + start;
            var timer = setTimeout(function() {
                if ( !flag ) {
                    hasFinish = true;
                    flag = false ;
                    window.location.href = "{$formUrl}&{$codeName}="+code;
                }
            }, 3000);
        }
STR;
    }

    /**
     * 获取外网要跳转的内网地址
     * @return string
     */
    public static function getJumpBaseurl(): string
    {
        $managerAddress = getManagerAddress();
        if (hlp_net::isDomain($managerAddress)) {
            try {
                $baseUrl = ServerServiceProvider::getGotoPrefixUrl();
            } catch (\Exception $e) {
                $baseUrl = "";
                cutil_php_log("errcode:" . $e->getCode() . ", errmsg:" . $e->getMessage() . ":", 'exception');
            }
        } else {
            $managerIp = getManagerIp();
            $baseUrl = get_cururl(true, 'http', $managerIp);
        }
        return $baseUrl;
    }

    /**
     * 显示飞书跳转页
     *
     * @param $code
     * @param $closewindow
     * @param $formUrl
     * @throws Exception
     */
    public static function showFeiShu($code, $closewindow, $formUrl): void
    {
        $isInternal = IS_INTERNAL;
        if (!$isInternal) {
            $managerIp = getManagerIp();
            $baseUrl = self::getJumpBaseurl();
            $pingTestCode = self::getPingTestCode($baseUrl, $formUrl, 'feishucode');
        } else {
            $managerIp = '';
            $pingTestCode = "";
        }
        echo <<<STR
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script type="text/javascript" src="/mobile/ui/babel/feishu/h5-js-sdk-1.4.13.js"></script>
    <script type="text/javascript" src="/mobile/ui/babel/other/jquery-1124.min.js"></script>
</head>
<body>
</body>
<script>
    var code = '{$code}';
    var closewindow = '{$closewindow}';
    var isInternal = '{$isInternal}';
    {$pingTestCode}
    if (window.h5sdk){
        window.h5sdk.ready(function () {
            if (closewindow == '1') {
                window.h5sdk.biz.navigation.close({
                    onSuccess: function (result) {
                    }
                });
            } else {
                if (code == '') {
                    window.h5sdk.device.notification.alert({
                        message: "飞书应用配置错误，请仔细检查！",
                        title: "错误",
                        buttonName: "OK"
                    });
                } else {
                    if (isInternal === '1') {
                      window.location.href = "{$formUrl}&feishucode="+code;
                    } else {
                      ping_test("{$managerIp}", code);
                    }
                }
            }
        });
    } else {
        if (isInternal === '1') {
          window.location.href = "{$formUrl}&feishucode="+code;
        } else {
          ping_test("{$managerIp}", code);
        }
    }
</script>
</html>
STR;
        exit(0);
    }

    /**
     * 显示钉钉跳转页
     *
     * @param        $corpId
     * @param        $closewindow
     * @param        $formUrl
     * @param int    $debug
     * @throws Exception
     */
    public static function showDingtalk($corpId, $closewindow, $formUrl, $debug = 0): void
    {
        $isInternal = IS_INTERNAL;
        if (!$isInternal) {
            $managerIp = getManagerIp();
            $baseUrl = self::getJumpBaseurl();
            $pingTestCode = self::getPingTestCode($baseUrl, $formUrl, 'dingtalkcode');
        } else {
            $managerIp = '';
            $pingTestCode = "";
        }
        echo <<<STR
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script type="text/javascript" src="/mobile/ui/babel/dingtalk/dingtalk.open.js"></script>
    <script type="text/javascript" src="/mobile/ui/babel/other/jquery-1124.min.js"></script>
</head>
<body>
</body>
<script>
    var corpId = '{$corpId}';
    var close = '{$closewindow}';
    var debug = '{$debug}';
    var isInternal = '{$isInternal}';
    {$pingTestCode}
    if (corpId == '') {
        alert('请先配置企业ID！');
    } if (close == '1') {
        dd.ready(function () {
            dd.biz.navigation.close({
                onSuccess : function(result) {
                },
                onFail : function(err) {
                }
            });
        });
        dd.error(function (err) {
            alert('dd error: ' + JSON.stringify(err));
        });
    } else {
        dd.ready(function () {
            /* 获得免登授权码 */
            dd.runtime.permission.requestAuthCode({
                corpId: corpId,
                onSuccess: function (info) {
                    /* 此处调用后端的接口，传入params获得用户信息进行相关操作 */
                    //alert(info.code);
                    if (isInternal === '1') {
                      window.location.href = "{$formUrl}&dingtalkcode="+info.code;
                    } else {
                      ping_test("{$managerIp}", info.code);
                    }
                },
                onFail: function (err) {
                    if (debug === '1') {
                        alert(JSON.stringify(err));
                    } else {
                        alert('认证失败，用户名不存在或者配置错误!')
                    }
                }
            });

        });
        dd.error(function (err) {
            alert('dd error: ' + JSON.stringify(err));
        });
    }
</script>
</html>
STR;
        exit(0);
    }

    /**
     * 显示钉钉登录跳转页
     *
     * @param        $corpId
     * @param        $session
     */
    public static function showDingtalkLogin($corpId, $session): void
    {
        $isLogin = !empty($session) ? '1' : '0';
        echo <<<STR
        <!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title></title>
  <script src="/mobile/ui/babel/dingtalk/dingtalk.open.js"></script>
  <script src="/mobile/ui/babel/other/os_browser_info.js"></script>
</head>
<body>
</body>
<script>
  // 跳转回调地址
  function gotoRedirectUrl(code) {
    if (redirectUrl.indexOf("?") >=0){
      // alert(redirectUrl+'&code='+result.code)
      window.location.href=redirectUrl+'&code='+code+'&ver='+Date.now()+"&osType="+os_type
    }else {
      // alert(redirectUrl+'?code='+result.code)
      window.location.href=redirectUrl+'?code='+code+'&ver='+Date.now()+"&osType="+os_type
    }
  }
  let corpId = '{$corpId}';
  let isLogin = '{$isLogin}';
  let url = window.location.href;
  let redirectUrl = url.substr(url.indexOf("redirect_url")+13)
  if (corpId == '') {
	    alert('请先配置企业ID！');
  }
  if (isLogin === '1') {
    gotoRedirectUrl('');
  } else {
    dd.ready(function() {
      // dd.ready参数为回调函数，在环境准备就绪时触发，jsapi的调用需要保证在该回调函数触发后调用，否则无效。
      dd.runtime.permission.requestAuthCode({
        corpId: corpId,
        onSuccess: function(result) {
          gotoRedirectUrl(result.code);
        },
        onFail : function(err) {
             alert('err:'+JSON.stringify(err));
        }
      });
    });
  }
</script>
</html>
STR;
        exit(0);
    }

    /**
     * 显示企业微信跳转页 外网使用
     *
     * @param $formUrl
     * @param $code
     * @throws Exception
     */
    public static function showWework($formUrl, $code): void
    {
        $managerIp = getManagerIp();
        $baseUrl = self::getJumpBaseurl();
        $pingTestCode = self::getPingTestCode($baseUrl, $formUrl, 'code');
        echo <<<STR
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title></title>
    <script type="text/javascript" src="/mobile/ui/babel/other/jquery-1124.min.js"></script>
</head>
<body>
</body>
<script>
    var code = '{$code}';
    {$pingTestCode}
    ping_test("{$managerIp}", code);
</script>
</html>
STR;
        exit(0);
    }
}
