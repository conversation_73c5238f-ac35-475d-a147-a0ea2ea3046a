<?php

/**
 * Description: 访客安全域配置信息表
 * User: <EMAIL>
 * Date: 2022/10/15 10:32
 * Version: $Id: GuestRelationModel.php 166879 2022-01-11 04:50:15Z duanyc $
 */

class OtherRegionModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TOtherRegion';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one'  => 'ID',
        '*'    => '*',
    ];

    /**
     * 不分页获取所有数据，如果需要分页请用getList
     *
     * @param array $cond
     * @param string $Column
     *
     * @return mixed
     */
    public static function getAll(array $cond = [], $Column = 'one')
    {
        self::$data = [];
        $where = !empty($cond) ? self::getWhere($cond) : '';

        $column = self::$columns[$Column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 插入数据
     *
     * @param array $key_values
     *
     * @return int
     */
    public static function insert($key_values = [])
    {
        $lastId = parent::insert($key_values);
        if(isset($key_values['ID']) && $key_values['ID'] > 0){
            $lastId = $key_values['ID'];
        }
        return $lastId;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        if (isset($cond['RegionIDs'])) {
            $where .= "AND RegionIDs = ".self::setData($cond['RegionIDs']);
        }

        if (isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
