<?php

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class SdcController extends BaseController
{
    public $publicKey = <<<EOF
-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDNC7qbvAip8WqKWu/JafZ1a4/+
XA84EDMTSc29fYOnNuhY7YO4qRdeJko7+GzltJ+4uJenXGuVb2phk/v4ml2RUwgR
s9KsSwr6EqNeeMsfiBVxSRwutDt9++yIKY6+FblOWmXALHkScq7mEoG+ybHSVOBj
6ZOEuEBJ9o9f/YrhCQIDAQAB
-----END PUBLIC KEY-----
EOF;

    /**
     * @return array
     * @throws Exception
     */
    public function getUserToken()
    {
        $Account = request('Account', 'request');
        $Password = request('Password', 'request');
        $asmToken = request('ASMToken', 'request');
        $agentId = request('agentId', 'request');
        return $this->getSdcToken($asmToken, $agentId);
    }

    /**
     * 获取RSA加密后密码
     *
     * @param $originPass // 原密码
     *
     * @return bool|string
     */
    public function getRsaPass($originPass)
    {
        $res = openssl_pkey_get_public($this->publicKey);
        if (!$res) {
            cutil_php_debug('公钥信息获取失败！', 'SDC');
            return false;
        }
        $resEncrypt = openssl_public_encrypt($originPass, $cryptedPass, $res);
        if (!$resEncrypt) {
            cutil_php_debug('公钥加密失败！', 'SDC');
            return false;
        }
        return base64_encode($cryptedPass);
    }

    /**
     * 请求深信达获取token
     * @return bool|string|array
     */
    public function getSdcToken($asmToken, $agentId = "", $count = 0)
    {
        $return = $this->getUserByToken($asmToken);
        $account = $return['account'];
        $password = $return['password'];
        $key = md5($account . $agentId);
//        $token = lib_redis::get("TOKEN_SDC", $key);
//        todo 此处先注释掉，等客户端完成上报异常信息后再打开判断
//        if (empty($token)) {
        $config = ConfigServiceProvider::getDictAll("SDC");
        $adminToken = $this->getAdminToken($config);
        $password = $this->getRsaPass($password);
        if (!$password) {
            T(********);
        }
        $postData1 = [
            'Account' => $account,
            'Pwd' => $password,
        ];
        cutil_php_debug(var_export($postData1, true), 'SDC');
        $resToken = curl($config['Url'] . '/FAHome/index', "POST", $postData1, 10, 3, ['Authorization: Bearer ' . $adminToken]);
        $res = json_decode($resToken['data'], true);
        if ($resToken['code'] == 200) {
            $res = json_decode($resToken['data'], true);
            if ($res['result'] == 1) {
//                    lib_redis::set("TOKEN_SDC", $key, $res['token'], 43200);
                return $res;
            } else {
                T(********, ['message' => $res['data']]);
            }
        } else if ($resToken['code'] == 401) {
            cutil_php_log(var_export($resToken, true), 'SDC');
            $count += 1;
            if ($count > 3) {
                T(********, ['message' => $res['data']]);
            }

            lib_redis::del("", SDC_TOKEN);
            return $this->getSdcToken($asmToken, $agentId, $count);
        } else {
            T(********, ['message' => $res['data']]);
        }

//        }
        return ['result' => 1, 'token' => ""];
    }

    /**
     * 获取api token
     * @param $config
     * @param $adminTokenKey
     * @return mixed|null
     * @throws Exception
     */
    public function getAdminToken($config, $adminTokenKey = 'SDC_TOKEN')
    {
        $adminToken = lib_redis::get("", $adminTokenKey);
        if (empty($adminToken)) {
            $Password = $this->getRsaPass($config['Password']);
            if (!$Password) {
                T(********);
            }
            $postData = [
                'UserId' => $config['Account'],
                'Password' => $Password,
            ];
            $res = curl($config['Url'] . '/main/index', "POST", $postData);
            $data = json_decode($res['data'], true);

            if ($res['code'] == 200) {
                if ($data['result'] == 1) {
                    lib_redis::set("", $adminTokenKey, $data['token'], 43200);
                    $adminToken = $data['token'];
                } else {
                    T(********, ['message' => $data['data']]);
                }
            } else {
                T(********, ['message' => $data['data']]);
            }
        }
        return $adminToken;
    }

    /**
     * 通过token获取用户信息
     * @param $token
     * @return array|void
     */
    public
    function getUserByToken($token)
    {

        $userInfo = AuthServiceProvider::getUserInfoByToken($token);
        if (!$userInfo) {
            T(********, ['message' => "异常错误，请重新认证！"]);
        }
        if ($userInfo['Type'] == 'User') {
            $password = doXorDecrypt(hexTostring($userInfo['Password2']), 'infogoasm');
        } else {
            $password = md5($userInfo['UserName'] . 'infogoasm');
        }


        return ['account' => $userInfo['UserName'] . "_" . $userInfo['Type'], 'password' => $password];

    }

}
