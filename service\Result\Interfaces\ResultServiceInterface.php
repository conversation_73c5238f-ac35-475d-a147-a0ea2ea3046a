<?php
/**
 * Description: 认证后上报结果服务接口
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: ResultServiceInterface.php 151146 2021-07-23 10:03:16Z duanyc $
 */

namespace Services\Auth\Interfaces;

interface ResultServiceInterface
{
    /**
     * DeviceServiceInterface constructor.
     * @param $params
     */
    public function __construct($params);

    /**
     * 初始化
     *
     * @return mixed
     */
    public function init();

    /**
     * 提交
     *
     * @return bool
     */
    public function submit();
}
