<?php
/**
 * Description: 极速入网 扫码绑定设备
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: PermitAuthServiceProvider.php 174901 2022-04-29 06:03:47Z duanyc $
 */

use Common\Facades\NACServiceFacade;
use Services\Common\Services\DESService;
use Services\Device\Services\DeviceStatusService;

class PermitAuthServiceProvider extends BaseServiceProvider
{
    /**
     * 超时5分钟
     */
    public const QRCODE_TIME = 300;

    /**
     * 日志文件
     * @var string
     */
    public static $logFile = 'permit';

    /**
     * 获取极速入网二维码地址
     * @param $cryptData
     * @param $time
     * @return string
     */
    public static function getScanCodeUrl($cryptData, $time)
    {
        $urlPrefix = URL_BASE;
        $code = "{$urlPrefix}/access/qr?c={$cryptData}&isOpenUrl=0";
        $url = "/access/qrcode?code=" . urlencode($code) . "&time=" . $time;
        cache_set_info('qrcode', dataEncrypt($cryptData), ['status' => false, 'time' => $time], 300);
        return $urlPrefix . $url;
    }

    /**
     * 限制同一账户最大可认证设备数
     * @param $params
     * @param $authNums int 可认证设备数
     * @param $userID int 用户ID
     * @throws Exception
     */
    public static function checkLimitUser($params, $authNums, $userID)
    {
        $UserBind = DictModel::getAll('UserDevBind');
        if ($authNums > 0) {
            $UserBind['IsUse'] = $authNums;
        }
        if ($UserBind['IsUse'] <= 0) {
            return;
        }
        $cond = ['AuthType' => $params['userType'], 'UserName' => $params['username'], 'DeviceID' => $params['deviceId'], 'UserID' => $userID];
        $order = 'GROUP BY A.DeviceID ORDER BY A.AuthTime';
        $onlineDeviceList = NacOnLineDeviceModel::getListJoin($cond, $order, 0, 100);
        $Usernum = count($onlineDeviceList);

        for ($i = 0; $i < $Usernum; $i++) {
            //比设置的数字多删除一条记录 因为下面逻辑要加入一条新记录
            if (($Usernum - $i) >= $UserBind['IsUse']) {
                $formatArr = [
                    'num'  => $Usernum,
                    'name' => $onlineDeviceList[$i]["DevName"],
                    "ip"   => $onlineDeviceList[$i]['IP'],
                ];
                T(21139009, $formatArr);
            }
        }
    }

    /**
     * 检查PC绑定设备信息
     * @param $params
     * @param $authNums int 可认证设备数
     * @param $userID int 用户ID
     *
     * @throws Exception
     */
    public static function checkBindDeviceInfo($params, $authNums, $userID)
    {
        // 查询设备信息
        $deviceInfo = DeviceModel::getJoinComputer($params['deviceId'], 'gateInfo');
        if (!is_array($deviceInfo)) {
            T(21139003);
        }

        $deviceStatusService = new DeviceStatusService();
        // 被扫码设备是否在线
        $isOnline = $deviceStatusService->isOnlineDevice($params['deviceId']);
        if (!$isOnline) {
            T(21139004);
        }

        // 被扫码设备是否放开网络 处于安检放开域
        $deviceIpRegion = $deviceStatusService->getDeviceIpRegion($params['deviceId']);
        if ($deviceIpRegion !== 1) {
            T(21139005);
        }
        // 检查账号限制数
        self::checkLimitUser($params, $authNums, $userID);
    }

    /**
     * 建立手机扫码设备绑定被扫码设备
     * @param int $deviceId 扫码设备
     * @param int $roleId 角色ID
     * @param int $userId 账户ID
     * @param int $bindId 被扫码设备ID
     * @throws Exception
     */
    public static function bindScanCodeDevice(int $deviceId, int $roleId, int $userId, int $bindId): void
    {
        // 设置扫码设备已注册
        ComputerModel::update($deviceId, ['Registered' => 1]);
        $bindInfo = PermitDeviceModel::getSingle(['DeviceID' => $deviceId]);
        if (!empty($bindInfo)) {
            if ((int)$bindInfo['BindID'] !== $bindId) {
                // 如果和之前绑定不一致则删除并新建绑定
                self::unbindScanCodeDevice($deviceId);
                // 插入扫码绑定关系
                self::insertBindScanCodeDevice($deviceId, $roleId, $userId, $bindId);
            } else {
                // 否则只更新角色ID、账户ID信息
                self::updateBindScanCodeDevice($bindInfo['RID'], $roleId, $userId);
            }
        } else {
            // 不存在则插入 插入扫码绑定关系
            self::insertBindScanCodeDevice($deviceId, $roleId, $userId, $bindId);
        }
        // 立即放开网络
        $IpRegion = DevIpRegionModel::getJoinPermit($deviceId);
        $IPListId = $IpRegion['IPListId'] ?? 0;
        $params = ['device_id' => $deviceId, 'iplistid' => $IPListId];
        NACServiceFacade::access("WebAccess:bindScanCodeDevice", [$params], 'AuthorizedAccess');
    }

    /**
     * 扫码设备取消绑定
     * @param string $deviceId 扫码设备ID
     */
    public static function unbindScanCodeDevice(string $deviceId): void
    {
        self::log('unbindScanCodeDevice:' . $deviceId);
        PermitDeviceModel::delete(['InDeviceID' => $deviceId]);
    }

    /**
     * 被扫码设备取消绑定
     * @param string $deviceId 扫码设备ID
     */
    public static function unbindScannedDevice(string $deviceId): void
    {
        self::log('unbindScannedDevice:' . $deviceId);
        PermitDeviceModel::delete(['InBindID' => $deviceId]);
    }

    /**
     * 插入扫码绑定关系
     * @param int $deviceId
     * @param int $roleId 角色ID
     * @param int $userId 账户ID
     * @param int $bindId 被扫码设备
     */
    private static function insertBindScanCodeDevice(int $deviceId, int $roleId, int $userId, int $bindId): void
    {
        $params = [];
        $params['DeviceID'] = $deviceId;
        $params['RoleID'] = $roleId;
        $params['UserID'] = $userId;
        $params['BindID'] = $bindId;
        PermitDeviceModel::insert($params);
    }

    /**
     * 更新绑定信息
     * @param int $RID TPermitDevice RID
     * @param int $roleId 角色ID
     * @param int $userId 账户ID
     */
    private static function updateBindScanCodeDevice(int $RID, int $roleId, int $userId): void
    {
        $params = [];
        $params['RoleID'] = $roleId;
        $params['UserID'] = $userId;
        PermitDeviceModel::update($RID, $params);
    }

    /**
     * 加密QrCode
     *
     * @param $params
     *
     * @return string
     */
    public static function encryptCode($params)
    {
        $data = "{$params['deviceId']}|{$params['roleID']}|{$params['username']}|{$params['userType']}|{$params['sceneID']}";
        $deQrCode = DESService::desEcbEncrypt($data);
        return "{$params['clientType']}{$deQrCode}";
    }

    /**
     * 解密QrCode
     * @param string $qrCode
     * @return array|false
     */
    public static function decryptQrCode(string $qrCode = '')
    {
        if (empty($qrCode)) {
            return false;
        }

        $type = $qrCode[0];
        // A:agent，W:web，O:other
        if (!in_array($type, ['A', 'W', 'O'])) {
            return false;
        }

        // 密文
        $ciphertext = substr($qrCode, 1);
        $deQrCode = DESService::desEcbDecrypt($ciphertext);
        $deQrCode = explode('|', $deQrCode);

        if (count($deQrCode) < 4) {
            return false;
        }
        return [
            'deviceId' => trim($deQrCode[0]),
            'roleID'   => trim($deQrCode[1]),
            'username' => trim($deQrCode[2]),
            'userType' => trim($deQrCode[3]),
            'sceneID'  => trim($deQrCode[4]),
        ];
    }
}
