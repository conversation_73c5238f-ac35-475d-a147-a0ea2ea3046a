{"name": "nac/asm", "description": "ASM", "type": "project", "license": "Commercial", "require": {"php": ">=7.2", "phpmailer/phpmailer": "6.4.1", "apereo/phpcas": "1.4.0", "jenssegers/agent": "2.6.4", "asm-web/service-sdk": "dev-6039.3746.R003", "asm-web/php-common": "dev-6039.3746.R003.LTS02", "ext-json": "*", "phpstan/phpstan": "^1.2", "ext-iconv": "*", "ext-openssl": "*", "ext-redis": "*", "ext-mbstring": "*"}, "config": {"platform": {"php": "7.2"}}, "autoload": {"psr-4": {"Services\\": "service/"}, "classmap": ["helper/", "library/", "service/", "model/"]}, "repositories": {"package-service-sdk": {"type": "git", "url": "*******************:ASM-Web/service-sdk.git"}, "package-php-common": {"type": "git", "url": "*******************:ASM-Web/php-common.git"}}}