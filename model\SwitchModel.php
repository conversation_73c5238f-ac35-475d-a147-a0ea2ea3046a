<?php

/**
 * Description: 行为记录TUserActionRecord表
 * User: <EMAIL>
 * Date: 2021/08/12 10:02
 * Version: $Id
 */

class SwitchModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TSwitch';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one'  => 'TSwitch.DeviceID',
        '*'    => '*',
    ];

    /**
     * 通过IP获取交换机信息
     *
     * @param $IP
     * @param string $column
     *
     * @return array|bool
     */
    public static function getSwitchJoinDeviceByIP($IP,$column = 'one')
    {
        if (empty($IP)) {
            return false;
        }
        self::$data = [];
        $_column = self::$columns[$column];
        $sql = "SELECT {$_column} FROM TSwitch join TDevice on TSwitch.DeviceID=TDevice.DeviceID WHERE TDevice.IP=".self::setData($IP);
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }
    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
