<?php

/**
 * Description: 远程应用服务器
 */

class RemoteServiceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TRemoteService';
    public const PRIMARY_KEY = 'ResID';
    protected static $columns = [
        '*' => '*',
        'push' => 'Addr as IP,ResID,GateWayID,GateWayType,Port,RemotePort',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";
        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
