<?php

/**
 * Description: 设备TOtherUser表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: OtherUserModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class OtherUserModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TOtherUser';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'ID' => 'ID',
        '*' => '*',
    ];

    /**
     * 根据openid获取用户数据
     * @param $openid
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByOpenId($openid, $Column = 'one')
    {
        if (empty($openid)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Openid = ".self::setData($openid);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }

        if (isset($cond['LikeLastUpTime'])) {
            $LikeLastUpTime = "{$cond['LikeLastUpTime']}%";
            $where .= "AND LastUpTime LIKE ".self::setData($LikeLastUpTime);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
