<?php
/**
 * ip2region php seacher client class
 *
 * <AUTHOR>
 * @date    2015-10-29
 */

defined('INDEX_BLOCK_LENGTH') or define('INDEX_BLOCK_LENGTH', 12);
defined('TOTAL_HEADER_LENGTH') or define('TOTAL_HEADER_LENGTH', 8192);
include_once   PATH_ROOT.'/library/Ip2region/Ip2Region.php';

class lib_ipregion
{

    public static $ipRegion = null;

    /**
     * 初始化 Ip2Region类
     * @return void
     */
    public static function init()
    {
        if (self::$ipRegion === null){
            self::$ipRegion = new Ip2Region();
        }

    }

    /**
     * 根据出口IP 查询出真实地理位置
     * @param $ip
     * @return mixed
     */
    public static function memorySearch($ip)
    {
        self::init();
        return self::$ipRegion->memorySearch($ip);
    }


}
