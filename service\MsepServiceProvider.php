<?php

namespace Services;

use BaseServiceProvider;
use ComputerModel;
use ConfigServiceProvider;
use DeviceModel;
use DeviceServiceProvider;
use LinkAgeReplyModel;
use LinkAgeTaskModel;
use ThirdClientLinkageConfigModel;

/**
 * Description: Msep相关操作
 * User: yinbz
 * Date: 2024/4/8 16:2-
 */
class MsepServiceProvider extends BaseServiceProvider
{
    /**
     * MSEP上报安装状态变更
     * @param $deviceId
     * @param $msepStatus
     * @param $tamStatus
     * @return void
     */
    public static function setupChange($deviceId, $msepStatus, $tamStatus)
    {
        cutil_php_log("MSEP Call setupChange DeviceId:$deviceId MsepClient:$msepStatus TAM:$tamStatus", 'msep');
        $ids = explode(",", $deviceId);
        $cond = [
            'DeviceIds' => $ids,
            "column" => "dasc"
        ];
        $devices = DeviceModel::getList($cond);

        $data = [];
        $config = ThirdClientLinkageConfigModel::getSingle(['type' => 1]);
        $ApplyType = $config['ApplyType'] ?? 'All';
        $ipRange = [];
        if ($ApplyType != "All") {
            $ipRange = explode(",", $config['ApplyRange'] ?? '');
        }

        foreach ($devices as $device) {
            $flag = self::isInIpRange($ipRange, $device['IP']);
            if ($flag) {
                $data[] = [
                    'Type' => 2,
                    'DeviceID' => $device['DeviceID'],
                    'Content' => json_encode(["MsepClient" => $msepStatus, "TamClient" => $tamStatus]),
                    'InsertTime' => date('Y-m-d H:i:s'),
                    'UpdateTime' => date('Y-m-d H:i:s'),
                ];
                ComputerModel::update($device['DeviceID'], ["MsepClient" => $msepStatus, "TamClient" => $tamStatus]);
                try {
                    sql_string_pub("update TComputer set MsepClient = {$msepStatus}, TamClient = {$tamStatus} where DeviceID = {$device['OrigID']}", $device['DAscID']);
                } catch (\Exception $e) {
                    cutil_php_log("update TComputer set MsepClient = {$msepStatus}, TamClient = {$tamStatus} where DeviceID = {$device['OrigID']} error", 'msep');
                }
            }
        }
        LinkAgeReplyModel::insertPatch($data);
    }

    /**
     * 检查IP是否在在IP段内
     * @param $ipRange
     * @param $deviceIP
     * @return bool
     */
    public static function isInIpRange($ipRange, $deviceIP)
    {
        if (count($ipRange) == 0) {
            return true;
        }
        foreach ($ipRange as $item) {
            $ips = explode("-", $item);
            $ip = ip2long($deviceIP);
            $ipStart = ip2long($ips[0]);
            $ipEnd = ip2long($ips[1]);
            if (is_numeric($ip) && $ip >= $ipStart && $ip <= $ipEnd) {
                return true;
            }
        }
        return false;
    }

    /**
     * MSEP上报杀毒状态变更
     * @param $deviceIds
     * @return void
     * @throws \Exception
     */
    public static function virusChange($deviceIds)
    {
        cutil_php_log("MSEP Call virusChange DeviceIds:$deviceIds", 'msep');
        $devices = explode(",", $deviceIds);
        if (count($devices) > 100) {
            T(21100002);
        }
        $res = self::getVirusDetail($deviceIds);
        if ($res) {
            $result = json_decode($res, true);
            $items = json_decode($result['info'] ?? "[]", true);
            foreach ($items as $deviceId => $item) {
                // 构建数据
                $record = [
                    'Type' => 1,
                    'DeviceID' => $deviceId,
                    'Content' => json_encode($item),
                    'InsertTime' => date('Y-m-d H:i:s'),
                    'UpdateTime' => date('Y-m-d H:i:s'),
                ];
                // 插入记录并获取插入的 RID
                $rid = LinkAgeReplyModel::insert($record);
                // 更新 LinkAgeTaskModel 中的 Status 和 RID
                $list = LinkAgeTaskModel::getLastRIds($deviceId);
                $ids = array_column($list, 'TID');
                if ($ids) {
                    LinkAgeTaskModel::updatePatch(['TID' => $ids], ['Status' => 1, 'RID' => $rid]);
                }
            }

            DeviceServiceProvider::setDeviceAntiVirusResult($items);
        }
    }

    /**
     * @description 下发静默杀毒指令给MSEP
     * @param $deviceIds
     * @return array|false
     */
    public static function virusDetection($deviceIds)
    {
        cutil_php_log("received VirusDetection Call DeviceIds:$deviceIds", 'msep');
        $ids = explode(",", $deviceIds);
        $cond = [
            'DeviceIds' => $ids,
            'SubType' => 1, //只允许设备类型为Windows终端
            "column" => "msep"
        ];
        $devices = DeviceModel::getList($cond);

        $config = ThirdClientLinkageConfigModel::getSingle(['type' => 1]);
        $ApplyType = $config['ApplyType'] ?? 'All';
        if ($ApplyType == "All") {
            $devices = array_column($devices, 'DeviceID');
        } else {
            $allowDeviceIds = [];
            $ipRange = explode(",", $config['ApplyRange'] ?? '');
            foreach ($devices as $device) {
                if (self::isInIpRange($ipRange, $device["IP"])) {
                    $allowDeviceIds[] = $device["DeviceID"];
                }
            }
            $devices = $allowDeviceIds;
        }

        // 如果设备范围为空，直接返回，记录日志
        if (empty($devices)) {
            cutil_php_log("allowDeviceIds is null, return false.", 'msep');
            return false;
        }

        $data = [];
        foreach ($devices as $device) {
            $data[] = [
                'Type' => 1,
                'Status' => 0,
                'DeviceID' => $device,
                'InsertTime' => date('Y-m-d H:i:s'),
                'UpdateTime' => date('Y-m-d H:i:s'),
            ];
        }
        LinkAgeTaskModel::insertPatch($data);
        $deviceIds = implode(",", $devices);
        return self::callMsep("viruskilltask", $deviceIds);
    }

    /**
     * 获取MSEP查杀结果
     * @param $deviceIds
     * @return string|false
     * @throws \Exception
     */
    public static function getVirusDetail($deviceIds)
    {
        return self::callMsep("virusoverview", $deviceIds);
    }

    /**
     * @description 调用MSEP接口
     * @param $action
     * @param $deviceIds
     * @return string|false
     * @throws \Exception
     */
    public static function callMsep($action, $deviceIds)
    {
        $config = ConfigServiceProvider::getDictAll("MSEP");
        if ($config['State'] == 0) {
            return false;
        }
        cutil_php_log("request MSEP action:$action deviceId:$deviceIds", 'msep');
        $params = [];
        $params['action'] = $action;
        $params['userip'] = getRemoteAddress();
        $params['asm_ip'] = getManagerIp();
        $params['userie'] = getBrower() ?? 'Chrome';
        $params['username'] = "admin";  //使用admin去请求接口 保证一定有这个帐号
        $ip = $config['Ip'];
        $port = $config['Port'];

        $query = http_build_query($params);

        $type = $port == "443" || $port == "8443" ? "https" : "http";
        $url = "{$type}://{$ip}:{$port}/phpdir/mod/default/infogo_login.php?" . $query;
        $data = [
            "asm_ip" => getManagerIp(),
            "DeviceID" => $deviceIds,
        ];

        for ($i = 0; $i < 3; $i++) {
            try {
                $res = curl($url, "POST", $data, 10, 60);
                if ($res && $res['code'] == 200) {
                    cutil_php_log("MSEP response:" . var_export($res, true), 'msep');
                    return $res['data'];
                }
            } catch (\Exception $e) {
                cutil_php_log("请求MSEP接口失败 action:$action DeviceID:$deviceIds", 'msep');
            }
        }
        return false;
    }
}