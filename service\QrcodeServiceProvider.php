<?php

/**
 * Description: 二维码业务
 * User: <EMAIL>
 * Date: 2021/06/09 16:52
 * Version: $Id: QrcodeServiceProvider.php 175145 2022-05-06 07:53:42Z huyf $
 */

class QrcodeServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'qrcode';

    /**
     * 获取二维码用户列表
     *
     * @param array $cond
     * @param bool $order
     * @param int $start
     * @param int $limit
     *
     * @return array|bool
     */
    public static function getQrcodeUserList($cond = [], $order = false, $start = 0, $limit = 20)
    {
        if (empty($cond)) {
            return false;
        }

        return QRcodeUserModel::getList($cond, $order, $start, $limit);
    }

    /**
     * 添加二维码用户
     *
     * @param $params
     *
     * @return bool|int
     */
    public static function addQrcodeUser($params)
    {
        if (empty($params['DeviceID'])) {
            return false;
        }
        $params['CREATTIME'] = 'now()';
        self::log('addQrcodeUser:' . var_export($params, true));
        return QRcodeUserModel::insert($params);
    }

    /**
     * 删除二维码用户
     *
     * @param $deviceId
     *
     * @return bool|int
     */
    public static function deleteQrcodeUser($deviceId)
    {
        if (empty($deviceId)) {
            return false;
        }

        $cond = ['LikeToken' => '|||', 'DeviceID' => $deviceId];
        self::log('deleteQrcodeUser:' . $deviceId);
        return QRcodeUserModel::delete($cond);
    }

    /**
     * 普通二维码创建用户
     *
     * @param $deviceId
     *
     * @return bool|string
     */
    public static function createQrcodeUser($deviceId)
    {
        if (empty($deviceId)) {
            return false;
        }
        $addid = QRcodeUserModel::getAutoIncrement();
        $token_str = 'abcdefghijklmnopqrstuvwxyz1234567890';
        $token = "";
        $maxIndex = strlen($token_str) - 1;
        for ($i = 0; $i < 8; $i++) {
            $token .= $token_str[rand(0, $maxIndex)];
        }
        $token .= $addid;
        QRcodeUserModel::delete(['DeviceID' => $deviceId]);
        $data = ['Token' => $token, 'DeviceID' => $deviceId, 'CREATTIME' => 'now()', 'Type' => 'User'];
        QRcodeUserModel::insert($data);
        cache_set_info('pqrcode', dataEncrypt($token), ['status' => false, 'time' => time()], 300);
        return $token;
    }

    /**
     * 获取二维码
     *
     * @param $deviceId
     * @throws Exception
     */
    public static function getQrcodeImg($deviceId, $qrCodeType = 'image')
    {
        $token = self::createQrcodeUser($deviceId);
        $urlPrefix = URL_BASE;
        $imginfo = "{$urlPrefix}/mobile/ui/wel.html?isOpenUrl=1&token=" . $token . "&scan=nac";
        if ($qrCodeType !== 'image') {
            return $imginfo;
        } else {
            lib_qrcode::png($imginfo, false, "L", 6, 1);
            exit();
        }
    }

    /**
     * 生成IOS客户端二维码下载链接
     * @throws Exception
     */
    public static function getIosQrcodeImg()
    {
        $iosUrl = ConfigServiceProvider::getDictOne('Mobile', 'mobile_IosApp_Address');
        lib_qrcode::png($iosUrl, false, "L", 6, 1);
    }

    /**
     * 生成安卓客户端二维码下载链接
     * @throws Exception
     */
    public static function getAndroidQrcodeImg()
    {
        $urlPrefix = URL_BASE;
        $androidUrl = "{$urlPrefix}/asm.apk";
        lib_qrcode::png($androidUrl, false, "L", 6, 1);
    }

    /**
     * 获取二维码状态
     *
     * @param $deviceId
     *
     * @return array
     */
    public static function getQrcodeStatus($deviceId)
    {
        $ret = ['state' => false];
        $cond = ['DeviceID' => $deviceId, 'Type' => 'User', 'column' => 'one'];
        $userList = QRcodeUserModel::getList($cond, false, 0, 20);
        if (empty($userList)) {
            $ret['type'] = "overtime";
            return $ret;
        }
        $token = '';
        foreach ($userList as $user) {
            $token = $user['Token'];
            if (!empty($user['UserID'])) {
                $ret['state'] = true;
                $ret['type'] = "scaned";
                $ret['token'] = $user['Token'];
                $userDetail = AuthUserModel::getOne($user['UserID'], 'base');
                $ret['username'] = $userDetail['UserName'];
                break;
            }
        }
        if (!empty($token)) {
            $cache = cache_get_info('pqrcode', dataEncrypt($token));
            if (empty($cache) || $cache['time'] + 300 < time()) {
                $ret['type'] = "overtime";
                return $ret;
            }
        }
        if ($ret['state'] == false) {
            $ret['type'] = "prescaned";
        } else {
            self::deleteQrcodeUser($deviceId);
        }
        return $ret;
    }

    /**
     * 二维码手机扫码处理
     *
     * @param $params
     *
     * @return bool|void
     * @throws Exception
     */
    public static function scanQrcodeAuth($params)
    {
        if (!empty($params['deviceId'])) {
            //客户端未获取到deviceid时容灾处理
            if ($params['deviceId'] == "N/A") {
                T(21138002);
            }
            //客户端扫描
            $deviceInfo = DeviceModel::getJoinOnlineDevice(['DeviceID' => $params['deviceId']]);
        } else {
            //微信、QQ等扫描
            $ip = $_SERVER['REMOTE_ADDR'];
            $deviceInfo = DeviceModel::getJoinOnlineDevice(['IP' => $ip]);
        }

        self::log($deviceInfo);
        $userId = (int)$deviceInfo['UserID'];
        if (empty($deviceInfo['DeviceID'])) {
            //扫描设备未注册
            T(21138008);
        }
        if ($deviceInfo['UserID'] == '0' || empty($deviceInfo['UserName'])) {
            //未找到对应账号
            T(21138008);
        }
        if ($deviceInfo['AuthType'] == 'Guest' || $deviceInfo['AuthType'] == 'NoAuth') {
            //来宾账号不允许扫码
            T(21138005);
        }
        $qrcodeUser = QRcodeUserModel::getOneByToken($params['token']);
        if (empty($qrcodeUser)) {
            T(21138004);
        }
        $cache = cache_get_info('pqrcode', dataEncrypt($params['token']));
        if (empty($cache) || $cache['time'] + 300 < time()) {
            T(21138004);
        }
        $cond = ['Token' => $params['token']];
        QRcodeUserModel::updatePatch($cond, ['UserID' => $userId]);
        cache_set_info('pqrcode', dataEncrypt($params['token']), ['status' => true, 'time' => time()], 300);
        self::log(['token' => $params['token'], 'UserID' => $userId]);
    }

    /**
     * 保存第三方code
     *
     * @param $authKey
     * @param $code
     * @return boolean
     */
    public static function saveAuthCode($authKey, $code): bool
    {
        if (empty($authKey) || empty($code)) {
            return false;
        }
        return cache_set_info('otherAuth:', $authKey, ['code' => $code]);
    }

    /**
     * 获取第三方code
     *
     * @param $authKey
     *
     * @return array|mixed|null|string
     */
    public static function getAuthCode($authKey)
    {
        if (empty($authKey)) {
            return '';
        }
        $result = cache_get_info('otherAuth:', $authKey);
        if (!empty($result['code'])) {
            return $result['code'];
        }
        return '';
    }
}
