<?php
/**
 * Description: 零信任访问 IP 变动策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;


class ChangeIPPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *
     * @return bool
     */
    public function check(): bool
    {
        if ((int)$this->params['Config']['IsOpen'] === 0 || !isset($this->params['session']['Token'])) {
            return true;
        }
        $token = $this->params['session']['Token'];
        $uuid= $this->params['session']['Uuid'];
        $lockTime = \lib_redis::get('ASG_','ChangeIPLock:'.$uuid);
        if ($lockTime && $lockTime > time()) { //还在被禁止访问时间内
            return false;
        }
        if ($lockTime && $lockTime < time()) { //超出被禁止时间 ，手动删除redis key  虽然设置了redis过期时间
            \lib_redis::del('ASG_','ChangeIPLock:'.$uuid);
        }

        $ip = $this->getClientIp();
        if ($ip !== $this->params['session']['ConnectIp']){ // IP发生变动
            \SessionRedis::setOne($token, ['ConnectIp'=>$ip]);
            //redis 有序集合 实现 多少分钟内 变动 少于多少次的判断
            $flag=$this->_limit('ChangeIP:'.$token,$this->params['Config']['LimitNum'],$this->params['Config']['LimitTime']*60);
            if (!$flag){
                //锁定多少分钟
                $lockTime = $this->params['Config']['Action']*60; //分钟转为秒
                \lib_redis::set('ASG_','ChangeIPLock:'.$uuid,time() + $lockTime,$lockTime);
                return false;
            }
        }
        return true;
    }
}
