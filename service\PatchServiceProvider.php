<?php
/**
 * Description: 补丁相关操作
 * User: renchen
 * Date: 2021/8/6 11:37
 * Version: $Id: PatchServiceProvider.php 174901 2022-04-29 06:03:47Z duanyc $
 */


class PatchServiceProvider extends BaseServiceProvider
{
    /**
     * 初始化补丁服务
     * @return bool|Object|\Services\Patch\Services\PatchService
     * @param $params
     * @throws Exception
     */
    public static function initPathService($params = [])
    {
        $serviceClass = '\\Services\\Patch\\Services\\' . 'PatchService';

        return (new ReflectionClass($serviceClass))->newInstance($params);
    }

}