<?php

/**
 * Description: 用户附加认证
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: UserAddAuthRedis.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class UserAddAuthRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'UserAddAuth';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'Status,LifeTime';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = 'Status,LifeTime';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => ['Status'],
    ];

    /**
     * 单条
     *
     * @param string $token
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($token, $column = 'one')
    {
        return self::get($column, $token);
    }

    /**
     * 单条
     *
     * @param string $token
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($token, $data)
    {
        return self::set($data, $token);
    }

    /**
     * 删除
     *
     * @param string $token
     *
     * @return mixed
     */
    public static function deleteOne($token)
    {
        return self::del($token);
    }
}
