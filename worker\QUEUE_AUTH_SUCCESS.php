<?php

/**
 * Description: 认证成功队列
 * User: <EMAIL>
 * Date: 2021/09/13 15:53
 * Version: $Id: QUEUE_AUTH_SUCCESS.php 156563 2021-09-13 15:04:16Z duanyc $
 */

use Common\Facades\EventBrokerFacade;

if (!defined('IN_ACCESS')) {
    exit('Access Denied!');
}

if (PHP_SAPI != 'cli' || !isset($this) || !$this instanceof cls_queue) {
    exit();
}

/**
 * 任务主函数
 *
 * @param $params
 */
if (!function_exists('QUEUE_AUTH_SUCCESS')) {
    /**
     * 认证成功处理
     *
     * @param $params
     *
     * @return bool
     */
    function QUEUE_AUTH_SUCCESS($params)
    {
        cutil_php_log("AUTH_SUCCESS:" . var_export($params, true), 'net_auth');
        if (empty($params['deviceId'])) {
            return false;
        }
        if (!empty($params['needUnbind'])) {
            PermitAuthServiceProvider::unbindScanCodeDevice($params['deviceId']);
        }
        if ($params['Type'] !== AUTH_TYPE_PERMIT) {
            $eventData = [
                'DeviceID' => (int)$params['deviceId'],
                'Remark' => 'Auth',
                'AccessStatus' => isset($params['AccessStatus']) ? (int)$params['AccessStatus'] : 0,
                'IPlistId' => isset($params['IPlistId']) ? (int)$params['IPlistId'] : 0,
                'Expand' => $params['UserName'] ?? ''
            ];
            EventBrokerFacade::publish("Auth", [$eventData], "WebAuth", $params['create_time'] ?? 0);
        }
        if (!isset($params['UserID']) || !isset($params['Token'])) {
            return false;
        }
        ResourceServiceProvider::setUserPower($params['Token'], 'QUEUE_AUTH_SUCCESS');
        return true;
    }
}

QUEUE_AUTH_SUCCESS($this->params);
