<?php

/**
 * Description: 常量配置
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: inc_constants.php 175145 2022-05-06 07:53:42Z huyf $
 */
/* 访问控制 */
!defined('IN_INIT') && exit('Access Denied');

/* 调试模式，生产关闭 */
define('DEBUG_LEVEL', false);

/* 当前域名地址 */
define('URL', 'http://127.0.0.1');

/* 类库文件目录常量 */
define('PATH_LIBRARY', PATH_ROOT . '/library');

/* 数据库文件目录常量 */
define('PATH_MODEL', PATH_ROOT . '/model');

/* 帮助文件目录常量 */
define('PATH_HELPER', PATH_ROOT . '/helper');

/* 配置文件目录常量 */
define('PATH_CONFIG', PATH_ROOT . '/config');

/* 数据文件目录常量 */
define('PATH_DATA', PATH_ROOT . '/data');

/* 日志文件目录常量 */
define('PATH_LOG', PATH_SYSTEM . '/tmp/logs');

/* 临时目录常量 */
define('PATH_TMP', PATH_SYSTEM . '/tmp');

/* local目录常量 */
define('PATH_LOCAL', PATH_SYSTEM . '/usr/local');

/* etc文件目录常量 */
define('PATH_ETC', (PHP_OS == 'WINNT') ? 'c:/etc/' : PATH_SYSTEM . '/etc/');

/* asm文件目录常量 */
define('PATH_ASM', PATH_SYSTEM . '/asm/');

/* usr文件目录常量 */
define('PATH_USR', PATH_SYSTEM . '/usr/');

/* sys文件目录常量 */
define('PATH_SYS', PATH_SYSTEM . '/sys/');

/* 原代码目录 */
define('PATH_HTML', PATH_SYSTEM . '/var/www/html');

/* API文件目录常量 */
define('PATH_API', PATH_ROOT . '/api');

/* admin-api文件目录常量 */
define('PATH_ADMIN', PATH_SYSTEM . '/var/www/admin.asm.com');

/* 业务模型层 */
define('PATH_SERVICE', PATH_ROOT . '/service');

/* 控制层目录 */
define('PATH_CONTROL', PATH_ROOT . '/control');

/* 入口当前目录 */
define('PATH_DIR', getcwd());

/* 请求时间 */
define('TIME', $_SERVER['REQUEST_TIME']);

/*******************业务常量定义******************/

define('DEVICE_TYPE_PC', '101');                //  设备类型ID PC
define('DEVICE_TYPE_MOBILE', "103");             //  设备类型ID 移动端
define('DEVICE_TYPE_NOTYPE', "9999");             //  设备类型ID 未分类

define('OSTYPE_WINDOWS', 'windows');        //  设备类型 Windows
define('OSTYPE_LINUX', "linux");            //  设备类型 Linux
define('OSTYPE_MAC', 'mac');                //  设备类型 Mac
define('OSTYPE_ANDROID', "android");        //  设备类型 Android
define('OSTYPE_IOS', 'ios');                //  设备类型 iOS
define('OSTYPE_HARMANYOS', 'harmonyos');    //  设备类型 harmonyos

define('ROLE_ID_DEFAULT', "1");             //  缺省角色
define('ROLE_ID_GUEST', "2");               //  来宾角色
define('ROLE_ID_TRUST', "3");               //  可信角色

define('REGION_ID_DEFAULT', "1");             //  安全域默认ID 互联网

define('AUTH_TYPE_USER', "User");             //  用户名密码认证
define('AUTH_TYPE_GUEST', "Guest");           //  来宾认证
define('AUTH_TYPE_NOAUTH', "NoAuth");         //  免认证
define('AUTH_TYPE_MOBILE', "Mobile");         //  短信认证
define('AUTH_TYPE_EMAIL', "Email");           //  邮箱认证
define('AUTH_TYPE_PERMIT', "Permit");          //  极速入网认证

define('AUTH_FROM_8021X', "802.1x");         //  认证来源802.1x

define('STRING_TRUE', "1");           //  字符串真
define('STRING_FALSE', "0");          //  字符串假

define('LDAP_OP', 0x0032);  // ldap操作
define('DEFAULT_TIME', '1970-01-01 00:00:00');  // 默认时间

define('PATH_TBRIDGE_PRIVATE', PATH_ETC . 'asm/asc/etc/tbridge_private.ini');  // 桥配置文件

define('DEVTYPE_DASM', "dasm");         // dasm设备
define('DEVTYPE_DASC', "dasc");         // dasc设备
define('DEVTYPE_ASM', "asm");           // asm设备
define('DEVTYPE_ASG', "asg");           // asg设备

// sso
define('SSO_CONFIG_PATH', PATH_ETC . 'asm/sso/');
define('SSO_CONFIG_FILE', 'ssoConfig.json');

// 资源访问类型
define('ACCESS_TYPE_COMMON', '299'); // 普通代理
define('ACCESS_TYPE_OAUTH2', '300'); // Oauth2
define('ACCESS_TYPE_FORM', '301'); // 表单代填
define('ACCESS_TYPE_RDP', '302'); // RDP
define('ACCESS_TYPE_VNC', '303'); // VNC
define('ACCESS_TYPE_SSH', '304'); // SSH
define('ACCESS_TYPE_TELENT', '305'); // TELENT
define('ACCESS_TYPE_REMOTE_APP', '310'); // 远程应用资源
define('ACCESS_TYPE_MICRO_APP', '311'); // 微应用
define('ACCESS_TYPE_CUSTOM_APP', '312'); // 自定义应用
define('ACCESS_TYPE_NO_AUTH', '313'); // 免认证资源应用,透明代理

// 网关类型
define('GATEWAY_TYPE_COMMON', '1'); // 网关
define('GATEWAY_TYPE_GROUP', '2'); // 网关组

// 会话状态
define('SESSION_STATUS_NO_LOGIN', '-1'); // 未登录
define('SESSION_STATUS_OK', '0'); // 流程完成
define('SESSION_STATUS_NO_USER', '1'); // 无用户ID，认证完
define('SESSION_STATUS_USER', '2'); // 有用户ID，认证完
define('SESSION_STATUS_SAFE_CHECK', '8'); // 安检完

// HTTP
define('HTTP_PORT', 80);
define('HTTPS_PORT', 443);
define('HTTP_PROTOCOL', 'http://');
define('HTTPS_PROTOCOL', 'https://');

// 场景信息配置
define('ENABLE_SCENE_CACHE', 1);    // 是否启用场景信息缓存，默认先不开启，如果有性能问题再开启
define('DEFAULT_SCENE_ID', 1); // 默认场景ID
define('TRUST_SCENE_ID', 2); // 可信场景ID

// 升级相关
define('FORCED_UPGRADE', 1); // 是否提示客户端强制升级
