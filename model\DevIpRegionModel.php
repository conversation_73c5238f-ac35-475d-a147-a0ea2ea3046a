<?php

/**
 * Description: TDevIpRegion表
 * User: <EMAIL>
 * Date: 2021/06/09 16:52
 * Version: $Id: DevIpRegionModel.php 171857 2022-03-24 09:16:07Z renchen $
 */

class DevIpRegionModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevIpRegion';
    public const PRIMARY_KEY = 'MacID';
    protected static $columns = [
        '*'    => '*',
        'info' => 'TTmpNetRole.Type',
        'net' => 'IPListId',
        'IP' => 'IP, IPListId',
        'device' => 'DeviceID'
    ];

    /**
     * 根据MAC获取数据
     * @param $MAC
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByMac($MAC, $Column = 'device')
    {
        if (empty($MAC)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE MAC = ".self::setData($MAC);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据MAC获取数据
     * @param $cond
     * @param string $Column
     * @return array|bool
     */
    public static function getJoinTmpRole($cond, $Column = 'info')
    {
        if (empty($cond)) {
            return false;
        }

        self::$data = [];
        if (isset($cond['DeviceID'])) {
            $where = "WHERE DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['MAC'])) {
            $where = "WHERE TDevIpRegion.MAC = ".self::setData($cond['MAC']);
        }

        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $leftJoin = "left join TTmpNetRole on TDevIpRegion.IPListId = TTmpNetRole.ID";
        $sql = "select {$column} from {$table['name']} {$leftJoin} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据设备ID获取数据
     * @param $deviceId
     * @param string $Column
     * @return array|bool
     */
    public static function getJoinPermit($deviceId, $Column = 'net')
    {
        if (empty($deviceId)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "where TPermitDevice.DeviceID = ".self::setData($deviceId);
        $leftJoin = "left join TPermitDevice on TDevIpRegion.DeviceID = TPermitDevice.BindID ";
        $sql = "select {$column} from {$table['name']} {$leftJoin} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['MacID'])) {
            $where .= "AND MacID = ".self::setData($cond['MacID']);
        }

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
