<?php
/**
 * Description: 公共提示
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: Common.php 170672 2022-03-09 06:23:04Z lihao $
 */

$GLOBALS['LANG'][21100] = [
    21100000 => 'The operation failed',
    21100001 => 'The queue doesn\'t exist!',
    21100002 => 'The argument is wrong!',
    21100003 => 'The insertion failed!',
    21100004 => 'No cache configured!',
    21100005 => 'The request method is incorrect!',
    21100006 => '100 Access Denied: {message}!',
    21100007 => 'The terminal time is incorrect!',
    21100008 => 'Request failed!',
    21100009 => 'The parameter can only be a word!',
    21100010 => 'Phone number format is incorrect!',
    21100011 => '{columnName} consists of letters, numbers, Chinese and -_*@. and the length does not exceed 50 characters',
    21100012 => '{columnName} format error',
    21100013 => '{columnName} cannot enter \',",/,~,\,<,>,^,&,+ and other characters, and cannot exceed 500 characters',
    21100014 => ' ID format is incorrect',
    21100015 => 'The date format is incorrect!',
    21100016 => 'Cannot enter \',",~,/,\,<,>,^,&,+,=,{,} and other characters, and cannot exceed 1000 characters',
    21100017 => 'Cannot enter \',",/,~,\,<,>,^,&,+ and other characters, and cannot exceed 500 characters',
    21100018 => 'Parameter error or ini file does not exist!',
    21100019 => 'File [{file}] failed to upload!',
    21100020 => 'Illegal IP format!',
    21100021 => 'Illegal MAC format!',
    21100022 => 'Operation is too frequent!',
    21100023 => 'The parameter contains illegal characters, please check carefully!',
    21100024 => 'serious',
    21100025 => 'important',
    21100026 => 'medium',
];
