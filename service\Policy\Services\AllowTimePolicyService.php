<?php
/**
 * Description: 零信任允许访问时间策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowTimePolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *
     * @return bool
     */
    public function check(): bool
    {
        if ((!$this->params['Config']['StartTime'] && !$this->params['Config']['EndTime'])) {
            return true;
        }
        if ($this->params['Config']['StartTime'] === '00:00:00' && $this->params['Config']['EndTime'] === '23:59:59') {
            return true;
        }

        $startTime = $this->params['Config']['StartTime'] ? strtotime($this->params['Config']['StartTime']) : strtotime('00:00:00');
        $endTime = $this->params['Config']['EndTime'] ? strtotime($this->params['Config']['EndTime']) : strtotime('23:59:59');
        $nowTime = time();

        if ($startTime >= $endTime) {
            return $nowTime >= $startTime || $nowTime <= $endTime;
        }
        return $nowTime >= $startTime && $nowTime <= $endTime;
    }
}
