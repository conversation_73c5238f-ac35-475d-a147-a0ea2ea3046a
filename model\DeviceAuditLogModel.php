<?php

/**
 * Description: 设备TDeviceAuditLog表
 * User: <EMAIL>
 * Date: 2021/06/24 10:02
 * Version: $Id: DeviceAuditLogModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class DeviceAuditLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDeviceAuditLog';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'A.ID',
        '*'    => '*',
    ];

    /**
     * 获取连表查询数据: TComputer
     *
     * @param $DeviceID
     * @param $Column
     *
     * @return array|bool
     */
    public static function getJoinComputer($DeviceID, $Column = 'one')
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns[$Column];
        $where = "WHERE B.DeviceID=".self::setData($DeviceID);
        $sql = "select {$column} from TDeviceAuditLog A inner join TComputer B ON A.Hard=B.Hard {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Mac'])) {
            $where .= "AND Mac = ".self::setData($cond['Mac']);
        }

        if (isset($cond['Hard'])) {
            $where .= "AND Hard = ".self::setData($cond['Hard']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
