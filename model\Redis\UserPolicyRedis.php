<?php

/**
 * Description: 用户策略结果
 * User: <EMAIL>
 * Date: 2022/05/16 10:32
 * Version: $Id$
 */

class UserPolicyRedis extends BaseRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'UserPolicy';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'AllowSoftResult,AllowSoftReason,ForbidSoftResult,ForbidSoftReason'.
        ',AllowProcessResult,AllowProcessReason,ForbidProcessResult,ForbidProcessReason';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => '',
    ];

    /**
     * 单条
     *
     * @param string $token
     * @param string $policyId
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($token, $policyId, $column = 'one')
    {
        return self::get($column, $token, $policyId);
    }

    /**
     * 单条
     *
     * @param string $token
     * @param string $policyId
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($token, $policyId, $data)
    {
        $data['LifeTime'] = 86400;
        return self::set($data, $token, $policyId);
    }
}