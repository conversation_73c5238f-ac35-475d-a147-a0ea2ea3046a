<?php
/**
 * Description: SSO认证
 * User: <EMAIL>
 * Date: 2022/02/18 14:48
 * Version: $Id:
 */

use Services\Auth\Services\SsoAuthService;

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class SsoController extends BaseController
{
    /**
     * 使用get方式的白名单方法
     * auth SSO重定向接口
     * @var bool[]
     */
    public $whiteList = ['auth' => true];

    /**
     * SSO服务
     * @var SsoAuthService
     */
    private $ssoService;

    /**
     * SSO参数
     * @var array
     */
    public $params = [];

    public function __construct()
    {
        parent::__construct();

        if (strtoupper(lib_request::$method) == 'OPTIONS') {
            header('Access-Control-Allow-Origin:*'); // *代表允许任何网址请求
            header('Access-Control-Allow-Methods:POST,GET,OPTIONS,DELETE'); // 允许请求的类型
            header('Access-Control-Allow-Credentials: true'); // 设置是否允许发送 cookies
            header('Access-Control-Allow-Headers: Content-Type,Content-Length,Accept-Encoding,X-Requested-with, Origin'); // 设置允许自定义请求头的字段
            exit;
        }
        $this->params['deviceId'] = request('deviceId', 'request', 0, 'int');
        $this->params['isClient'] = request('isClient', 'request', -1, 'int');
        $this->params['isHttps'] = request('isHttps', 'request', isHttps() ? '1' : '0');// 是否https
        $this->params['resId'] = request('resId', 'request', '');
        $this->params['resurl'] = request('resurl', 'request', '');
        // 手动处理phpcas需要参数
        $_GET = [];
        $_POST = [];

        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        if (isset($basicConfig['Mode']) && (int)$basicConfig['Mode'] === 1) {
            $_GET['ticket'] = ssoCommon::cacheTicket($this->params['deviceId'], request('ticket', 'get'));

            $_GET['pgtIou'] = request('pgtIou', 'get');
            $_GET['pgtId'] = request('pgtId', 'get');
            $_POST['pgtIou'] = request('pgtIou');
            $_POST['pgtId'] = request('pgtId');
            $_POST['rebroadcast'] = request('rebroadcast');
            $_POST['logoutRequest'] = request('logoutRequest');
        } else {
            if (empty($this->params['deviceId'])) {
                $state = request('state', 'get');
                try {
                    $state = Base64DeExt($state);
                    $state = json_decode($state, true, 10, JSON_THROW_ON_ERROR);
                    if (isset($state['deviceId']) && !empty($state['deviceId'])) {
                        $this->params['deviceId'] = $state['deviceId'];
                    } else {
                        T(21147011);
                    }
                    if (isset($state['resId']) && !empty($state['resId'])) {
                        $this->params['resId'] = $state['resId'];
                    }
                    if (isset($state['resurl']) && !empty($state['resurl'])) {
                        $this->params['resurl'] = $state['resurl'];
                    }
                    if (isset($state['isClient']) && !empty($state['isClient']) && $this->params['isClient'] < 0) {
                        $this->params['isClient'] = $state['isClient'];
                    }
                } catch (Exception $e) {
                    T(21147011);
                }
            }
            $this->params['ticketField'] = request($basicConfig['TicketField'], 'get');
            //            $this->params['ticketField'] = ssoCommon::cacheTicket(
            //                $this->params['deviceId'],
            //                request($basicConfig['TicketField'], 'get'),
            //                'oauth'
            //            );
        }

        ssoCommon::recordLog('params:' . var_export($this->params, true));

        $this->params['servicePrefix'] = 'Sso';
        $this->ssoService = AuthServiceProvider::initAuthService($this->params);
    }

    /**
     * 认证，未认证则跳转SSO，已认证则跳转入网
     * @return bool
     * @throws Exception
     */
    public function auth(): bool
    {
        return $this->ssoService->login();
    }

    /**
     * 登出
     * @return bool|string
     * @throws Exception
     */
    public function logout()
    {
        return $this->ssoService->logout();
    }

    /***
     * 检测是否已经单点登录，已登录则返回用户名
     * @throws Exception
     */
    public function checkLoginState()
    {
        return $this->ssoService->checkLoginState();
    }

}
