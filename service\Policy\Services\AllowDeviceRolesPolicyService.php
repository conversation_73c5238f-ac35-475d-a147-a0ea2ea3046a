<?php
/**
 * Description: 零信任指定设备角色可访问策略项服务
 * User: <EMAIL>
 * Date: 2023/08/14 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowDeviceRolesPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *@throws \Exception
     * @return bool
     */
    public function check(): bool
    {
        $device = $this->device;
        $rolesArr = explode(',', $this->params['Config']);
        foreach ($rolesArr as $roles){
            if ($device['RoleID'] == $roles){
                return true;
            }
        }
        return false;
    }
}
