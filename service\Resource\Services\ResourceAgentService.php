<?php
/**
 * Description: 资源代理相关
 * User: duanyc
 * Date: 2023/10/26 21:51
 * Version: $Id: PatchService.php 157413 2021-09-22 08:11:28Z duanyc $.
 */

namespace Services\Resource\Services;

use Exception;
use GateWayModel;
use lib_yar;
use Services\Common\Services\CommonService;

class ResourceAgentService extends CommonService
{
    /**
     * 资源权限
     * @param $params
     */
    public function __construct($params)
    {
        parent::__construct();
        $this->logFileName = 'ResourceAgent';
        $this->writeLog(!is_string($params) ? json_encode($params) : $params);
    }

    /**
     * 获取资源在代理网关的联动性
     * @param $address
     * @return array
     */
    public function checkResConnection($address, $resInfo): array
    {
        $returnArr = ['Result' => false, 'Summary' => L(21148066), 'Details' => L(21148067)];
        if (empty($resInfo)) {
            //直接返回联通失败
            return $returnArr;
        }
        //查询网关信息，获取对应的地址
        if (!empty($resInfo['GateWayType'])) {
            //如果是网关则只需要调用一个网关，如果是网关组则需要调用网关组下所有的ASG去测试
            if ($resInfo['GateWayType'] == 1) {
                $gateWayInfoArr[] = GateWayModel::getOne($resInfo['GateWayID']);
            } else if ($resInfo['GateWayType'] == 2) {
                $gateWayInfoArr = GateWayModel::getList(['GroupID' => $resInfo['GateWayID']]);
            }
        }

        if (!empty($gateWayInfoArr)) {
            foreach ($gateWayInfoArr as $gateWayInfo) {
                //通过RPC调用ASG对应的服务器去探测连通性，带端口则telnet,否则ping
//                $yar_res = lib_yar::clients('ztpGateway', 'checkResConnection', ['address' => $address, 'resInfo' => $resInfo], $gateWayInfo['IP']);
                WorkerUdpSend("MessageToGw", "checkResConnection", ['address' => $address, 'resInfo' => $resInfo], [$gateWayInfo['IP']]);

                $test_num = 0;
                $yar_res = '';
                do {
                    sleep(1);
                    $yar_res = \lib_redis::get('ASM_', 'checkResConnection:' . $resInfo['ResID']);
                    if (!empty($yar_res)) {
                        break;
                    }
                    $test_num++;
                } while ($test_num < 3);
                $yar_res = json_decode($yar_res, true);
                if (empty($yar_res) || empty($yar_res['data'])) {
                    $returnArr['Details'] = "RPC调用失败！";
                    break;
                }
                $returnArr['Result'] = $yar_res['data']['Result'] ?? false;
                $returnArr['Summary'] = $yar_res['data']['Summary'] ?? L(21148066);
                $returnArr['Details'] = $yar_res['data']['Details'] ?? L(21148067);
                if (empty($yar_res['data']['Result'])) {
                    break;
                }
            }
        }

        return $returnArr;
    }

    /**
     * 获取资源在代理网关的联动性提供RPC查询使用
     * @param $params
     * @return array
     */
    public function checkResConnectionRpc($params): array
    {
        $returnArr = ['Result' => false, 'Summary' => L(21148066), 'Details' => L(21148067)];
        $resInfo = $params['resInfo'];
        $address = $params['address'];
        //解析地址参数构成，带端口与不带端口 如：example.com:8080
        $urlParts = parse_url($address);
        $host = $urlParts['host'] ?? ($urlParts['path'] ?? '');
        $port = $urlParts['port'] ?? '';
        if (empty($host)) {
            return $returnArr;
        }
        //测试目标地址与网关的连通性
        $isConn = false;
        if ($resInfo['ResType'] == 0) {
            // 检查实际访问地址与网关服务器连接状态
            $isConn = $this->checkConnection($resInfo['DomainName'], $resInfo['RealPort']);
        } elseif ($resInfo['ResType'] == 1) {
            // 如果是访问域资源，如果是纯IP类的匹配，执行ping检查
            if (!in_array($resInfo['ip_match_type'], [5, 6])) {
                $isConn = $this->checkConnection($host, $port, 'PING');
            } elseif (strpos(strtoupper($resInfo['ip_match']), "UDP") !== false) {
                $isConn = $this->checkConnection($host, $port, "UDP");
            } else {
                $isConn = $this->checkConnection($host, $port);
            }
        }
        if ($isConn) {
            $returnArr['Result'] = true;
            $returnArr['Summary'] = L(21148064);
            $returnArr['Details'] = L(21148065);
        }
        $res['data'] = $returnArr;
        lib_yar::clients('backend', 'setCheckResConnection', ['returnArr' => $res, 'ResID' => $params['resInfo']['ResID']]);
        return $returnArr;
    }

    /**
     * 检查连通性
     * @param $host
     * @param $port
     * @param $type
     * @return bool
     * @throws Exception
     */
    public function checkConnection($host, $port, $type = 'TCP'): bool
    {
        $result = false;
        switch ($type) {
            case 'TCP':
                $timeout = 10; // 连接超时时间（单位：秒）
                $socket = @fsockopen($host, $port, $errno, $errStr, $timeout);
                if ($socket) {
                    fclose($socket);
                    $result = true;// 连接成功
                }
                break;
            case 'UDP':
                $timeout = 10; // 连接超时时间（单位：秒）
                $socket = @fsockopen("udp://$host", $port, $errno, $errStr, $timeout);
                if ($socket) {
                    fclose($socket);
                    $result = true;// 连接成功
                }
                break;
            case 'PING':
                $cmd = "ping -c 1 {$host}";
                $res = cutil_exec_wait($cmd);
                if (!empty($res) && strpos($res, '1 packets transmitted, 1 received') !== false) {
                    $result = true;
                }
                break;
            default:
                break;
        }
        return $result;
    }


}
