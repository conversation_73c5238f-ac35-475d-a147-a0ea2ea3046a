<?php



class SpeedLimitModel extends BaseModel
{
    public const TABLE_NAME = 'TSpeedLimit';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'list'   => 'ID,UpTraffic,DownTraffic,Priority,Status'
    ];

    /**
     * 获取新的资源信息,关联多个网关
     * **/

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
