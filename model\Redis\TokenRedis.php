<?php

/**
 * Description: 删除一些过期的废弃的token
 * User: <EMAIL>
 * Date: 2024/05/16 10:32
 * Version: $Id$
 */

class TokenRedis extends BaseListRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Token';

    /**
     * 是否同步到ASG
     *
     * @var string
     */
    protected static $remote = false;

    /**
     * 由列表头部添加字符串值。如果不存在该键则创建该列表。
     * 如果该键存在，而且不是一个列表，返回FALSE
     *
     * @param string $data
     * @param string $key
     * @return mixed
     */
    public static function lPushData(string $data, string $key = '')
    {
        return self::lpush($data, $key);
    }

    /**
     * 由列表头部批量添加字符串值。如果不存在该键则创建该列表。
     *
     * @param array $data
     * @param string $key
     * @return mixed
     */
    public static function lMultiPushData(array $data, string $key = '')
    {
        return self::lmultipush($data, $key);
    }

    /**
     * 从尾部并删除一个元素,并返回删除的值
     *
     * @return mixed
     */
    public static function rPopData($key = '')
    {
        return self::rpop($key);
    }

    /**
     * 由列表尾部添加字符串值。如果不存在该键则创建该列表。
     *  如果该键存在，而且不是一个列表，返回FALSE。
     * @param string $data
     * @param string $key
     * @return bool|null
     */
    public static function rPushData(string $data, string $key = ''): ?bool
    {
        return self::rpush($data, $key);
    }

    /**
     *  计算redis Lists 元素数量
     * @param string $key
     * @return bool|int|Redis|null
     */
    public static function lLen(string $key = '')
    {
        return self::lsize($key);
    }

    /**
     * 删除
     *
     * @param string $key
     * @return boolean
     */
    public static function delData(string $key = ''): bool
    {
        return self::del($key);
    }

}