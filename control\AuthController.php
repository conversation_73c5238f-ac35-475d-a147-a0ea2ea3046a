<?php

/**
 * Description: 认证信息交易
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: AuthController.php 175145 2022-05-06 07:53:42Z huyf $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class AuthController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['flag' => true, 'other' => true];

    /**
     * 公共判断
     * @throws Exception
     */
    public function __construct()
    {
        include_config('auth');
        parent::__construct();
    }

    /**
     * 参数校验.
     * @return mixed
     * @throws \Exception
     */
    protected function checkParams()
    {
        cutil_php_log(lib_request::$requests, 'net_auth');
        $params = [];
        // 通过何种方式认证type
        $type = request('type', 'request');
        // 认证认证方式转换
        $params['isGuestAuth'] = request('isGuestAuth', 'request', 0, 'int');
        if (!empty($params['isGuestAuth'])) {
            $type = 'Guest';
        }
        if (request('authtypes', 'request') === 'TwoFactor') {
            //兼容老接口的短信双因子;双因子指明了认证方式，可直接检查跟配置是否一致
            $userDict = BindServiceProvider::getUserDict();
            $params['factorType'] = request('factorType', 'request');
            if (!empty($params['factorType']) && $params['factorType'] !== $userDict['MFAServer']) {
                T(21120010);
            }
        }
        $params['authType'] = $type;
        $params['servicePrefix'] = $GLOBALS['AUTH_CONFIG']['TypeService'][$type] ?? $type;
        $params['formMobile'] = request('form_mobile', 'request');
        $params['userName'] = request('user_name', 'request');
        $params['password'] = request('password', 'request');
        $params['beforeAuthType'] = request('BeforeAuthType', 'request');
        $params['deviceId'] = request('deviceid', 'request', 0, 'int');
        $params['mac'] = request('mac', 'request');
        $params['remoteIp'] = request('remoteIp', 'request');
        $params['tokenId'] = request('tokenId', 'request');
        $params['idp'] = request('idp', 'request');
        $params['from'] = request('from', 'request');
        $this->otherParams($params);
        if (request('authFrom', 'request') === 'bind') {
            // 兼容手机入网绑定认证时，未传入authserver；
            $params['authserver'] = $params['authserver'] ?: Base64EnExt($params['bindAuthServer']);
            //主账号绑定认证检查
            $oldUser = BindServiceProvider::getUserInfo($params['userid']);
            BindServiceProvider::bindCheck($params['bindAuthServer'], $oldUser);
            $session = LoginServiceProvider::getSessionInfo(LoginServiceProvider::getLoginToken(), 'user');
            $params['oldAccountID'] = $session['AccountID'] ?? -1;
        }
        return $params;
    }

    /**
     * 其他参数处理
     *
     * @param $params
     */
    private function otherParams(&$params)
    {
        // 802.1x服务端不记录认证失败记录
        $params['isRecordErr'] = !request('NoRecordLog', 'request', 0, 'int');
        // 如果是802.1x调用则不记录认证记录
        $params['isRecord'] = request('isRecord', 'request', $params['isRecordErr'], 'int');
        if (OS_TYPE === OSTYPE_MAC) {
            // 当前mac不支持无线的自主协议,那就不进行双因子认证,所以也不弹框了bug:1827 TODO 后续应该要去掉
            $params['noFactorAuth'] = request('noFactorAuth', 'request', 0, 'int');
        }
        $from = !empty($params['from']) ? $params['from'] : 'web';
        $params['callfrom'] = request('callfrom', 'request', $from);
        $params['subType'] = request('subType', 'request');
        $params['newMobile'] = request('NewMobile', 'request'); // 表示移动端
        $params['authweb'] = request('authweb', 'request'); // 如果是等于mobile则表示通过手机页面认证的 否则就为pcweb //用于手机web
        $params['isMobile'] = request('is_mobile', 'request', 0, 'int'); // 是1 否0 是否手机客户端 //用于手机客户端交易
        $params['twoAuthType'] = request('authtypes', 'request'); //双因子认证时使用
        $params['qrlogin'] = request('qrlogin', 'request', 0, 'int');
        $params['dingQrlogin'] = request('dingqrlogin', 'request', 0, 'int');
        $params['weworkQrlogin'] = request('weworkqrlogin', 'request', 0, 'int');
        $params['feishuQrlogin'] = request('feishuqrlogin', 'request', 0, 'int');
        $params['appId'] = request('appid', 'request');
        $params['authserver'] = request('authserver', 'request', '');
        $params['domainName'] = request('domain_name', 'request');
        $params['fingerBind'] = request('fingerBind', 'request');
        $params['noAuthCode'] = request('noAuthCode', 'request');
        $params['mobilePhone'] = request('mobile_phone', 'request');
        $params['checkCode'] = request('check_code', 'request', 0, 'string');
        $params['remark'] = request('remark', 'request');
        $params['hintOver'] = request('hintOver', 'request');
        $params['autoAuth'] = request('autoAuth', 'request');
        $params['verifyCode'] = request('verify_code', 'request');
        $params['ip'] = request('IP', 'request'); // 802.1x时传的终端IP
        $params['fakeIsInDomain'] = request('fakeIsInDomain', 'request');// ad域防伪造
        $params['isHttps'] = request('isHttps', 'request');// sso 是否https
        $params['userid'] = request('userid', 'request');//OTP 用户账户ID
        $params['bindAuthServer'] = request('bindAuthServer', 'request');//主账号绑定认证的认证源服务器类型
        $params['sceneId'] = request('sceneId', 'request', 0, 'int'); // 场景ID，如果没有传就从TRelationComputer表中取
    }

    /**
     * 认证交易，对应老交易 net_auth
     *
     * @return array|int
     * @throws Exception
     */
    public function index()
    {
        // 前端附加认证、绑定认证复用普通认证
        $authFrom = request('authFrom', 'request');
        if (!empty($authFrom)) {
            $businessTypes = ['addition' => true, 'bind' => false];
            if (!empty($businessTypes[$authFrom])) {
                return $this->$authFrom();
            }
        }
        try {
            $params = $this->checkParams();
            $authService = AuthServiceProvider::initAuthService($params);
            $authService->parseParams();
            $authService->authBefore();

            // 调用具体认证服务进行校验
            $authServers = $authService->getAuthServer();
            $authParams = $authService->getAuthParams();
            $data = AuthServiceProvider::validateRpcAuthServer($authServers, $authParams, $authService);
            $params = $authService->getParams();

            // 检查设备与用户绑定
            if ($params['authType'] !== AUTH_TYPE_NOAUTH) {
                $authService->checkDeviceBindUser($params['authType'], $data['UserID']);
            }
            $userInfo = $authService->getUserInfo();
            $this->errmsg = L(21120004);
            if ($params['twoAuthType'] === 'TwoFactor') {
                $data['Token'] = LoginServiceProvider::getLoginToken();
                // 校验双因子账号是否当前登录用户绑定的
                AuthServiceProvider::checkfactorAuth($data, $params);
                $authService->authTwoFactorAfter($data, $userInfo);
                $authService->setFactorCommonInfo($data);
                $authService->updateTwoFactorAuthLog();
                return ['code' => '200', 'errorMsg' => $this->errmsg];
            }
            $data['Token'] = LoginServiceProvider::getToken($data, $params);
            BindServiceProvider::bindMainUserDeal($data, $userInfo, $params);
            $authService->authAfter($data, $userInfo, $params['authType']);
            $authService->setCommonInfo($data, $userInfo, $params);
            $authId = $authService->recordAuthSucLog($params['authType'], $data, '', $data['ID'] ?? false, $data['BindAuth']);
            $data["LastAuthID"] = !empty($authId) ? (string)$authId : '0';
            cutil_php_log($data, 'net_auth');
            return $this->returnAuthData($data);
        } catch (Exception $e) {
            if (!empty($authService)) {
                $message = $e->getMessage();
                $previous = $e->getPrevious();
                if ($previous && $previous->getMessage()) {
                    $message = $previous->getMessage();
                }
                $authService->recordAuthErrLog($params['authType'], $message);
                $GLOBALS['CONFIG']['ALL_MESSAGE'] = $message;
            }
            throw $e;
        }
    }

    /**
     * 返回认证结果
     * @throws Exception
     */
    public function result()
    {
        $deviceId = request('deviceid', 'request', 0, 'int');
        $session = LoginServiceProvider::checkLogin($deviceId);
        $data = AuthServiceProvider::getAuthResult($session);
        cutil_php_log($data, 'net_auth');
        return $this->returnAuthData($data);
    }

    /**
     * 认证失败上报
     *
     * @return array|int
     * @throws Exception
     */
    public function fail()
    {
        $params = $this->checkParams();
        $failReason = request('fail_reason', 'request');
        hlp_check::checkEmpty($failReason);
        $authService = AuthServiceProvider::initAuthService($params);
        $authService->parseParams();
        $authService->authBefore();
        $authType = $authService->getAuthType();
        $authService->recordAuthErrLog($authType, $failReason);
        return [];
    }

    /**
     * 限制返回的字段
     * @param $data
     * @return array
     */
    private function returnAuthData($data)
    {
        $return = [];
        foreach ($GLOBALS['AUTH_CONFIG']['RequireColumns'] as $column) {
            $return[$column] = $data[$column];
        }
        foreach ($GLOBALS['AUTH_CONFIG']['OptionalColumns'] as $column) {
            if (isset($data[$column])) {
                $return[$column] = $data[$column];
            }
        }
        return $return;
    }

    /**
     * 资源附加认证（强化认证）
     *
     * @return mixed
     * @throws Exception
     */
    public function addition()
    {
        $params = $this->checkParams();
        $params['noAuthCode'] = 1;
        $session = LoginServiceProvider::checkLogin($params['deviceId']);
        $params['currUserID'] = $session['Uuid'];
        try {
            $authService = AuthServiceProvider::initAuthService($params);
            $authService->parseParams();
            $authService->authBefore();

            // 调用具体认证服务进行校验
            $authServers = $authService->getAuthServer();
            $authParams = $authService->getAuthParams();
            $data = AuthServiceProvider::validateRpcAuthServer($authServers, $authParams, $authService);
            LoginServiceProvider::checkAddAuthType($data['auth_type'] ?? $data['AuthType']);
            // 校验附加认证的账号是否当前登录用户绑定的
            if ($session['Uuid'] !== $data['UserID']) {
                T(21148028);
            }
            ResourceServiceProvider::addAuth($session['Token']);
            ResourceServiceProvider::setUserPower($session['Token']);
            $authService->addAuthAfter();
            $content = L(21148032, ['UserName' => $session['UserName'], 'Time' => date('Y-m-d H:i:s')]);
            LoginServiceProvider::addOtherUserLog($session, 62, $content);
            return [];
        } catch (Exception $e) {
            $content = L(21148033, ['UserName' => $session['UserName'], 'Time' => date('Y-m-d H:i:s')]);
            LoginServiceProvider::addOtherUserLog($session, 62, $content, ['Status' => '1']);
            throw $e;
        }
    }

    /**
     * 敲门账号校验
     *
     * @return mixed
     * @throws Exception
     */
    public function fwknop()
    {
        $AuthData = request('AuthData', 'request');
        $DeviceData = request('DeviceData', 'request');
        hlp_check::checkEmpty($AuthData);
        hlp_check::checkEmpty($DeviceData);
        $deviceId = DeviceServiceProvider::parseFwknopData($DeviceData);
        lib_request::$requests = AuthServiceProvider::parseFwknopData($AuthData, $deviceId);
        lib_request::$requests['deviceid'] = $deviceId;
        $params = $this->checkParams();
        $params['noAuthCode'] = 1;
        $params['fwknopAuth'] = 1;
        $authService = AuthServiceProvider::initAuthService($params);
        $authService->parseParams();
        $authService->authBefore();

        // 调用具体认证服务进行校验
        $authServers = $authService->getAuthServer();
        $authParams = $authService->getAuthParams();
        AuthServiceProvider::validateRpcAuthServer($authServers, $authParams, $authService);
        $authService->fwknopAfter();
        return [];
    }

    /**
     * 认证ip段范围，对应老交易 get_authflag
     *
     * @return int
     * @throws Exception
     */
    public function flag()
    {
        return 1;
    }

    /**
     * 获取用户认证配置（下次认证必须修改密码），对应老交易 get_userflag
     *
     * @return mixed
     * @throws Exception
     */
    public function config()
    {
        $username = request('username', 'request');
        hlp_check::checkEmpty($username);
        $username = Base64DeExt($username, true);
        $isMust = AuthServiceProvider::isMustChangePasswrod($username);

        if (API_VERSION < '1.0') {
            return $isMust;
        }

        return ['isMustChangePasswrod' => (int)$isMust];
    }

    /**
     * 自动跳过认证进行安检，对应老交易 auto_auth
     *
     * @return mixed
     * @throws Exception
     */
    public function auto()
    {
        $deviceid = request('deviceid', 'request', 0, 'int');
        hlp_check::checkEmpty($deviceid);
        $onlineDevice = AuthServiceProvider::getOnlineInfo($deviceid);
        if (empty($onlineDevice)) {
            T(21120036);
        }
        return $onlineDevice;
    }

    /**
     * 获取场景配置信息，对应老交易 get_roleinfo
     *
     * @return array
     * @throws Exception
     */
    public function sceneinfo(): array
    {
        $params = [];
        $params['sceneId'] = request('sceneId', 'request', 0, 'int');
        $params['is_guest'] = request('is_guest', 'request', 0, 'int');
        if (empty($params['sceneId'])) {
            T(21120028);
        }

        $return = AuthServiceProvider::getSceneInfoBySceneID($params['sceneId']);
        unset($return['CheckParam']);
        $return['Engine'] = SystemServiceProvider::getEngine();
        return $return;
    }

    /**
     * 获取第三方用户信息路由类如微信等 只有企业微信和微信用户使用，飞书和钉钉直接用控制器的jump方法
     *
     * @return void
     * @throws Exception
     */
    public function other()
    {
        $managerIp = getManagerIp();
        $t = request('t', 'request');
        $routeType = request('route_type', 'request', 'wechat');
        $code = request('code', 'request');
        if (!empty($t)) {
            hlp_check::checkWord($routeType);
            cutil_php_log("{$routeType}, {$managerIp}", $routeType);
            AuthServiceProvider::getOtherUserInfo($routeType, $managerIp);
        } else {
            $param = '';
            if (!empty($routeType)) {
                $param = '&route_type=' . $routeType;
            }
            $otherParams = hlp_common::otherAuthParams();
            $param = "{$param}&{$otherParams}&state=1&t=" . random_int(1000, 9999) . '&host=' . $_SERVER['HTTP_HOST'];
            $url = "/access/getotheruser?" . $param;
            cutil_php_log($url, $routeType);
            if ($routeType === 'wework') {
                hlp_page::showWework($url, $code);
            }
            header("Location: {$url}&code={$code}");
        }
    }

    /**
     * 处理微应用单点登录跳转地址
     * @param $code
     * @throws Exception
     */
    private function resourceLogin($code)
    {
        $module = request('module', 'request', '', 'string');
        if ($module === 'resource') {
            $this->otherLogin();
            $redirect_url = request('redirect_url', 'request', '', 'string');
            $referToken = request('referToken', 'request', '', 'string');
            $url = "{$redirect_url}&referToken={$referToken}&code={$code}";
            header("Location: {$url}");
            exit();
        }
    }

    /**
     * 获取第三方用户临时码
     *
     * @return mixed
     * @throws Exception
     */
    public function otherToken()
    {
        $routeType = request('routeType', 'request');
        $authKey = request('authKey', 'request');
        hlp_check::checkEmpty($authKey);
        $code = QrcodeServiceProvider::getAuthCode($authKey);
        if (empty($code)) {
            T(21123009);
        }
        return ['authCode' => $code, 'routeType' => $routeType];
    }
}
