<?php
/**
 * Description: 隧道代理虚拟IP申请
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: VpnServiceProvider.php 153494 2021-08-19 01:25:07Z duanyc $
 */


class VirPoolServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'virPool';
    /**
     * 根据token申请虚拟IP
     * @param string $token
     * @return true|false
     * **/
    public static function ApplyVirIpForToken($token)
    {
        $Session = SessionRedis::getOne($token, 'usergw');
        $aVirIp = $Session['VirIpInfo'] ?? '';
        // WEB应用资源,IP资源 可以下发虚拟IP
        if (
            (empty($Session['vpnIpList']) || $Session['vpnIpList'] == '' || $Session['IpListGate'] == '' || $Session['IpListGate'] == '[]')
            && (empty($Session['AuthAppGate']) || $Session['AuthAppGate'] == '[]')) {
            // 无资源权限,无虚拟ip池 不需要分配IP
            return false;
        }
        $aGwId = [];
        $aGateWay = [];
        $Session['IpListGate'] = json_decode($Session['IpListGate'], true);
        $Session['AuthAppGate'] = json_decode($Session['AuthAppGate'], true);
        $aGate = array_unique(array_merge($Session['IpListGate'], $Session['AuthAppGate']));
        $aVirIp = json_decode($aVirIp, true);
        if (is_array($aGate)) {
            foreach ($aGate as $gid) {
                $aGw = GatewayRedis::getOne($gid);
                $aGateWay[$gid] = $aGw;
//                self::log('--------------GwVirState---------3--::::gwid'.$gid.'::'.$aGw['GwVirState']);
                $aWhere = ['UserID'=>$Session['Uuid'],'GwID'=>$gid];
                if ($aGw['GwVirState'] == 1) {
                    if (isset($aVirIp[$gid])) {
                        if (!empty($aVirIp[$gid]['ip'])) {
                            $flag = self::getVirIpFromDb($token, $aWhere, 3, $Session);
//                            self::log('--------------flag---------4--:::'.$flag);
                            if (!$flag) {
                                self::makeNewVirIp($token, $Session, $aGateWay, [$aVirIp[$gid]]);
                            }
                        } elseif (!empty($aVirIp[$gid]['disVirIp']) && $aVirIp[$gid]['disVirIp'] == 2) { //禁止虚拟IP连接
                            // 禁用模式
                            $aGwId[] = $gid;
                        } else {
                            // NAT
                            $flag = self::getVirIpFromDb($token, $aWhere, 2, $Session);
//                            self::log('--------------flag---------4--:::'.$flag);
                            if (!$flag) {
                                self::makeNewVirIp($token, $Session, $aGateWay, [['ID'=>0,'gwId'=>$gid,'ip'=>'']]);
                            }
                        }
                    } else {
                        $flag = self::getVirIpFromDb($token, $aWhere, 1, $Session);
//                        self::log('--------------flag---------555--gwid::'.$gid.':::'.$flag);
                        if (!$flag) {
                            $aGwId[] = $gid;
                        }
                    }
                } elseif ($aGw['GwVirState'] == 0) {
                    // NAT 模式下生成在线隧道记录
                    $flag = self::getVirIpFromDb($token, $aWhere, 2, $Session);
//                    self::log('--------------flag---------4--:::'.$flag);
                    if (!$flag) {
                        self::makeNewVirIp($token, $Session, $aGateWay, [['ID'=>0,'gwId'=>$gid,'ip'=>'']]);
//                        self::log('--------------flag---------5--:::'.$flag);
                    }
                }
            }
        }
        if (count($aGwId) > 0) {
            $aVpnIpAll = [];
            if (!empty($Session['vpnIpRangleAll'])&&$Session['vpnIpRangleAll']!=[]) {
                $aVpnIpAll = json_decode($Session['vpnIpRangleAll'] ?? '', true);
            }
            $aVirIpInfo = self::ApplyVirIp($token, json_encode($aGwId), $aGateWay, $aVpnIpAll);
            self::makeNewVirIp($token, $Session, $aGateWay, $aVirIpInfo);
        }
        return true;
    }
    /**
     * 在线申请虚拟IP
     * @param string $token
     * @param string $gwId
     * @param array $aGateWay
     * @param array $aVpnIpAll
     * @return array
     * **/
    private static function ApplyVirIp($token, $gwId, $aGateWay, $aVpnIpAll)
    {
        $url = 'http://127.0.0.1:50015/v1/virip/get';
        $aRes = curl($url, 'post', $gwId);
        $aVirIpInfo = [];
        $aGwID = json_decode($gwId, true);
        $aGwHasID = [];
//        self::log('--------------aRes-----------'.var_export($aRes, true));
        $aForResID = [];
        $aForGwID = [];
        if (isset($aRes['code']) && $aRes['code'] == 200) {
            if (!empty($aRes['data'])) {
                $aR = json_decode($aRes['data'], true);
                if (isset($aR['code']) && $aR['code'] == 0) {
                    foreach ($aR['data'] as $item) {
//                        self::log('--------------item-----------'.var_export($item, true));
                        if ($item['code'] == 0) {
                            // 正常分配IP
                            $aData = $item['data'];
                            $aVirIpInfo[$aData['gwId']] = $aData;
                            $vip = ['VirIp'=> $aData['ip']];
//                            self::log('--------------vip-----------'.var_export($vip,true));
                            $aGateWays = $aGateWay[$aData['gwId']];
                            SessionRedis::push("UserSession:".$token, $vip, [$aGateWays['IpIn']??'']);
                        } elseif ($item['code'] == 1) {
                            // 池满了
                            $aData = $item['data'];
                            if ($aGateWay[$aData['gwId']]['disVirIp'] == 1) {
                                $aVirIpInfo[$aData['gwId']] = ['gwId'=>$aData['gwId'],'disVirIp' =>1];
                            } else if (isset($aVpnIpAll[$aData['gwId']]) && is_array($aVpnIpAll[$aData['gwId']])) {
                                foreach ($aVpnIpAll[$aData['gwId']] as $key =>$val) {
//                                        UserResourceRedis::setOne($token, $key, ["VirIpResult"=>0,"Result"=>0,"Reason"=>L(21148059),"VirIpReason"=>L(21148059), "ErrCode" => "21148059"]);
                                    $aForResID[$key] = $key;
                                    $aForGwID[$aData['gwId']] = $aData['gwId'];
                                }
                                $aVirIpInfo[$aData['gwId']] = ['gwId'=>$aData['gwId'],'disVirIp' =>2];
                            }
                        }
                        $aGwHasID[] = $aData['gwId'] ?? 0;
                    }
                    self::log('VirIpInfo::---token:'.$token.':::'.var_export($aVirIpInfo, true));
                }
            }
        }
        if (is_array($aGwID)) {
            $aNewGwID = array_diff($aGwID, $aGwHasID);
            if (is_array($aNewGwID) && count($aNewGwID)>0) {
                foreach ($aNewGwID as $id) {
                    if (!empty($aGateWay[$id])) {
                        if ($aGateWay[$id]['disVirIp'] == 1) {
                            $aVirIpInfo[$id] = ['gwId'=>$id,'disVirIp' =>1];
                        } else if (is_array($aVpnIpAll[$id])) {
                            foreach ($aVpnIpAll[$id] as $key =>$val) {
//                                    UserResourceRedis::setOne($token, $key, ["VirIpResult"=>0,"Result"=>0,"Reason"=>L(21148059),"VirIpReason"=>L(21148059), "ErrCode" => "21148059"]);
                                $aForResID[$key] = $key;
                                $aForGwID[$id] = $id;
                            }
                            $aVirIpInfo[$id] = ['gwId'=>$id,'disVirIp' =>2];
                        }
                    }
                }
            }
        }
        if ($aVirIpInfo != []) {
            SessionRedis::setOne($token, ["VirIpInfo" => json_encode($aVirIpInfo)]);
            foreach ($aVirIpInfo as $item) {
                if (isset($item['disVirIp']) && $item['disVirIp'] == 2 && $aGateWay[$item['gwId']]['noIpPermit'] != 1) {
                    // 申请虚拟IP失败后,重置网关虚拟IP状态
                    GatewayRedis::setOne($item['gwId'], ['noIpPermit'=>1]);
                } elseif ((!isset($item['disVirIp']) || $item['disVirIp'] != 2) &&$aGateWay[$item['gwId']]['noIpPermit'] != 0) {
                    // 成功申请虚拟IP后,重置网关虚拟IP状态
                    GatewayRedis::setOne($item['gwId'], ['noIpPermit'=>0]);
                }
            }
        }
        if ($aForResID != [] && $aForGwID != []) {
            self::makeForbitGw($token, $aForResID, $aForGwID, $aVpnIpAll);
        }
        self::log('lastResult::---token:'.$token."::VirIpInfo::".var_export($aVirIpInfo, true));
        return $aVirIpInfo;
    }
    /**
     * 计算下发的禁止连接的资源
     * 一个资源对应多个网关,所有的网关均禁止连接才能在界面上展示禁止连接
     * @param string $token
     * @param array $aForResID
     * @param array $aForGwID
     * @param  array $aVpnRes
     * @return false|true
     * **/
    private static function makeForbitGw($token, $aForResID, $aForGwID, $aVpnRes)
    {
        foreach ($aVpnRes as $gwId => $aRes) {
            if (!isset($aForGwID[$gwId])) {
                foreach ($aRes as $resId => $res) {
                    if (isset($aForResID[$resId])) {
                        unset($aForResID[$resId]);
                    }
                }
            }
        }
        if (is_array($aForResID)) {
            foreach ($aForResID as $key => $gwId) {
                UserResourceRedis::setOne($token, $key, ["VirIpResult"=>0,"Result"=>0,"Reason"=>L(21148059),"VirIpReason"=>L(21148059), "ErrCode" => "21148059"]);
            }
        }
        self::log('makeForbit---token::'.$token.':::ResID:::'.var_export($aForResID, true));
        return true;
    }

    /**
     * 从在线表获取 虚拟IP
     * @param string $token
     * @param array $aQuery
     * @param  $state
     * @return false|true
     * **/
    private static function getVirIpFromDb($token, $aQuery, $state = 1, $Session)
    {
        if ($state == 1) {
            // 可分配IP
            $con = ['columns'=>'list','UserID'=>$aQuery['UserID'],'GwID'=>$aQuery['GwID'],'State'=>0];
        } elseif ($state == 2) {
            // NAT
            $con = ['columns'=>'list','UserID'=>$aQuery['UserID'],'GwID'=>$aQuery['GwID'], 'Token'=>$token];
            $aIp = UserVirtIpOnlineModel::getSingle($con);
            if (isset($aIp['ID'])&&$aIp['VirtualIp'] == '') {
                return true;
            }
            return false;
        } else {
            // 同一用户 同一token登录
            $con = ['columns'=>'list','UserID'=>$aQuery['UserID'],'GwID'=>$aQuery['GwID'], 'Token'=>$token];
        }
//        self::log('--------------con-----------'.var_export($con,true));
        $aIp = UserVirtIpOnlineModel::getSingle($con);
//        self::log('--------------aIp-----------'.var_export($aIp, true));
        if (!empty($aIp['VirtualIp'])) {
            $aGateWay = GatewayRedis::getOne($aQuery['GwID']);
//            self::log('--------------aGateWay-----------'.var_export($aGateWay, true));
            SessionRedis::push("UserSession:".$token, ['VirIp'=>$aIp['VirtualIp']], [$aGateWay['IpIn']??'']);
            self::log('getVirIpFromDb::---token:'.$token."::VirIpInfo::".var_export(['VirIp'=>$aIp['VirtualIp'],'GateWay'=>$aGateWay['IpIn']??''], true));
            $aDep = explode(",", $Session['DepartIDs']);
            $num = count($aDep)-1;
            $GwVirState = $aGateWay['GwVirState'] ?? 0;
            UserVirtIpOnlineModel::updatePatch(['ID'=>$aIp['ID']], [
                'State'=>1 ,
                'Token'=>$token,
                'AccessTime'=>date('Y-m-d H:i:s'),
                'LastTime'=>date('Y-m-d H:i:s'),
                'OffTime'=>'null',
                'UserID' => $Session['Uuid'] ?? 0,
                'DeviceID' => $Session['DeviceID']?? 0,
                'DevName' => $Session['DevName']?? '',
                'DevInfo' => $Session['DevInfo']?? '',
                'UserName' => $Session['TrueNames']?? '',
                'Account' => $Session['UserName']?? '',
                'DepartID' => $aDep[$num] ?? 0,
                'Mac' => $Session['Mac']??'',
                'LinkTimes' => 0,
                'AccessIp' => $Session['ConnectIp']?? '',
                'UpTraffic' => 0,
                'DownTraffic' => 0,
                'RecyStatus' => isset($aGateWay['RecoveVirIp']) && $GwVirState == 1 ? ($aGateWay['RecoveVirIp'] == 0 ? -1 : ($aGateWay['RecoveVirIp'] == 1 ? 0 : $aGateWay['RecoveHours']??0)) : -2,
                'Model' => 1
            ]);
            return true;
        }
        return false;
    }
    //
    /**
     * 生成新的在线记录
     * @param string $token
     * @param array $Session
     * @param  array $aGateWay
     * @param  array $aVirIpInfo
     * @return true
     * **/
    private static function makeNewVirIp($token, $Session, $aGateWay, $aVirIpInfo)
    {
//        self::log('makeNewVirIp--------------aVirIp-----------'.var_export($aVirIpInfo,true));
        if ($aVirIpInfo != []) {
            $aDep = explode(",", $Session['DepartIDs']);
            $num = count($aDep)-1;
            $aGwId = [0];
            foreach ($aVirIpInfo as $item) {
                $GwVirState = $aGateWay[$item['gwId']]['GwVirState'] ?? 0;
                if (isset($item['disVirIp']) && $item['disVirIp'] == 2) {
                    // 禁止连接,不生成记录
                    continue ;
                }
                $aVirIp = [
                    'Token' => $token,
                    'UserID' => $Session['Uuid'],
                    'DeviceID' => $Session['DeviceID'],
                    'DevName' => $Session['DevName'],
                    'DevInfo' => $Session['DevInfo'],
                    'UserName' => $Session['TrueNames'],
                    'Account' => $Session['UserName'],
                    'DepartID' => $aDep[$num] ?? 0,
//                    'UserType' => $Session['UserType'],
                    'Mac' => $Session['Mac']??'',
                    'AccessIp' => $Session['ConnectIp'],
                    'AccessTime' => date('Y-m-d H:i:s', time()),
                    'LinkTimes' => 0,
                    'UpTraffic' => 0,
                    'DownTraffic' => 0,
                    'PoolID' => $item['ID'] ?? 0,
                    'GwID' => $item['gwId'] ?? 0,
                    'GwIP' => $aGateWay[$item['gwId']]['IpIn'] ?? '',
                    'VirtualIp' => $item['ip'] ?? '',
                    'RecyStatus' => isset($aGateWay[$item['gwId']]['RecoveVirIp']) && $GwVirState == 1 ? ($aGateWay[$item['gwId']]['RecoveVirIp'] == 0 ? -1 : ($aGateWay[$item['gwId']]['RecoveVirIp'] == 1 ? 0 : $aGateWay[$item['gwId']]['RecoveHours']??0)) : -2,
                    'Model' => (isset($item['ip']) && $item['ip'] !== '') ? 1 : 0,
                    'LastTime' => date('Y-m-d H:i:s', time())
                ];
//                self::log('makeNewVirIp--------------aVirIp-----------'.var_export($aVirIp,true));
                UserVirtIpOnlineModel::insert($aVirIp);
            }
        }
        return true;
    }
    /**
     * 根据token回收IP
     * @param $aToken = ['token1','token2,......] 或 'token1','token2,......
     * @param $flag  0: 根据现有规则进行处理, 1:强制立即回收
     * @return true
     * **/
    public static function delVirIpOnLine($param)
    {
        $aToken = $param['Token'] ?? '';
        $virIp = $param['virIp'] ?? '';
        $flag = $param['flag'] ?? '';
        if ($aToken!='' && !is_array($aToken)) {
            $aToken = explode(",", $aToken);
        }
        if (is_array($aToken)) {
            foreach ($aToken as $token) {
                $aParam = [];
                $aParam['Token'] = $token;
                $aParam['virIp'] = $virIp;
                $aParam['flag'] = $flag;
                self::delOneVirIpOnLine($aParam);
            }
        }
        return true;
    }
    /**
     * 根据token回收单个IP
     * @param array $param
     * @return true|false|array
     * **/
    public static function delOneVirIpOnLine($param)
    {
        $Token = $param['Token'] ?? '';
        $virIp = $param['virIp'] ?? '';
        $flag = $param['flag'] ?? ''; // $flag : adminRecoveIp:管理员强制回收, 其他状态: 'Cutoff'
        if (strlen($Token) != 32) {
            return false;
        }

        $aWhere = ['Token'=>$Token];
        if ($virIp != '') {
            $aWhere = ['Token'=>$Token, 'VirtualIp'=>$virIp];
        }

        $aVips = UserVirtIpOnlineModel::getList($aWhere);
        if (empty($aVips)) {
            return true;
        }
        self::log($aVips);
        foreach ($aVips as $aVip) {
            // 当前状态时立即回收或管理员强制回收 或nat模式,没有虚拟Ip

            if (
                'adminRecoveIp' == $flag // 管理员强制回收
                || 'RecoveCmd'== $flag // 超过回收期强制回收
                || $aVip['RecyStatus'] == 0 // 立即回收
                || $aVip['Model'] == 0 // nat模式,立即删除在线记录
//            || ($aVip['RecyStatus'] > 0 && strtotime($aVip['EndTime'])>100000000 && ((strtotime($aVip['EndTime']) + $aVip['RecyStatus'] * 60) <= time()))
            ) {
                $aWhere = ['ID'=>$aVip['ID']];
                UserVirtIpOnlineModel::delete($aWhere);

                $url = 'http://127.0.0.1:50015/v1/virip/del';
                $delData = [
                    [
                        "ID" => $aVip['PoolID'],
                        "gwId" => $aVip['GwID'],
                        "ip" => [$aVip['VirtualIp']]
                    ]
                ];
                $json = json_encode($delData);
                $aRes = ['data' => '{"code":"0","msg":"success"}'];
                if (!empty($aVip['VirtualIp'])) {
                    $aRes = curl($url, 'post', $json);
                }
                unset($aVip['ID']);
                if ($aVip['State'] == 1) {
                    $aVip['OffTime'] = date('Y-m-d H:i:s') ;
                    $aVip['LinkTimes'] = time() - strtotime($aVip['AccessTime']);
                    $aVip['State'] = 0 ;
                    if ($flag != 'RecoveCmd') {
                        // 超时删除时日志已生成,不需要再次生成
                        UserVirtIpHistoryModel::insert($aVip);
                    }
                }
                // 回收虚拟IP后,重置网关虚拟IP状态
                GatewayRedis::setOne($aVip['GwID'], ['noIpPermit'=>0]);
            } else {
                UserVirtIpOnlineModel::updatePatch(['ID'=>$aVip['ID']], ['State'=>0,'OffTime'=>date("Y-m-d H:i:s", time()),'LinkTimes'=>0, 'UpTraffic' => 0,'DownTraffic' => 0]);
                $aVip['LinkTimes'] = time() - strtotime($aVip['AccessTime']);
                unset($aVip['ID']);
                $aVip['State'] = 0 ;
                $aVip['OffTime'] = date('Y-m-d H:i:s');
                UserVirtIpHistoryModel::insert($aVip);
            }
        }
        return  ['code'=>0,'msg'=>'success!'];
    }
}
