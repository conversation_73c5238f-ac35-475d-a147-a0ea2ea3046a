<?php
/**
 * Description: 指纹认证
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: FingerAuth.php 159670 2021-10-22 04:21:05Z duanyc $
 */

$GLOBALS['LANG'][21125] = [
    21125001 => 'The fingerprint device manufacturer does not exist!',
    21125002 => 'Fingerprint user',
    21125003 => 'Authentication failed, user ID or fingerprint template is empty!',
    21125004 => 'Authentication failed, fingerprint authentication server address is empty!',
    21125005 => 'Authentication failed, SOAP call failed, please confirm whether the certificate server address is correct or Unicom!',
    21125006 => 'Authentication failed, the user does not exist in the fingerprint authentication system!',
    21125007 => 'Fingerprint comparison failed!',
    21125008 => 'The fingerprint user does not exist!',
    21125009 => 'Authentication failed, unable to connect with fingerprint server!',
];
