<?php

/**
 * Description: TDepCutNet
 * User: <EMAIL>
 * Date: 2021/05/27 10:32
 * Version: $Id
 */

class DevAswInfoModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevAswInfo';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one'  => 'IP',
        '*'    => '*',
    ];

    /**
     * 获取关联部门的数据.
     * @param int $departID
     * @return array|false|null
     */
    public static function getJoinDepart(int $departID)
    {
        if (empty($departID)) {
            return false;
        }

        self::$data = [];
        $where = "WHERE TDevAswInfo.DepartID = TDepart.DepartID AND TDevAswInfo.DepartID = ".self::setData($departID);
        $sql = "SELECT TDevAswInfo.flag,TDevAswInfo.IsManage,TDepart.IPSegment FROM TDevAswInfo,TDepart {$where} ";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if(isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if(isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
