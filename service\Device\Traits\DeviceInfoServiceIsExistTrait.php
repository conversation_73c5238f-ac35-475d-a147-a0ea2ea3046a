<?php
/**
 * Description: 设备服务判断是否存在代码块 只能DeviceInfoService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: DeviceInfoServiceIsExistTrait.php 172480 2022-03-31 10:05:26Z renchen $
 */

namespace Services\Device\Traits;

use Common\Facades\NACServiceFacade;
use ComputerModel;
use DevAllIPMACModel;
use DeviceServiceProvider;
use GuestSelfApplyModel;
use NoticeServiceProvider;
use Services\Common\Exceptions\AsmException;
use Services\Common\Services\DepartService;
use Services\Common\Services\DeviceOperationService;
use Srv\Nacservice\V1\Device;
use lib_yar;

trait DeviceInfoServiceIsExistTrait
{

    /**
     * 部门信息服务对象.
     *
     * @var DepartService
     */
    public $departService;

    /**
     * 设备id统一管理服务对象.
     *
     * @var DeviceOperationService
     */
    public $deviceOperationService;

    /**
     * @param string $mac
     *
     * @return bool
     */
    protected function isDeviceExistByMac(string $mac): bool
    {
        try {
            if (0 !== $this->params['deviceid'] && $this->params['license'] === $this->params['licenseInfo']['ManageCode']) {
                $res = \DeviceModel::getOneByType($this->params['deviceid'], $this->params['deviceType']);
                $this->writeLog('根据设备ID:' . $this->params['deviceid'] . '设备类型:' . $this->params['deviceType'] . '查询结果为：' . var_export($res, true));
                if ('' !== $res['Mac'] && '' !== $mac && $mac === $res['Mac']) { //发现设备存在
                    if ($res['IP'] === $this->params['linkIP']) {
                        $this->deviceId = $this->params['deviceid'];
                        $this->mac = $mac;
                        $this->writeLog("由设备id、mac判断设备存在：DeviceID=" . $this->deviceId);

                        return true;
                    }
                    $this->devExistButIPChange = 1;
                    $this->writeLog('由设备id、mac判断设备存在,但是设备已切换IP，查到的设备IP为：' . $res['IP'] . ' 现IP为：' . $this->params['linkIP']);

                    return false;
                }

                if ($this->params['fakeHard']) {
                    $res = \ComputerModel::getOneByHard($this->params['fakeHard']);
                    if (!empty($res['DeviceID'])) {
                        $this->deviceId = $res['DeviceID'];
                        $this->mac = $mac;
                        $this->writeLog('伪造hard获得设备[' . $res['DeviceID'] . ']' . $this->params['fakeHard']);

                        return true;
                    }
                }

                $this->writeLog("设备MAC:[{$mac}]不相等且没有查询到伪造hard[{$this->params['fakeHard']}]");

                return false;
            }

            $this->writeLog('可能切换asm服务器:' . var_export($this->params, true));
        } catch (\Exception $e) {
            $this->writeLog('isDeviceExistByMac 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 更新设备信息 zhangkb 20141227
     * 如果是有控件的情况下更新设备信息.
     *
     * @param array $aDevInfo
     *
     * @return bool|mixed
     */
    public function updateDeviceInfo(array $aDevInfo)
    {
        try {
            // ComputerName保持全路径，DeviceName只取最后一段 与服务端AsmSvr保持一致
            if (false !== strpos($aDevInfo['ComputerName'], '\\')) {
                $aDevInfo['DeviceName'] = substr(strstr($aDevInfo['ComputerName'], '\\'), 1);
                $aDevInfo['ComputerName'] = str_replace("\\", "\\\\", $aDevInfo['ComputerName']);
            } else {
                $aDevInfo['DeviceName'] = $aDevInfo['ComputerName'];
            }

            if ((1 === $this->params['newMobile'] || 1 === $this->params['is_mobile'])) {
                $aDevInfo['DeviceName'] = $aDevInfo['ComputerName'];
            }

            $aDevInfoS = $aDevInfo;
            if (empty($aDevInfoS['ProxyServer']) && $aDevInfoS['Ip'] !== $_SERVER['REMOTE_ADDR']) {
                $this->writeLog('web proxyAddr:' . $_SERVER['REMOTE_ADDR'] . ':' . $_SERVER['REMOTE_PORT'] . ' control proxyAddr:' . $aDevInfoS['ProxyServer']);
                $aDevInfoS['ProxyServer'] = $_SERVER['REMOTE_ADDR'] . ':' . $_SERVER['REMOTE_PORT'];
            }
            $firstToINTERNAL = false;

            $now = date('Y-m-d H:i:s');
            if (is_numeric($this->deviceId) && $this->deviceId > 0) {
                $updateData = array(
                    'DeviceID' => $this->deviceId,
                    'IP' => $aDevInfoS['Ip'],
                    'Hard' => $aDevInfoS['DiskId'],
                    'TDevice' => array(
                        'Online' => 1,
                        'ChangeFlags' => 'ass',
                        'ComputerName' => $aDevInfoS['ComputerName'],
                        'LastTime' => $now,
                        'ProxyServer' => $aDevInfoS['ProxyServer']
                    ),
                    'TComputer' => array(
                        'AllIP' => $aDevInfoS['AllIp'],
                        'AllMac' => $aDevInfoS['AllMac'],
                        'GateWay' => $aDevInfoS['GateWay'],
                        'Mask' => $aDevInfoS['Mask'],
                        // 手机新增字段属性 yancheng 2015-03-02
                        'Runmemory' => $aDevInfoS['Runmemory'],
                        'Phonestoragespace' => $aDevInfoS['Phonestoragespace'],
                        'Phoneavailablestorage' => $aDevInfoS['Phoneavailablestorage'],
                        'Sdcardavailablestorage' => $aDevInfoS['Sdcardavailablestorage'],
                        'Imei' => $aDevInfoS['Imei'],
                        'Networksystem' => $aDevInfoS['Networksystem'],
                        'Kernelnumber' => (int)$aDevInfoS['Kernelnumber'],
                        'Roam' => $aDevInfoS['Roam'],
                        'Phonetype' => $aDevInfoS['Phonetype'],
                        'Whetheroot' => $aDevInfoS['Whetheroot'],
                        'Charging' => $aDevInfoS['Charging'],
                        // end
                    ),
                );
                // IPv6 所有IP信息
                if (!empty($aDevInfoS['AllIp_IPv6'])) {
                    $updateData['TComputer']['AllIPv6'] = $aDevInfoS['AllIp_IPv6'];
                }

                if ('' !== $aDevInfoS['Mac']) {
                    $updateData['Mac'] = $aDevInfoS['Mac'];
                }
                if (isset($aDevInfoS['DevBootTime']) && '' !== $aDevInfoS['DevBootTime']) {
                    $updateData['TDevice']['BootTime'] = $aDevInfoS['DevBootTime'];
                }
                if ('' !== $this->params['aswid']) {
                    $updateData['TDevice']['AswID'] = $this->params['aswid'];
                }
                // yanzj 如果是管理员修改的设备类型则不更新
                $result = \DeviceModel::getDevHand($this->deviceId);
                if (1 !== (int)$result['IsHandDevName']) {
                    $updateData['TDevice']['DevName'] = $aDevInfoS['DeviceName'];
                }
                if (1 !== (int)$result['IsHandType']) {
                    if ('1' === $result['IsUserDef']) {
                        $updateData['TDevice']['Type'] = $result['Type'];
                    } else {
                        $updateData['TDevice']['Type'] = $this->params['deviceType'];
                    }
                    $updateData['TDevice']['SubType'] = $this->params['deviceSubType'];
                }
                $isNeedUpdatePolicyVersion = false;
                // 如果没有ascid则用数据库保留的值，不做更新
                if ('' !== $this->params['ascid']) {
                    $updateData['TDevice']['AscID'] = $this->params['ascid'];
                    if ($result['AscID'] !== $updateData['TDevice']['AscID']) {
                        $isNeedUpdatePolicyVersion = true;
                    }
                }
                // IsInNAT为空则不保存
                if ('' !== trim($aDevInfo['IsInNAT'])) {
                    $updateData['TDevice']['IsInNAT'] = $aDevInfoS['IsInNAT'];
                }
                // InstallClient为空则不保存
                if (!empty($aDevInfo['InstallClient'])) {
                    $updateData['TDevice']['InstallClient'] = $aDevInfo['InstallClient'];
                }
                // AgentVersion为空则不保存
                if (!empty($aDevInfoS['AgentVersion'])) {
                    $updateData['TDevice']['AgentVersion'] = $aDevInfoS['AgentVersion'];
                }
                // InternalAgentVersion为空则不保存
                if (!empty($aDevInfoS['InternalAgentVersion'])) {
                    $updateData['TDevice']['InternalAgentVersion'] = $aDevInfoS['InternalAgentVersion'];
                }
                if ('' !== trim($aDevInfo['OS'])) {
                    $updateData['TComputer']['OSName'] = $aDevInfoS['OS'];
                }
                if ('' !== trim($aDevInfo['OSInstallTime'])) {
                    $updateData['TComputer']['OSInstallTime'] = $aDevInfoS['OSInstallTime'];
                }
                if (isset($aDevInfo['IeVersion']) && '' !== trim($aDevInfo['IeVersion'])) {
                    $updateData['TComputer']['IEVersion'] = $aDevInfoS['IeVersion'];
                }
                if ('' !== trim($aDevInfo['CPU'])) {
                    $updateData['TComputer']['CPU'] = $aDevInfoS['CPU'];
                }
                if ('' !== trim($aDevInfo['DiskSize'])) {
                    $updateData['TComputer']['DiskSize'] = $aDevInfoS['DiskSize'];
                }
                if (isset($aDevInfo['PhysicalMac']) && '' !== trim($aDevInfo['PhysicalMac'])) {
                    $updateData['TComputer']['PhysicalMac'] = $aDevInfoS['PhysicalMac'];
                }
                if ('' !== trim($aDevInfo['Memory'])) {
                    $updateData['TComputer']['Memory'] = $aDevInfoS['Memory'];
                }
                if ('' !== trim($this->params['linkIP'])) {
                    $updateData['TComputer']['GateIP'] = $this->params['linkIP'];
                }
                if ('' !== trim($aDevInfo['RealDiskId'])) {
                    $updateData['TComputer']['RealDiskId'] = $aDevInfoS['RealDiskId'];
                }

                $deviceInfo = $this->deviceInfo;
                if ($this->deviceId && !$this->deviceInfo) {
                    $deviceInfo = \DeviceModel::getOne($this->deviceId, 'info');
                }

                $Registered = ComputerModel::getOne($this->deviceId,'info');
                $Registered = $Registered['Registered'] ?? 0;
                if (($deviceInfo['ZtpDevice'] == \DeviceModel::ZTPDEVICE ||
                        ($deviceInfo['ZtpDevice'] == \DeviceModel::UZTPDEVICE && $deviceInfo['RegistIsInternal'] == \DeviceModel::UNKNOWZTPDEVICE))
                    && IS_INTERNAL && $Registered != -1) { //终端首次接入内网
                    $updateData['TComputer']['Registered'] = -1;
                    $updateData['TComputer']['ReRegReason'] = L(21105004);
                    $firstToINTERNAL = true;
                }
                $this->deviceOperationService->updateDevice($updateData);
                if ($isNeedUpdatePolicyVersion) {
                    $this->updatePolicyVersion($this->deviceId, 'Control:AscID');
                }
            }
            if (isset($aDevInfo['AllIp_Ok'], $aDevInfo['AllMac_Ok']) && '' !== $aDevInfo['AllIp_Ok'] && '' !== $aDevInfo['AllMac_Ok']) {
                $aIP = explode(',', $aDevInfo['AllIp_Ok']);
                $aMac = explode(',', $aDevInfo['AllMac_Ok']);
                $allIPMac = [];
                $i = 0;
                \DevAllUseIPMACModel::delete(['DeviceID' => $this->deviceId]);
                foreach ($aIP as $item) {
                    if (empty($aMac[$i])) {
                        $aMac[$i] = MakeMacByIP($item);
                    }
                    // 考虑IPv6场景（双栈），同一个Mac地址同时对应IPv4地址和IPv6地址;bugID=6436
                    if ('0.0.0.0' !== $item && $item !== $aDevInfo['Ip']) {
                        if (empty($aMac[$i])) {
                            $aMac[$i] = MakeMacByIP($item);
                        }
                        \DevAllUseIPMACModel::delete(['IpOrMac' => 1, 'IP' => $item, 'Mac' => $aMac[$i]]);
                        $allIPMac[] = ['DeviceID' => $this->deviceId, 'IP' => $item, 'Mac' => $aMac[$i]];
                        //\DevAllUseIPMACModel::insert(['DeviceID' => $this->deviceId, 'IP' => $item, 'Mac' => $aMac[$i]]);
                    }
                    ++$i;
                }
                \DevAllUseIPMACModel::insertPatch($allIPMac);// 改为批量插入
            }
            // 增加AllMac字段，将所有的mac地址存入数据库TDevAllIPMAC表中
            if (!empty($aDevInfo['AllMac'])) {
                // 分割 MAC 地址并过滤掉空值，验证 MAC 地址格式
                $allMacs = array_filter(array_map('trim', explode(',', $aDevInfo['AllMac'])), static function($value) {
                    return $value !== '' &&  filter_var($value, FILTER_VALIDATE_MAC);
                });
                // 先删除该设备id的所有的mac地址
                \DevAllIPMACModel::delete(['DeviceID' => $this->deviceId]);

                // 批量删除无效的 MAC 地址记录
                if (!empty($allMacs)) {
                    DevAllIPMACModel::delete(['Mac' => ['IN', $allMacs]]);
                }
                // 准备插入的数据
                $allMacData = array_map(function($mac) {
                    return ['DeviceID' => $this->deviceId, 'Mac' => $mac];
                }, $allMacs);

                if (!empty($allMacData)) {
                   DevAllIPMACModel::insertPatch($allMacData); // 批量插入所有的mac地址
                }
            }
            if ('isopen' === $this->params['isopennet'] && $this->deviceId) {
                if ($firstToINTERNAL) {
                    $this->firstToInternalCutOff();
                    $this->writeLog('IP变化，终端首次在内网接入，不放开网络，要求重注册' . $this->deviceId . ' ' . $aDevInfo['Ip']);
                } else {
                    $netStatus = DeviceServiceProvider::ipRangeOpenNet((int)$this->deviceId, $aDevInfo['Ip'], 'updateDeviceInfo');
                    $msg = $netStatus === 1 ? 'IP变动,放开网络:' : 'IP变动,未放开网络不处理';
                    $this->writeLog($msg . $this->deviceId . ' ' . $aDevInfo['Ip']);
                }
            }elseif($firstToINTERNAL && $this->params['isopennet'] !=='false'){
                $this->firstToInternalCutOff();
            }
            return true;
        } catch (\Exception $e) {
            $this->writeLog('updateDeviceInfo 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 终端首次内网入网要求断网
     * @return void
     */
    public function firstToInternalCutOff(): void
    {
        $cutoffConfig = [
            "isolation" => 0,
            'is_trust_cut_off' => 0,
            'is_temp_force_cutoff' => 0,
            'is_force_cutoff' => 1,
            'is_isolated_access' => 1,
            'is_send_cutoff_message' => IS_MOBILE ?  0 : 1,
            'show_user_one_message' => L(21105004),
            'source' => 1,
            'msg' => L(21105004),
            'remark' => 'FirstToInternal',
        ];

        //删除在线表之前通知MSEP
        NoticeServiceProvider::sendSyncStatus($this->deviceId,'deviceToUser');
        NACServiceFacade::cutoff('WebCutoff:cutoff_net', [['device_id' => $this->deviceId]], $cutoffConfig);
        // 清除待审核级联
        GuestSelfApplyModel::updatePatch(['Status' => 0, 'DeviceID' => $this->deviceId], ['Status' => -1, 'Reason' => '终端强制下线']);

    }

    /**
     * 通过IP获取Mac.
     *
     * @param string $ip
     *
     * @return bool|mixed
     * @throws \Exception
     */
    protected function getMacByIp(string $ip, $config = [])
    {
        try {
            if ('' === trim($ip)) {
                return false;
            }
            if (6 !== $this->params['ipVer']) {
                // 1.第一步根据ascid查询MAC
                $res = array();
                $UrlAscid = $this->params['UrlAscid'];
                $this->writeLog('获得URLASCID=' . $UrlAscid);
                if ('undefined' === $UrlAscid || '' === trim($UrlAscid)) {
                    $res['LinkIP'] = '127.0.0.1';
                } else {
                    $res = \DevASCInfoModel::getSingle(['AscID' => $UrlAscid, 'column' => 'one']);
                    $this->writeLog('查询ASC IP=[' . $res['LinkIP'] . ']');
                }

                if ('' !== $res['LinkIP']) {
                    $bufSend = "<SocketTelnetCmd><CmdName>GetDevInfo</CmdName><Ip>{$ip}</Ip></SocketTelnetCmd>";
                    $buffer = '';
                    /*                      return
                      <SocketTelnetCmd>
                      <Ip>%s</Ip>
                      <Mac>%s</Mac>
                      <Switch>%s</Switch>
                      <SwitchPort>%s</SwitchPort>
                      <VlanId>%s</VlanId>
                      <IsHub>%d</IsHub>
                      </SocketTelnetCmd> */
                    // 由于MVG修改了获取mac的流程，原来1秒钟的情况很可能获取不到mac，调整到5秒钟
                    UdpSendAndRecv($res['LinkIP'], 36540, $bufSend, 5, $buffer);
                    preg_match('/<Mac>(.*)<\/Mac>/i', $buffer, $out);
                    $this->writeLog('调用：GetDevInfo ' . $bufSend . '返回' . var_export($out, true));
                    if (isset($out[1]) && '' !== trim($out[1])) {
                        $this->writeLog('通过soket GetDevInfo 获得MAC[' . $out[1] . ']');

                        return $out[1];
                    }
                    $this->writeLog('通过soket GetDevInfo 没有获取到MAC');

                    // wangjian 20180716
                    // 尝试从portal认证的URL参数中获取得到,确认参数是个MAC
                    if (isset(\lib_request::$requests['wlanusermac'])
                        && preg_match('/^([0-9A-Fa-f]{2}:){5}([0-9A-Fa-f]{2})$/', $this->params['wlanusermac'])
                    ) {
                        $this->writeLog('通过portal认证url参数获取到MAC[' . $this->params['wlanusermac'] . ']');

                        return $this->params['wlanusermac'];
                    }
                }

                // 是否只尝试GetDevInfo
                if (($config['isOnlyGetDevInfo'] ?? false) === true) {
                    return false;
                }

                //2.第二步从redis查询MAC
                // 1).通过IP查询相同IP的设备ID
                $devIds = \DeviceModel::getSingle(['IP' => $ip, 'column' => 'deviceIds']);
                $this->writeLog("具有相同IP的设备ID:" . $devIds['DevID'] . ' ;IP:' . $ip);
                $DevIDStr = $devIds['DevID'];
                // 2).从redis中获取lasttime,取最新lasttime的条目的设备ID
                $lastTimeDevIDPairs = [];
                $DevIDArr = explode(',', $DevIDStr);
                if (count($DevIDArr) > 1 && IS_INTERNAL) {
                    foreach ($DevIDArr as $DevID) {
                        $lastTime = strtotime(cache_get_info("DeviceID_", $DevID, 'TDevice.LastTime'));
                        $lastTimeDevIDPairs[$lastTime] = $DevID;
                    }
                    asort($lastTimeDevIDPairs);// 数组按键值从小到大排序
                    $this->writeLog("LastTime&DeviceID关系数组:" . var_export($lastTimeDevIDPairs, true));
                    $deviceID = end($lastTimeDevIDPairs);//取时间戳最大的设备ID
                    // 3).返回取到的设备ID的MAC
                    $deviceInfo = \DeviceModel::getSingle(['DeviceID' => $deviceID, 'column' => 'mac']);
                    if (isset($deviceInfo['Mac']) && $deviceInfo['Mac']) {
                        $this->writeLog("redis查询到MAC：[" . $deviceInfo["Mac"] . "]");
                        $this->deviceId = $deviceInfo["DeviceID"];
                        return $deviceInfo['Mac'];
                    }
                }

                //3.第三步从数据库查询MAC,从TDevice表中取LastTime为24小时内的最新的一条记录
                $lastDaytime = date('Y-m-d H:i:s', strtotime('-1 day'));
                $deviceInfo = \DeviceModel::getSingle(['IP' => $ip, 'column' => 'mac'], 'LastTime DESC');
                if (isset($deviceInfo['Mac']) && $deviceInfo['Mac'] && IS_INTERNAL) {
                    if ($deviceInfo['LastTime'] > $lastDaytime) {
                        // 如果开启了portal准入，只能取未安装小助手的（为防止该准入下，短暂断线准入认为能入网交换机给重定向问题，装了小助手的获取入网状态时，如果网络是放开的则自动发起portal认证）
                        // 为解决：urtacker 47143 终端A使用ip1portal认证通过之后，终端2将ip修改为ip1接入portal管控的网络，可以访问网络 问题
                        $enablePortal = is_file(PATH_ETC . 'portalCnf.ini') && get_ini_info(PATH_ETC . 'portalCnf.ini', 'EnablePortal');
                        $this->writeLog('根据IP:' . $ip . ' 在数据库查询到MAC：[' . $deviceInfo['Mac'] . ']' . ' InstallClient: ' . $deviceInfo['InstallClient'] . ' enablePortal: ' . var_export($enablePortal, true));
                        if ($enablePortal && (int)$deviceInfo['InstallClient'] === 0) {
                            $this->deviceId = $deviceInfo["DeviceID"];
                            return $deviceInfo['Mac'];
                        }

                        if (!$enablePortal) {
                            $this->deviceId = $deviceInfo["DeviceID"];
                            return $deviceInfo['Mac'];
                        }
                    }
                    $this->writeLog('根据IP:' . $ip . ' 在数据库查询到MAC：[' . $deviceInfo['Mac'] . ']最后在线时间为：' . $deviceInfo['LastTime'] . '已超过24小时放弃使用');
                }

                // 3.第四步通过ip伪造mac
                $fakeMac = MakeMacByIP($ip, 4);
                $this->writeLog('通过IP：[' . $ip . ']伪造MAC：' . $fakeMac);

                // 去数据库中查一下fakeMake有没有对应的记录，有就修改全局deviceId，防止后续重复插入
                if (!IS_INTERNAL && !empty($this->params['fakeHard'])) {
                    $res = \ComputerModel::getOneByHard($this->params['fakeHard']);
                } else {
                    $res = \DeviceModel::getOneByMac($fakeMac);
                }
                if (isset($res['DeviceID'])) {
                    $this->writeLog('伪造MAC在数据库查询到设备ID为：[' . $res['DeviceID'] . ']');
                    $this->deviceId = $res['DeviceID'];
                }

                return $fakeMac;
            }

            $ipv6info = \DevIPv6AddressModel::getSingle(['ipv6_address' => $ip, 'column' => 'one']);
            if (isset($ipv6info['device_id']) && is_numeric($ipv6info['device_id'])) {
                $this->deviceId = $ipv6info['device_id'];
                $this->writeLog('通过IPV6地址查找设备：' . var_export($ipv6info, true));

                $deviceInfo = \DeviceModel::getOne($ipv6info['device_id'], 'one');
                if (isset($deviceInfo['Mac']) && '' !== $deviceInfo['Mac']) {
                    return $deviceInfo['Mac'];
                }
            }
            $this->writeLog('通过IPV6地址没有找到设备');

            // 通过ipv6地址伪造mac插入设备
            //fd86:139:7311:2020:1840:7535:2272:243';
            $this->mac = $fakeMac = MakeMacByIP($this->params['linkIP'], 6);
            $this->writeLog('通过IPV6地址:' . $this->params['linkIP'] . '伪造Mac:' . $this->mac);

            // 没找到对应设备，发送删除IPv6设备的消息
            $xml = '<?xml version="1.0" encoding="gbk"?>
                <ASM>
                    <TradeCode>DelIPv6AddrForDevice</TradeCode>
                    <IPv6Addrs>
                        <DelAllIPv6Addr>
                            <DeviceIDList>' . $this->deviceId . '</DeviceIDList>
                        </DelAllIPv6Addr>
                    </IPv6Addrs>
                </ASM>';
            cutil_exec_no_wait("asmsvr_internal_trade  -c '{$xml}'");
            $this->writeLog('发送IPv6xml报文:' . $xml);

            return $fakeMac;
        } catch (\Exception $e) {
            $this->writeLog('getMacByIp 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 发送ipv6报文.
     *
     * @return bool|mixed
     */
    protected function sendIpv6ControlXml()
    {
        try {
            $ipv6_enable = get_ini_info(PATH_ETC . 'asm/asc/etc/tbridge_private.ini', 'ipv6_bridge_enable');
            if ((int)$ipv6_enable === 1) {
                $ipv6addrs = GetSubStr($this->params['dev_xml'], '<IPv6Addrs>', '</IPv6Addrs>');
                $ipv6data['IPv6Addrs'] = '<IPv6Addrs>' . $ipv6addrs . '</IPv6Addrs>';
                $ipv6data['DeviceID'] = $this->deviceId;
                $ipv6data['TradeCode'] = 'AddIPv6AddrForDevice';
                $ipv6xml = setIPv6Xml($ipv6data);
                cutil_exec_no_wait("asmsvr_internal_trade  -c '{$ipv6xml}'");
                $this->writeLog('控件发送srv ipv6xml报文' . $ipv6xml);
            }
            return true;
        } catch (\Exception $e) {
            $this->writeLog('sendIpv6ControlXml 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 通过ip获取mac方式插入设备信息.
     *
     * @param $ip
     * @param $mac
     * @param $hard
     *
     * @param $devBootTime
     * @return bool
     */
    protected function insertDeviceByIpGetMac($ip, $mac, $hard, $devBootTime = ''): bool
    {
        try {
            // 读取是否根据连接IP取部门信息
            $dictConf = \DictModel::getAll('ShowAllDevice');
            $departId = $this->departService->getDepartByIp('1' === $dictConf['priorityip'] ? $this->params['linkIP'] : $ip);
            /*if (false !== strpos($ip, ':')) {
                $mac = MakeMacByIP($ip, 6); // 多余无用的逻辑
                $ip = '0.0.0.0';  // 这里设置为全0，第二次时会更新为IPv6
            }*/
            $now = date('Y-m-d H:i:s');
            $insertData = array(
                'IP' => $ip,
                'Mac' => $mac,
                'Hard' => ($hard !== '' && $hard !== $mac) ? $hard : $mac,
                'TDevice' => array(
                    'BootTime' => $devBootTime ?: $now,
                    'Online' => 1,
                    'LastTime' => $now,
                    'AscID' => !empty($this->params['ascid']) ? $this->params['ascid'] : '11:11:11:11:11:11',
                    'AswID' => $this->params['aswid'],
                    'Type' => $this->params['deviceType'],
                    'SubType' => $this->params['deviceSubType'],
                    'IsInternal' => (int)IS_INTERNAL,
                    'ZtpDevice'=> (int)!IS_INTERNAL,
                    'Remark' => 'web',
                    'DepartId' => $departId,
                ),
                'TComputer' => array(
                    'GateIP' => $this->params['linkIP'],
                    'Registered' => $this->params['Registered'],
                    'OSName' => $this->params['OS'],
                    'OSInstallTime' => $this->params['OSInstallTime'],
                    'IEVersion' => $this->params['browser']
                ),
            );
            $insertRes = $this->deviceOperationService->insertDevice($insertData);

            if ($insertRes['code'] !== 1) {
                throw new AsmException("由IP:[{$ip}]GetMAC：[{$mac}]插入设备失败," . $insertRes['message']);
            }
            $this->deviceId = $insertRes['data']['DeviceID'];

            return true;
        } catch (\Exception $e) {
            $this->writeLog('insertDeviceByIpGetMac 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 更新设备类型.
     *
     * @param int $deviceId
     * @param int $deviceType
     * @param string $flag 更新方式，cookie等
     *
     * @return bool
     */
    protected function updateDeviceType(int $deviceId, int $deviceType, $flag = ''): bool
    {
        try {
            // 减少数据库查询
            if (isset($this->deviceInfo['DeviceID']) && (int)$this->deviceInfo['DeviceID'] === $deviceId) {
                $result = $this->deviceInfo;
            } else {
                $result = \DeviceModel::getDevHand($deviceId);
            }
            if (isset($result['IsHandType']) && 1 !== (int)$result['IsHandType'] && '1' !== $result['IsUserDef']) {
                \DeviceModel::update($deviceId, ['Type' => $deviceType, 'SubType' => $this->params['deviceSubType']], $result);
                $this->writeLog('入网流程更新设备类型' . $flag);
            }
        } catch (\Exception $e) {
            $this->writeLog('updateDeviceType 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');

            return false;
        }

        return true;
    }

    /**
     * 比较小助手的版本是否合服务器版本一致
     * @param string $version
     * @return bool|false
     * @throws \Exception
     */
    protected function checkAgentVersion(string $version): bool
    {
        if (empty($version)) {
            return false;
        }
        $systemVersion = get_ini_info(PATH_ETC . 'deveth.ini.noback', 'engine');
        // 如果获取服务器版本失败，则认为版本校验成功，否则会误判
        if (empty($systemVersion)) {
            return true;
        }
        // $version : V6.0.6039.3746
        // $systemVersion : 6.0.6039.3746
        // 国产服务器对应的销售许可证还是5.2 修改成只取后面两个进行比较
        $versionLower = strtolower($version);
        $systemVersionLower = strtolower($systemVersion);
        $systemVersionArr = explode('.', $systemVersionLower);
        if (!is_array($systemVersionArr)) {
            return true;
        }
        $systemVersionArr = array_slice($systemVersionArr, 0, 4);
        $lastTwoVersion = implode('.', array_slice($systemVersionArr, -2));
        if (strpos($versionLower, $lastTwoVersion) !== false) {
            return true;
        }

        if (OS_TYPE === OSTYPE_IOS && $version === implode('.', array_slice($systemVersionArr, 0, 4))) {
            return true;
        }
        return false;
    }
}
