<?php
/**
 * Description: 极速入网
 * User: <EMAIL>
 * Date: 2023/12/18
 * Version: $Id: PermitAuthService.php 169023 2022-02-18 01:08:20Z huyf $
 */

namespace Services\Auth\Services;

use DeviceServiceProvider;
use Exception;
use PermitAuthServiceProvider;
use Services\Auth\Interfaces\AuthServiceInterface;
use Services\Device\Services\DeviceStatusService;

class PermitAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'Permit';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['QrCode'] = request('qrCode', 'request');
        $this->params['time'] = request('_', 'request', 0, 'int');
        // 授权未到期且注册数未达到上限
        $deviceRegistrable = DeviceServiceProvider::deviceRegistrable($this->deviceId);
        if ($deviceRegistrable === false) {
            T(21139006);
        }
        // 被扫码设备是否在线
        $deviceStatusService = new DeviceStatusService();
        $isOnline = $deviceStatusService->isOnlineDevice($this->deviceId);
        if (!$isOnline) {
            T(21139004);
        }
        return $this->params;
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        if (!isset($data['bindInfo'])) {
            T(21100002);
        }
        $bindInfo = $data['bindInfo'];
        $SceneService = DeviceServiceProvider::initDeviceSceneService();
        $sceneInfo = $SceneService->getSceneInfoById((int)$bindInfo['sceneID']);
        if (!$sceneInfo['IsAuthorizedAccess']) {
            T(21138009);
        }
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);
        $this->writeLog(var_export([$this->deviceId, $bindInfo, $userInfo], true));
        // 建立绑定关系
        PermitAuthServiceProvider::bindScanCodeDevice($this->deviceId, $bindInfo['roleID'], $userInfo['ID'], $bindInfo['deviceId']);
        cache_set_info('qrcode', dataEncrypt($this->params['QrCode']), ['status' => true, 'time' => time()], 300);

        // 根据主设备的场景信息更新授权设备的场景信息
        $SceneService->updateDeviceScene($this->deviceId, (int)$bindInfo['sceneID'], '');
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'QrCode', 'time'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'QrCode', 'time'];
    }
}
