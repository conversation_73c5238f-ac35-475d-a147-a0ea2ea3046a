<?php

/**
 * Description: 设备TComputer表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: ComputerModel.php 162715 2021-11-24 04:43:19Z duanyc $
 */

class ComputerModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TComputer';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        'one'  => 'ID,DeviceID',
        'name' => 'OSName',
        'ip'   => 'GateIP',
        'roam' => 'RoamFlag',
        'info' => 'GateIP, RoamFlag, Registered, RegTime, ReRegReason, OSName, IEVersion',
        '*'    => '*',
    ];

    /**
     * 单条
     *
     * @param string $Hard
     *
     * @return mixed
     */
    public static function getOneByHard(string $Hard)
    {
        if (empty($Hard)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns['one'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Hard = ".self::setData($Hard);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 漫游设备发现dasm已删除原设备时，漫游设备需重新注册审核
     * @param $deviceID
     * @throws Exception
     */
    public static function changeRoamToLocal($deviceID): void
    {
        if (get_server_type() === 'dasc') {
            self::updatePatch(['DeviceID' => $deviceID], ['Registered' => -2]);

            DeviceModel::DAscRoamAsmDevice($deviceID);
        }
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['BrowserFingerprint'])) {
            $where .= "AND BrowserFingerprint = ".self::setData($cond['BrowserFingerprint']);
        }

        if (isset($cond['NotDAscID'])) {
            $where .= "AND DAscID != ".self::setData($cond['NotDAscID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
