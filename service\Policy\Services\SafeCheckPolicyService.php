<?php
/**
 * Description: 零信任安检策略项服务
 * User: <EMAIL>
 * Date: 2022/05/12 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class SafeCheckPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *
     * @return bool
     * @throws \Exception
     */
    public function check(): bool
    {
        // 未安装客户端且不要求安装客户端，则不检查客户端策略
        if (empty($this->device['InstallClient']) && empty($this->params['InstallClient'])) {
            return true;
        }
        if (empty($this->params['DeviceID']) || empty($this->params['PolicyID'])) {
            return false;
        }
        $device = \RelationComputerModel::getOne($this->params['DeviceID'], 'safecheck');
        if (!empty($device)) {
            // 安检通过
            if ($device['CheckResult'] === 'success') {
                return true;
            }
            // 非关键安检项不通过，但修复未到期
            if ($device['CheckResult'] === 'fault') {
                //获取对应的入网场景策略ID
                $policyID = $this->params['session']['PolicyID'] ?? 0;
                if (empty($policyID)) {
                    return true;
                }
                // 存在修复期限
                $result = false;
                $policyInfo = \NacPolicyListModel::getOne($policyID, 'policy');
                $xml = new \xml_safe_check_policy($policyInfo['PolicyBody']); //解析规范
                $policyData = $xml->parseXml();
                $RepairInterval = $policyData['RepairConf'];
                if ($RepairInterval > 0) {
                    // 存在修复期限
                    $lastFaultTime = strtotime(substr($device['LastFaultTime'], 0, 10));
                    $repairTime = strtotime("+" . $RepairInterval . " day", $lastFaultTime);
                    $remainDate = ($repairTime - strtotime(func_time_getNow("Y-m-d")) ) / 86400;

                    if (in_array($device['CheckResult'], array("success", "false"))) {
                        if ($device['CheckResult'] === 'false' && $remainDate <= 0) {
                            $result = false;
                        } else {
                            $result = true;
                        }
                    } else {
                        if ($device['LastFaultTime'] === DEFAULT_TIME) {
                            $result = true;
                        } else {
                            if ($remainDate > 0) {
                                $result = true;
                            }
                        }
                    }
                } else {
                    $result = true;
                }
                return $result;
            }
        }

        return false;
    }
}