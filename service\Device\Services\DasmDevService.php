<?php
/**
 * Description: 分布式场景下，Dasm上获取数据
 * User: renchen
 * Date: 2021/8/9 10:39
 * Version: $Id: DasmDevService.php 172852 2022-04-07 13:12:47Z renchen $.
 */

namespace Services\Device\Services;

use Services\Common\Services\CommonService;
use Services\Common\Services\DeviceOperationService;
use Services\Device\Traits\DeviceInfoServiceRoamTrait;

class DasmDevService extends CommonService
{
    public function __construct()
    {
        parent::__construct();
        $this->logFileName = 'get_dasm_dev_info';
    }

    // 漫游相关代码块
    use DeviceInfoServiceRoamTrait;

    /**
     * 获取DASM上的设备数据，用于设备漫游场景.
     * @param array $params
     * @return array
     */
    public function getDevInfoFromDasm(array $params): array
    {
        try {
            $this->writeLog('所有入参：'.var_export($params, true));

            $devType = get_server_type();
            if ('dasm' !== $devType) {
                T(21100006, ['message' => 'This type of device cannot be accessed']);
            }

            // 通过小助手上报的信息查找设备是否存在
            $getDeviceIDRes = \DeviceModel::getDeviceID($params);
            $resDeviceID = $getDeviceIDRes['DeviceID'] ?? 0;
            if (!$resDeviceID) {
                $this->writeLog('通过小助手上报的设备信息找不到设备，相关参数：' . var_export($params, true));
                // 基于浏览器指纹的漫游
                if (isset($params['BrowserFingerprint']) && \strlen($params['BrowserFingerprint']) > 10) {
                    $res = \ComputerModel::getSingle([
                        'BrowserFingerprint' => $params['BrowserFingerprint'],
                        'NotDAscID' => '11:11:11:11:11:11',
                        'column' => 'one'
                    ]);
                    if (!isset($res['DeviceID'])) {
                        $this->writeLog('通过浏览器指纹找不到设备，相关参数：' . var_export($params, true));
                    } else {
                        $resDeviceID = $res['DeviceID'];
                    }
                }
            }
            // 所有途径都找不到对应的设备
            if (!$resDeviceID) {
                T(21103006);
            }
            // 来宾设备和未注册不漫游
            $devInfo = \DeviceModel::getJoinComputer($resDeviceID, 'computer');
            if ((int)$devInfo['IsGuestDevice'] === 1 || (int)$devInfo['Registered'] !== 1) {
                $this->writeLog('来宾设备和未注册设备不支持漫游：' . var_export($devInfo, true));
                T(21103006);
            }

            $this->return['data'] = \DeviceModel::getDeviceAllInfo($resDeviceID, 'device');
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, var_export($params, true));
        }

        return $this->return;
    }

    /**
     * 漫游设备插入本地
     * 用于分布式场景下，asmsvr等模块插入设备时，判断当前设备不存在本dasc，从dasm漫游设备数据插入本地
     * @param array $params
     * @return array
     * @throws \Exception
     */
    public function roamDevInsertToLocal($params = []): array
    {
        $dasmData = $this->getDevFromDasm($params);
        $this->writeLog("通过向ASM询问，漫游设备至本DASC，本DASC返回：" . json_encode($dasmData));
        if (empty($dasmData)) {
            T(21103006);
        }
        $returnData = ['status' => 'n','data' => (int)$dasmData];
        if (is_numeric($dasmData)) {
            // 设备存在 ①被别的DASC管理了 ②漫游设备已存在本地  不插入新的设备
            $returnData['status'] = 's';
        }

        if (is_array($dasmData) && !empty($dasmData)) {
            $insertDeviceRes = \DeviceModel::insertIntoTable($dasmData);
            if ($insertDeviceRes) {
                $returnData = ['status' => 'y','data' => (int)$insertDeviceRes];
            }
        }

        if ($returnData['status'] !== 'n') {
            $this->accessRoamDevice($returnData['data'], $params);
        }

        return  $returnData;
    }

    /**
     * 检查指纹是否违规.
     *
     * @param array $params
     * @return array
     */
    public function checkFingerIllegal($params = []): array
    {
        try {
            $deviceID = $this->getDevOrigID($params);
            $this->return['data']['count'] = \DeviceModel::getCount(['DeviceID' => $deviceID]);
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, json_encode($params, JSON_UNESCAPED_UNICODE));
        }

        return $this->return;
    }

    /**
     * 获取USB设备信息.
     *
     * @param array $params
     * @return array
     */
    public function getUsbInfoFromDasm($params = []):array
    {
        try {
            $UsbID = $params['UsbID'] ?? '';
            $count = \UAMDeviceListModel::getCount(['UsbID' => $UsbID]);
            if ($count > 0) {
                $this->return['data'] = \UAMDeviceListModel::getAllUsbInfo($UsbID);
            } else {
                // 设备不存在
                T(21103006);
            }
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, json_encode($params, JSON_UNESCAPED_UNICODE));
        }

        return $this->return;
    }

    /**
     * 从DASM上获取交换机（网络）设备的信息.
     *
     * @param array $params
     *
     * @return array
     */
    public function getSwitchInfoFromDasm($params = []):array
    {
        try {
            $res = \SwitchModel::getSwitchJoinDeviceByIP($params['SwitchIP']);
            if (isset($res['DeviceID']) && 0 !== (int) $res['DeviceID']) {
                $this->return['data'] = \DeviceModel::getDeviceAllInfo($res['DeviceID'], 'switch');
            }
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, json_encode($params));
        }

        return $this->return;
    }

    /**
     * @param array $params
     * @return array
     */
    public function importDevFromDasm(array $params = []): array
    {
        try {
            if (empty($params)) {
                T(21100002);
            }
            if (get_server_type() !== 'dasc') {
                T(21103015);
            }
            // 检查授权点数
            if (!\SystemServiceProvider::isRegistrable()) {
                T(21139006);
            }
            $deviceOperationService = new DeviceOperationService();
            $insertRes = $deviceOperationService->insertDevice($params);
            if ($insertRes['code'] !== 1) {
                T(21103013);
            }
            $deviceId = $insertRes['data']['DeviceID'];
            if (!empty($params['TDeviceExpand'])) {
                $params['TDeviceExpand']['DeviceID'] = $deviceId;
                \DeviceExpandModel::insert($params['TDeviceExpand']);
            }
        } catch (\Exception $e) {
            $this->recordErrorMessage($e, json_encode($params));
        }

        return $this->return;
    }

    /**
     * 获取DASM上的设备ID
     * @param array $params
     *
     * @return int|mixed
     */
    public function getDevOrigID($params = [])
    {
        $AscID = $params['AscID'] ?? '';
        $deviceID = $params['deviceID'] ?? 0;
        $roamFlag = $params['roamFlag'] ?? 0;

        if (1 === (int) $roamFlag) {
            return $deviceID;
        }

        if (!$AscID || !$deviceID) {
            return false;
        }

        $result = \DeviceModel::getSingle(['DAscID' => $AscID, 'OrigID' => $deviceID, 'column' => 'one']);

        return $result['DeviceID'] ?? 0;
    }
}
