<?php

/**
 * Description: 二维码操作
 * User: <EMAIL>
 * Date: 2022/07/01 10:32
 * Version: $Id$
 */
!defined('IN_INIT') && exit('Access Denied');

include_once   PATH_ROOT.'/library/phpqrcode/qrlib.php';

class lib_qrcode
{
    /**
     * 生成二维码
     *
     * @param $text
     * @param bool $outfile
     * @param int $level
     * @param int $size
     * @param int $margin
     * @param bool $saveandprint
     *
     * @return bool|string
     */
    public static function png($text, $outfile = false, $level = QR_ECLEVEL_L, $size = 3, $margin = 4, $saveandprint=false)
    {
        if (empty($text)) {
            return;
        }

        QRcode::png($text, $outfile, $level, $size, $margin);
        exit();
    }
}
