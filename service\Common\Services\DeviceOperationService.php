<?php
/**
 * Description: 设备操作公共类（包括查询，更新，插入）
 * User: renchen
 * Date: 2020/7/15 11:07
 * Version: $Id: DeviceOperationService.php 157665 2021-09-24 08:15:51Z duanyc $.
 */

namespace Services\Common\Services;

use Common\Facades\EventBrokerFacade;
use Exception;
use Services\Common\Interfaces\DeviceOperationInterface;

class DeviceOperationService extends CommonService implements DeviceOperationInterface
{
    /**
     * @var string appid常量，跟服务端约定好的不能修改
     */
    public const APP_ID = 'WEB';

    /**
     * @var string API版本常量，尽量跟服务端保持一致
     */
    public const API_VERSION = 'V1.0';

    /**
     * @var int 超时时间 单位毫秒
     */
    public const TIMEOUT = 5000;

    /**
     * 交易code.
     *
     * @var string
     */
    public $tradeCode;

    /**
     * 发送的交易报文.
     *
     * @var string
     */
    public $sendXml = '';

    /**
     * 接收到的报文.
     *
     * @var string
     */
    public $receiveXml = '';

    /**
     * xml 公共报文头.
     *
     * @var string
     */
    public $xmlHeader;

    /**
     * DeviceOperationService constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->logFileName = 'device_operation';
        $this->xmlHeader = '<?xml version="1.0" encoding="gbk"?><ASM Version="5.2">';
        $this->xmlHeader .= '<APIVersion>'.self::API_VERSION.'</APIVersion><AppID>'.self::APP_ID.'</AppID>';
        $this->xmlHeader .= '<TimeOut>'.self::TIMEOUT.'</TimeOut>';
    }

    /**
     * 根据Hard,Mac,IP获取设备id.
     *
     * @param string $hard
     * @param string $mac
     * @param string $ip
     *
     * @throws Exception
     *
     * @return array
     */
    public function getDeviceID(string $hard, string $mac, string $ip):array
    {
        try {
            $this->tradeCode = 'DevSvcAgent_QueryDevID';
            if (!$mac && !$ip) {
                T(21100002);
            }
            if ($ip && !filter_var($ip, FILTER_VALIDATE_IP)) {
                T(21103002);
            }
            if ($mac && !IsStrHaveMac($mac)) {
                T(21103003);
            }
            $this->setGetDeviceIDXml($hard, $mac, $ip);
            $this->return['data'] = $this->sendXml();
        } catch (Exception $e) {
            $this->message = $e->getMessage();
            $this->code = $e->getCode();
            $this->recordErrorMessage($e, '发送的报文：'.$this->sendXml.'##接收的报文：'.$this->receiveXml);
        }

        return $this->return;
    }

    /**
     * 更新设备信息（IP,Mac,Hard）.
     *
     * @param array $data
     * IP,Mac,Hard 通过统一接口更新，其他信息在service服务中更新
     * TDevice表示更新该表的信息，TComputer表示更新TComputer的信息
     * $data = array(
     *  'DeviceID' => 1,
     *  'IP' => '************',
     *  'Mac' => 'xxxx',
     *  'TDevice' => array(
     *      'Type' => 101,
     *  ),
     * 'TComputer' => array(
     *      'Registered' => 1
     * )
     * );
     * @throws Exception
     *
     * @return array
     */
    public function updateDevice(array $data):array
    {
        try {
            $this->tradeCode = 'DevSvcAgent_UpdateDevByID';
            if (!isset($data['DeviceID']) || !is_numeric($data['DeviceID'])) {
                T(21100002);
            }
            // 当IP，MAC为空时，发送报文给设备id统一服务，会提示报文格式错误
            // 修改为：当IP,MAC为空时不发送报文，更新其他字段信息 update by renchen 2021-02-18
            if ((isset($data['IP']) && $data['IP'])
                || (isset($data['Mac']) && $data['Mac'])
                || (isset($data['Hard']) && $data['Hard'])
            ) {
                $this->setUpdateDeviceXml($data);
                $this->return['data'] = $this->sendXml();
            } else {
                $this->writeLog('没有更新到IP,Mac,Hard不发送报文，更新其他字段信息,相关数据:'.var_export($data, true), 'INFO');
            }

            $this->updateDeviceAndComputer($data);
        } catch (Exception $e) {
            $this->message = $e->getMessage();
            $this->code = $e->getCode();
            $this->recordErrorMessage($e, '发送的报文：'.$this->sendXml.'##接收的报文：'.$this->receiveXml);
        }

        return $this->return;
    }

    /**
     * 插入设备.
     *
     * @param array $data
     *
     * @throws Exception
     *
     * @return array
     */
    public function insertDevice(array $data):array
    {
        try {
            $this->code = 1;
            $this->tradeCode = 'DevSvcAgent_InsertDev';
            if (!isset($data['Mac'], $data['Hard'], $data['IP'])) {
                T(21100002);
            }
            if (!filter_var($data['IP'], FILTER_VALIDATE_IP)) {
                T(21103002);
            }
            if (!IsStrHaveMac($data['Mac'])) {
                T(21103003);
            }
            $this->setInsertDeviceXml($data);
            $this->return['data'] = $this->sendXml();
            $data['DeviceID'] = $this->return['data']['DeviceID'];
            $this->updateDeviceAndComputer($data);
        } catch (Exception $e) {
            $this->message = $e->getMessage();
            $this->code = $e->getCode();
            $this->recordErrorMessage($e, '发送的报文：'.$this->sendXml.'##接收的报文：'.$this->receiveXml);
        }

        return $this->return;
    }

    /**
     * 更新设备缓存信息.
     * @param array $data
     * @return bool
     */
    public function updateDeviceCacheAndLastTime(array $data):bool
    {
        if (!isset($data['DeviceID']) || !$data['DeviceID']) {
            return false;
        }
        $time = time();
        $date = date('Y-m-d H:i:s',$time);
        // 原来设备在线状态(用缓存中的数据，缓存中没有认为不在线)
        $oldOnline = cache_get_info('DeviceID_',$data['DeviceID'],'TDevice.Online');
        //只要访问页面，就更新Redis数据库在线、在线时间，不关心是否设置成功
        $updateInfo = [
            'TDevice.LastTime' => $date,
            'TDevice.WebAccessLastOnlineTime' => $date,
            'LastCacheHotTime' =>  $time % (3600 * 24 * 365 * 5)
        ];
        if(isset($data['TNacOnLineDevice.LastHeartTime'])){
            $updateInfo['TNacOnLineDevice.LastHeartTime']=$data['TNacOnLineDevice.LastHeartTime'];
        }
        if(isset($data['TDevice.LastChangeTime'])){
            $updateInfo['TDevice.LastChangeTime']=$data['TDevice.LastChangeTime'];
        }

        //仅web页面调用时更新basIP
        if (isset($data['basIP']) && $data['gettype'] !== 'control') {
            $updateInfo['basIP'] = $data['basIP'];
        }
        if (isset($data['TDevice.Online'])) {
            $updateInfo['TDevice.Online'] = $data['TDevice.Online'];
        }
        $setSuccess = cache_set_info('DeviceID_', $data['DeviceID'], $updateInfo);
        if (!$setSuccess) {
            $this->writeLog('写入缓存失败, 键:[DeviceID_'.$data['DeviceID'].'] 写入失败', 'ERROR');
        }

        $updateDevice = ['LastTime' => $date];
        // 判断是否要发上线或者下线事件
        if (isset($data['TDevice.Online']) && $oldOnline != $data['TDevice.Online']) {
            if ((int)$data['TDevice.Online'] === 1) {
                $eventType = 'Online';
                // 如果由离线到在线更新一下开机时间 fix 5422 历史IP使用记录表中上线时间错误
                $updateDevice['BootTime'] = $date;
            } else {
                $eventType = 'Offline';
            }

            EventBrokerFacade::publish($eventType,[['DeviceID' => (int)$data['DeviceID'],'Remark'=>$eventType]],'WebAccess:updateDeviceCacheAndLastTime');
        }

        // 更新TDevice最后在线时间
        \DeviceModel::update($data['DeviceID'], $updateDevice);

        return true;
    }

    /**
     * 发送xml报文.
     * @return bool|string
     * @throws Exception
     */
    private function sendXml()
    {
        /* 返回报文格式
         <?xml version="1.0" encoding="gbk"?>
            <ASM>
                <TradeCode>DEVSVC_AGENT_API_OP_QUERY</TradeCode>
                <APIVersion>v1.00</APIVersion>
                <AppID></AppID>
                <IP></IP>
                <MAC></MAC>
                <HardID></HardID>
                <DeviceID></DeviceID>
                <Result>400</Result>
                <ResultDesc>失败</ResultDesc>
            </ASM>
        */
        $sendResult = udp_send_xml($this->sendXml, $this->tradeCode, self::TIMEOUT, 0, '127.0.0.1');
        if ($sendResult['code'] !== 1) {
            throw new \RuntimeException($sendResult['message'], $sendResult['code']);
        }
        $this->receiveXml = $sendResult['data']['receiveXml'];


        // 报文格式错误
        if (strlen($this->receiveXml) < 200) {
            T(21103004);
        }

        // 解析xml
        $result = xmlToArray($this->receiveXml);

        if (!is_array($result) || !isset($result['Result'], $result['ResultDesc'], $result['DeviceID'])) {
            T(21103004);
        }

        // 查询设备信息，如果返回404表示设备不存在，不代表交易失败
        if ($result['TradeCode'] === 'DevSvcAgent_QueryDevID' && (int)$result['Result'] === 404) {
            return $result;
        }

        if ((int)$result['Result'] !== 200) {
            throw new \RuntimeException($result['ResultDesc']);
        }

        return $result;
    }

    /**
     * 拼装查询设备id XMl报文.
     *
     * @param string $hard
     * @param string $mac
     * @param string $ip
     *
     * @return string
     */
    private function setGetDeviceIDXml(string $hard, string $mac, string $ip):string
    {
        $this->sendXml = $this->xmlHeader;
        $this->sendXml .= '<TradeCode>'.$this->tradeCode.'</TradeCode>';
        $this->sendXml .= "<IP>{$ip}</IP><MAC>{$mac}</MAC><HardID>{$hard}</HardID>";
        $this->sendXml .= "<DeviceID></DeviceID><UpdateIP></UpdateIP><UpdateMAC></UpdateMAC>";
        $this->sendXml .= "<UpdateHardID></UpdateHardID></ASM>";

        return $this->sendXml;
    }

    /**
     * 拼装更新设备的XMl报文.
     *
     * @param array $data
     *
     * @return string
     */
    private function setUpdateDeviceXml(array $data):string
    {
        $this->sendXml = $this->xmlHeader;
        $this->sendXml .= '<TradeCode>'.$this->tradeCode.'</TradeCode>';
        $this->sendXml .= "<DeviceID>{$data['DeviceID']}</DeviceID>";

        if (isset($data['IP'])) {
            $this->sendXml .= "<IP>{$data['IP']}</IP><UpdateIP>1</UpdateIP>";
        } else {
            $this->sendXml .= '<IP></IP><UpdateIP>0</UpdateIP>';
        }

        if (isset($data['Mac'])) {
            $this->sendXml .= "<MAC>{$data['Mac']}</MAC><UpdateMAC>1</UpdateMAC>";
        } else {
            $this->sendXml .= '<MAC></MAC><UpdateMAC>0</UpdateMAC>';
        }

        if (isset($data['Hard'])) {
            $this->sendXml .= "<HardID>{$data['Hard']}</HardID><UpdateHardID>1</UpdateHardID>";
        } else {
            $this->sendXml .= '<HardID></HardID><UpdateHardID>0</UpdateHardID>';
        }
        $this->sendXml .= '</ASM>';

        return $this->sendXml;
    }

    /**
     * 拼装插入设备的xml报文.
     *
     * @param array $data
     *
     * @return string
     */
    private function setInsertDeviceXml(array $data):string
    {
        $this->sendXml = $this->xmlHeader;
        $this->sendXml .= '<TradeCode>'.$this->tradeCode.'</TradeCode>';
        $this->sendXml .= "<IP>{$data['IP']}</IP>";
        $this->sendXml .= "<MAC>{$data['Mac']}</MAC>";
        $this->sendXml .= "<HardID>{$data['Hard']}</HardID>";
        $this->sendXml .= '</ASM>';

        return $this->sendXml;
    }

    /**
     * 更新TDevice,TComputer表
     * ps：在调用服务(更新或者插入)之后，再更新这两个表的其他信息，存在服务处理成功，这里处理失败的情况
     * @param array $data
     * @throws Exception
     */
    private function updateDeviceAndComputer(array $data):void
    {
        try {
            if (!isset($data['DeviceID'])) {
                T(21100002);
            }
            // 更新TDevice表其他字段
            if (!empty($data['TDevice'])) {
                \DeviceModel::update($data['DeviceID'], $data['TDevice']);
                if (isset($data['TDevice']['Online'])) {
                    $this->updateDeviceCacheAndLastTime(['TDevice.Online' => $data['TDevice']['Online'], 'DeviceID' => $data['DeviceID']]);
                }
            }

            // 更新TComputer表其他字段
            !empty($data['TComputer']) && \ComputerModel::update($data['DeviceID'], $data['TComputer']);

            // 更新TRelationComputer表其他字段
            !empty($data['TRelationComputer']) && \RelationComputerModel::update($data['DeviceID'], $data['TRelationComputer']);

            //查询认证记录，获取对应的认证账号
            $nacAuthLogInfo = \NacAuthLogModel::getSingle(['DeviceID' => $data['DeviceID']], 'InsertTime desc');
            !empty($nacAuthLogInfo['UserName']) && $data['TAddDevice']['AuthUser'] = $nacAuthLogInfo['UserName'];

            // 更新TAddDevice部分字段
            !empty($data['TAddDevice']) && \AddDeviceModel::update($data['DeviceID'], $data['TAddDevice']);

        } catch (\Exception $e) {
            $this->message = '调用服务成功后，更新设备其他信息失败：'.$e->getMessage();
            $this->code = $e->getCode();
            $this->recordErrorMessage($e, '发送的报文：'.$this->sendXml.'##接收的报文：'.$this->receiveXml);
        }
    }

}