<?php

/**
 * Description: 动态策略主表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id
 */

class DynamicSafePolicyRelationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDynamicSafePolicyRelation';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        'one' => 'RID,DPolicyID,PolicyID',
        'res' => 'ResID,ResName,DPolicyID,PolicyID',
        '*' => '*',
    ];

    /**
     * 根据动态策略ID查询对应的资源
     * @param $dPolicyID
     * @return array
     */
    public static function getResourceByDid($dPolicyID)
    {
        if (empty($dPolicyID)) {
            return [];
        }
        self::$data = [];
        $sql = ' SELECT Distinct C.ResID,C.ResName,A.DPolicyID,A.PolicyID ';
        $sql .= ' FROM TDynamicSafePolicyRelation A INNER JOIN TResPolicyRelation B ON A.PolicyID=B.PolicyID';
        $sql .= ' INNER JOIN TResource C ON B.ResID=C.ResID';
        $sql .= " WHERE A.DPolicyID = " . self::setData($dPolicyID);
        $table = hlp_common::getSplitTable(0, self::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DPolicyID'])) {
            $where .= "AND DPolicyID = " . self::setData($cond['DPolicyID']);
        }

        if (isset($cond['PolicyID'])) {
            $where .= "AND PolicyID = " . self::setData($cond['PolicyID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
