{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "ae54cc6152ca25635c57e5f4f6c3c721", "packages": [{"name": "apereo/phpcas", "version": "1.4.0", "source": {"type": "git", "url": "https://github.com/apereo/phpCAS.git", "reference": "ea27d122c4c7114006b33d15668c90f1904d53df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/apereo/phpCAS/zipball/ea27d122c4c7114006b33d15668c90f1904d53df", "reference": "ea27d122c4c7114006b33d15668c90f1904d53df", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.0.0", "psr/log": "^1.0.0"}, "require-dev": {"monolog/monolog": "^1.0.0", "phpunit/phpunit": ">=4.8.35 <8"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.3.x-dev"}}, "autoload": {"classmap": ["source/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/jfritschi"}, {"name": "<PERSON>", "homepage": "https://github.com/adamfranco"}, {"name": "<PERSON>", "homepage": "https://github.com/phy25"}], "description": "Provides a simple API for authenticating users against a CAS server", "homepage": "https://wiki.jasig.org/display/CASC/phpCAS", "keywords": ["apereo", "cas", "jasig"], "support": {"issues": "https://github.com/apereo/phpCAS/issues", "source": "https://github.com/apereo/phpCAS/tree/1.4.0"}, "time": "2021-05-30T19:53:34+00:00"}, {"name": "asm-web/php-common", "version": "dev-6039.3746.R003.LTS02", "source": {"type": "git", "url": "*******************:ASM-Web/php-common.git", "reference": "380a48c46b9c1021c40fb62968ea23591128c197"}, "require": {"asm-web/service-sdk": "dev-6039.3746.R003", "ext-grpc": "*", "ext-json": "*", "grpc/grpc": "^1.42"}, "type": "library", "autoload": {"psr-4": {"Common\\": "src/Common/"}}, "authors": [{"name": "WenmingTang", "email": "<EMAIL>"}], "time": "2024-04-10T03:11:37+00:00"}, {"name": "asm-web/service-sdk", "version": "dev-6039.3746.R003", "source": {"type": "git", "url": "*******************:ASM-Web/service-sdk.git", "reference": "b524df88ba3edf8bb4f826c1597a42c13ee2b2b5"}, "require": {"google/protobuf": "^v3.13.0", "grpc/grpc": "^v1.38.0"}, "type": "library", "autoload": {"psr-4": {"Srv\\": "src/Srv", "GPBMetadata\\": "src/GPBMetadata"}}, "authors": [{"name": "WenmingTang", "email": "<EMAIL>"}], "time": "2024-04-15T08:42:23+00:00"}, {"name": "google/protobuf", "version": "v3.25.3", "source": {"type": "git", "url": "https://github.com/protocolbuffers/protobuf-php.git", "reference": "983a87f4f8798a90ca3a25b0f300b8fda38df643"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/protocolbuffers/protobuf-php/zipball/983a87f4f8798a90ca3a25b0f300b8fda38df643", "reference": "983a87f4f8798a90ca3a25b0f300b8fda38df643", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"phpunit/phpunit": ">=5.0.0"}, "suggest": {"ext-bcmath": "Need to support JSON deserialization"}, "type": "library", "autoload": {"psr-4": {"Google\\Protobuf\\": "src/Google/Protobuf", "GPBMetadata\\Google\\Protobuf\\": "src/GPBMetadata/Google/Protobuf"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "description": "proto library for PHP", "homepage": "https://developers.google.com/protocol-buffers/", "keywords": ["proto"], "support": {"source": "https://github.com/protocolbuffers/protobuf-php/tree/v3.25.3"}, "time": "2024-02-15T21:11:49+00:00"}, {"name": "grpc/grpc", "version": "1.57.0", "source": {"type": "git", "url": "https://github.com/grpc/grpc-php.git", "reference": "b610c42022ed3a22f831439cb93802f2a4502fdf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/grpc/grpc-php/zipball/b610c42022ed3a22f831439cb93802f2a4502fdf", "reference": "b610c42022ed3a22f831439cb93802f2a4502fdf", "shasum": ""}, "require": {"php": ">=7.0.0"}, "require-dev": {"google/auth": "^v1.3.0"}, "suggest": {"ext-protobuf": "For better performance, install the protobuf C extension.", "google/protobuf": "To get started using grpc quickly, install the native protobuf library."}, "type": "library", "autoload": {"psr-4": {"Grpc\\": "src/lib/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "gRPC library for PHP", "homepage": "https://grpc.io", "keywords": ["rpc"], "support": {"source": "https://github.com/grpc/grpc-php/tree/v1.57.0"}, "time": "2023-08-14T23:57:54+00:00"}, {"name": "jaybizzle/crawler-detect", "version": "v1.2.117", "source": {"type": "git", "url": "https://github.com/JayBizzle/Crawler-Detect.git", "reference": "6785557f03d0fa9e2205352ebae9a12a4484cc8e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/JayBizzle/Crawler-Detect/zipball/6785557f03d0fa9e2205352ebae9a12a4484cc8e", "reference": "6785557f03d0fa9e2205352ebae9a12a4484cc8e", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8|^5.5|^6.5|^9.4"}, "type": "library", "autoload": {"psr-4": {"Jaybizzle\\CrawlerDetect\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "CrawlerDetect is a PHP class for detecting bots/crawlers/spiders via the user agent", "homepage": "https://github.com/JayBizzle/Crawler-Detect/", "keywords": ["crawler", "crawler detect", "crawler detector", "crawlerdetect", "php crawler detect"], "support": {"issues": "https://github.com/JayBizzle/Crawler-Detect/issues", "source": "https://github.com/JayBizzle/Crawler-Detect/tree/v1.2.117"}, "time": "2024-03-19T22:51:22+00:00"}, {"name": "jenssegers/agent", "version": "v2.6.4", "source": {"type": "git", "url": "https://github.com/jenssegers/agent.git", "reference": "daa11c43729510b3700bc34d414664966b03bffe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jenssegers/agent/zipball/daa11c43729510b3700bc34d414664966b03bffe", "reference": "daa11c43729510b3700bc34d414664966b03bffe", "shasum": ""}, "require": {"jaybizzle/crawler-detect": "^1.2", "mobiledetect/mobiledetectlib": "^2.7.6", "php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5.0|^6.0|^7.0"}, "suggest": {"illuminate/support": "Required for laravel service providers"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}, "laravel": {"providers": ["Jenssegers\\Agent\\AgentServiceProvider"], "aliases": {"Agent": "Jenssegers\\Agent\\Facades\\Agent"}}}, "autoload": {"psr-4": {"Jenssegers\\Agent\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "homepage": "https://jenssegers.com"}], "description": "Desktop/mobile user agent parser with support for <PERSON><PERSON>, based on Mobiledetect", "homepage": "https://github.com/jenssegers/agent", "keywords": ["Agent", "browser", "desktop", "laravel", "mobile", "platform", "user agent", "useragent"], "support": {"issues": "https://github.com/jenssegers/agent/issues", "source": "https://github.com/jenssegers/agent/tree/v2.6.4"}, "funding": [{"url": "https://github.com/jenssegers", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/jenssegers/agent", "type": "tidelift"}], "time": "2020-06-13T08:05:20+00:00"}, {"name": "mobiledetect/mobiledetectlib", "version": "2.8.45", "source": {"type": "git", "url": "https://github.com/serbanghita/Mobile-Detect.git", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/serbanghita/Mobile-Detect/zipball/96aaebcf4f50d3d2692ab81d2c5132e425bca266", "reference": "96aaebcf4f50d3d2692ab81d2c5132e425bca266", "shasum": ""}, "require": {"php": ">=5.0.0"}, "require-dev": {"phpunit/phpunit": "~4.8.36"}, "type": "library", "autoload": {"psr-0": {"Detection": "namespaced/"}, "classmap": ["Mobile_Detect.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "serban<PERSON><PERSON>@gmail.com", "homepage": "http://mobiledetect.net", "role": "Developer"}], "description": "Mobile_Detect is a lightweight PHP class for detecting mobile devices. It uses the User-Agent string combined with specific HTTP headers to detect the mobile environment.", "homepage": "https://github.com/serbanghita/Mobile-Detect", "keywords": ["detect mobile devices", "mobile", "mobile detect", "mobile detector", "php mobile detect"], "support": {"issues": "https://github.com/serbanghita/Mobile-Detect/issues", "source": "https://github.com/serbanghita/Mobile-Detect/tree/2.8.45"}, "funding": [{"url": "https://github.com/serbanghita", "type": "github"}], "time": "2023-11-07T21:57:25+00:00"}, {"name": "phpmailer/phpmailer", "version": "v6.4.1", "source": {"type": "git", "url": "https://github.com/PHPMailer/PHPMailer.git", "reference": "9256f12d8fb0cd0500f93b19e18c356906cbed3d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPMailer/PHPMailer/zipball/9256f12d8fb0cd0500f93b19e18c356906cbed3d", "reference": "9256f12d8fb0cd0500f93b19e18c356906cbed3d", "shasum": ""}, "require": {"ext-ctype": "*", "ext-filter": "*", "ext-hash": "*", "php": ">=5.5.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "^0.7.0", "doctrine/annotations": "^1.2", "phpcompatibility/php-compatibility": "^9.3.5", "roave/security-advisories": "dev-latest", "squizlabs/php_codesniffer": "^3.5.6", "yoast/phpunit-polyfills": "^0.2.0"}, "suggest": {"ext-mbstring": "Needed to send email in multibyte encoding charset or decode encoded addresses", "hayageek/oauth2-yahoo": "Needed for Yahoo XOAUTH2 authentication", "league/oauth2-google": "Needed for Google XOAUTH2 authentication", "psr/log": "For optional PSR-3 debug logging", "stevenmaguire/oauth2-microsoft": "Needed for Microsoft XOAUTH2 authentication", "symfony/polyfill-mbstring": "To support UTF-8 if the Mbstring PHP extension is not enabled (^1.2)"}, "type": "library", "autoload": {"psr-4": {"PHPMailer\\PHPMailer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-2.1-only"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>"}], "description": "PHPMailer is a full-featured email creation and transfer class for PHP", "support": {"issues": "https://github.com/PHPMailer/PHPMailer/issues", "source": "https://github.com/PHPMailer/PHPMailer/tree/v6.4.1"}, "funding": [{"url": "https://github.com/Synchro", "type": "github"}], "time": "2021-04-29T12:25:04+00:00"}, {"name": "phpstan/phpstan", "version": "1.10.66", "source": {"type": "git", "url": "https://github.com/phpstan/phpstan.git", "reference": "94779c987e4ebd620025d9e5fdd23323903950bd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpstan/zipball/94779c987e4ebd620025d9e5fdd23323903950bd", "reference": "94779c987e4ebd620025d9e5fdd23323903950bd", "shasum": ""}, "require": {"php": "^7.2|^8.0"}, "conflict": {"phpstan/phpstan-shim": "*"}, "bin": ["phpstan", "phpstan.phar"], "type": "library", "autoload": {"files": ["bootstrap.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPStan - PHP Static Analysis Tool", "keywords": ["dev", "static analysis"], "support": {"docs": "https://phpstan.org/user-guide/getting-started", "forum": "https://github.com/phpstan/phpstan/discussions", "issues": "https://github.com/phpstan/phpstan/issues", "security": "https://github.com/phpstan/phpstan/security/policy", "source": "https://github.com/phpstan/phpstan-src"}, "funding": [{"url": "https://github.com/ondrejmirtes", "type": "github"}, {"url": "https://github.com/phpstan", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/phpstan/phpstan", "type": "tidelift"}], "time": "2024-03-28T16:17:31+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": {"asm-web/service-sdk": 20, "asm-web/php-common": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"php": ">=7.2", "ext-json": "*", "ext-iconv": "*", "ext-openssl": "*", "ext-redis": "*", "ext-mbstring": "*"}, "platform-dev": [], "platform-overrides": {"php": "7.2"}, "plugin-api-version": "2.6.0"}