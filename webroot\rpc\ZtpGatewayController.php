<?php

/**
 * Description: 零信任-下发后-网关本地执行（ASM控制器下发给ASG网关）
 * TradeType为MessageToGw
 * User: <EMAIL>
 * Date: 2022/03/22 15:53
 * Version: $Id: GatewayController.php 172377 2022-03-30 10:02:11Z duanyc $
 */

require PATH_ROOT . "/webroot/rpc/AsmYarServer.php";

class ZtpGatewayController extends AsmYarServer
{
    /**
     * 指定服务
     * @var string
     */
    protected $_server = 'ztpGateway';

    /**
     * 同步配置文件
     *
     * @param $params
     * @return mixed
     * @throws \Exception
     */
    protected function overridefile($params)
    {
        $action = $params['action'] ?? '';
        if (empty($params['filepaths'])) {
            return false;
        }
        $updateFiles = [];
        foreach ($params['filepaths'] as $row) {
            if (!empty($row['path']) && !empty($row['content'])) {
                if (!file_exists($row['path'])) {
                    cutil_exec_wait("touch " . $row['path']);
                }
                $content = file_get_contents($row['path']);
                $row['content'] = base64_decode($row['content']);
                if ($content === $row['content']) {
                    $this->log("{$row['path']} no change.");
                    continue;
                }
                if (!is_writable($row['path']) || !is_readable($row['path'])) {
                    cutil_exec_wait("chmod 666 " . $row['path']);
                }
                $updateFiles[] = $row['path'];
                file_put_contents($row['path'], $row['content']);
            }
        }
        if (empty($updateFiles)) {
            return false;
        }

        switch ($action) {
            case 'Fwknop':
                // 重启敲门
                cutil_exec_no_wait(PATH_ASM . "etc/init.d/fwknopd restart");
                // 重启tomcat服务
                cutil_exec_no_wait(PATH_ASM . "etc/init.d/tomcatd restart");
                //重启NGINX
                sleep(4);
                cutil_exec_no_wait(PATH_ETC . "init.d/nginx restart && DascPubMssage");
                $this->log("service restart success");
                break;
            case 'Gatevpn':
                // 重启vpn
                cutil_exec_no_wait(PATH_ASM . "etc/init.d/gatewayvpnd restart");
                $this->log("vpn restart success");
                break;
            case 'Firewall':
                // 执行防火墙策略
                $devtype = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
                if ($devtype === DEVTYPE_ASG) {
                    $iptables = get_ini_info(PATH_ETC . 'iptables.ini');
                    ConfigServiceProvider::saveDict('AccessContorl', 'ssh', $iptables['ssh_iprange_enable']);
                    ConfigServiceProvider::saveDict('AccessContorl', 'snmp', $iptables['snmp_iprange_enable']);
                    ConfigServiceProvider::saveDict('AccessContorl', 'mysql', $iptables['mysql_iprange_enable']);
                    $wconfig = read_inifile(PATH_ETC . "/workerman.ini");
                    cutil_exec_wait('php ' . PATH_ROOT . "/bin/iptables.php -a addWhitelist -ip {$wconfig['remotehost']}");
                }
                cutil_exec_no_wait(PATH_HTML . "/bin/asm_access_control.php &");
                $this->log("firewall run success");
                break;
        }
        return true;
    }

    /**
     * 下发用户配置信息，原ReportInfo::userconfig，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function userconfig($params)
    {
        return [];
    }

    /**
     * 下发资源配置到网关，原ReportInfo::resconfig，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function resconfig($params)
    {
        AppServiceProvider::setResConfig($params);
        return true;
    }

    /**
     * 下发redis配置到网关，原ReportInfo::redisconfig，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function redisconfig($params)
    {
        return [];
    }

    /**
     *  策略状态变更 用户策略违规/合规信息下发到网关，原ReportInfo::user_policy_info，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function policyinfo($params)
    {
        return [];
    }

    /**
     *  下发UDP命令到网关，原ReportInfo::udpcommand，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function udpcommand($params)
    {
        return [];
    }

    /**
     *  下发用户和设备信息到网关，原ReportInfo::syncUserAndDev，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function syncUserAndDev($params)
    {
        return [];
    }

    /**
     *  控制器向网关同步IP资源表数据，原ReportInfo::syncTable，必须为protected
     *  零信任用于TNetSecurityDomain表数据同步
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function syncIpRes($params)
    {
        $resList = $params['ResList'] ?? [];
        GatewayServiceProvider::syncResList($resList);
        return [];
    }

    /**
     *  向网关下发网关配置数据，原ReportInfo::gwconfig，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function gwconfig($params)
    {
        GatewayServiceProvider::setGatewayConfig($params);
        return [];
    }

    /**
     *  下发vpn文件配置信息，原ReportInfo::vpnConfig，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function fileConfig($params)
    {
        if (empty($params['filepath'])) {
            return false;
        }

        GatewayServiceProvider::updateConfigFile($params);
        return true;
    }

    /**
     *  下发vpn路由信息，原GateWayConfig::setVpnRoute，必须为protected
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function setVpnRoute($params)
    {
        $this->log(var_export($params, true));
        if (empty($params['content'])) {
            return false;
        }

        GatewayServiceProvider::updateRouteConfig($params['content']);
        return true;
    }

    /**
     * 清理网关数据
     * @param $params
     * @return bool
     */
    protected function clearGatewayData($params)
    {
        GatewayServiceProvider::clearGatewayData();
        return true;
    }

    /**
     *  检查网关与资源连通性
     *
     * @param $params
     * @return mixed
     * @throws Exception
     */
    protected function checkResConnection($params)
    {
        $this->log(var_export($params, true));
        if (empty($params['address']) || empty($params['resInfo'])) {
            return false;
        }
        $agentService = ResourceServiceProvider::initResourceService('Agent');
        return $agentService->checkResConnectionRpc($params);
    }
}
