<?php
/**
 * Description: 零信任必须安装客户端策略项服务
 * User: <EMAIL>
 * Date: 2022/05/12 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class InstallClientPolicyService extends BasePolicyService implements PolicyServiceInterface
{

    /**
     * 检查是否通过
     * @return bool
     * @throws \Exception
     */
    public function check(): bool
    {
        $device = $this->device;
        return !empty($device['InstallClient']);
    }
}
