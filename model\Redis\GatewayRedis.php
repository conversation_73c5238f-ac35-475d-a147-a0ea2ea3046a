<?php

/**
 * Description: 网关配置 读数据
 * User: <EMAIL>
 * Date: 2022/06/06 10:32
 * Version: $Id$
 */

class GatewayRedis extends BaseRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Gateway';

    /**
     * 控制中心的网关数据
     * @var string
     */
    protected static $localColumns = 'ID,Name,Priority,IpIn,IpEx,PortIn,PortEx,ParseDomain,GwVirState,disVirIp,ProxyPort,RecoveHours,RecoveVirIp,noIpPermit';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'    => ['ID', 'Name', 'Priority', 'IpIn', 'IpEx', 'PortIn', 'PortEx', 'ParseDomain','GwVirState','disVirIp','ProxyPort','RecoveHours','RecoveVirIp','noIpPermit'],
        'ipPermit' => ['ID','noIpPermit']
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['ParseDomain' => true];

    /**
     * 单条
     *
     * @param string $gatewayId
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($gatewayId, $column = 'one')
    {
        return self::get($column, $gatewayId);
    }

    /**
     * 单条
     *
     * @param string $gatewayId
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($gatewayId, $data)
    {
        return self::set($data, $gatewayId);
    }
}
