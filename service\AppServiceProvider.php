<?php
/**
 * Description: App资源相关逻辑service
 * User: <EMAIL>
 * Date: 2022/06/10 10:32
 * Version: $Id$
 */

class AppServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'app';


    /**
     * 获取header头参数
     *
     * @param $HttpHeader
     *
     * @return array
     */
    private static function getHttpHeader($HttpHeader): array
    {
        $aHeader = [];
        $data = json_decode($HttpHeader, true);
        if (empty($data)) {
            return [];
        }
        foreach ($data as $row) {
            $aHeader[$row['key']] = $row['value'];
        }
        return $aHeader;
    }

    /**
     * 获取Healcheck
     *
     * @param $resource
     * @param $isRdp
     *
     * @return array
     */
    private static function getHealcheck($resource, $isRdp = false): array
    {
        $aHealcheck = [];
        $HealthCode = json_decode($resource['HealthCode'], true);
        $httpStatuses = is_array($HealthCode) ? array_map('intval', array_column($HealthCode, 'code')) : [0];
        $healthy = [
            'http_statuses' => $httpStatuses,
            'successes' => (int)$resource['SuccessNums'],
        ];
        $unhealthy = [
            'http_statuses' => [0],
            'tcp_failures' => (int)$resource['TcpFailNums'],
            'http_failures' => (int)$resource['FailNums'],
        ];
        $protocol = $resource['RealProtocol'] ?: "https";
        $type = $isRdp ? 'tcp' : $protocol;
        if ($resource['IsPassive'] === "2") {
            //被动模式
            $passive = ['type' => $type];
            $passive['healthy'] = $healthy;
            $unhealthy['timeouts'] = ((int)$resource['Timeout'] < 1 ? 5 : (int)$resource['Timeout']);
            $passive['unhealthy'] = $unhealthy;
            $aHealcheck['passive'] = $passive;
        } elseif ($resource['IsPassive'] === "1") {
            //主动模式
            $active = [
                'timeouts' => (int)$resource['Timeout'], 'http_path' => $resource['RequestUrl'],
                'type' => $type,
            ];
            $active['https_verify_certificate'] = false;
            $healthy['interval'] = (int)$resource['IntervalTime1'];
            $active['healthy'] = $healthy;
            $unhealthy['interval'] = (int)$resource['IntervalTime2'];
            $unhealthy['timeouts'] = (int)$resource['Timeout'];
            $active['unhealthy'] = $unhealthy;
            $aHealcheck['active'] = $active;
        }
        return $aHealcheck;
    }

    /**
     * 获取资源组装信息
     *
     * @param $domain
     * @param $aResInfo
     * @param $aResRdpInfo
     * @param $aResInfos
     *
     * @return mixed
     */
    public static function setResInfo($domain, $aResInfo, $aResRdpInfo, &$aResInfos)
    {
        $aResInfos["Version"] = "1";
        if (!empty($domain)) {
            //区分普通资源与免认证资源
            if (isset($aResInfo['APPType']) && $aResInfo['APPType'] == ACCESS_TYPE_NO_AUTH) {
                $aResInfos["noauthappcfg"][$domain] = $aResInfo;
            } else {
                $aResInfos["appcfg"][$domain] = $aResInfo;
            }
            self::setResDependSiteInfo($aResInfo, $aResInfos);
        }
        if (!isset($aResInfos["appcfg"])) {
            $aResInfos["appcfg"] = [];
        }
        if (!isset($aResInfos["noauthappcfg"])) {
            $aResInfos["noauthappcfg"] = [];
        }
        if (!empty($aResRdpInfo)) {
            $aResInfos["terminalcfg"][$aResRdpInfo["id"]] = $aResRdpInfo;
        }
        if (!isset($aResInfos["terminalcfg"])) {
            $aResInfos["terminalcfg"] = [];
        }
        $aResInfos["appnum"] = count($aResInfos["appcfg"]) + count($aResInfos["noauthappcfg"]) + (isset($aResInfos["terminalcfg"]) ?
                count($aResInfos["terminalcfg"]) : 0);
        return $aResInfos;
    }

    /**
     * 获取header头参数
     *
     * @param $aResInfo
     * @param $aResInfos
     * @return array
     */
    private static function setResDependSiteInfo($aResInfo, &$aResInfos): array
    {
        // 判断下如果是依赖站点，则给每个依赖域名添加资源
        if (!empty($aResInfo['IsOpen']) && $aResInfo['IsOpen'] == '1' && !empty($aResInfo['DependSite'])) {
            foreach ($aResInfo['DependSite'] as $dependSite) {
                // 依赖站点中依赖站点信息不冗余，不然数据会很庞大。
                $accUrlArr = hlp_net::analyzeURL($dependSite['OriginUrl']);
                //如果依赖站点是母站点则直接跳过
                if (hlp_net::getDomainByUrl($dependSite['AccessUrl']) == $aResInfo['Host']) {
                    continue;
                }
                $dResInfoTmp = [
                    'DependDomain' => $aResInfo['Host'],
                    'RealPort' => $accUrlArr['port'],
                    'RealProtocol' => $accUrlArr['protocol'],
                    'RealUrl' => $accUrlArr['domain'],
                ];
                if (isset($aResInfo['APPType']) && $aResInfo['APPType'] == ACCESS_TYPE_NO_AUTH) {
                    $aResInfos["noauthappcfg"][hlp_net::getDomainByUrl($dependSite['AccessUrl'])] = $dResInfoTmp;
                } else {
                    $aResInfos["appcfg"][hlp_net::getDomainByUrl($dependSite['AccessUrl'])] = $dResInfoTmp;
                }

            }
        }
        return $aResInfos;
    }

    /**
     * 获取远程应用组装信息
     *
     * @param $resource
     * @param $ProxyProtocol
     *
     * @return array
     */
    public static function getRemoteAppResourceInfo($resource, $ProxyProtocol = 'RDP'): array
    {
        $aResRmtAppInfo = [];
        //REMOTE_APP 资源
        $aResRmtAppInfo["id"] = $resource['ResID'];
        $aResRmtAppInfo["PolicyID"] = $resource['PolicyID'] ?? '0';
        $aResRmtAppInfo["Protocol"] = "https"; // 代理协议-https 网关
        $aResRmtAppInfo["Port"] = 443; // 代理端口 --网关
        $aResRmtAppInfo["Host"] = ''; // 代理地址
        $aResRmtAppInfo["RealUrl"] = $resource['Addr']; // 实际地址 --应用服务器地址
        $aResRmtAppInfo["HostName"] = $resource['ResName'];  // 资源名称
        $aResRmtAppInfo["IP"] = $resource['Addr']; // 实际地址 --应用服务器地址
        $aResRmtAppInfo["RealPort"] = (int)$resource['Port'] ?: 443; // 远程服务连接端口（远程服务器新增）  --应用服务器地址
        $aResRmtAppInfo["RealProtocol"] = $ProxyProtocol; //当前协议 写死 RDP
        $aResRmtAppInfo["APPType"] = ACCESS_TYPE_REMOTE_APP;
        $aResRmtAppInfo["APPSubType"] = null;
        $aResRmtAppInfo["ClipboardCopy"] = $resource['IsCopy']; // 是否允许复制
        $aResRmtAppInfo["ClipboardPaste"] = $resource['IsPaste'];// 是否允许黏贴
        $aResRmtAppInfo['Watermark'] = $resource['Watermark'] ?? 0; // 水印开关
        // 远程应用资源新增
        $aResRmtAppInfo["AppName"] = $resource['AppName'];
        $aResRmtAppInfo["AppPath"] = $resource['AppPath'];
        $aResRmtAppInfo["RemoteClitPort"] = $resource['RemotePort'];
        $aResRmtAppInfo["IsRemoteApp"] = 1;
        $aResRmtAppInfo["IsHealthCheck"] = $resource['IsPassive'];
        return $aResRmtAppInfo;
    }

    /**
     * 获取RDP资源组装信息
     *
     * @param $resource
     * @param $ProxyProtocol
     *
     * @return array
     */
    public static function getRdpResourceInfo($resource, $ProxyProtocol): array
    {
        $FileRoot = PATH_ADMIN . "/storage/app/";
        $aResRdpInfo = [];
        //RDP VNC SSH TELNET 资源
        $aResRdpInfo['id'] = $resource['ResID'];
        $aResRdpInfo["PolicyID"] = $resource['PolicyID'] ?? '0';
        $aResRdpInfo['Protocol'] = $resource['ProxyProtocol'] ?: "https"; // 代理协议
        $aResRdpInfo['Port'] = $resource['ProxyPort'] ?: 443; // 代理端口
        $aResRdpInfo['Host'] = $resource['Url']; // 代理地址
        $aResRdpInfo['Watermark'] = $resource['Watermark'] ?? 0; // 水印开关
        $aResRdpInfo['RealUrl'] = $resource['DomainName']; // 实际地址
        $aResRdpInfo["HostName"] = $resource['ResName'];
        $aResRdpInfo["IP"] = $resource['DomainName']; // 实际地址
        $aResRdpInfo["RealPort"] = (int)$resource['RealPort'] ?: 443; // 实际端口
        $aResRdpInfo["RealProtocol"] = $ProxyProtocol; // 当前协议
        $aResRdpInfo['APPType'] = (string)$resource['AccessTypeID'];
        $aResRdpInfo['APPSubType'] = $resource['GrantType'];
        $aResRdpInfo["ClipboardCopy"] = $resource['IsCopy']; // 是否允许复制
        $aResRdpInfo["ClipboardPaste"] = $resource['IsPaste'];// 是否允许黏贴
        $aResRdpInfo['OtherField'] = self::getRdpOtherField($resource['OtherField']);
        if (in_array(intval($resource['AccessTypeID']), [ACCESS_TYPE_SSH])) {
            $aResRdpInfo['SSHPrivateKey'] = !empty($resource['CertKeyPath']) && is_file($FileRoot . $resource['CertKeyPath']) ?
                file_get_contents($FileRoot . $resource['CertKeyPath']) : "";
            if (empty($aResRdpInfo['SSHPrivateKey'])) {
                $aResRdpInfo['SSHPrivateKey'] = !empty($resource['CertKeyPath']) && is_file($resource['CertKeyPath']) ?
                    file_get_contents($resource['CertKeyPath']) : "";
            }
        }
        if (in_array($resource['IsPassive'], ['1', '2'], false)) {
            $aResRdpInfo['Healcheck'] = self::getHealcheck($resource, true);
        }
        return $aResRdpInfo;
    }

    /**
     * 生成RDP的其他字段值
     * @param $OtherFieldStr
     * @return array
     */
    public static function getRdpOtherField($OtherFieldStr): array
    {
        if (empty($OtherFieldStr)) {
            return [];
        }
        $OtherField = json_decode($OtherFieldStr, true);
        $OtherField['remitSwitch'] = $OtherField['remitSwitch'] ?? '0';
        // 注意：数据库存储的是ASm的Base64下发到网关，转为Ztp2.0的Base64，因为pubEncrypt用的是这个base64
        if (!empty($OtherField['defaultAccount'])) {
            $OtherField['defaultAccount'] = Base64EnExt(Base64DeExt($OtherField['defaultAccount']), true);
        }
        // 数据库存储的是ASm的Base64下发到网关，转为RSA加密且转为Ztp2.0的Base64
        if (!empty($OtherField['defaultPassword'])) {
            $OtherField['defaultPassword'] = pubEncrypt(Base64DeExt($OtherField['defaultPassword']));
        }
        return $OtherField;
    }

    /**
     * 获取普通资源组装信息
     *
     * @param $resource
     *
     * @return array
     */
    public static function getCommonResourceInfo($resource): array
    {
        $FileRoot = PATH_ADMIN . "/storage/app/";
        $aResInfo = [];
        // 普通https代理资源
        $aResInfo['id'] = $resource['ResID'];
        $aResInfo["PolicyID"] = $resource['PolicyID'] ?? '0';
        $aResInfo["Watermark"] = $resource['Watermark'] ?? '0';
        $aResInfo['Protocol'] = $resource['ProxyProtocol'] ?: "https"; // 代理协议
        $aResInfo['Port'] = $resource['ProxyPort'] ?: 443; // 代理端口
        $aResInfo['Host'] = $resource['Url']; // 代理地址
        $aResInfo['RealUrl'] = $resource['DomainName']; // 实际地址
        $aResInfo["HostName"] = $resource['ResName'];
        $aResInfo['RealProtocol'] = $resource['RealProtocol'] ?: "https"; // 实际协议
        $aResInfo['RealPort'] = (int)$resource['RealPort'] ?: 443; // 实际端口
        $aResInfo['APPType'] = (string)$resource['AccessTypeID']; // 300 :oauth2, 301:表单代填, 302:RDP
        $aResInfo['APPSubType'] = $resource['GrantType'];
        $aResInfo['OtherField'] = $resource['OtherField'];
        $aResInfo['LoginURL'] = $resource['LoginURL'];
        $aResInfo['ConnUrl'] = $resource['ConnUrl'] ?? '';
        $aResInfo['IsNat'] = $resource['IsNat'] ?? 0;
        $aResInfo['CID'] = $resource['CID'] ?? 0;
        $aResInfo['IsInternet'] = $resource['IsInternet'] ?? 0;
        $aResInfo['IsSmartRewriting'] = $resource['IsSmartRewriting'] ?? 0;
        $urlData = $resource['LoginURL'] ? parse_url($resource['LoginURL']) : [];
        $aResInfo['LoginPath'] = $urlData['path'] ?? null;
        self::log("filter {$resource['ResID']}: {$aResInfo['OtherField']}: {$aResInfo['APPSubType']}");

        //兼容新旧版本证书
        $httpsPrivateKey = !empty($resource['CertKeyPath']) && is_file($FileRoot . $resource['CertKeyPath']) ?
            file_get_contents($FileRoot . $resource['CertKeyPath']) : "";
        if (empty($httpsPrivateKey)) {
            $httpsPrivateKey = !empty($resource['CertKeyPath']) && is_file($resource['CertKeyPath']) ?
                file_get_contents($resource['CertKeyPath']) : "";
        }
        $httpsCrt = !empty($resource['CertPath']) && is_file($FileRoot . $resource['CertPath']) ?
            file_get_contents($FileRoot . $resource['CertPath']) : "";

        if (empty($httpsCrt)) {
            $httpsCrt = !empty($resource['CertPath']) && is_file($resource['CertPath']) ?
                file_get_contents($resource['CertPath']) : "";
        }

        // 证书处理，目前将证书从该合集里面移除放入单独的Key，存入CID,如果对应的证书不存在，则刷新一下证书缓存key
        if (empty($aResInfo['CID'])) {
            //使用默认证书 CID
            $aCerts = CertificateListModel::getList(['Type' => 2], 'IsDefault DESC', 0, 1);
            if (!empty($aCerts)) {
                $aResInfo['CID'] = $aCerts[0]['ID'] ?? 0;
            }
        }

        // ASG_Cert 判断证书对应的缓存是否存在，并下发到ASG，此处可以每次都下发一次，在证书管理列表操作也会触发下发
        //刷新并下发，目前设置10年过期，避免设置完被删除
        CertRedis::setOne($aResInfo['CID'], ['KeyContent' => $httpsPrivateKey, 'CrtContent' => $httpsCrt,'LifeTime'=>86400*3600]);

        if (!empty($resource['HttpHeader'])) {
            $aResInfo['AddHeader'] = self::getHttpHeader($resource['HttpHeader']);
        }
        if (in_array($resource['IsPassive'], ['1', '2'], false)) {
            $aResInfo['Healcheck'] = self::getHealcheck($resource);
        }
        //判断依赖站点，组装依赖站点数据
        $dependSiteArr = [];
        $isOpen = '0';
        if (!empty($resource['DependSite'])) {
            is_string($resource['DependSite']) && $resource['DependSite'] = json_decode($resource['DependSite'], true);
            //开启依赖站点
            if (!empty($resource['DependSite']['isOpen']) && $resource['DependSite']['isOpen'] == '1') {
                $isOpen = 1;
                if (!empty($resource['DependSite']['originUrls']) && !empty($resource['DependSite']['accessUrls'])) {
                    $originUrlArr = explode(PHP_EOL, $resource['DependSite']['originUrls']);
                    $accessUrlArr = explode(PHP_EOL, $resource['DependSite']['accessUrls']);
                    if (count($originUrlArr) > 0 && count($originUrlArr) == count($accessUrlArr)) {
                        foreach ($originUrlArr as $key => $value) {
                            $dependSiteArr[] = ['AccessUrl' => $accessUrlArr[$key] ?? '', 'OriginUrl' => $value];
                        }
                        // 如果有依赖站点，则同时母站点是依赖子站的依赖站点，不然在子站点中引用母站点的资源会失败
                        $dependSiteArr[] = [
                            'AccessUrl' => $aResInfo['Protocol'] . '://' . $aResInfo['Host'],
                            'OriginUrl' => $aResInfo['RealProtocol'] . '://' . $aResInfo['RealUrl']
                                . (!in_array($aResInfo['RealPort'], [80, 443]) ? ':' . $aResInfo['RealPort'] : '')
                        ];
                    }
                }
            }
        }
        $aResInfo['IsOpen'] = (string)$isOpen;
        $aResInfo['DependSite'] = $dependSiteArr;
        return $aResInfo;
    }

    /**
     * 设置资源数据
     *
     * @param array $GwIps 网关IP数组
     *
     * @return array
     * @throws Exception
     */
    public static function getAppConfigs($GwIps = []): array
    {
        $GwIpIndex = [];
        foreach ($GwIps as $GwIp) {
            $GwIpIndex[$GwIp] = true;
        }
        $aGwInfo = [];
        [$GwGroupIps, $GwAllIps] = GateWayModel::getAppGroupColumns('app');
        $resourceList = ResourceModel::getAllResource();
        //远程应用资源获取
        $remoteAppList = ResourceModel::getRemoteAppResource();
        $resourceList = array_merge($resourceList, $remoteAppList);
        //已经下架的免认证资源，需要单独弹出一个错误提示框
        $noAuthResourceList = ResourceModel::getNoAuthResource();
        $resourceList = array_merge($resourceList, $noAuthResourceList);
        //根据授权,下发资源
        $rdpPower = getmoduleregist(12);
        $sshPower = getmoduleregist(13);
        $remotePower = getmoduleregist(28);
        foreach ($resourceList as $resource) {
            if ($resource['AccessTypeID'] === ACCESS_TYPE_CUSTOM_APP) {
                continue;
            }
            $domain = "";
            $aResInfo = [];
            $aResRdpInfo = [];
            // 判断远程应用资源
            if (isset($resource['ResType']) && $resource['ResType'] === ResourceServiceProvider::RES_TYPE_REMOTE_APP) {
                $ProxyProtocol = "Remote";
            } else {
                $ProxyProtocol = hlp_net::getProxyProtocol($resource['AccessTypeID']);
            }
            // Extend为扩展类型，为解决编辑器的提示，暂无用
            if ((in_array($ProxyProtocol, ['RDP', 'VNC'], true) && empty($rdpPower)) ||
                (in_array($ProxyProtocol, ['SSH', 'TELNET'], true) && empty($sshPower)) ||
                (in_array($ProxyProtocol, ['Remote', 'Extend'], true) && empty($remotePower))) {
                self::log("filter {$resource['ResID']}: {$rdpPower}: {$sshPower}: {$remotePower}");
                continue;
            }
            // 添加远程应用资源处理
            if (!empty($ProxyProtocol)) {
                if ($ProxyProtocol === "Remote") {
                    $aResRdpInfo = self::getRemoteAppResourceInfo($resource);
                } else {
                    $aResRdpInfo = self::getRdpResourceInfo($resource, $ProxyProtocol);
                }
            } else {
                $domain = $resource['Url'] ?: ($resource['IP'] ?? '');
                $aResInfo = self::getCommonResourceInfo($resource);
            }
            if ($resource["GateWayType"] === '1') {
                $ip = $GwAllIps[$resource["GateWayID"]]['IP'] ?? '';
                if (!empty($GwIpIndex) && !empty($ip) && empty($GwIpIndex[$ip])) {
                    self::log("filter gateway {$resource['ResID']}: {$ip}");
                    continue;
                }
                self::setResInfo($domain, $aResInfo, $aResRdpInfo, $aGwInfo[$ip]);
            } elseif ($resource["GateWayType"] === '2') {
                if (!empty($GwGroupIps[$resource["GateWayID"]])) {
                    foreach ($GwGroupIps[$resource["GateWayID"]] as $val) {
                        $valip = $val['IP'];
                        if (!empty($GwIpIndex) && empty($GwIpIndex[$valip])) {
                            self::log("filter gateway group {$resource['ResID']}: {$valip}");
                            continue;
                        }
                        self::setResInfo($domain, $aResInfo, $aResRdpInfo, $aGwInfo[$valip]);
                    }
                }
            }
        }
        foreach ($GwIps as $GwIp) {
            if (!isset($aGwInfo[$GwIp])) {
                $aGwInfo[$GwIp] = ['appnum' => 0, 'appcfg' => [], 'terminalcfg' => []];
            }
        }
        return ['TradeCode' => 'resconfig', 'AsgList' => $aGwInfo];
    }

    /**
     * 设置资源数据
     *
     * @param array $GwIps 网关IP数组
     *
     * @return array
     * @throws Exception
     */
    public static function getVpnConfigs($GwIps = []): array
    {
        $GwIpIndex = [];
        foreach ($GwIps as $GwIp) {
            $GwIpIndex[$GwIp] = true;
        }
        $aGwInfo = [];
        [$GwGroupIps, $GwAllIps] = GateWayModel::getGroupColumns('IP');
        $resList = ResIPListModel::getList(['column' => 'push']);
        //合并远程应用数据到$resList
        $remoteSerList = RemoteServiceModel::getList(['column' => 'push']);
        $resList = ResourceServiceProvider::getAllGwResource($resList);
        $remoteResList = self::dealRemoteConfig($remoteSerList);
        $resList = array_merge($resList, $remoteResList);
        foreach ($resList as $resource) {
            if ($resource["GateWayType"] === '1') {
                $ip = $GwAllIps[$resource["GateWayID"]] ?? '';
                if (!empty($GwIpIndex) && !empty($ip) && empty($GwIpIndex[$ip])) {
                    self::log("filter gateway {$resource['ResID']}: {$ip}");
                    continue;
                }
                $row = ['ResID' => $resource['ResID'], 'IP' => $resource['IP']];
                $aGwInfo[$ip]['ResList'][$resource['ResID']] = $row;
            } elseif ($resource["GateWayType"] === '2') {
                if (!empty($GwGroupIps[$resource["GateWayID"]])) {
                    foreach ($GwGroupIps[$resource["GateWayID"]] as $valip) {
                        if (!empty($GwIpIndex) && empty($GwIpIndex[$valip])) {
                            self::log("filter gateway group {$resource['ResID']}: {$valip}");
                            continue;
                        }
                        $row = ['ResID' => $resource['ResID'], 'IP' => $resource['IP']];
                        $aGwInfo[$valip]['ResList'][$resource['ResID']] = $row;
                    }
                }
            }
        }
        return ['TradeCode' => 'syncIpRes', 'AsgList' => $aGwInfo];
    }

    /**
     * 获取资源数据
     *
     * @param $key
     * @param $type
     *
     * @return array
     * @throws Exception
     */
    public static function getAppConfigResInfo($key, $type = 'terminalcfg'): array
    {
        if (empty($key)) {
            return [];
        }
        $appConfig = AppConfigRedis::getOne($type);
        $appcfgs = json_decode($appConfig[$type], true);
        return $appcfgs[$key] ?? [];
    }

    /**
     * 下发资源配置
     *
     * @param $params
     */
    public static function setResConfig($params): void
    {
        AppConfigRedis::setOne($params);
    }

    /**
     * 获取单点登录地址
     *
     * @param $refer
     *
     * @return string
     * @throws Exception
     */
    public static function getSingleLoginUrl($refer): string
    {
        $GateConfig = GateConfigRedis::getOne('control');
        $controlUrlPrefix = $GateConfig['ControlUrl'] ?? '';
        $urlData = parse_url($refer);
        $cond = ['URL' => $urlData['host'] ?? '', 'column' => 'agent'];
        $resInfo = ResConfigListModel::getSingle($cond);
        if (!empty($resInfo) && $resInfo['AccessTypeID'] === ACCESS_TYPE_MICRO_APP) {
            $controlConfig = ['DingTalk' => 'dingtalk', 'WeWork' => 'wework', 'FeiShu' => 'feishu'];
            $control = $controlConfig[$resInfo['GrantType']] ?? '';
            $ClickID = str_replace('.', '', (string)microtime(true)) . random_int(1000, 9999);
            $redirectUrl = "{$controlUrlPrefix}/access/{$control}/auth?resDomain={$resInfo['URL']}";
            $paramStr = "module=resource&resDomain={$resInfo['URL']}&redirect_url={$redirectUrl}&ClickID={$ClickID}";
            lib_redis::set('ASG_ResourceOriginUrl:', $ClickID, $refer, 300);
            return "{$controlUrlPrefix}/access/{$control}/jump?$paramStr";
        }
        return '';
    }

    /**
     * 上报健康检查结果
     *
     * @param $gwip
     * @param $data
     *
     * @return bool|false|string
     */
    public static function reportHealthcheck($gwip, $data)
    {
        if (empty($data)) {
            return false;
        }
        $gwIds = WebSocketLinkModel::getAllGwIds();
        foreach ($data as $aItem) {
            $aInfo["ResID"] = $aItem["id"] ?? "0";
            $aInfo["Res_Name"] = $aItem["res_name"] ?? "";
            $aInfo["IP"] = $aItem["ip"] ?? "";
            $aInfo["ResType"] = $aItem["dev_type"] ?? "0";
            $aInfo["CheckTime"] = $aItem["check_time"];
            $aInfo["ResStatus"] = isset($aItem["res_status"]) ? (int)$aItem["res_status"] : "0";
            $aInfo["GwStatus"] = isset($aItem["gw_state"]) ? (int)$aItem["gw_state"] : "1";
            $aInfo["GwID"] = $gwIds[$gwip] ?? '';
            //新增远程应用资源健康检查结果状态
            if (isset($aItem['is_remote_app']) && $aItem['is_remote_app']) {
                $resObj = ResRemoteModel::getOne($aInfo["ResID"]);
            } else {
                $resObj = ResConfigListModel::getOne($aInfo["ResID"], 'health');
            }

            if (!$resObj) {
                continue;
            }
            HealthCheckLogModel::insert($aInfo);
            // 更新资源表的健康状态，暂时没有地方使用
            //新增远程应用资源健康 更新资源状态
            if (isset($aItem['is_remote_app']) && $aItem['is_remote_app']) {
                $isValid = $aInfo["ResStatus"] == 1 ? 1 : 0;
                RemoteAppModel::update($resObj['RemotAppID'], ['IsValid' => $isValid]);
            } else {
                ResConfigListModel::update($aInfo["ResID"], ['Status' => $aInfo["ResStatus"]]);
            }
        }
        return true;
    }

    /**
     * 获取下发远程应用配置数据
     * @param $remoteInfos
     * @return array
     */
    public static function dealRemoteConfig($remoteInfos): array
    {
        //获取远程服务器处理后数据
        $remoteSer = array();
        foreach ($remoteInfos as $remoteInfo) {
            $remoteInfo['IP'] = $remoteInfo['IP'] . ' ' . $remoteInfo['Port'] . ',' . $remoteInfo['RemotePort'];
            unset($remoteInfo['Port'], $remoteInfo['RemotePort']);
            $remoteSer[$remoteInfo['ResID']] = $remoteInfo;
        }

        //获取配置的远程应用ids
        $cond = [];
        $cond['ResType'] = 3;
        $cond['ActionType'] = 0;
        $cond['column'] = 'one';
        $remote_res = ResourceModel::getList($cond);
        $remote_ids = array();
        foreach ($remote_res as $remote_info) {
            $remote_ids[] = $remote_info['ResID'];
        }

        //获取配置的远程应用处理后数据
        $remo_cond = ['column' => 'serve', 'InResID' => $remote_ids];
        $remote_app_config = array();
        $remoteRes = ResRemoteModel::getRelationList($remo_cond);
        foreach ($remoteRes as $remote_info) {
            $tmp_info = $remoteSer[$remote_info['serResID']];
            $tmp_info['ResID'] = $remote_info['ResID'];
            $remote_app_config[] = $tmp_info;
        }
        return $remote_app_config;
    }
}
