<?php

/**
 * Description: redis hash基本类 本地网关和控制中心共用数据、控制中心若不是双机时，下发到其他网关的字段会过滤。
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: BaseRedis.php 175039 2022-05-05 07:33:41Z duanyc $
 */
class BaseRedis
{

    public const  IS_LOG = true;
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASG_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = '';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = '';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = '';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => 'LifeTime',
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = [];

    /**
     * 修改的数据
     * @var mixed
     */
    protected static $changeDatas = [];

    /**
     * 原来的数据
     * @var mixed
     */
    protected static $oldDatas = [];

    /**
     * 获取key
     *
     * @param array $keys
     *
     * @return mixed
     */
    public static function getKey($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }

        return static::TABLE_NAME . ":" . implode('_', $keys);
    }

    /**
     * 获取key,用于批量获取
     *
     * @param array $keys
     *
     * @return mixed
     */
    public static function getKeys($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }
        $returnKeys = [];
        foreach ($keys as $ks) {
            $returnKeys[] = static::TABLE_NAME . ":{$ks}";
        }
        return $returnKeys;
    }

    /**
     * 单条
     *
     * @param string $column
     * @param array $keys
     *
     * @return mixed
     */
    protected static function get($column = '', ...$keys)
    {
        $fields = static::$columns[$column];
        $key = static::getKey($keys);
        $return = lib_redis::getHash(static::PREFIX, $key, $fields);
        if ($return) {
            foreach ($return as $column => $val) {
                if ($val === null) {
                    unset($return[$column]);
                }
            }
        }
        return $return;
    }

    /**
     * 修改
     *
     * @param array $key_values
     * @param array $keys
     *
     * @return boolean
     */
    protected static function set($key_values = [], ...$keys)
    {
        if (empty($key_values)) {
            return false;
        }
        static::$changeDatas = [];
        static::$oldDatas = [];
        $expire = null;
        if (isset($key_values['LifeTime'])) {
            $expire = $key_values['LifeTime'];
        }
        $isVersion = false; // 是否更新版本号
        if (isset($key_values['Version'])) {
            $isVersion = true;
            unset($key_values['Version']);
        }
        $columns = explode(',', static::$localColumns);
        $vals = [];
        foreach ($columns as $column) {
            if (isset($key_values[$column])) {
                $vals[$column] = isset(static::$jsonColumns[$column]) ?
                    json_encode($key_values[$column]) : (string)$key_values[$column];
            }
        }
        // 修改存储的续期时间，有心跳时会用来续期
        $isUpdateLifeTime = false;
        if (isset($key_values['UpdateLifeTime'])) {
            $vals['LifeTime'] = $key_values['UpdateLifeTime'];
            $isUpdateLifeTime = true;
        }
        $key = static::getKey($keys);
        // 过滤掉不变的数据
        $fields = array_keys($vals);
        if ($isVersion) {
            $fields[] = 'Version';
        }
        $oldData = lib_redis::getHash(static::PREFIX, $key, $fields);
        if (!empty($oldData)) {
            foreach ($vals as $column => $val) {
                if (isset($oldData[$column]) && $column !== 'LifeTime' && $val === $oldData[$column]) {
                    unset($vals[$column]);
                }
            }
        }
        if (empty($vals)) {
            return false;
        }
        if ($isVersion) {
            $vals['Version'] = !empty($oldData['Version']) ? $oldData['Version'] + 1 : 1;
        }
        if (!empty(static::$remoteColumns) && !$isUpdateLifeTime) {
            static::push($key, $vals);
        }

        if (!empty($vals['LifeTime'] )&& static::IS_LOG) {
            cutil_php_log(['set', $key, $vals], "model_" . static::TABLE_NAME);
        }

        $redoCount = $key_values['redoCount'] ?? 3;
        for ($i = 0; $i < $redoCount; $i++) {
            $return = lib_redis::hMSetEx(static::PREFIX . $key, $vals, $expire);
            if ($return) {
                break;
            }
            cutil_php_log(['setErr', $key, $return], "model_" . static::TABLE_NAME);
        }
        if ($return) {
            static::$changeDatas = $vals;
            static::$oldDatas = $oldData;
        }
        return $return;
    }

    /**
     * 获取修改的数据
     * @return mixed
     */
    public static function getChangeDatas()
    {
        return self::$changeDatas;
    }

    /**
     * 获取原数据
     * @return mixed
     */
    public static function getOldDatas()
    {
        return self::$oldDatas;
    }

    /**
     * 删除
     *
     * @param array $keys
     *
     * @return boolean
     */
    public static function del(...$keys)
    {
        if (empty($keys)) {
            return false;
        }

        $key = static::getKey($keys);
        if (!empty(static::$remoteColumns)) {
            static::pushDel(static::PREFIX . $key);
        }
        cutil_php_log(['del', $key], "model_" . static::TABLE_NAME);
        return lib_redis::mDel(static::PREFIX, [$key]);
    }

    /**
     * 删除批量
     *
     * @param array $keys
     *
     * @return boolean
     */
    public static function delPatch(...$keys)
    {
        $key = static::getKey($keys);
        $keys = lib_redis::keys(static::PREFIX . $key . "*");
        if (!empty(static::$remoteColumns)) {
            static::pushDelPatch($keys);
        }
        cutil_php_log(['delPatch', $key], "model_" . static::TABLE_NAME);
        return lib_redis::mDel('', $keys);
    }

    /**
     * 删除字段
     *
     * @param string $column
     * @param array $keys
     *
     * @return boolean
     */
    public static function delColumn($column, $keys)
    {
        if (empty($keys) || empty($column)) {
            return false;
        }

        $keys = static::getKeys($keys);
        if (!empty(static::$remoteColumns)) {
            static::pushDelColumn($keys, $column);
        }
        cutil_php_log(['delColumn', $keys], "model_" . static::TABLE_NAME);
        return lib_redis::mDel(static::PREFIX, $keys, $column);
    }

    /**
     * 下发数据到网关
     *
     * @param $key
     * @param array $key_values
     * @param array $GwInfo  网关信息
     */
    public static function push($key, $key_values = [], $GwInfo = []): void
    {
        try {
            $key = static::PREFIX .$key;
            $columns = explode(',', static::$remoteColumns);
            $vals = [];
            foreach ($columns as $column) {
                if (isset($key_values[$column])) {
                    $vals[$column] = $key_values[$column];
                }
            }
            if (empty($vals)) {
                return;
            }
            WorkerUdpSend('MessageToGw', 'setHashRedis', [$key => $vals], $GwInfo);
            if (static::IS_LOG) {
                cutil_php_log(['push', $key, $vals, $GwInfo], "model_" . static::TABLE_NAME);
            }
        } catch (Exception $e) {
            cutil_php_log(['push', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }

    /**
     * 手动推送
     *
     * @param mixed ...$keys
     */
    public static function handPush(...$keys): void
    {
        try {
            $key = static::getKey($keys);
            $columns = explode(',', static::$remoteColumns);
            $key_values = lib_redis::getHash(static::PREFIX, $key, $columns);
            $vals = [];
            foreach ($columns as $column) {
                if (isset($key_values[$column])) {
                    $vals[$column] = $key_values[$column];
                }
            }
            if (empty($vals)) {
                return;
            }
            $pushkey = static::PREFIX . $key;
            WorkerUdpSend('MessageToGw', 'setHashRedis', [$pushkey => $vals]);
            cutil_php_log(['handPush', $pushkey, $vals], "model_" . static::TABLE_NAME);
        } catch (Exception $e) {
            cutil_php_log(['handPush', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }

    /**
     * 推送全部key
     *
     * @param $gwIp
     * @param $return
     * @param int $limit
     */
    public static function getPushAll($gwIp, &$return, $limit = 10): void
    {
        $columns = explode(',', static::$remoteColumns);
        $keys = lib_redis::keys(static::PREFIX . static::TABLE_NAME);
        $data = [];
        foreach ($keys as $key) {
            $key_values = lib_redis::getHash('', $key, '');
            $vals = [];
            foreach ($columns as $column) {
                if (isset($key_values[$column])) {
                    $vals[$column] = $key_values[$column];
                }
            }
            if (isset($key_values['LifeTime'])) {
                $vals['LifeTime'] = $key_values['LifeTime'];
            }
            $data[$key] = $vals;
            if (count($data) >= $limit) {
                $return[] = ['TradeCode' => 'setHashRedis', 'AsgList' => [$gwIp => $data]];
                $data = [];
            }
        }
        if (!empty($data)) {
            $return[] = ['TradeCode' => 'setHashRedis', 'AsgList' => [$gwIp => $data]];
        }
    }

    /**
     * 下发删除数据到网关
     *
     * @param $key
     */
    protected static function pushDel($key): void
    {
        try {
            WorkerUdpSend('MessageToGw', 'delHashRedis', [$key]);
            cutil_php_log(['pushDel', $key], "model_" . static::TABLE_NAME);
        } catch (Exception $e) {
            cutil_php_log(['pushDel', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }

    /**
     * 下发删除多条数据到网关
     *
     * @param $keys
     */
    protected static function pushDelPatch($keys): void
    {
        try {
            WorkerUdpSend('MessageToGw', 'delHashRedis', $keys);
            cutil_php_log(['pushDelPatch', $keys], "model_" . static::TABLE_NAME);
        } catch (Exception $e) {
            cutil_php_log(['pushDelPatch', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }

    /**
     * 下发删除hash数据字段到网关
     *
     * @param $keys
     * @param $column
     */
    protected static function pushDelColumn($keys, $column): void
    {
        try {
            foreach ($keys as $k => $key) {
                $keys[$k] = static::PREFIX . $key;
            }
            $data = ['keys' => $keys, 'column' => $column];
            WorkerUdpSend('MessageToGw', 'delHashColumnRedis', $data);
            cutil_php_log(['pushDelColumn', $keys], "model_" . static::TABLE_NAME);
        } catch (Exception $e) {
            cutil_php_log(['pushDelColumn', $e->getMessage(), $e->getCode()], "model_" . static::TABLE_NAME);
        }
    }
}
