<?php
/**
 * Description: 资源代理
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: AgentServiceProvider.php 175039 2022-05-05 07:33:41Z duanyc $
 */

use Services\Common\Services\RoleService;
use Services\Common\Services\DESService;

class AgentServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'agent';

    /**
     * 应用类型名字
     * @var array
     */
    public static $AppNames = [
        ACCESS_TYPE_COMMON => '普通应用',
        ACCESS_TYPE_OAUTH2 => 'Oauth2应用',
        ACCESS_TYPE_FORM => '表单代填',
        ACCESS_TYPE_RDP => 'RDP应用',
        ACCESS_TYPE_VNC => 'VNC应用',
        ACCESS_TYPE_SSH => 'SSH应用',
        ACCESS_TYPE_TELENT => 'Telent应用',
        ACCESS_TYPE_REMOTE_APP => '远程应用',
        ACCESS_TYPE_MICRO_APP => '微应用',
        ACCESS_TYPE_CUSTOM_APP => '自定义应用',
        ACCESS_TYPE_NO_AUTH => '免认证应用',//透明代理
    ];

    /**
     * 获取资源访问地址
     *
     * @param $LifeTime
     * @param $Token
     * @param $Resource
     * @param string $RedictUrl
     * @param string $urlPrefix
     *
     * @return mixed
     * @throws Exception
     */
    public static function getAccessUrl($LifeTime, $Token, $Resource, $RedictUrl = '', $urlPrefix = '')
    {
        $currentHost = getServerAddr();
        $GateInfo = GatewayServiceProvider::getGateIpInfo($Resource);
        if (empty($GateInfo)) {
            T(21148006);
        }
        //判断是否为内网，如果为内网且开启了内网访问实际地址，则直接返回实际地址
        if ($Resource['IsNat'] == 1 && IS_INTERNAL) {
            return $Resource['RealProtocol'] . "://" . $Resource['DomainName'] . ":" . $Resource['RealPort'] . $Resource['ConnUrl'];
        }
        $Lang = hlp_common::getLang();
        $SetPort = $GateInfo['NetworkPort'] ?: '443';
        // 资源set_cookies地址处理
        $winRunRemote = ResourceServiceProvider::isWinRunRemote($Resource);
        switch ($Resource['AccessTypeID']) {
            case ACCESS_TYPE_RDP:
            case ACCESS_TYPE_VNC:
            case ACCESS_TYPE_SSH:
            case ACCESS_TYPE_TELENT:
            case ACCESS_TYPE_REMOTE_APP:
                $SetHost = trim($GateInfo['NetworkIP'], '/');
                break;
            default:
                $SetHost = trim($Resource['URL'] ?: $Resource['ConnUrl'], '/');
                $SetPort = hlp_net::isInternalRequest() ? $GateInfo['NetworkPort'] : $GateInfo['NetworkPortEx'];
        }
        if (!empty($RedictUrl)) {
            $RedictUrl = urlencode($RedictUrl);
        } else {
            $proto = isHttps() ? 'https' : ' http';
            $urlPrefix = $urlPrefix ?: "$proto://{$currentHost}";
            $IS_CLIENT = $Resource['IS_CLIENT'] ?? IS_CLIENT;
            $OS_TYPE = $Resource['OS_TYPE'] ?? OS_TYPE;
            $ClickID = $Resource['ClickID'] ?? '';
            $query = "resId={$Resource['ResID']}&token={$Token}&addAuth={$Resource['AddAuth']}&client=" .
                $IS_CLIENT . "&ClickID={$ClickID}&os_type=" . $OS_TYPE . "&local_lguage_set=" . $Lang;
            if ($winRunRemote) {
                $query .= "&winRunRemote=1";
            }
            $query = DESService::desEcbEncrypt($query);
            $RedictUrl = urlencode("{$urlPrefix}/access/resourceCheck?access_token={$query}");
        }
        if ($winRunRemote) {
            return urldecode($RedictUrl);
        }
        $GwUrl = "{$Resource['ProxyProtocol']}://{$SetHost}:{$SetPort}/access/gateway/setCookies";
        //判断是否开启依赖站点，如果开启则需要设置对应的泛域名cookie
        $cookieDomain = '';
        if (!empty($Resource['DependSite'])) {
            is_string($Resource['DependSite']) && $Resource['DependSite'] = json_decode($Resource['DependSite'], true);
            $isOpen = $Resource['DependSite']['isOpen'] ?? 0;
            if ($isOpen == 1 && !empty($Resource['DependSite']['domain'])) {
                $cookieDomain = '.' . $Resource['DependSite']['domain'];
            }
        }
        $params = [
            'resId' => $Resource['ResID'],
            'AccessTypeID' => $Resource['AccessTypeID'],
            'token' => $Token,
            'expireTime' => $LifeTime ?: 7200,
            'appHost' => $SetHost,
            'redictUrl' => $RedictUrl,
            'cookieDomain' => $cookieDomain,
            'createTime' => time() . random_int(1000, 9999),
            'addAuth' => !empty($Resource['AddAuth']) ? '1' : '0',
            'local_lguage_set' => $Lang
        ];
        $query = http_build_query($params);
        $query = DESService::desEcbEncrypt($query);
        return "{$GwUrl}?access_token={$query}";
    }

    /**
     * 网关代理资源设置cookies
     *
     * @param $params
     *
     * @return bool
     * @throws Exception
     */
    public static function setCookies($params): bool
    {
        self::log("Token: {$params['Token']}");
        if (empty($params['CreateTime']) || strlen($params['CreateTime']) !== 14) {
            self::log("Error CreateTime: {$params['CreateTime']}");
            gotoErrorUrl("0x1013");
        }
        $Time = (int)(substr($params['CreateTime'], 0, 10));
        $CurrTimes = time();

        if ($CurrTimes > $Time + 60 || $CurrTimes < $Time - 60) {
            self::log("Error CreateTime: {$Time}: {$CurrTimes}");
            gotoErrorUrl("0x1014");
        }

        $Session = SessionRedis::getOne($params['Token'], 'cookie');
        if (empty($Session['Uuid'])) {
            self::log("Error Token: {$params['Token']}:" . var_export($Session, true));
            gotoSdpErrUrl("0x0012", $params['Token']);
        }
        $aInfo = [];
        $aInfo['Uuid'] = $Session['Uuid'];
        $aInfo['SessionID'] = $params['Token'];
        $aInfo['ExpireTime'] = $params['ExpireTime'] <= 0 ? 86400 : $params['ExpireTime'];
        $aInfo['AppHost'] = $params['AppHost'];
        $aInfo['AddAuth'] = $params['AddAuth'];
        $aInfo['DeviceID'] = $Session['DeviceID'] ?? '0';
        $aInfo['ResID'] = $params['ResID'];
        $aInfo['TypeID'] = $params['AccessTypeID'];
        $json = json_encode($aInfo);
        $Cookies = pubEncrypt($json);
        self::log("Cookies: {$Cookies} :");
        SessionRedis::setOne($params['Token'], ['LifeTime' => $Session['LifeTime']]);
        $Expire = time() + $aInfo['ExpireTime'];
        setcookie('ZTP_Cookie', $Cookies, $Expire, "/");
        setcookie('ZTP_FormInit', '0', $Expire, "/"); // 0表示未初始化，1表示已初始化
        setcookie('BalancerId', $Session['Uuid'], $Expire, "/");
        //如果是开启了依赖站点则需要设置泛域名对应的cookie
        if (!empty($params['cookieDomain'])) {
            $cookieDomain = $params['cookieDomain'];
            setcookie('ZTP_Cookie', $Cookies, $Expire, "/", $cookieDomain);
            setcookie('BalancerId', $Session['Uuid'], $Expire, "/", $cookieDomain);
        } else {
            // 如果不是依赖站点，则清除掉当前域名的母域名对应的cookie，避免升级前已经访问过cookie留下了旧的数据
            $domainParts = explode('.', $aInfo['AppHost']);
            // 判断域名是否已经为顶级域名了
            if (count($domainParts) > 2) {
                $parentDomain = substr($aInfo['AppHost'], strpos($aInfo['AppHost'], '.') + 1);
                setcookie('ZTP_Cookie', '', time() - 3600, '/', '.' . $parentDomain);
            }
        }
        self::log("setcookie: {$params['RedictUrl']}: " . var_export($aInfo, true));
        gotoUrl($params['RedictUrl']);
        return true;
    }

    /**
     * 代理资源处理
     *
     * @param $Session
     * @param $Resource
     *
     * @return bool
     * @throws Exception
     */
    public static function agentHandle($Session, $Resource): bool
    {
        if (empty($Session) || empty($Resource)) {
            return false;
        }
        $CreateTime = date("Y-m-d H:i:s");
        $Content = "【{$Session['UserName']}】 在 【{$CreateTime}】 访问了 ";
        $AppName = self::$AppNames[$Resource['AccessTypeID']] ?? '';
        $Content .= "【{$AppName}】 【{$Resource['ResName']}】";
        $Client = SystemServiceProvider::getClientTypeId($Session['OsType'], $Session['Client']);
        $Data = ['Client' => $Client, 'Content' => $Content, 'ResName' => $Resource['ResName']];
        $Data['AccessType'] = hlp_common::getAccessType($Resource['AccessTypeID']);
        switch ($Resource['AccessTypeID']) {
            case ACCESS_TYPE_OAUTH2:
                self::oauth2Handle($Session, $Resource, $Data);
                break;
            case ACCESS_TYPE_FORM:
                self::formHandle($Session, $Resource, $Data);
                break;
            case ACCESS_TYPE_MICRO_APP:
                self::microAppHandle($Session, $Resource, $Data);
                break;
            case ACCESS_TYPE_RDP:
            case ACCESS_TYPE_VNC:
            case ACCESS_TYPE_SSH:
            case ACCESS_TYPE_TELENT:
            case ACCESS_TYPE_REMOTE_APP:
                self::rdpHandle($Session, $Resource, $Data);
                break;
            default:
                self::commonHandle($Session, $Resource, $Data);
        }

        return true;
    }

    /**
     * 代理失败处理
     *
     * @param $Session
     * @param $Resource
     */
    public static function agentHandleFail($Session, $Resource)
    {
        $CreateTime = date("Y-m-d H:i:s");
        $Content = "【{$Session['UserName']}】 在 【{$CreateTime}】 访问了 ";
        $AppName = self::$AppNames[$Resource['AccessTypeID']] ?? '';
        $Content .= "【{$AppName}】 【{$Resource['ResName']}】";
        $Client = SystemServiceProvider::getClientTypeId($Session['OsType'], $Session['Client']);
        $Data = ['Client' => $Client, 'Content' => $Content, 'Status' => 1, 'ResName' => $Resource['ResName']];
        $Data['AccessType'] = hlp_common::getAccessType($Resource['AccessTypeID']);
        ResourceServiceProvider::addUserLog($Session, 28, $Resource['ResID'], $Data);
    }

    /**
     * Oauth2代理资源处理
     *
     * @param $Session
     * @param $Resource
     * @param $Data
     */
    public static function oauth2Handle($Session, $Resource, $Data): void
    {
        $ResponseType = 'code';
        if ($Resource['GrantType'] !== 'authorization_code') {
            $ResponseType = 'token';
        }
        $data = [
            'client_id' => $Resource['ClientId'],
            'response_type' => $ResponseType,
            'scope' => '',
            'redirect_uri' => $Resource['RedirectUrl'],
        ];
        $query = http_build_query($data);
        $key = "66FFOV";
        $auth2 = [
            'authorize' => Base64EnExt($query),
            'userId' => $Session['Uuid'],
            'token' => $Session['Token'],
            'time' => time(),
        ];
        $authQuery = http_build_query($auth2);
        $sign = md5("{$key}|{$authQuery}");
        $RedictUrl = HTTPS_PROTOCOL . getServerAddr() . "/backend/auth2?{$authQuery}&sign={$sign}";
        ResourceServiceProvider::addUserLog($Session, 28, $Resource['ResID'], $Data);
        hlp_page::showOauth2($RedictUrl);
    }

    /**
     * 表单代填代理资源处理
     *
     * @param $Session
     * @param $Resource
     * @param $Data
     * @throws Exception
     */
    public static function formHandle($Session, $Resource, $Data): void
    {
        $userCond = ['UserID' => $Session['Uuid'], 'ResID' => $Resource['ResID'], 'column' => 'user'];
        $userinfo = ResourceAccountModel::getSingle($userCond);
        if ($Resource['GrantType'] != 'auto_fill') {
            if (empty($userinfo) || empty($userinfo['UserName']) || empty($userinfo['Password'])) {
                gotoErrorUrl("0x1005");
            }
        }
        $GateInfo = GatewayServiceProvider::getGateIpInfo($Resource);
        $Host = trim($Resource['URL'] ?: $Resource['ConnUrl'], '/');
        $Port = $GateInfo['NetworkPortEx'] ?: '443';
        $NewUrlPrefix = "{$Resource['ProxyProtocol']}://{$Host}:{$Port}";

        $UrlData = parse_url($Resource['LoginURL']);
        $RedictUrl = "{$NewUrlPrefix}{$UrlData['path']}?{$UrlData['query']}";
        ResourceServiceProvider::addUserLog($Session, 28, $Resource['ResID'], $Data);
        hlp_page::showCommon($RedictUrl);
    }

    /**
     * RDP应用、VNC应用、SSH应用、Telent应用 代理资源处理
     *
     * @param $Session
     * @param $Resource
     * @param $Data
     */
    public static function rdpHandle($Session, $Resource, $Data): void
    {
        ResourceServiceProvider::addUserLog($Session, 28, $Resource['ResID'], $Data);
        $GateInfo = GatewayServiceProvider::getGateIpInfo($Resource);
        $Host = trim($GateInfo['NetworkIP'], '/');
        $UrlPrefix = "{$Resource['ProxyProtocol']}://{$Host}:{$GateInfo['NetworkPort']}";
        $data = ['resId' => $Resource['ResID'], 'deviceid' => $Session['DeviceID'], 'AddAuth' => $Resource['AddAuth']];
        $key = Base64EnExt(doXorEncrypt(json_encode($data), 'rdpkey'));
        $query = "webterminal=1&resId={$Resource['ResID']}&deviceid={$Session['DeviceID']}&key={$key}";
        $query = DESService::desEcbEncrypt($query);
        gotoUrl($UrlPrefix . "/access/ui/index.html?access_token={$query}#/webterminal");
    }

    /**
     * 微应用 代理资源处理
     *
     * @param $Session
     * @param $Resource
     * @param $Data
     * @throws Exception
     */
    public static function microAppHandle($Session, $Resource, $Data): void
    {
        $GateInfo = GatewayServiceProvider::getGateIpInfo($Resource);
        $Host = trim($Resource['URL'] ?: $Resource['ConnUrl'], '/');
        $Port = $GateInfo['NetworkPortEx'] ?: '443';
        $NewUrlPrefix = "{$Resource['ProxyProtocol']}://{$Host}:{$Port}";
        ResourceServiceProvider::addUserLog($Session, 28, $Resource['ResID'], $Data);
        $OriginUrl = lib_redis::get('ASG_ResourceOriginUrl:', $Resource['ClickID']);
        if (empty($OriginUrl)) {
            gotoErrorUrl("0x1016");
        }
        lib_redis::del('ASG_ResourceOriginUrl:', $Resource['ClickID']);
        $RedictUrl = preg_replace(hlp_check::$RegexUrlPrefix, $NewUrlPrefix, $OriginUrl);
        gotoUrl($RedictUrl);
    }

    /**
     * 普通 代理资源处理
     *
     * @param $Session
     * @param $Resource
     * @param $Data
     */
    public static function commonHandle($Session, $Resource, $Data): void
    {
        $GateInfo = GatewayServiceProvider::getGateIpInfo($Resource);
        $Host = trim($Resource['URL'] ?: $Resource['ConnUrl'], '/');
        $Port = hlp_net::isInternalRequest() ? $GateInfo['NetworkPort'] : $GateInfo['NetworkPortEx'];
        $RedictUrl = "{$Resource['ProxyProtocol']}://{$Host}:{$Port}{$Resource['ConnUrl']}";
        ResourceServiceProvider::addUserLog($Session, 28, $Resource['ResID'], $Data);
        hlp_page::showCommon($RedictUrl);
    }

    /**
     * 获取资源账号
     *
     * @param $UserID
     * @param $ResID
     *
     * @return mixed
     */
    public static function getResourceAccount($UserID, $ResID)
    {
        if (empty($UserID) || empty($ResID)) {
            return false;
        }

        $Cond = ['UserID' => $UserID, 'ResID' => $ResID, 'column' => 'user'];
        $Account = ResourceAccountModel::getSingle($Cond);

        if (!empty($Account)) {
            $Key = 'ztpkey';
            $Account['UserName'] = Base64EnExt(doXorDecrypt(hexTostring($Account['UserName']), $Key));
            $Account['Password'] = Base64EnExt(doXorDecrypt(hexTostring($Account['Password']), $Key));
        }
        return $Account;
    }

    /**
     * 保存资源账号密码
     *
     * @param $UserID
     * @param $ResID
     * @param $UserName
     * @param $Password
     *
     * @return bool|int
     * @throws Exception
     */
    public static function saveResourceAccount($UserID, $ResID, $UserName, $Password)
    {
        if (empty($UserID) || empty($ResID)) {
            return false;
        }

        $Resource = ResourceServiceProvider::getResource($ResID);
        if (empty($Resource)) {
            T(********);
        }

        $Cond = ['UserID' => $UserID, 'ResID' => $ResID, 'column' => 'user'];
        $Account = ResourceAccountModel::getSingle($Cond);
        $Key = 'ztpkey';
        $Params = ['UserName' => stringTohex(doXorEncrypt($UserName, $Key))];
        $Params['Password'] = stringTohex(doXorEncrypt($Password, $Key));

        if ($Params['UserName'] === $Account['UserName'] && $Params['Password'] === $Account['Password']) {
            return true;
        }

        if (!empty($Account)) {
            return ResourceAccountModel::update($Account['ResAccountID'], $Params);
        }

        $Params['UserID'] = $UserID;
        $Params['ResID'] = $ResID;
        return ResourceAccountModel::insert($Params);
    }

    /**
     * 通过refer获取资源
     *
     * @param $refer
     * @param $column
     *
     * @return mixed
     */
    public static function getResourceByRefer($refer, $column = 'one')
    {
        static $resInfo = null;
        if ($resInfo !== null) {
            return $resInfo;
        }
        $urlData = parse_url($refer);
        //解决某些特殊情况下host带了端口的情况,https://15974.innerztptest.ifztp.cn:17443:17443/?
        if (!empty($urlData['host']) && strpos($urlData['host'], ':') !== false) {
            $urlData['host'] = substr($urlData['host'], 0, strpos($urlData['host'], ':'));
        }
        $cond = ['URL' => $urlData['host'] ?? '', 'column' => $column];
        $resInfo = ResConfigListModel::getSingle($cond);
        return $resInfo;
    }

    /**
     * 获取资源地址
     *
     * @param $token
     * @param $urlPrefix
     * @param $session
     * @param $resInfo
     *
     * @return string
     * @throws Exception
     */
    public static function getResourceUrl($token, $urlPrefix, $session, $resInfo): string
    {
        try {
            $redictUrl = '';
            LoginServiceProvider::checkAddAuth($session['Uuid'], $token, $resInfo);
            return self::getAccessUrl($session['LifeTime'], $token, $resInfo, $redictUrl, $urlPrefix);
        } catch (Exception $e) {
            // 需要附加认证则跳资源平台
            self::log("aAddAuth check {$token}");
            return '';
        }
    }

    /**
     * 表单代填第一次资源账号密码处理
     * @param $UserID
     * @param $Resource
     * @throws Exception
     */
    public static function handFirstResourceAccount($UserID, $Resource): void
    {
        $ResID = $Resource['ResID'];
        $Account = self::getResourceAccount($UserID, $ResID);
        if (!empty($Account['UserName']) || !empty($Account['Password'])) {
            return;
        }
        $FormData = json_decode($Resource['OtherField'], true);
        $userInfo = AuthUserModel::getOne($UserID);
        $userName = '';
        $password = doXorDecrypt(hexTostring($userInfo['Password2']), 'infogoasm');
        if ($Resource['GrantType'] == 'auto_fill') {
            if (!isset($FormData['FillAuthType'])) {
                return;
            }
            switch (strval($FormData['FillAuthType'])) {
                case 'custom_set':
                    return;
                case 'system_set':
                    $userName = $userInfo['UserName'];
                    break;
                case 'phone_pwd':
                    $userName = $userInfo['Tel'];
                    break;
                case 'mail_pwd':
                    $userName = $userInfo['Email'];
                    break;
                case 'mail_pre_pwd':
                    if (strpos($userInfo['Email'], '@')) {
                        $userName = substr($userInfo['Email'], 0, strpos($userInfo['Email'], '@'));
                    }
                    break;
            }
        }
        $fillValueArr = [
            'system_account' => $userInfo['UserName'],
            'mail' => $userInfo['Email'],
            'mail_pro' => strpos($userInfo['Email'], '@') ? substr($userInfo['Email'], 0, strpos($userInfo['Email'], '@')) : '',
            'phone' => $userInfo['Tel'],
            'fixed' => $FormData['AccountDataValue']
        ];
        if ($Resource['GrantType'] == 'custom_fill') {
            $userName = isset($fillValueArr[$FormData['AccountDataType']]) ? $fillValueArr[$FormData['AccountDataType']] : '';
            $fillValueArr['system_account'] = $password;
            $fillValueArr['fixed'] = $FormData['PwdDataValue'];
            $password = isset($fillValueArr[$FormData['PwdDataType']]) ? $fillValueArr[$FormData['PwdDataType']] : '';
        }
        self::saveResourceAccount($UserID, $ResID, $userName, $password);
    }
}
