<?php

/**
 * Description: 配置角色映射TRoleRelation表
 * User: <EMAIL>
 * Date: 2021/05/27 16:27
 * Version: $Id: RoleRelationModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class RoleRelationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TRoleRelation';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'one' => 'ConfigID, RoleID'
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['ConfigID'])) {
            $where .= "AND ConfigID = ".self::setData($cond['ConfigID']);
        }

        if (isset($cond['RoleID'])) {
            $where .= "AND RoleID = ".self::setData($cond['RoleID']);
        }

        if (isset($cond['Groups'])) {
            $where .= "AND TRoleRelation.Groups = ".self::setData($cond['Groups']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
