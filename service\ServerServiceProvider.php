<?php

use Common\Facades\NACServiceFacade;

/**
 * Description: 系统服务暴露
 * User: <EMAIL>
 * Date: 2021/06/18 10:02
 * Version: $Id: ServerServiceProvider.php 175145 2022-05-06 07:53:42Z huyf $
 */
class ServerServiceProvider extends BaseServiceProvider
{
    /**
     * 获取服务器配置
     *
     * @return array
     */
    public static function getAsmConfig(): array
    {
        // ASM服务器配置
        return parse_ini_file(PATH_ETC . "access.ini");
    }

    /**
     * 获取内外网地址
     * @return array
     */
    public static function getAsmServerAddress()
    {
        $config = self::getAsmConfig();
        $data = ['ExternalUrl' => ''];
        $isIpV6 = hlp_net::isIpv6Ip();
        $ServerIn = $isIpV6 ? $config['ServerIPv6In'] : $config['ServerIPv4In'];
        $data['InternalUrl'] = "{$config['WebProtocolIn']}://{$ServerIn}:{$config['WebPortIn']}";
        $ServerEx = $isIpV6 ? $config['ServerIPv6Ex'] : $config['ServerIPv4Ex'];
        $data['ExternalUrl'] = "{$config['WebProtocolEx']}://{$ServerEx}:{$config['WebPortEx']}";
        return $data;
    }

    /**
     * 获取服务器$_SERVER信息
     *
     * @return mixed
     * @throws Exception
     */
    public static function getServerInfo()
    {
        $return = $_SERVER;
        $return['SERVERIP'] = getServerIPName(getServerAddr());
        $return['SERVERPORT'] = (int)$return["SERVER_PORT"];
        foreach ($return as $key => $item) {
            if (stripos($key, "SERVER_") !== false || stripos($key, "HTTP_") !== false) {
                $return[$key] = "";
            }
        }
        $return['DOCUMENT_URI'] = '';
        $return['SCRIPT_NAME'] = '';
        $return['PHP_SELF'] = '';
        $return['USER'] = '';
        unset($return['QUERY_STRING'], $return['REQUEST_URI'], $return['HOME'], $return['DOCUMENT_ROOT'], $return['SCRIPT_FILENAME'], $return['PATH']);
        return $return;
    }

    /**返回IPAM联动配置
     * @return string[]
     * @throws Exception
     */
    public static function ipamConfig(): array
    {
        $path = PATH_HTML . '/update/config.ini';
        $config = [
            'linkage' => '0',
            'linkage_url' => '',
            'dev_regulation_access_on_off' => '0',
            'dev_regulation_location' => '0'
        ];
        if (file_exists($path)) {
            $conf = get_ini_info($path);
            $config['linkage'] = $conf['linkage'] ?? '0';
            $config['dev_regulation_access_on_off'] = $conf['dev_regulation_access_on_off'] ?? '0';
            $config['dev_regulation_location'] = $conf['dev_regulation_location'] ?? '0';
            $config['linkage_url'] = isset($conf['linkage_url'], $conf['third_device_id']) && $conf['linkage_url'] ? rtrim($conf['linkage_url'], '/') . '/ip/new?asm_id=' . $conf['third_device_id'] : '';
        }
        return $config;
    }

    /**
     * 获取配置
     *
     * @param $isCache
     * @return array|mixed
     * @throws Exception
     */
    public static function getServerTDict($isCache = true)
    {
        $dictTypes = [
            'CLIENTCHECK',//检查前是否显示提示页面
            'INDEXREG',//注册页面显示的注册项
            'DSM',//是否启用DSM联动
            'LDAP',
            'SMS',
            'UKey',
            'ShowAllDevice',
            'ADMINISTRATORINFO',//用户个性配置
            'AUTHPARAM',//身份认证参数
            'ADDOMAIN',//AD域认证参数
            'User',//用户名/密码认证服务器
            'ChangePassword',//允许修改密码
            'ClientAuth',//客户端自定义配置
            'UserDevBind',//开启设备使用者同步配置
            'QRAuth',//二维码认证设置
            'GUESTAUTH',//来宾参数
            'UserAuthControl',//用户认证配置
            'DomainFake',//ad域防伪造配置
            'SSO',//sso配置
            'ZTP',//零信任配置
        ];
        $cache = GM('D_101', 'all');
        if ($isCache && $cache !== null && is_string($cache)) {
            return unserialize($cache);
        }
        $dict = DictModel::getAllByTypes($dictTypes);
        $dict['DingTalkConfig'] = AuthServiceProvider::getAuthDomainConfig('DingTalk');
        $dict['FeiShuConfig'] = AuthServiceProvider::getAuthDomainConfig('FeiShu');
        $dict['WeWorkConfig'] = AuthServiceProvider::getAuthDomainConfig('WeWork');
        $dict['UKey']['ukeyConfig'] = ServerServiceProvider::getUkeyConfig($dict['UKey']);
        include_config('dict');
        $dictCache = [];
        foreach ($GLOBALS['DICT'] as $type => $data) {
            foreach ($data as $key => $v) {
                if (isset($dict[$type][$key])) {
                    $dictCache[$type][$key] = $dict[$type][$key];
                }
            }
        }
        $dictCache['ADDOMAINSVR'] = self::getAdDomainSevInfo();
        self::initDomainAddress($dictCache);
        SM('D_101', 'all', serialize($dictCache));
        return $dictCache;
    }

    /**
     * 设置server/info数据返回
     *
     * @param $return
     * @param $isCache
     *
     * @throws Exception
     */
    public static function setServerDict(&$return, $isCache = true)
    {
        $dictList = self::getServerTDict($isCache);
        foreach ($dictList as $key => $dict) {
            $return[$key] = $dict;
        }
    }

    /**
     * 用于获取当前ad域服务器的所有配置
     * 当前只是用来获取是否开启单点登录
     *
     * @return array
     * *<AUTHOR>
     */
    public static function getAdDomainSevInfo(): array
    {
        $adDomains = AdDomainModel::getAll('autoLogin');
        $autoLogin = [];
        if (is_array($adDomains)) {
            foreach ($adDomains as $adDomain) {
                $autoLogin[strtolower($adDomain['Domain'])] = $adDomain['AutoLogin'];
            }
        }
        return $autoLogin;
    }

    /**
     * 封装iOS的下载地址
     *
     * @return string
     * @throws Exception
     */
    public static function getIosDownloadUrl()
    {
        $path = PATH_ETC . "config-version.ini";
        $aV = read_inifile($path);
        $version = $aV['engine'];
        $safedomain = 'mdm.infogo.com.cn';
        $version = str_replace('.', '_', $version);
        $plist = "asm_ios_ipas/{$version}/asm.plist";
        $url = "https://{$safedomain}/{$plist}?" . time();
        return "itms-services://?action=download-manifest&url={$url}";
    }

    /**
     * 获取全局语言
     *
     * @return mixed|string
     * @throws Exception
     */
    public static function getGlobalLang($lang)
    {
        if (file_exists(PATH_ETC . "version.ini")) {
            $ini_arr = read_inifile(PATH_ETC . "version.ini");
            $globalLang = trim($ini_arr['lang']);
        } else {
            $globalLang = "zh";
        }
        //浪潮前台安检页面多语言支持项目修改
        $globalLang = $lang != "" ? $lang : $globalLang;
        if ($globalLang != 'en') {
            $globalLang = 'zh';
        }
        return $globalLang;
    }

    /**
     * 获取例外列表
     *
     * @param $return
     *
     * @return int
     */
    public static function getIpException($return)
    {
        //例外列表 --2011-11-16 yancheng 添加
        $ipexist_list = '0';
        if ($return['IPADD'] != "" && $return['CLIENTCHECK']['FastAuth'] == 0) {
            $ipstr = $return['CLIENTCHECK']['exceptionip'];
            if ($ipstr != "") {
                $ipstr_arr1 = explode(",", $ipstr);
                foreach ($ipstr_arr1 as $str1) {
                    $iparr0 = explode(":", $str1);
                    $iparr = explode("-", $iparr0[0]);
                    $start = ipToNum($iparr[0]);
                    $Current = ipToNum($return['IPADD']);
                    $stop = ipToNum($iparr[1]);
                    if ($Current >= $start && $Current <= $stop) {
                        $ipexist_list = '1';
                    }
                }
            }
        }
        return $ipexist_list;
    }

    /**
     * 获取产品信息
     *
     * @return mixed
     */
    public static function getProductInfo()
    {
        include_once PATH_HTML . "/oem/copyright.php";
        $gProductInfo = isset($gProductInfo) ? $gProductInfo : [];
        foreach ($gProductInfo as $key => $value) {
            $gProductInfo[$key] = gbkToUtf8($value);
        }
        return $gProductInfo;
    }

    /**
     * 复制客户端/控件安装文件
     *
     * @param $return
     * @param $agentPromotion
     *
     * @throws Exception
     */
    public static function initClientInstallFile(&$return, $agentPromotion): void
    {
        // 特殊安装包状态值判断
        self::hasSpecialInstallFile($return);

        $http = isHttps() ? 'https' : 'http';
        $serverInfo = self::dealServerIP($return);
        $path = PATH_ETC . "sdp.ini";//系统映射设置配置，拼成入网时windows客户端下载名称
        $sdpInfo = get_ini_info($path);
        $isInternal = IS_INTERNAL;
        $winClientNameArr = [
            !$isInternal && !empty($sdpInfo['AccessAddress']) ? $sdpInfo['AccessAddress'] : $serverInfo[0],
            !$isInternal && !empty($sdpInfo['Port']) ? $sdpInfo['Port'] : $return['SERVERPORT'],
            $http,
            $isInternal ? '0' : '1',
        ];
        // ASM服务器配置
        $return['AsmConfig'] = parse_ini_file(PATH_ETC . "access.ini");
        $return['EnvSwitch'] = self::getEnvSwitchConfig($sdpInfo);
        $winClientNameStr = implode('_', $winClientNameArr);
        $return['winClientNameStr'] = $winClientNameStr;
        $return['IsInternal'] = $isInternal ? '1' : '0';
        $promotionConf = read_inifile(PATH_ETC . "asm/asc/etc/tbridge_agent_promotion.ini");
        $return['PushMethod'] = $promotionConf['PushMethod'] ?? '1';
        NacClientServiceProvider::noticeMakeClientInstallFile([
            'devSerial'        => self::getDevSerial(),
            'agentPromotion'   => $agentPromotion,
            'winClientNameStr' => $winClientNameStr,
            'protocol'         => $http,
            'serverIp'         => $return['MANAGER_IP'],
            'serverPort'       => $return['SERVERPORT'],
            'specialInstall'   => $return['SpecialInstall']
        ]);
    }

    /**
     * 获取切换配置
     * @param $sdpInfo
     * @return array
     * @throws Exception
     */
    public static function getEnvSwitchConfig($sdpInfo = false)
    {
        if (empty($sdpInfo)) {
            $sdpInfo = $sdpInfo = get_ini_info(PATH_ETC . "sdp.ini");
        }

        return ['AllowUserSwitch' => $sdpInfo['allowUserSwitch'],
            'SwitchMode' => $sdpInfo['switchMode']];
    }

    /**
     * 设置服务器类型/ASC等
     *
     * @param $return
     *
     * @throws Exception
     */
    public static function initServerDevinfo(&$return)
    {
        $return['OutServer'] = GatewayServiceProvider::getOutServer();
        $return['ControlUrl'] = self::getAsmControlUrl();
        // 在iOS纯内网浏览器打开时，需要返回内网跳转地址；否则前端使用正确入网地址ControlUrl跳转
        if (OS_TYPE === OSTYPE_IOS && !hlp_common::isDeployExternal()) {
            $return['ControlIosAccessUrl'] = hlp_page::getJumpBaseurl();
        }
        if (file_exists(PATH_ETC . "devinfo.ini")) {
            $aBbidgeT = read_inifile(PATH_ETC . 'devinfo.ini');
            $return['DEV_TYPE'] = $aBbidgeT['devtype'];
            if ($aBbidgeT['devtype'] === DEVTYPE_DASC) {
                $return['ControlUrl'] = self::getGotoPrefixUrl();
            }
            if (strpos($return['DEV_TYPE'], "asc") !== false &&
                strpos($return['DEV_TYPE'], "dasc") === false) {
                $return['IsAsc'] = true;
                if (file_exists(PATH_ETC . "asc.ini")) {
                    $return['ASMCONFIG'] = read_inifile(PATH_ETC . 'asc.ini');
                }
            } else {
                $return['IsAsc'] = false;
            }
        } else {
            $return['IsAsc'] = false;
        }
    }

    /**
     * 初始化桥信息
     *
     * @param $return
     *
     * @throws Exception
     */
    public static function initBridgeInfo(&$return)
    {
        $aBridge = read_inifile(PATH_ETC . 'asm/asc/etc/tbridge_private.ini');
        $return['BRIDGE_TYPE'] = $aBridge['BRIDGE_TYPE'];
        $aBbidgeT = self::getControllerConfig();
        $return['protocol'] = 'http:';
        if (stripos($aBbidgeT['URL'], HTTPS_PROTOCOL) === 0) {
            $return['protocol'] = 'https:';
        } elseif (!IS_INTERNAL) {
            // 外网固定为https
            $protocol = get_ini_info(PATH_ETC . "sdp.ini", 'Protocol');
            $return['protocol'] = empty($protocol) ? 'https:' : ($protocol .':');
        }
        $return['RE_URL'] = $aBbidgeT['URL'];
        $return['OPENNAT'] = $aBbidgeT['OPENNAT'];
        $return['NATIP'] = $aBbidgeT['NATIP'];
    }

    /**
     * 获取全局统一的url（在ASM上调用）。
     * @throws Exception
     */
    public static function getAsmControlUrl()
    {
        $sdp = read_inifile(PATH_ETC . "sdp.ini");
        $Protocol = $sdp['Protocol'] ?? "https";
        if (!empty($sdp['AccessAddress'])) {
            $AccessPort = $sdp['Port'] ?: "443";
            $urlPrefix = "{$Protocol}://{$sdp['AccessAddress']}:{$AccessPort}";
        } else {
            $urlPrefix = self::getGotoPrefixUrl();
        }
        return $urlPrefix;
    }

    /**
     * 获取入网内网准入跳转url前缀（在ASM上调用）
     *
     * @return string
     * @throws Exception
     */
    public static function getGotoPrefixUrl(): string
    {
        $aBbidgeT = self::getControllerConfig();
        if (empty($aBbidgeT['URL'])) {
            return '';
        }
        $urlData = parse_url($aBbidgeT['URL']);
        $portStr = !empty($urlData['port']) ? ":{$urlData['port']}" : '';
        return "{$urlData['scheme']}://{$urlData['host']}{$portStr}";
    }

    /**
     * 获取控制器入网web协议
     *
     * @param string $ControlUrl
     *
     * @return string
     * @throws Exception
     */
    public static function getControllerProtocol($ControlUrl = ''): string
    {
        if (empty($ControlUrl)) {
            $aBbidgeT = self::getControllerConfig();
            $ControlUrl = $aBbidgeT['URL'];
        }
        if (stripos($ControlUrl, 'https://') === 0) {
            return 'https';
        }
        return 'http';
    }

    /**
     * 初始化dom信息
     *
     * @param $return
     */
    public static function initDomInfo(&$return)
    {
        ///个性化项目显示隐藏DOM节点
        //需要显示的Dom节
        $return["DomShowControl"] = [];
        //需要隐藏的Dom节点
        $return["DomHideControl"] = [];
        /* $DomControls = PrivateSetModel::getList(['Type' => 'Dom', 'column' => 'info'], false, 0, 100);

        foreach ($DomControls as $Item) {
            if ($Item['isShow'] == '1') {
                $return["DomShowControl"][] = $Item["ProjectMod"];
            } else {
                $return["DomHideControl"][] = $Item["ProjectMod"];
            }
        } */
    }

    /**
     * 初始化域后缀
     *
     * @param $return
     */
    public static function initDomainAddress(&$return)
    {
        //需要显示的AD域后缀 Address API_VERSION < 1 时使用，后续可去掉 todo
        $return["Address"] = [];
        $return["AdDomainAddress"] = [];
        $return["LDAPAddress"] = [];
        $ADAddress = AdDomainModel::getAll('domain');
        foreach ($ADAddress as $AD) {
            $return["Address"][] = $AD["Domain"];
            $return["AdDomainAddress"][] = $AD["Domain"];
        }

        //需要显示的LDAP域后缀
        $LDAPAddress = LdapDomainModel::getAll('domain');
        foreach ($LDAPAddress as $LDAP) {
            $return["Address"][] = $LDAP["Domain"];
            $return["LDAPAddress"][] = $LDAP["Domain"];
        }
    }

    /**
     * 获取个性化设置
     *
     * @return array
     */
    public static function getPrivateSet()
    {
        ///个性化自定义页面
        $PrivateSet = [];
        $PrivateSet["message"] = "/a/message.html";
        $PrivateSet["auth"] = "/a/auth.html";
        $PrivateSet["reg"] = "/a/reg.html";
        $PrivateSet["safecheck"] = "/a/safecheck.html";
        $PrivateSet["submit"] = "/a/submit.php";
        $PrivateSet["insertcontrol"] = "/a/installcontrol.html";
        $PrivateSet["safecheckdetail"] = "/a/safecheckdetail.html";

        /* $cond = ['Type' => 'ClientCheck', 'isShow' => 1];
        $isPrivate = PrivateSetModel::getList($cond, false, 0, 10);
        foreach ($isPrivate as $item) {
            if (empty($item['ClientCheckName']) || empty($item['ClientCheckURL'])) {
                continue;
            }
            if (is_file(PATH_HTML . $item["ClientCheckURL"])) {
                $PrivateSet[$item["ClientCheckName"]] = $item["ClientCheckURL"];
            }
        } */
        return $PrivateSet;
    }

    /**
     * 初始化管理IP
     *
     * @param $return
     * @param $basIP
     *
     * @throws Exception
     */
    public static function initManagerIp(&$return, $basIP)
    {
        // yanzj 20180730 增加IPV6 获取IPv6管理地址
        $deveth = get_ini_info(PATH_ETC . 'deveth.ini.noback', ['manager_ipv6_addrs', 'managerip', 'emergency']);
        $manager_ipv6addrarr = explode(',', $deveth['manager_ipv6_addrs']);
        $hainfo = get_ini_info(PATH_ETC . 'ha.ini', ['Mode', 'MANAGER_VIRTUAL_IP', 'MANAGER_VIRTUAL_IPv6']);
        $managerip = $deveth['managerip'];
        if ($hainfo['Mode'] === 'DUALHOST') {
            if (isIP($hainfo['MANAGER_VIRTUAL_IP'], 4)) {
                $managerip = $hainfo['MANAGER_VIRTUAL_IP'];
            }
            if (isIP($hainfo['MANAGER_VIRTUAL_IPv6'], 6)) {
                array_unshift($manager_ipv6addrarr, $hainfo['MANAGER_VIRTUAL_IPv6']);
            }
        }
        $return['MANAGER_IPV6_ADDRESS'] = $manager_ipv6addrarr;
        $return['MANAGER_IP'] = $managerip;
        // todo 这一块单独提供接口给前端去调用 不要让server/info接口业务越来越复杂
        $return['isEmergency'] = (int)$deveth['emergency'];
        $return['isArubaPortalAuth'] = self::getPortalConfig($return['IPADD']);
        // 如果是Aruba portal准入方式，切换紧急时候，交换机重定向之后直接放开网络 add by renchen 2021-05-14
        if ($return['isEmergency'] && $return['isArubaPortalAuth'] !== false && array_key_exists('Aruba', $return['isArubaPortalAuth'])) {
            // 不需要认证，安检，注册直接放开
            if ($basIP === '' || ($basIP !== '' && $return['isArubaPortalAuth']['Aruba']['BasIP'] === $basIP)) {
                $netParams = [
                    'device_id' => 0,
                    'ip' => $return['IPADD'],
                    'iplistid' => 1,
                    'bas_ip' => $basIP
                ];
                NACServiceFacade::access("WebAccess:initManagerIp", [$netParams], 'Access');
            }
        }
        $return['isCiscoAccess'] = false;
        if (isset($return['isArubaPortalAuth']['Cisco'])) {
            foreach ($return['isArubaPortalAuth']['Cisco']['IP'] as $item) {
                list($start, $end) = explode(',', $item);
                $ip = ipToNumber($return['IPADD']);
                if ($ip <= $end && $ip >= $start) {
                    $return['isCiscoAccess'] = '*******';
                    break;
                }
            }
        }
    }

    /**
     * 获取portal配置
     * @param $ip string 终端IP
     * @param $flag bool
     * @return array|bool
     * @throws Exception
     */
    public static function getPortalConfig($ip = '', $flag = false)
    {
        $ip = !empty($ip) ? $ip : getRemoteAddress();
        if (is_file(PATH_ETC . 'portalCnf.ini')) {
            $enablePortal = get_ini_info(PATH_ETC . 'portalCnf.ini', 'EnablePortal');
            if ((int)$enablePortal === 1) {
                $filter = array('IPType' => isIP($ip, 6) ? 2 : 1);
                if ($flag) {
                    $filter['IP'] = ipToNumber($ip);
                    $filter['Protocol'] = 'Cisco';
                    $res = PortalCfgModel::getIPList($filter);
                    if (!empty($res)) {
                        return '*******';
                    }
                } else {
                    // 查询是否有使用Aruba的协议
                    $res = PortalCfgModel::getIPList($filter);
                    $return = array();
                    foreach ($res as $item) {
                        $return[$item['Protocol']]['BasIP'] = $item['BasIP'];
                        $return[$item['Protocol']]['IP'][] = $item['StartIP'] . ',' . $item['EndIP'];
                    }
                    return $return;
                }
            }
        }

        return false;
    }

    /**
     * 处理数据
     *
     * @param $return
     */
    public static function dealServerData(&$return)
    {
        $return['SMS']['TelAuthRemark'] = strlen($return['SMS']['TelAuthRemark']) > 0 ?
            explode("||", $return['SMS']['TelAuthRemark']) : "";
        $return['AUTHPARAM']['AllowAuthType'] = strlen($return['AUTHPARAM']['AllowAuthType']) > 0 ?
            explode("|", $return['AUTHPARAM']['AllowAuthType']) : "";

        $authTypeToRemove = [];
        if (OS_TYPE === OSTYPE_LINUX) {
            $authTypeToRemove = ['Finger'];
        } elseif (OS_TYPE === OSTYPE_MAC) {
            $authTypeToRemove = ['UKey', 'Finger'];
        }
        if (!empty($authTypeToRemove)) {
            $return['AUTHPARAM']['AllowAuthType'] = array_values(array_diff($return['AUTHPARAM']['AllowAuthType'], $authTypeToRemove));
        }
        // 修改BUGID 13508 来宾3.0后，AllowAuthType不再控制来宾认证的菜单
        $return['AUTHPARAM']['AllowAuthType'] = array_values(array_diff($return['AUTHPARAM']['AllowAuthType'], ['Guest']));
        if (LANG == 'en') {
            $return['ADMINISTRATORINFO']['Tel'] = $return['ADMINISTRATORINFO']['Tel_en'];
        }

        // 处理来宾自助数据
        if (API_VERSION < '1.0') {
            return;
        }
        $types = ['', '_en'];
        foreach ($types as $type) {
            $i = 1;
            while (!empty($return['CLIENTCHECK']["GuestRequireExpand_{$i}{$type}"])) {
                $guestExpand = explode('|', $return['CLIENTCHECK']["GuestRequireExpand_{$i}{$type}"]);
                $Select = $guestExpand[0];
                if ($Select != '0' && $Select != '1') {
                    unset($return['CLIENTCHECK']["GuestRequireExpand_{$i}{$type}"]);
                }
                $i++;
            }
        }
    }

    /**
     * 已废弃
     * 处理飞书的数据
     *
     * @param $return
     */
    public static function dealFeiShuData(&$return)
    {
        /* 飞书IP段判断 */
        if ($return['IPADD'] != "" && $return['FeiShuConfig']['IP_1'] != '') {
            $ipstr = $return['FeiShuConfig']['IP_1'];
            $ipstr_arr1 = explode(",", $ipstr);
            foreach ($ipstr_arr1 as $str1) {
                $iparr0 = explode(":", $str1);
                $iparr = explode("-", $iparr0[0]);
                $start = ipToNum($iparr[0]);
                $Current = ipToNum($return['IPADD']);
                $stop = ipToNum($iparr[1]);
                if ($Current >= $start && $Current <= $stop) {
                    $return['FeiShuConfig']['appid'] = $return['FeiShuConfig']['appid_1'];
                    $return['FeiShuConfig']['appsecret'] = $return['FeiShuConfig']['appsecret_1'];
                }
            }
        }
        unset($return['FeiShuConfig']['appid_1'], $return['FeiShuConfig']['appsecret_1']);
        unset($return['FeiShuConfig']['IP_1']);
    }

    /**
     * windows客户端下载文件名，兼容负载环境场景
     * @param $serverInfo
     * @return array
     */
    public static function dealServerIP($serverInfo)
    {
        $serverObj = [$serverInfo['MANAGER_IP']];
        $ipArr = explode('_', $serverInfo['SERVERIP']);
        $ip = $ipArr[0];
        $host = parse_url($serverInfo['RE_URL'], PHP_URL_HOST);
        if ($ip === $host && isHavIP($ip)) {
            $serverObj = [$ip];
        }
        if (isset($ipArr[1]) && !empty($ipArr[1])) {
            $serverObj = [!empty($serverObj[0]) ? $serverObj[0] . '_' . $ipArr[1] : $ipArr[1]];
        }
        if (!empty($host) && !isIP($host)) {
            $serverObj = [$host];
        }
        unset($ipArr, $ip, $host);
        return $serverObj;
    }

    /**
     * 初始化语言相关字段
     *
     * @param $lang
     * @param $return
     */
    public static function initServerLang($lang, &$return)
    {
        if ($lang != "") {
            $return['PrivateSet']['submit'] = $return['PrivateSet']['submit'] . "?local_lguage_set=" .
                ($lang == "en" ? "en" : "zh");
            if ($lang == "en") {
                // 新版本不处理
                if (API_VERSION >= '1.0') {
                    return;
                }
                $return['ADMINISTRATORINFO']["sTitle"] = $return['ADMINISTRATORINFO']["sTitle_en"];
                $return['ClientAuth']["ClientUser"] = $return['ClientAuth']["ClientUser_en"];
                $return['ClientAuth']["ClientPassword"] = $return['ClientAuth']["ClientPassword_en"];
                foreach ($return['CLIENTCHECK'] as $key => $value) {
                    if (!empty($return['CLIENTCHECK'][$key . "_en"])) {
                        $return['CLIENTCHECK'][$key] = $return['CLIENTCHECK'][$key . "_en"];
                    }
                }
            }
        }
    }

    /**
     * 获取全局配置
     * @return int|mixed
     */
    public static function getGlobalVarConf()
    {
        $aGlobalVar = DictModel::getAll("GlobalVar");
        $istaff = $aGlobalVar['IsStaff'];
        // 山东浪潮项目中英文版切换
        $SDLCControl = []; // PrivateSetModel::getSingle(['Type' => 'Dom', 'ProjectMod' => 'sdlc_3506_2016_30']);
        $SDLCControl["isShow"] = "1";
        if ($SDLCControl["isShow"] == "1" && LANG == "en_US") {
            $istaff = 1;
            $aGlobalVar["GuestUser"] = $aGlobalVar["GuestUser_en"];
            $aGlobalVar["StaffUser"] = $aGlobalVar["StaffUser_en"];
        }
        if ($istaff) {
            return $aGlobalVar;
        } else {
            return 1;
        }
    }

    /**
     * 代理发送心跳
     *
     * @param $deviceId
     *
     *   set_cache(ctx_set, szAgentID, "TDevice.LastTime" ,    pNowTime);
     *   set_cache(ctx_set, szAgentID, "TDevice.LastChangeTime" , pNowTime);
     *   set_cache(ctx_set, szAgentID, "TDevice.ChangeFlags" , "ass");
     *   set_cache(ctx_set, szAgentID, "TNacOnLineDevice.LastHeartTime" , pNowTime);
     *   set_cache(ctx_set, szAgentID, "TDevice.Online","1");
     *
     * @return array
     * @throws Exception
     */
    public static function sendHeartbeat($deviceId)
    {
        $token = LoginServiceProvider::getLoginToken();
        $url = "http://127.0.0.1:37527/SendKeepalive.html?deviceid={$deviceId}&token={$token}";
        return curl($url, 'GET', '', 1, 1);
    }

    /**
     * https加密方式信息，默认是普密（pm）
     * @return int|mixed
     */
    public static function getEncryptionConf()
    {
        $path = PATH_ETC . 'Encryption.ini';
        $Encryption = 'pm';
        if (file_exists($path)) {
            $Encryption = get_ini_info($path, 'Encryption');
        }
        return $Encryption;
    }

    /**
     * 添加国密Ukey的驱动路径配置
     * @return array
     */
    public static function getUkeyConfig($ukey): array
    {
        $ukeyConfigs = [];
        if ($ukey['GMUkeyState']) {
            if (!empty($ukey['GMUkeyConfig'])) {
                $GMUkeyConfigs = explode(',', $ukey['GMUkeyConfig']);
                foreach ($GMUkeyConfigs as $GMUkeyConfig) {
                    $GMUkey = explode('||||', $GMUkeyConfig);
                    $ukeyConfig = [];
                    $ukeyConfig['Name'] = $GMUkey[0];
                    $ukeyConfig['Path'] = $GMUkey[1];
                    $ukeyConfig['Type'] = $GMUkey[2];
                    $ukeyConfig['Enable'] = $GMUkey[3];
                    $ukeyConfigs['Item'][] = $ukeyConfig;
                }
            }
        }
        return $ukeyConfigs;
    }

    /**
     * 获取主题色配置
     * @return void
     */
    public static function getThemeColor(&$return)
    {
        //主题色数组
        $themeColorJson = DictModel::getOneItem('SystemSet', 'ThemeColor');
        $themeConfigArray = json_decode($themeColorJson['ItemValue'], true);

        //获取三端的主题色，并且匹配拼接数组
        $platforms = ['ThemeColorMobile', 'ThemeColorPc'];
        foreach ($platforms as $platformItem) {
            $theme = $return['ADMINISTRATORINFO'][$platformItem] ?? '#536ce6';

            //循环从数组匹配颜色
            $colorArray = null;
            foreach ($themeConfigArray as $item) {
                if ($theme == $item['primaryColor']) {
                    $colorArray = $item;
                    break;
                }
            }
            //未获取到匹配的主色调,则取第一个主题色配色信息
            if (is_null($colorArray)) {
                $colorArray = reset($themeConfigArray);
            }

            $return['ADMINISTRATORINFO'][$platformItem] = $colorArray;
        }
    }

    /**
     * 判断是否下载特殊安装包
     * 特殊安装包不存在,使前端走通用安装包下载
     * @param $return
     * @return void
     */
    public static function hasSpecialInstallFile(&$return)
    {
        if (file_exists(PATH_HTML . "/download/clientzip/IsSetup_Agent.zip.exe")) {
            $return['SpecialInstall'] = 1;
            // 如果发现特殊安装包不存在，则下载通用安装包
            if (!file_exists(PATH_HTML . "/download/IsSetup_Special_Agent.exe")){
                $return['SpecialInstall'] = 0;
            }
        }
    }

    /**
     * 监控平台调用时，金仓组建双机时特殊处理返回值，避免失联
     * @return array|false
     * @throws Exception
     */
    public static function initHaInfo()
    {
        if (!isKingBaseHaOpening()) {
            return false;
        }

        return [
            'BRIDGE_TYPE'       => '',
            'ADMINISTRATORINFO' => [
                'CompanyName' => ''
            ]
        ];
    }
}
