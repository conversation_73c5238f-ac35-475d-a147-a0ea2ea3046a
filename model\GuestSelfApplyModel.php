<?php

/**
 * Description: 来宾自助申请
 * User: <EMAIL>
 * Date: 2021/05/26 12:46
 * Version: $Id: GuestSelfApplyModel.php 175785 2022-05-13 01:41:52Z huyf $
 */

class GuestSelfApplyModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGuestSelfApply';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'        => '*',
        'info'     => 'UserID,Status,ApproveName,Reason,AccessType,DeviceID',
        'data'     => 'Name,Tel',
        'userid'   => 'UserID',
        'infoList' => 'Name,Unit,B.Tel,A.DeviceID',
        'auditrecord' => 'ID,Status',
        'approveInfoList' => 'A.ID,A.Name,A.Tel,A.Unit,A.IP,A.AccessType,A.InsertTime,A.ValidTime,B.AllowRegionIDs,B.AllowTime',
        'max'      => 'DeviceID, MAX(ID)'
    ];

    /**
     * 获取批准来宾账户列表
     *
     * @param $cond
     *
     * @return array|bool
     */
    public static function getInfoList($cond)
    {
        if (empty($cond['approveName']) || empty($cond['approveType'])) {
            return false;
        }
        self::$data = [];
        $columnName = $cond['column'] ?? 'infoList';
        $infoColumn = self::$columns[$columnName];
        $maxColumn = self::$columns['max'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "select {$infoColumn} from " .
            "( SELECT {$maxColumn} AS ID FROM {$table['name']} " .
            "WHERE STATUS = 1 and ApproveUserName = ".self::setData($cond['approveName'])." " .
            "and ApproveType = ".self::setData($cond['approveType'])." GROUP BY DeviceID ) A " .
            "LEFT JOIN {$table['name']} B ON A.DeviceID = B.DeviceID AND A.ID = B.ID " .
            "INNER JOIN TDevice C ON A.DeviceID = C.DeviceID ";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getall($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取来宾待批准账户信息
     * @param $cond
     * @return array|bool
     */
    public static function getApproveInfoList($cond)
    {
        $cond['GreetUserID'] = $cond['GreetUserID'] ?? 0;
        self::$data = [];
        $LikeGreetUserID = "%{$cond['GreetUserID']}%";
        $LikeGreetDeviceID = "%{$cond['GreetDeviceID']}%";
        $columnName = $cond['column'] ?? 'approveInfoList';
        $infoColumn = self::$columns[$columnName];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$infoColumn} FROM {$table['name']} A " .
            "left join TGuestRelation B on A.UserID = B.UserID ".
            "WHERE A.STATUS = 0 ".
            " and (A.SelfApplyGreetUserIDs like ".self::setData($LikeGreetUserID)." ".
            "or A.SelfApplyGreetDeviceIDs like ".self::setData($LikeGreetDeviceID).")".
            "and 30>=(UNIX_TIMESTAMP(now()) - (UNIX_TIMESTAMP(A.ValidTime))) ORDER BY A.InsertTime desc";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getall($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Status'])) {
            $where .= "AND Status = ".self::setData($cond['Status']);
        }

        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        if (isset($cond['AuditType'])) {
            $where .= "AND AuditType = ".self::setData($cond['AuditType']);
        }

        if (isset($cond['GreetType']) && $cond['GreetType'] == "empty") {
            $where .= "AND (GreetType is null or GreetType ='')";
        }

        if (isset($cond['SelfApplyGreetUserIDs'])) {
            $where .= "AND SelfApplyGreetUserIDs like '%".$cond['SelfApplyGreetUserIDs']."%'";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
