<?php

/**
 * Description: 普通代理资源表
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: ResConfigListModel.php 174952 2022-04-29 13:33:57Z duanyc $
 */

class ResConfigListModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResConfigList';
    public const PRIMARY_KEY = 'ResID';
    protected static $columns = [
        '*'    => '*',
        'one'  => 'ResID,AccessTypeID,URL,ConnUrl,ProxyProtocol,ProxyPort,HttpHeader,GateWayID,GateWayType,GrantType,LoginURL,OtherField,Content,RealProtocol,DomainName,DependSite,RealPort',
        'gateway' => 'DISTINCT GateWayID,GateWayType',
        'agent' => 'ResID,AccessTypeID,GrantType,ClientId,RedirectUrl,IsUseMainAcount,UserNameField,'.
                   'PasswordField,LoginSubmitURL,HomePageURL,ProxyProtocol,URL,ConnUrl,LoginURL,ProxyPort,GateWayType,GateWayID',
        'list' => 'Icon,Remark,Tel,Username,AccessTypeID,ResID,Status',
        'power' => 'AccessTypeID,ResID,URL,IsParseDomain,GateWayType,GateWayID,DomainName as IP,DependSite',
        'group' => 'ResID,GateWayID,GateWayType',
        'guacamole' => 'ResID,AccessTypeID,RealProtocol,RealPort,DomainName',
        'microApp' => 'ResID,AccessTypeID,URL,ConnUrl,ProxyProtocol,ProxyPort,GateWayID,GateWayType,'.
                 'ManuFactory as corpId,AppID as agentid,ClientId as appkey,ClientSecret as secret',
        'microAppAgent' => 'RealProtocol,RealPort,DomainName,HomePageURL',
        'health' => 'IsPassive',
        'push' => 'DomainName as IP,ResID,GateWayID,GateWayType'
    ];

    /**
     * 获取资源的网关ID和类型
     *
     * @return mixed
     */
    public static function getResGateways()
    {
        static::$data = [];
        $column = static::$columns['group'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, static::$data);
        $allDatas = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $allDatas[$row['ResID']] = [
                    'GateWayID' => $row['GateWayID'], 'GateWayType' => $row['GateWayType']];
            }
        }
        return $allDatas;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (!empty($cond['notAuthAccessTypeID'])) {
            $where .= " AND AccessTypeID  NOT IN (" . self::setArrayData($cond['notAuthAccessTypeID']) . ")";
        }

        if (!empty($cond['InAccessTypeID'])) {
            $where .= " AND AccessTypeID  IN (" . self::setArrayData($cond['InAccessTypeID']) . ")";
        }

        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }

        if (isset($cond['URL'])) {
            $where .= "AND URL = ".self::setData($cond['URL']);
        }

        if (isset($cond['ProxyPort'])) {
            $where .= "AND ProxyPort = ".self::setData($cond['ProxyPort']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
