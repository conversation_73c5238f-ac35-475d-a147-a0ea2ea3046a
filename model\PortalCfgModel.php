<?php

/**
 * Description: TPortalCfg表
 * User: <EMAIL>
 * Date: 2021/08/03 15:53
 * Version: $Id: PortalCfgModel.php 170669 2022-03-09 06:14:05Z lihao $
 */
class PortalCfgModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TPortalCfg';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'all' => 'BasIP,Protocol',
        '*' => '*',
    ];

    /**
     * 所有条目
     *
     * @param $column
     * @return mixed
     */
    public static function getAll($column = 'all')
    {
        $column = self::$columns[$column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']}";
        return lib_database::getAll($sql, $table['index']);
    }

    /**
     * 获取Portal准入的终端IP范围
     *
     * @param $cond array 联表查询后的过滤条件
     *
     * @return mixed
     */
    public static function getIPList($cond = [])
    {
        self::$data = [];
        $sql = 'select BasIP,Protocol,StartIP,EndIP from TPortalCfg left join TPortalCfgSegment on TPortalCfg.ID=TPortalCfgSegment.PortalCfgID';
        $where = !empty($cond) ? self::getWhere($cond) : '';
        $sql .= $where;
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['BasIP'])) {
            $where .= 'AND BasIP = ' . self::setData($cond['BasIP']);
        }
        if (isset($cond['Protocol'])) {
            $where .= ' AND Protocol = ' . self::setData($cond['Protocol']);
        }
        if (isset($cond['IP'])) {
            $where .= ' AND StartIP <= ' . self::setData($cond['IP']) . ' AND EndIP >= ' . self::setData($cond['IP']);
        }
        if (isset($cond['IPType'])) {
            $where .= ' AND IPType = ' . self::setData($cond['IPType']);
        }

        return !empty($where) ? " WHERE 1 = 1 {$where}" : "";
    }
}
