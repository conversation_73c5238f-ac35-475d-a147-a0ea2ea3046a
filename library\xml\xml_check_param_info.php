<?php

/**
 * Description: 解析安检参数信息 原basic.class.php
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: xml_check_param_info.php 151780 2021-07-31 10:55:14Z duanyc $
 */


/**
 * ***************************************** 解析安检参数信息 **********************************************
 */
class xml_check_param_info extends cls_xml
{
    /**
     * 解析安检报文
     * @return array
     */
    public function parseXml()
    {
        $this->res ['CheckIntervalDay'] = $this->xml->CheckIntervalDay;
        $this->res ['IsSafeCheck'] = $this->xml->IsSafeCheck;
        $this->res ['NoPassCutNet'] = $this->xml->NoPassCutNet;
        $this->res ['ClientCheckTitle'] = $this->xml->ClientCheckTitle;
        $this->res ['InstallAssistant'] = $this->xml->InstallAssistant;
        $this->res ['AssistantName'] = $this->xml->AssistantName;
        $this->res ['HideAssistant'] = $this->xml->HideAssistant;
        $this->res ['ReCheck'] = $this->xml->ReCheck;
        $this->res ['ReCheckTime'] = $this->xml->ReCheckTime;
        $this->res ['Disturb'] = $this->xml->Disturb;
        $this->res ['IsUseMiniAuthWnd'] = $this->xml->IsUseMiniAuthWnd;
        $this->res ['IsOpenNetworkBeforeLogon'] = $this->xml->IsOpenNetworkBeforeLogon;
        $this->res ['DelDevice'] = $this->xml->DelDevice;
        $this->res ['AboutTitle'] = $this->xml->AboutTitle;
        $this->res ['AboutMessage'] = $this->xml->AboutMessage;
        $this->res ['IsNavigation'] = $this->xml->Navigation->IsShow;
        if (is_object($this->xml->Navigation->Item)) {
            foreach ($this->xml->Navigation->Item as $Item) {
                $row ['Name'] = $Item->Name;
                $row ['Url'] = htmlspecialchars($Item->Url);
                $this->res ["NavigationList"] [] = $row;
            }
        }
        $this->convertEncode();
        return $this->res;
    }
}
