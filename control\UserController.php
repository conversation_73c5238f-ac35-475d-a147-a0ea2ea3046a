<?php

/**
 * Description: 用户，角色，部门，位置等交易
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: UserController.php 165781 2021-12-24 16:28:46Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class UserController extends BaseController
{
    /**
     * 修改密码，对应老交易 changepass和m_changepass
     *
     * @return array
     * @throws Exception
     */
    public function changePass()
    {
        $action = request('action', 'request');
        $ChangePassword = ConfigServiceProvider::getDictAll('ChangePassword');
        if ($action === 'getconfig') {
            return $ChangePassword;
        }
        // 如果没有开启修改密码，则不允许通过接口修改密码
        // 20221110 jlj要求，仅控制身份认证界面是否有修改入口，接口仍可修改
        /*if (isset($ChangePassword['IsChangePassword']) && (int)$ChangePassword['IsChangePassword'] !== 1) {
            T(21137013);
        }*/
        // 修改密码
        $username = request('user', 'request');
        $type = request('type', 'request', 'User');
        $type = !empty($type) && $type != 'undefined' ? $type : 'User';
        $priKey= getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');
        /** 经确认非密文解密后返回原文 兼容base64 */
        $onenewpass = request('onenewpass', 'request');
        // 兼容移动端 原m_changepass接口
        if (!empty($onenewpass)) {
            $oldpassword = Base64DeExt(request('oldpassword', 'request'));
            $newpassword = Base64DeExt($onenewpass);
            $newpassword2 = Base64DeExt(request('newpassword', 'request'));
        } else {
            // 兼容PC端 原changepass接口
            $oldpassword = Base64DeExt(request('oldpassword', 'request'));
            $newpassword = Base64DeExt(request('newpassword', 'request'));
            $newpassword2 = Base64DeExt(request('newpassword2', 'request'));
        }
        if (empty($newpassword) || empty($newpassword2)) {
            T(21137001);
        }
        openssl_private_decrypt($oldpassword, $oldpassword, $priKey, OPENSSL_PKCS1_PADDING);
        openssl_private_decrypt($newpassword, $newpassword, $priKey, OPENSSL_PKCS1_PADDING);
        openssl_private_decrypt($newpassword2, $newpassword2, $priKey, OPENSSL_PKCS1_PADDING);
        if (empty($newpassword) || empty($newpassword2) || empty($oldpassword)) {
            T(21100002);
        }
        cutil_php_log("old:{$oldpassword},new:{$newpassword},new2:{$newpassword2}", 'passwd');
        $userAuthService = AuthServiceProvider::initAuthService(['servicePrefix' => 'User', 'deviceId' => '']);
        $ret = $userAuthService->verifyPassword($ChangePassword, $newpassword, $username);
        if ($ret['pwdCheckResCode'] !== STRING_TRUE) {
            throw new Exception($ret['pwdCheckResMsg']);
        }
        $oldpass= dataEncrypt($oldpassword, 'md5withsm3');
        $newpass= dataEncrypt($newpassword, 'md5withsm3');
        $newpass2= dataEncrypt($newpassword2, 'md5withsm3');
        // 2次密码是否一致
        if ($newpass !== $newpass2) {
            T(21137002);
        }
        if ($oldpass === $newpass) {
            T(21137016);
        }
        $result = UserServiceProvider::changePassword($type, $username, $oldpass, $newpass, $newpassword);
        if (!empty($result)) {
            $this->errmsg = $result['ok'];
        }

        return $result;
    }

    /**
     * 移动端修改密码，对应老交易 m_changepass
     *
     * @return array
     * @throws Exception
     */
    public function mChangePass()
    {
        $return = ['status' => '0'];
        try {
            $this->changePass();
            $return['msg'] = L(21137011);
            $return['status'] = '1';
        } catch (Exception $e) {
            $return['msg'] = $e->getMessage();
        }
        return $return;
    }

    /**
     * 认证账号注册
     *
     * @return array
     * @throws Exception
     */
    public function register()
    {
        $params = [];
        $DeviceID = request('DeviceID', 'request', 0, 'int');
        $params['UserName'] = trim(request('UserName', 'request'));
        $params['TrueNames'] = trim(request('TrueNames', 'request'));
        $Password = Base64DeExt(request('Password', 'request'));
        $CPassword = Base64DeExt(request('ConfirmPassword', 'request'));
        $params['DepartID'] = request('DepartID', 'request', 0, 'int');
        $params['Tel'] = request('Tel', 'request');
        $params['Email'] = request('Email', 'request');
        $priKey= getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');
        openssl_private_decrypt($Password, $Password, $priKey, OPENSSL_PKCS1_PADDING);
        openssl_private_decrypt($CPassword, $CPassword, $priKey, OPENSSL_PKCS1_PADDING);
        hlp_check::checkUsername($params['UserName'], L(21137009));
        hlp_check::checkEmpty($params['TrueNames']);
        hlp_check::checkEmpty($params['DepartID']);
        hlp_check::checkEmpty($Password);
        hlp_check::checkEmpty($CPassword);
        hlp_check::checkEmpty($DeviceID);
        // 2次密码是否一致
        cutil_php_log("Password: {$Password}, CPassword: {$CPassword}", 'UserRegister');
        if ($Password != $CPassword) {
            T(21137002);
        }
        $ChangePassword = ConfigServiceProvider::getDictAll('ChangePassword');
        $userAuthService = AuthServiceProvider::initAuthService(['servicePrefix' => 'User', 'deviceId' => '']);
        $ret = $userAuthService->verifyPassword($ChangePassword, $Password, $params['UserName']);
        if ($ret['pwdCheckResCode'] !== STRING_TRUE) {
            throw new Exception($ret['pwdCheckResMsg']);
        }
        // 注册
        $ip = getRemoteAddress();
        $Password = dataEncrypt($Password, 'md5withsm3');
        $CPassword = stringTohex(doXorEncrypt($CPassword, "infogoasm"));
        $uparams = ['UserName' => $params['UserName'], 'Password' => $Password, 'DepartID' => $params['DepartID'],
            'Position' => '', 'Tel' => $params['Tel'], 'Email' => $params['Email'], 'TrueNames' => $params['TrueNames'],
            'Password2' => $CPassword, 'IP' => $ip, 'IsDisabled' => 1, 'Type' => 'User'];
        $userId = UserServiceProvider::registerUser($uparams, $DeviceID);
        if (empty($userId)) {
            T(21137007);
        }
        $this->errmsg = L(21137008, ['username' => $params['UserName']]);
        return [];
    }

    /**
     * 获取位置信息，对应老交易 get_device_location
     *
     * @return array
     * @throws Exception
     */
    public function location()
    {
        $action = request('action', 'request');
        $locationId = request('locationid', 'request');
        $return = [];
        switch ($action) {
            case 'alllocation':
                $return = DeviceServiceProvider::getAllLocation();
                break;
            case 'firstlocation':
                $return = DeviceServiceProvider::getDeviceLocation('0');
                break;
            case 'childlocation':
                $return = DeviceServiceProvider::getDeviceLocation($locationId);
                break;
        }
        return $return;
    }

    /**
     * 位置列表
     * @throws Exception
     */
    public function locationList()
    {
        $parentLocationId = request('parentLocationId', 'request', 0, 'int');
        $keyword = request('keyword', 'request');
        $start = request('start', 'request', 0, 'int');
        $limit = request('limit', 'request', 100, 'int');
        $cond = [];
        if ($parentLocationId >= 0) {
            $cond['UpID'] = $parentLocationId;
        }
        if (!empty($keyword)) {
            $cond['Keyword'] = $keyword;
        }
        $alldepart = DeviceServiceProvider::getLocationList($cond, $start, $limit);
        $allCount = DeviceServiceProvider::getLocationCount($cond);
        return ['total' => $allCount, 'list' => $alldepart];
    }

    /**
     * 获取管理员配置的AD域用户名和密码，对应老交易 get_domainconfig
     *
     * @return mixed
     * @throws Exception
     */
    public function domainConfig()
    {
        $type = request('type', 'request');
        $domain = Base64DeExt(request('domain_names', 'request'), true);
        hlp_check::checkEmpty($domain);
        $aResult = UserServiceProvider::getAdDomain($domain);
        $columnConfig = ['UserName', 'Password'];

        if ($type==="getpN") {
            $data =  base64_encode($aResult['UserName']);
        } else if ($type==="getAll") {
            $data = [];
            foreach ($columnConfig as $column) {
                $data[$column] = isset($aResult[$column]) ? base64_encode($aResult[$column]) : '';
            }
        } else {
            $data = base64_encode($aResult['Password']);
        }

        return $data;
    }

    /**
     * 校验指定的账户id是否是管理账户
     *
     * @return mixed
     * @throws Exception
     */
    public function isManageAccount()
    {
        $userid = request('userid', 'request', 0, 'int');
        hlp_check::checkEmpty($userid);
        $return = [];
        $return['state'] = UserServiceProvider::checkIsManageAccount($userid);
        return $return;
    }

    /**
     * 获取指定帐号生成的移动端APP管理平台URL
     *
     * @return mixed
     * @throws Exception
     */
    public function getAPPManageUrl()
    {
        $userid = request('userid', 'request', 0, 'int');
        // 限制访问频率，防止恶意调用，穷举出管理平台登录token
        $accessFrequency = hlp_net::limitAccessFrequency(getRemoteAddress(), 'getAPPManageUrl');
        if (!$accessFrequency) {
            T(********);
        }
        hlp_check::checkEmpty($userid);
        $return = [];
        $return['manageUrl'] = UserServiceProvider::getAPPManageUrl($userid);
        return $return;
    }

    /**
     * 忘记密码-重置密码
     * @return array
     * @throws Exception
     */
    public function forgetPass()
    {
        // 修改密码
        $deviceid = request('deviceid', 'request', 0, 'int');
        $username = request('user', 'request');
        $codetype = request('codeType', 'request', '');
        $type = request('type', 'request', 'User');
        $type = !empty($type) && $type != 'undefined' ? $type : 'User';
        $checkCode = request('check_code', 'request');

        hlp_check::checkUsername($username);
        hlp_check::checkEmpty($type);
        hlp_check::checkEmpty($deviceid);
        hlp_check::checkEmpty($codetype);
        hlp_check::checkEmpty($checkCode);

        $priKey= getRsaPriKey('net_auth', PATH_HTML . '/download/rsa/');
        $newpassword = Base64DeExt(request('newpassword', 'request'));
        $newpassword2 = Base64DeExt(request('newpassword2', 'request'));
        if (empty($newpassword) || empty($newpassword2)) {
            T(21137001);
        }

        openssl_private_decrypt($newpassword, $newpassword, $priKey, OPENSSL_PKCS1_PADDING);
        openssl_private_decrypt($newpassword2, $newpassword2, $priKey, OPENSSL_PKCS1_PADDING);
        if (empty($newpassword) || empty($newpassword2)) {
            T(21100002);
        }

        // 先查询 本地用户 是否存在，不存在则查询 用户是否第三方用户，给出对应提示
        $userinfo = AuthServiceProvider::getUserByUserName('User', $username);
        if (empty($userinfo)) {
            $userinfo = AuthServiceProvider::getUserByUserName('', $username);
            if (empty($userinfo)) {
                T(21137021);
            } else {
                T(21137022);
            }
        }

        // 校验验证码
        if ($codetype == 'email') {
            $phone = $userinfo['EMail'];
        } else {
            $phone = $userinfo['Tel'];
        }

        $aLogData = SmsServiceProvider::getCheckCode($phone, $checkCode);
        if (empty($aLogData["ID"])) {
            T(21131001);
        }

        cutil_php_log("new:{$newpassword},new2:{$newpassword2}", 'passwd');
        $ChangePassword = ConfigServiceProvider::getDictAll('ChangePassword');
        $userAuthService = AuthServiceProvider::initAuthService(['servicePrefix' => 'User', 'deviceId' => '']);
        $ret = $userAuthService->verifyPassword($ChangePassword, $newpassword, $username);
        if ($ret['pwdCheckResCode'] !== STRING_TRUE) {
            throw new Exception($ret['pwdCheckResMsg']);
        }
        $newpass= dataEncrypt($newpassword, 'md5withsm3');
        $newpass2= dataEncrypt($newpassword2, 'md5withsm3');
        // 2次密码是否一致
        if ($newpass !== $newpass2) {
            T(21137002);
        }
        $result = UserServiceProvider::changePassword($type, $username, $userinfo['Password'], $newpass, $newpassword);
        SmsServiceProvider::useCheckCode($phone, $checkCode);
        if (!empty($result)) {
            $result['ok'] = L(21137025);
            $this->errmsg = $result['ok'];
        }
        return $result;
    }
}
