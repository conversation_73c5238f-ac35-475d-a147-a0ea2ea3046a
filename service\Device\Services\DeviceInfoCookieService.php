<?php
/**
 * Description: 设备信息cookie方式服务
 * User: <EMAIL>
 * Date: 2021/07/2 15:53
 * Version: $Id: DeviceInfoCookieService.php 157980 2021-09-27 08:03:11Z renchen $
 */

namespace Services\Device\Services;

class DeviceInfoCookieService extends DeviceInfoService
{
    /**
     * 判断设备是否存在.
     *
     * @return mixed
     */
    public function isDeviceExist()
    {
        $result = $this->isDeviceExistByCookie();

        if ($result === false) {
            $this->writeLog('通过['.$this->params['gettype'].']方式，没有获取到设备信息');
        }
        return $result;
    }

    /**
     * 通过cookie中的信息判断设备是否存在.
     *
     * @return mixed
     */
    private function isDeviceExistByCookie()
    {
        if ($this->isDeviceExistByMac($this->params['mac'])) {
            return $this->updateDeviceType($this->params['deviceid'], $this->params['deviceType'], 'cookie');
        }

        return false;
    }
}
