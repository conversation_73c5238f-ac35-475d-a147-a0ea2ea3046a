<?php
/**
 * Description: 通知下发相关逻辑service
 * User: <EMAIL>
 * Date: 2022/07/26 23:32
 * Version: $Id: NoticeServiceProvider.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class NoticeServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'notice';

    /**
     * 通知更新用户信息
     *
     * @param $UserId
     *
     * @return bool
     */
    public static function noticeUpdateUserinfo($UserId): bool
    {
        $user = AuthUserModel::getOne($UserId, 'update');
        if (empty($user)) {
            return false;
        }
        $userinfo = UserInfoRedis::getOne($UserId);
        if (empty($userinfo) || empty($userinfo['Sessions'])) {
            return false;
        }
        if ($user['TrueNames'] !== $userinfo['TrueNames']) {
            UserInfoRedis::setOne($UserId, ['TrueNames' => $user['TrueNames']]);
        }
        $departInfo = DepartModel::getOne($user['DepartID'], 'one');
        $user['DepartIDs'] = str_replace('/', ',', $departInfo["AllDepartID"]);
        $Sessions = json_decode($userinfo['Sessions'], true);
        $columns = ['UserName', 'DepartIDs', 'RoleID', 'TrueNames'];
        foreach ($Sessions as $Token => $State) {
            $data = [];
            $session = SessionRedis::getOne($Token, 'update');
            if (!empty($session)) {
                foreach ($columns as $column) {
                    if ($session[$column] !== $user[$column]) {
                        $data[$column] = $user[$column];
                    }
                }
            }
            SessionRedis::setOne($Token, $data);
            ResourceServiceProvider::setUserPower($Token);
        }
        return true;
    }

    /**
     * 通知更新部门下的用户信息
     *
     * @param $DepartIds
     *
     * @return bool
     */
    public static function noticeUpdateDepartUserinfonotice($DepartIds): bool
    {
        if (empty($DepartIds)) {
            return false;
        }
        $list = NacOnLineDeviceModel::getList(['column' => 'name']);
        if (empty($list)) {
            return true;
        }
        $userIds = array_unique(array_column($list, 'UserID'));
        foreach ($userIds as $userId) {
            self::noticeUpdateUserinfo($userId);
        }
        return true;
    }

    /**
     * 通知更新设备下的用户信息
     *
     * @param $DeviceIds
     *
     * @return bool
     */
    public static function noticeUpdateDeviceUserinfo($DeviceIds): bool
    {
        if (empty($DeviceIds)) {
            return false;
        }
        $list = NacOnLineDeviceModel::getList(['DeviceIds' => $DeviceIds, 'column' => 'name']);
        if (empty($list)) {
            return true;
        }
        $userIds = array_unique(array_column($list, 'UserID'));
        foreach ($userIds as $userId) {
            self::noticeUpdateUserinfo($userId);
        }
        return true;
    }

    /**
     * 通知更新多个用户权限
     *
     * @param $UserIds
     *
     * @return bool
     */
    public static function noticeUpdateManyUserPower($UserIds): bool
    {
        if (empty($UserIds)) {
            return false;
        }
        foreach ($UserIds as $UserId) {
            self::noticeUpdateUserPower($UserId);
        }
        return true;
    }

    /**
     * 通知更新用户权限
     *
     * @param $UserId
     *
     * @return bool
     */
    public static function noticeUpdateUserPower($UserId,$from=""): bool
    {
        $userinfo = UserInfoRedis::getOne($UserId);
        self::log("noticeUpdateUserPower {$UserId}");
        if (empty($userinfo) || empty($userinfo['Sessions'])) {
            return false;
        }
        $Sessions = json_decode($userinfo['Sessions'], true);
        foreach ($Sessions as $Token => $State) {
            ResourceServiceProvider::setUserPower($Token,$from);
        }
        return true;
    }

    /**
     * 通知更新角色对应的用户权限
     *
     * @param $RoleIds
     * @param $return
     *
     * @return bool|array
     */
    public static function noticeUpdateRolePower($RoleIds, $return = false)
    {
        if (empty($RoleIds)) {
            return false;
        }
        $list = AuthUserModel::getList(['column' => 'base', 'RoleIds' => $RoleIds]);
        if (empty($list)) {
            return true;
        }
        $userIds = array_unique(array_column($list, 'ID'));
        if ($return) {
            return $userIds;
        }
        foreach ($userIds as $userId) {
            self::noticeUpdateUserPower($userId);
        }
        return true;
    }

    /**
     * 通知更新部门对应的用户权限
     *
     * @param $DepartIds
     * @param $return
     *
     * @return bool|array
     */
    public static function noticeUpdateDepartPower($DepartIds, $return = false)
    {
        if (empty($DepartIds)) {
            return false;
        }
        $list = AuthUserModel::getList(['column' => 'base', 'DepartIds' => $DepartIds]);
        if (empty($list)) {
            return true;
        }
        $userIds = array_unique(array_column($list, 'ID'));
        if ($return) {
            return $userIds;
        }
        foreach ($userIds as $userId) {
            self::noticeUpdateUserPower($userId);
        }
        return true;
    }

    /**
     * 通知更新设备对应的用户权限
     *
     * @param $DeviceIds
     * @param $return
     *
     * @return bool|array
     */
    public static function noticeUpdateDevicePower($DeviceIds, $return = false)
    {
        if (empty($DeviceIds)) {
            return false;
        }
        $list = NacOnLineDeviceModel::getList(['DeviceIds' => $DeviceIds, 'column' => 'name']);
        if (empty($list)) {
            return true;
        }
        $userIds = array_unique(array_column($list, 'UserID'));
        if ($return) {
            return $userIds;
        }
        foreach ($userIds as $userId) {
            self::noticeUpdateUserPower($userId);
        }
        return true;
    }

    /**
     * 通过资源ID获取关联的用户ID列表
     *
     * @param $ResIDs
     *
     * @return array|bool
     */
    public static function getUserIdsByResIds($ResIDs)
    {
        if (empty($ResIDs)) {
            return false;
        }
        $cond = ['column' => 'object'];
        if (is_array($ResIDs)) {
            $cond['InResID'] = $ResIDs;
        } else {
            $cond['ResID'] = $ResIDs;
        }
        $objectList = ResObjectRelationModel::getList($cond);
        if (empty($objectList)) {
            return true;
        }
        $UserIds = [];
        $RoleIds = [];
        $DepartIds = [];
        $DeviceIds = [];
        foreach ($objectList as $object) {
            switch ($object['ObjectType']) {
                case 0:
                    $UserIds[] = $object['UserID'];
                    break;
                case 1:
                    $RoleIds[] = $object['RoleID'];
                    break;
                case 2:
                    $DepartIds[] = $object['DepartID'];
                    break;
                case 3:
                    $DeviceIds[] = $object['DeviceID'];
                    break;
            }
        }
        $objectUserIds = [];
        if (!empty($RoleIds)) {
            $objectUserIds['role'] = self::noticeUpdateRolePower($RoleIds, true);
        }
        if (!empty($DepartIds)) {
            $objectUserIds['depart'] = self::noticeUpdateDepartPower($DepartIds, true);
        }
        if (!empty($DeviceIds)) {
            $objectUserIds['device'] = self::noticeUpdateDevicePower($DeviceIds, true);
        }
        if (!empty($objectUserIds)) {
            foreach ($objectUserIds as $rUserIds) {
                if (!empty($rUserIds) && is_array($rUserIds)) {
                    foreach ($rUserIds as $userId) {
                        $UserIds[] = $userId;
                    }
                }
            }
        }
        $UserIds = array_unique($UserIds);
        return $UserIds;
    }

    /**
     * 通知更新资源对应的所有对象对应的用户权限
     *
     * @param $ResID
     *
     * @return bool
     */
    public static function noticeUpdateResourcePower($ResID): bool
    {
        $UserIds = self::getUserIdsByResIds($ResID);
        if (!is_array($UserIds)) {
            return $UserIds;
        }
        foreach ($UserIds as $userId) {
            self::noticeUpdateUserPower($userId);
        }
        return true;
    }

    /**
     * 通知授权组对象对应的用户权限
     *
     * @param $authorizationID
     *
     * @return bool
     */
    public static function noticeUpdateAuthorizationGroupPower($authorizationID): bool
    {
        //查询授权组对应的类型，用户授权组/终端授权组
        list($deviceIds, $userIds) = self::getIdsByAuthorizationGroupId($authorizationID);

        if (!empty($deviceIds)) {
            self::noticeUpdateDevicePower($deviceIds);
        }

        if (!empty($userIds)) {
            foreach ($userIds as $userId) {
                self::noticeUpdateUserPower($userId);
            }
        }
        return true;
    }

    /**
     * 通过授权组ID获取关联的用户ID/设备ID列表
     *
     * @param $authorizationID
     * @return array
     */
    public static function getIdsByAuthorizationGroupId($authorizationID): array
    {
        $deviceIds = $userIds = [];
        $authorization = AuthorizationGroupModel::getOne($authorizationID, 'one');
        if (!empty($authorization)) {
            //授权组类型1用户，2终端
            if ($authorization['Type'] == 1) {
                //查询用户被标记为该授权组的用户
                $userIdsInfo = AuthUserModel::getListByAuthorizationGroup($authorizationID);
                $userIds = array_column($userIdsInfo, 'ID');
            } else {
                //查询设备标记为该授权组的终端
                $deviceIdsInfo = DeviceModel::getListByAuthorizationGroup($authorizationID);
                $deviceIds = array_column($deviceIdsInfo, 'DeviceID');
            }
        }
        return [$deviceIds, $userIds];
    }

    /**
     * 通知策略变更资源关联的用户权限
     *
     * @param $ResIDs
     *
     * @return bool
     */
    public static function noticeUpdatePolicyPower($ResIDs): bool
    {
        if (empty($ResIDs)) {
            return false;
        }
        self::log("noticeUpdatePolicyPower ResIDs:".var_export($ResIDs, true));
        $UserIds = self::getUserIdsByResIds($ResIDs);
        if (!is_array($UserIds)) {
            return $UserIds;
        }
        if (empty($UserIds)) {
            return true;
        }
        foreach ($UserIds as $userId) {
            self::noticeUpdateUserPower($userId,"UpdatePolicyPower");
        }
        return true;
    }

    /**
     * 通知策略变更
     *
     * @param $ResIDs
     *
     * @return bool
     */
    public static function noticeUpdatePolicyChangePower($ResIDs): bool
    {
        if (empty($ResIDs)) {
            return false;
        }
        self::log("noticeUpdatePolicyChangePower ResIDs:".var_export($ResIDs, true));
        $UserIds = self::getUserIdsByResIds($ResIDs);
        if (!is_array($UserIds)) {
            return $UserIds;
        }
        if (empty($UserIds)) {
            return true;
        }
        self::log("noticeUpdatePolicyChangePower UserIds:".var_export($UserIds, true));
        $list = NacOnLineDeviceModel::getList(['UserIds' => $UserIds, 'column' => 'name']);
        if (empty($list)) {
            return true;
        }
        $DeviceIDs = array_column($list, 'DeviceID');
        AuthServiceProvider::tacticsChangeSel($DeviceIDs);
        return true;
    }

    /**
     * 通知网关变更
     *
     * @param $GwID
     * @param $GroupID
     *
     * @return bool
     * @throws Exception
     */
    public static function noticeUpdateGateway($GwID, $GroupID): bool
    {
        if (empty($GwID)) {
            return false;
        }
        // 网关
        $ResIDs = self::getNewGwResID($GwID, $GroupID);
        self::log("noticeUpdateGateway ResIDs:".var_export($ResIDs, true));
        $UserIds = self::getUserIdsByResIds($ResIDs);
        if (!is_array($UserIds)) {
            return $UserIds;
        }
        if (empty($UserIds)) {
            return true;
        }
        self::log("noticeUpdateGateway UserIds:".var_export($UserIds, true));
        $list = NacOnLineDeviceModel::getList(['UserIds' => $UserIds, 'column' => 'name']);
        if (empty($list)) {
            return true;
        }
        $DeviceIDs = array_column($list, 'DeviceID');
        foreach ($DeviceIDs as $DeviceId) {
            self::noticeZtpConfigChange($DeviceId);
        }
        return true;
    }
    /**
     * 新proxy模式下,一个IP资源可以对应多个网关.
     * 返回所有对应的资源和网关信息
     * 兼容老的vpn
     * @return array
     * **/
    private static function getNewGwResID($GwID, $GroupID)
    {
        $cond = ['column' => 'list', 'GwType' => '1', 'GwID' => $GwID, 'Type'=>'1'];
        $list = GateWayExpendModel::getList($cond);
        $ResIDs = array_column($list, 'RID');
        // 网管组
        if (!empty($GroupID)) {
            $cond = ['column' => 'list', 'GwType' => '2', 'GwID' => $GroupID, 'Type'=>'1'];
            $glist = GateWayExpendModel::getList($cond);
            if (!empty($glist)) {
                foreach ($glist as $row) {
                    $ResIDs[] = $row['RID'];
                }
            }
        }
        $ResIDs = array_unique($ResIDs);
        return $ResIDs;
    }
    /**
     * 通知下发策略配置变更
     *
     * @return bool
     * @throws Exception
     */
    public static function noticeUpdateConfig(): bool
    {
        self::log("noticeUpdateConfig:");
        $list = NacOnLineDeviceModel::getList(['column' => 'name']);
        if (empty($list)) {
            return true;
        }
        $DeviceIDs = array_column($list, 'DeviceID');
        foreach ($DeviceIDs as $DeviceId) {
            self::noticeZtpConfigChange($DeviceId);
        }
        return true;
    }

    /**
     * 通知变更
     * @param $DeviceId
     */
    public static function noticeZtpConfigChange($DeviceId)
    {
        try {

            // 清除ASM:Sdp_Ini缓存,避免内外网直连相关功能开关变动未直接生效
            lib_redis::del("ASM:", "Sdp_Ini");

            $content = "<ASM><TradeCode>ZtpConfigChange</TradeCode><WhatToDo>ZtpGateway</WhatToDo><Msg></Msg></ASM>";
            $msgSenderInfo = ['DeviceId' => $DeviceId, 'Content' => $content];
            lib_alarm::createMsgSender($msgSenderInfo);
        } catch (Exception $e) {
            self::log("noticeZtpConfigChange fail {$DeviceId}:" . $e->getMessage());
        }
    }

    /**
     * 通知token回收
     *
     */
    public static function noticeTokenRecovery($DeviceId)
    {
        try {
            $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>TokenRecovery</WhatToDo><Msg></Msg></ASM>";
            $msgSenderInfo = ['DeviceId' => $DeviceId, 'Content' => $content];
            lib_alarm::createMsgSender($msgSenderInfo);
        } catch (Exception $e) {
            self::log("noticeZtpConfigChange fail {$DeviceId}:" . $e->getMessage());
        }
    }

    /**
     * 发送通知到第三方系统
     * @param $ids
     * @param $type
     * @return bool
     */
    public static function sendSyncStatus($ids, $type): bool
    {
        $params = ['ids' => $ids, 'type' => $type];
        // 验证 $params 是否包含必要字段
        if (!isset($params['type']) || !isset($params['ids'])) {
            return false;
        }

        try {
            if (file_exists(PATH_ETC . 'msep_link.ini')) {
                $linkState = get_ini_info(PATH_ETC . 'msep_link.ini', 'link_state');
                if ($linkState == '1') {
                    if ($params['type'] === 'deviceToUser') {
                        $list = self::noticeUpdateDevicePower([$params['ids']], true);
                        $params['ids'] = is_array($list) ? implode(',', $list) : '';
                        $params['type'] = 'user';
                    }
                    $params['source'] = get_server_type();
                    $params['DAscID'] = get_ini_info(PATH_ETC . 'asc.ini', 'AscID');
                    // 调用 YAR 客户端
                    \lib_yar::clients('net', 'setMSEPSyncStatus', $params, get_dasm_ip() ?: '127.0.0.1', 3000, 1);// 如是DASM，走RPC由DASM调用MSEP的接口
                }
            }
            return true;
        } catch (Exception $e) {
            // 记录异常信息
            return false;
        }
    }
}
