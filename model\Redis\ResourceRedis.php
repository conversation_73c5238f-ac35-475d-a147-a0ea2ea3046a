<?php

/**
 * Description: 根据设备ID和用户ID获取token
 * User: <EMAIL>
 * Date: 2022/05/16 10:32
 * Version: $Id$
 */

class ResourceRedis extends BaseStringRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'IpResource';

    /**
     * 是否同步到ASG
     *
     * @var string
     */
    protected static $remote = false;

    /**
     * 单条
     *
     * @param string $deviceId
     *
     * @return mixed
     */
    public static function getOne($resId)
    {
        return self::get($resId);
    }

    /**
     * 单条
     *
     * @param string $resId
     * @param string $value
     *
     * @return mixed
     */
    public static function setOne($resId, $value)
    {
        return self::set($value, null, $resId);
    }
    /**
     * 删除
     *
     * @param string $token
     *
     * @return mixed
     */
    public static function deleteOne($token)
    {
        return self::del($token);
    }
}
