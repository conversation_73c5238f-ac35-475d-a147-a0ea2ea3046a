<?php

/**
 * Description: redis list通用redis
 * User: <EMAIL>
 * Date: 2024/04/19 23:32
 * Version: $Id: BaseListRedis.php 175039 2024-04-19 23:33:41Z xlj $
 */
class BaseListRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = '';

    /**
     * 获取key
     *
     * @param array $keys
     *
     * @return mixed
     */
    public static function getKey($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }

        return static::TABLE_NAME . ":" . implode('_', $keys);
    }

    /**
     * 从头部并删除一个元素,并返回删除的值
     *
     * @param array $keys
     *
     * @return mixed
     */
    protected static function lpop(...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::lpop(static::PREFIX, $key);
    }

    /**
     * 从尾部并删除一个元素,并返回删除的值
     *
     * @param array $keys
     *
     * @return mixed
     */
    protected static function rpop(...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::rpop(static::PREFIX, $key);
    }

    /**
     * 由列表头部添加字符串值。如果不存在该键则创建该列表。
     * 如果该键存在，而且不是一个列表，返回FALSE
     *
     * @param string $data
     * @param array $keys
     *
     * @return mixed
     */
    protected static function lpush(string $data, ...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::lpush(static::PREFIX, $key, $data);
    }

    /**
     *
     * 批量由列表头部批量添加字符串值。如果不存在该键则创建该列表。
     *
     * @param array $data
     * @param array $keys
     *
     * @return mixed
     */
    protected static function lmultipush(array $data, ...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::lmultipush(static::PREFIX, $key, $data);
    }

    /**
     * 由列表尾部添加字符串值。如果不存在该键则创建该列表。
     *  如果该键存在，而且不是一个列表，返回FALSE。
     * @param string $data
     * @param array $keys
     *
     * @return bool|null
     */
    protected static function rpush(string $data, ...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::rpush(static::PREFIX, $key, $data);
    }

    /**
     *  计算redis Lists 元素数量
     * @param array $keys
     *
     * @return bool|int|Redis|null
     */
    protected static function lsize(...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::lLen(static::PREFIX, $key);
    }


    /**
     * 删除
     *
     * @param array $keys
     *
     * @return boolean
     */
    public static function del(...$keys): bool
    {
        $key = static::getKey($keys);
        cutil_php_log(['del', $key], "model_" . static::TABLE_NAME);
        return lib_redis::del(static::PREFIX, $key);
    }

}
