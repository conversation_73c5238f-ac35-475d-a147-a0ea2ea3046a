<?php
/**
 * Description: 零信任资源
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: ZtpResource.php 175007 2022-05-05 02:51:17Z duanyc $
 */


$GLOBALS['LANG'][21148] = [
    ******** => 'No permission for this resource!',
    ******** => 'The resource does not exist or has been removed from the shelf, please refresh the page!',
    ******** => 'Please fill in the account and password of the resource first!',
    ******** => 'Please conduct additional certification first!',
    ******** => 'Status check succeeded!',
    ******** => 'Failed to get app configuration!',
    ******** => 'Failed to access history!',
    ******** => 'Resources have been collected, please do not add again!',
    ******** => 'Resource collection failed!',
    ******** => 'Resource not collected!',
    ******** => 'Failed to save, user name and password cannot be empty!',
    ******** => 'Client policy check result not reported!',
    ******** => 'The security check fails or the repair deadline has expired!',
    ******** => 'The terminal operating system does not meet the requirements!',
    ******** => 'The geographical location of the terminal does not meet the requirements!',
    ******** => 'The terminal IP / Mac does not meet the requirements!',
    ******** => 'The access time does not meet the requirements!',
    ******** => 'The user department does not meet the requirements!',
    ******** => 'User role does not meet requirements!',
    ******** => 'User type does not meet the requirements!',
    ******** => 'Incomplete device hardware information!',
    ******** => 'Incomplete knocking parameters!',
    ******** => 'Login timeout',
    ******** => 'Resource does not exist',
    ******** => '[{UserName}] at [{Time}] login to success.',
    ******** => '[{UserName}] at [{Time}] logout to success.',
    ******** => '[{UserName}] at [{Time}] login to fail.',
    ******** => 'Not the account of the current user!',
    ******** => 'Client needs to be installed',
    ******** => 'Enhanced authentication is not enabled!',
    ******** => 'The enhanced certification type is incorrect!',
    ******** => '[{UserName}] at [{Time}] enhanced certification to success.',
    ******** => '[{UserName}] at [{Time}] enhanced certification to fail.',
    ******** => 'The terminal already has a running process and cannot be accessed remotely. Please try again later.',
    ******** => 'Resource information has been changed!',
    ******** => 'The resource platform is not authorized, please contact the administrator!',
    ******** => 'This resource type is not authorized, please contact the administrator!',
    ******** => 'IP changes frequently, please try again later!',
    ******** => 'Address changes frequently, please try again later!',
    ******** => 'Sorry, you do not have permission to access this resource!',
    ******** => 'Sorry, you cannot be accessed remotely. Please contact the administrator or try again later.',
    21148050 => 'Sorry, the resource configuration is incorrect!',
    21148051 => 'This application has a dependent application, and you currently do not have access permissions to the dependent application. Please contact the administrator to configure the dependent application!',
    21148052 => 'This application can only run within Windows clients!',
    21148053 => 'You do not have the resource platform permission. Please contact the administrator.',
    21148054 => 'The device role does not meet the requirements!',
    21148055 => 'The resource has been disabled. Please contact the administrator!',
    21148056 => 'Resource application failed!',
    21148057 => 'The resources have already been applied for, please do not reapply!',
    21148058 => 'The resource has changed. Please try again!',
    21148059 => 'The virtual IP pool is insufficient, please contact the administrator!',
    21148060 => 'IP address pool is full, switch to NAT mode!',
    21148061 => 'You have the resource permission!',
    21148062 => 'The user has permission to access the resource!',
    21148063 => 'The user does not have permission to access the resource!',
    21148064 => 'The gateway successfully connected to the resource!',
    21148065 => 'The gateway can access the specified resource normally!',
    21148066 => 'Gateway connection to resource failed!',
    21148067 => 'The gateway cannot access the specified resource!',
    21148068 => 'The Policy test failed!',
    21148069 => 'Triggered dynamic security policy, access to resource forbidden!',
];
