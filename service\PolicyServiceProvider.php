<?php
/**
 * Description: 策略
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: PolicyServiceProvider.php 174901 2022-04-29 06:03:47Z duanyc $
 */

use Services\Policy\Services;

class PolicyServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'policy';

    /**
     * 用户属性和环境属性的CODE映射
     * @var array
     */
    public static $UserCode = [
        // 用户属性
        'AccessDepartIds' => 21148018,
        'ForbidDepartIds' => 21148018,
        'AccessRoleIds' => 21148019,
        'ForbidRoleIds' => 21148019,
        'AccessUserTypes' => 21148020,
        'ForbidUserTypes' => 21148020,
        'RequireAuth' => 21148004,
        // 环境属性
        'SafeCheck' => 21148013,
        'AllowOS' => 21148014,
        'ForbidOS' => 21148014,
        'InstallClient' => 21148029,
        'AllowLocation' => 21148015,
        'ForbidLocation' => 21148015,
        'ChangeIP' => 21148038,
        'ChangeLocation' => 21148039,
        'AllowTime' => 21148017,
        'AllowIPAccess' => 21148016,
        'ForbidIPAccess' => 21148016,
        'AllowMacAccess' => 21148016,
        'ForbidMacAccess' => 21148016,
        'AllowDeviceRoles' => 21148054,
        'ForbidDeviceRoles' => 21148054,
        'DynamicTactic' => 21148069,
    ];

    /**
     * 客户端策略项
     * @var array
     */
    public static $ClientItems = [
        'AllowSoft' => true,
        'ForbidSoft' => true,
        'AllowProcess' => true,
        'ForbidProcess' => true,
        'Default' => true, // 默认策略按照客户端策略执行
    ];

    /**
     * 获取资源对应的策略ID
     *
     * @param $ResID
     *
     * @return mixed
     */
    public static function getPolicyID($ResID)
    {
        if (empty($ResID)) {
            return false;
        }
        $PolicyRelation = ResPolicyRelationModel::getSingle(['column' => 'policy', 'ResID' => $ResID]);
        // 资源未分配策略
        if (empty($PolicyRelation['PolicyID'])) {
            return false;
        }
        return $PolicyRelation['PolicyID'];
    }

    /**
     * 获取策略对应的资源ID列表
     *
     * @param $PolicyID
     *
     * @return mixed
     */
    public static function getPolicyResIDs($PolicyID)
    {
        if (empty($PolicyID)) {
            return false;
        }
        $PolicyRelations = ResPolicyRelationModel::getList(['column' => 'res', 'PolicyID' => $PolicyID]);
        // 策略未关联资源
        if (empty($PolicyRelations)) {
            return [];
        }
        $ResIDs = [];
        foreach ($PolicyRelations as $Relation) {
            $ResIDs[] = $Relation['ResID'];
        }
        return $ResIDs;
    }

    /**
     * 检查资源的策略检查结果
     *
     * @param $Session
     * @param $PolicyID
     * @throws Exception
     */
    public static function checkPolicy($Session, $PolicyID): void
    {
        try {
            $messages = self::checkPolicyResult($Session, $PolicyID);
            if (!empty($messages)) {
                // 策略检查未通过
                $message = implode('<br>', $messages);
                throw new Exception($message, 21148068);
            }
            // 策略检查通过
            self::issuePolicyResult($Session['Token'], $PolicyID, '1');
        } catch (Exception $e) {
            // 策略检查未通过
            self::issuePolicyResult($Session['Token'], $PolicyID, '0', $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查无策略的资源结果检查
     *
     * @param $Session
     * @param $ResIds
     * @throws Exception
     */
    public static function checkNoPolicy($Session, $ResIds): void
    {
        try {
            LoginServiceProvider::checkResult($Session['DeviceID']);
            self::issuePolicyResult($Session['Token'], 0, '1', '', $ResIds);
        } catch (Exception $e) {
            // 策略检查未通过
            self::issuePolicyResult($Session['Token'], 0, '0', $e->getMessage(), $ResIds);
            throw $e;
        }
    }

    /**
     * 下发策略结果
     *
     * @param $Token
     * @param $PolicyID
     * @param $Result
     * @param $Reason
     * @param $ResIDs
     */
    public static function issuePolicyResult($Token, $PolicyID, $Result, $Reason = '', $ResIDs = null): void
    {
        if ($ResIDs === null) {
            $ResIDs = self::getPolicyResIDs($PolicyID);
        }
        $Data = ['Result' => $Result, 'Reason' => $Reason];
        if ($Result == '0') {
            //此处做个兼容,如果内容包含强化认证，则对应的错误码更新
            $position = mb_stripos($Reason, L(21148004));
            if ($position !== false) {
                $Data['ErrCode'] = '21148004';
            } else {
                $Data['ErrCode'] = '21148068';
            }
        }
        if (!empty($ResIDs)) {
            $changeResult = false;
            foreach ($ResIDs as $ResID) {
                UserResourceRedis::setOne($Token, $ResID, $Data);
                $changeDatas = UserResourceRedis::getChangeDatas();
                if (isset($changeDatas['Result'])) {
                    $changeResult = true;
                }
            }
            if ($changeResult) {
                ResourceServiceProvider::setUserPower($Token);
            }
        }
    }

    /**
     * 检查资源的策略检查结果
     *
     * @param $Session
     * @param $PolicyID
     *
     * @return mixed 失败则返回原因
     * @throws Exception
     */
    public static function checkPolicyResult($Session, $PolicyID)
    {
        $Policy = self::getPolicy($PolicyID);
        if (empty($Policy)) {
            return [];
        }
        $inEffect = self::checkInEffect($Session, $Policy['PolicyConfig']);
        // 策略未生效
        if (empty($inEffect)) {
            return [];
        }
        $usercheck = self::checkUser($Session, $Policy['PolicyObject']);
        self::log("usercheck: " . var_export($usercheck, true));
        list($envCheck, $reasons) = self::checkPolicyItem($Session, $PolicyID, $Policy['PolicyBody']);
        self::log("envCheck: " . var_export($envCheck, true));
        if (!empty($envCheck)) {
            $usercheck = array_merge($usercheck, $envCheck);
        }
        $messages = [];
        if (!empty($usercheck)) {
            foreach ($usercheck as $column => $checkResult) {
                if (!$checkResult) {
                    if (isset(self::$UserCode[$column])) {
                        $messages[] = L(self::$UserCode[$column]);
                    } else {
                        $messages[] = $reasons[$column];
                    }
                }
            }
            $messages = array_unique($messages);
        }
        return $messages;
    }

    /**
     * 获取策略
     *
     * @param $PolicyID
     *
     * @return mixed
     */
    public static function getPolicy($PolicyID)
    {
        static $PolicyList = null;
        if (empty($PolicyList[$PolicyID])) {
            $PolicyList[$PolicyID] = NacPolicyListModel::getSingle(['column' => 'ztp', 'PolicyID' => $PolicyID]);
        }
        $Policy = $PolicyList[$PolicyID];
        // 策略不存在或者未启用
        if (empty($Policy) || $Policy['State'] !== '1') {
            return [];
        }
        return $Policy;
    }

    /**
     * 获取策略高级参数
     *
     * @param $PolicyID
     *
     * @return array|mixed
     */
    public static function getPolicyConfig($PolicyID)
    {
        $Policy = self::getPolicy($PolicyID);
        if (empty($Policy)) {
            return [];
        }
        return json_decode($Policy['PolicyConfig'], true);
    }

    /**
     * 获取策略用户属性参数
     *
     * @param $PolicyID
     *
     * @return array|mixed
     */
    public static function getPolicyObject($PolicyID)
    {
        $Policy = self::getPolicy($PolicyID);
        if (empty($Policy)) {
            return [];
        }
        return json_decode($Policy['PolicyObject'], true);
    }

    /**
     * 获取策略环境属性
     *
     * @param $PolicyID
     *
     * @return array|mixed
     */
    public static function getPolicyBody($PolicyID)
    {
        $Policy = self::getPolicy($PolicyID);
        if (empty($Policy)) {
            return [];
        }
        return json_decode($Policy['PolicyBody'], true);
    }

    /**
     * 初始化策略项服务
     *
     * @param $policyName
     * @param $params
     *
     * @return Object|Services\SafeCheckPolicyService|Services\AllowOSPolicyService
     * @throws Exception
     */
    public static function getPolicyService($policyName, $params)
    {
        $serviceClass = "\\Services\\Policy\\Services\\{$policyName}PolicyService";
        return (new ReflectionClass($serviceClass))->newInstance($params);
    }

    /**
     * 检查策略环境属性
     *
     * @param $Session
     * @param $PolicyID
     * @param $PolicyBody
     * @return mixed
     * @throws Exception
     */
    public static function checkPolicyItem($Session, $PolicyID, $PolicyBody)
    {
        $PolicyBody = json_decode($PolicyBody, true);
        if (empty($PolicyBody)) {
            return false;
        }
        $return = [];
        $params = ['DeviceID' => $Session['DeviceID'], 'PolicyID' => $Session['PolicyID']];
        $reasons = [];
        $PolicyBody['Default'] = true;
        foreach ($PolicyBody as $column => $val) {
            if (empty($val)) {
                continue;
            }
            // 客户端策略项
            $params['InstallClient'] = !empty($PolicyBody['InstallClient']);
            if (isset(self::$ClientItems[$column])) {
                $params['PolicyID'] = $PolicyID;
                $params['session'] = $Session;
                $params['column'] = $column;
                $service = self::getPolicyService($column, $params);
                $return[$column] = $service->check();
                $reason = $service->getReason();
                if (!empty($reason)) {
                    $reasons[$column] = $reason;
                }
            } elseif (isset(self::$UserCode[$column])) {
                $params['Config'] = $val;
                $params['session'] = $Session;
                $params['PolicyID'] = $PolicyID;
                $service = self::getPolicyService($column, $params);
                $return[$column] = $service->check();
            }
        }
        return [$return, $reasons];
    }

    /**
     * 检查策略用户属性
     *
     * @param $Session
     * @param $PolicyObject
     * @return mixed
     */
    public static function checkUser($Session, $PolicyObject)
    {
        $PolicyObject = json_decode($PolicyObject, true);
        if (empty($PolicyObject)) {
            return false;
        }
        $DepartIds = explode(',', $Session['DepartIDs']);
        $return = [];
        foreach ($PolicyObject as $column => $val) {
            if ($val === '') {
                continue;
            }
            switch ($column) {
                case 'AccessDepartIds':
                    $return[$column] = hlp_match::checkIntersect($val, $DepartIds);
                    break;
                case 'ForbidDepartIds':
                    $return[$column] = hlp_match::checkIntersect($val, $DepartIds, '1');
                    break;
                case 'AccessRoleIds':
                    $return[$column] = hlp_match::checkContain($val, $Session['RoleID']);
                    break;
                case 'ForbidRoleIds':
                    $return[$column] = hlp_match::checkContain($val, $Session['RoleID'], '1');
                    break;
                case 'AccessUserTypes':
                    // $value = is_array($val) ? implode(',', $val) : $val;
                    // $return[$column] = hlp_match::checkContain($value, $Session['UserType']);
                    break;
                case 'ForbidUserTypes':
                    // $value = is_array($val) ? implode(',', $val) : $val;
                    // $return[$column] = hlp_match::checkContain($value, $Session['UserType'], '1');
                    break;
                case 'RequireAuth':
                    if (!empty($Session['NoCheckRequireAuth'])) {
                        $return[$column] = true;
                    } else {
                        $isOpen = LoginServiceProvider::isNeedAddAuth(0, false);
                        $require = $isOpen && !empty($val);
                        $return[$column] = LoginServiceProvider::getAddAuthStatus($require, $Session['Token']);
                    }
                    break;
            }
        }
        return $return;
    }

    /**
     * 检查策略是否有效
     *
     * @param $Session
     * @param $PolicyConfig
     * @return bool
     */
    public static function checkInEffect($Session, $PolicyConfig): bool
    {
        $PolicyConfig = json_decode($PolicyConfig, true);
        // 高级配置损坏，策略无效
        if (empty($PolicyConfig)) {
            return false;
        }
        // 在例外用户中，策略无效
        if (!hlp_match::checkContain($PolicyConfig['ExceptUserIds'], $Session['Uuid'], '1')) {
            return false;
        }

        // 有效工作日
        $checkWeek = hlp_match::checkWeek($PolicyConfig['WorkDate']);
        // 有效日期
        $EnableDateE = date("Y-m-d", strtotime($PolicyConfig['EnableDateE']) + 86400);
        $checkDate = hlp_match::checkDate($PolicyConfig['EnableDateS'], $EnableDateE);
        $ValidTimes = [];
        for ($i = 1; $i <= 3; $i++) {
            if (!isset($PolicyConfig["ValidTime{$i}S"])) {
                break;
            }
            $ValidTimes[] = ['StartTime' => $PolicyConfig["ValidTime{$i}S"],
                'EndTime' => $PolicyConfig["ValidTime{$i}E"]];
        }
        // 有效时间段
        $checkDay = hlp_match::checkDay($ValidTimes);
        self::log("checkInEffect: {$checkWeek},{$checkDate},{$checkDay}" . var_export($PolicyConfig, true));
        return $checkWeek && $checkDate && $checkDay;
    }

    /**
     * 计算用户策略
     *
     * @param $Token
     * @param $PolicyIDs
     * @param $ResPolicys
     * @return array
     */
    public static function setUserPolicy($Token, $PolicyIDs, $ResPolicys): array
    {
        $Policys = [];
        if (!empty($PolicyIDs)) {
            $cond = ['column' => 'item', 'InPolicyID' => $PolicyIDs, 'State' => '1'];
            $PolicyList = NacPolicyListModel::getList($cond);
            if (!empty($PolicyList)) {
                foreach ($PolicyList as $Policy) {
                    $Policys[$Policy['ItemID']] = $Policy['ChangeTime'] ?? $Policy['InsertTime'];
                }
            }
        }
        self::log("setUserPolicy {$Token}");
        return ['Policys' => $Policys, 'IpResPolicys' => $ResPolicys];
    }

    /**
     * 上报策略检查结果
     *
     * @param $Token
     * @param $ItemID
     * @param $CheckResult
     *
     * @return bool
     * @throws Exception
     */
    public static function reportPolicyResult($Token, $ItemID, $CheckResult): bool
    {
        if (empty($ItemID) || empty($CheckResult)) {
            return false;
        }
        $Policy = NacPolicyListModel::getSingle(['ItemID' => $ItemID, 'column' => 'item']);
        if (empty($Policy['PolicyID'])) {
            T(21104006);
        }
        $Data = [];
        foreach ($CheckResult as $ItemName => $Row) {
            if (isset(self::$ClientItems[$ItemName])) {
                $Data["{$ItemName}Result"] = $Row['Result'];
                $Data["{$ItemName}Reason"] = $Row['Reason'];
            }
        }
        UserPolicyRedis::setOne($Token, $Policy['PolicyID'], $Data);
        $Session = LoginServiceProvider::getSessionInfo($Token, 'policy');
        self::checkPolicy($Session, $Policy['PolicyID']);
        return true;
    }

    /**
     * 客户端获取IP资源列表
     *
     * @param $Token
     *
     * @return array
     * @throws Exception
     */
    public static function getIpResList($Token): array
    {
        $session = SessionRedis::getOne($Token, 'ipres');
        if (empty($session['vpnIpList'])) {
            return [];
        }
        $ipResIds = explode(',', $session['vpnIpList']);
        $cond = ['column' => 'client', 'InResID' => $ipResIds];
        $ipList = ResIPListModel::getList($cond);
        if (empty($ipList)) {
            return [];
        }
        $return = [];
        foreach ($ipList as $row) {
            $result = \UserResourceRedis::getOne($Token, $row['ResID']);
            $row['PolicyResult'] = $result['Result'] ?? '1';
            $NeedAddAuth = LoginServiceProvider::isNeedAddAuth($row['ResID'], true, $Token);
            $row['PolicyAddAuth'] = LoginServiceProvider::getAddAuthStatus($NeedAddAuth, $Token) ? '1' : '0';
            $return[] = $row;
        }
        return $return;
    }
}
