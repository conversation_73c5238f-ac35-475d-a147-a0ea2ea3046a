<?php

/**
 * Description: 场景信息关系表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id
 */

class SceneRelationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TSceneRelation';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        '*'   => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['RID'])) {
            $where .= "AND RID = ".self::setData($cond['RID']);
        }

        if (isset($cond['SceneID'])) {
            $where .= "AND SceneID >= ".self::setData($cond['SceneID']);
        }

        if (isset($cond['ConfigID'])) {
            $where .= "AND ConfigID = ".self::setData($cond['ConfigID']);
        }

        if (isset($cond['Groups'])) {
            $where .= "AND TSceneRelation.Groups = ".self::setData($cond['Groups']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
