<?php

/**
 * Description: 安检结果表
 * User: <EMAIL>
 * Date: 2021/07/23 15:53
 * Version: $Id: NacCheckResultModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class NacCheckResultModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacCheckResult';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
