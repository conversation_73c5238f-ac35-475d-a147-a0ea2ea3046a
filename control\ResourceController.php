<?php

/**
 * Description: 高级动态认证相关交易
 * User: <EMAIL>
 * Date: 2021/08/20 15:53
 * Version: $Id: ResourceController.php 162798 2021-11-24 14:40:40Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class ResourceController extends BaseController
{
    /**
     * 公共判断
     * @throws Exception
     */
    public function __construct()
    {
        include_config('auth');
        parent::__construct();
    }

    /**
     * 参数校验.
     * @return mixed
     * @throws \Exception
     */
    protected function checkParams()
    {
        cutil_php_log(lib_request::$requests, 'net_auth');
        $params = [];
        // 通过何种方式认证type
        $type = request('type', 'request');
        $params['isQrLogin'] = request('isQrLogin', 'request', 0, 'int');
        if ($params['isQrLogin'] == '1') {
            $type = 'Qrcode';
        }
        $params['authType'] = $type;
        $params['noAuthCode'] = STRING_TRUE;
        $params['newMobile'] = STRING_FALSE;
        $params['servicePrefix'] = $GLOBALS['AUTH_CONFIG']['TypeService'][$type] ?? $type;
        $params['userName'] = request('user_name', 'request');
        $params['password'] = request('password', 'request');
        $params['deviceId'] = request('deviceid', 'request', 0, 'int');
        $params['qrCode'] = request('qrCode', 'request');
        $params['time'] = request('_', 'request', 0, 'int');
        $params['mac'] = request('mac', 'request');
        $resourceAddr = request('resourceAddr', 'request');
        $resourceAddr = str_replace('?', '&', $resourceAddr);
        $params['resourceAddr'] = preg_replace("/&/", '?', $resourceAddr, 1);
        if (!empty($params['resourceAddr']) && !preg_match("/^http(s)?:\/\//i", $params['resourceAddr'])) {
            $params['resourceAddr'] = "http://{$params['resourceAddr']}";
        }
        $params['notNeedAuth'] = true;
        $params['isResourceAuth'] = true;
        return $params;
    }

    /**
     * 高级动态认证配置接口，对应老交易 resource_auth
     *
     * @return mixed
     * @throws Exception
     */
    public function auth()
    {
        try {
            $params = $this->checkParams();
            $authService = AuthServiceProvider::initAuthService($params);
            $authService->parseParams();
            $authService->resourceAuthBefore();

            // 调用具体认证服务进行校验
            $authServers = $authService->getAuthServer();
            $authParams = $authService->getAuthParams();
            $data = AuthServiceProvider::validateRpcAuthServer($authServers, $authParams, $authService);
            $params = $authService->getParams();

            $authService->resourceAuthAfter($data);
            cutil_php_log($data, 'net_auth');
            $data['code'] = '200';
            $authService->recordResourceAuthLog(STRING_TRUE, $params['authType']);
            return $data;
        } catch (Exception $e) {
            if (!empty($authService)) {
                $authService->recordResourceAuthLog(STRING_FALSE, $params['authType'], $e->getMessage());
            }
            throw $e;
        }
    }

    /**
     * 获取高级动态认证配置，对应老交易 get_resource_auth_config
     * @return mixed
     * @throws Exception
     */
    public function config()
    {
        $configData = ResourceServiceProvider::getConfig();
        // 暂不支持获取设备名称(无设备ID、小助手(可能))，先去除，后续再考虑支持
        if (!empty($configData['noticeMsg'])) {
            $configData['noticeMsg'] = str_replace('{$process}', '', $configData['noticeMsg']);
        }
        return ['resourceAuthConfig' => $configData];
    }

    /**
     * 高级动态认证时根据设备id获取当前认证用户名，对应老交易 get_resource_auth_user
     *
     * @return mixed
     * @throws Exception
     */
    public function user()
    {
        $deviceId = request('device_id', 'request', 0, 'int');
        hlp_check::checkEmpty($deviceId);
        $data = ResourceServiceProvider::getOnlineData($deviceId);
        return $data['UserName'] ?? '';
    }

    /**
     * 获取二维码、检查二维码是否已使用，对应老交易 get_senior_qrcode
     *
     * @return mixed
     * @throws Exception
     */
    public function qrcode()
    {
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $resourceAddr = request('resourceAddr', 'request');
        hlp_check::checkEmpty($deviceId);
        $data = ResourceServiceProvider::getOnlineData($deviceId);
        if (empty($data)) {
            T(21103011);
        }
        $return = [];
        switch ($action) {
            case "imgCheck":
                $imgInfo = ResourceServiceProvider::getAuthUrl($deviceId, $resourceAddr, $data['AuthType']);
                $return = ['url' => $imgInfo];
                break;
            case "usedCheck":
                $userID = $data['UserID'] ?? '';
                $keyPrefix = 'ResourceAuth:Success:' . $userID . ':';
                $success = cache_get_info($keyPrefix, $deviceId);
                if (empty($success)) {
                    $return = ['info' => 'no', 'state' => false];
                } else {
                    $return = ['info' => 'ok', 'state' => true];
                }
                break;
            default:
                T(21100002);
        }
        return $return;
    }
}
