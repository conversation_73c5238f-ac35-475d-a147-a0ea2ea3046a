## 数据库模型层
### 每个表一个文件一个类，读写该表数据调用该类方法。
### 每个类主要定义变量和方法即可，若有联合查询以主表对应的model定义单独的方法返回数据，如：DeviceModel::getJoinRelationComputer()。
#### 1、新建一个model，可以复制PolicyChangeModel.php，修改表名、主键、字段以及getWhere方法即可。

    public const TABLE_NAME = 'TPolicyChange'; // 定义表名
    public const PRIMARY_KEY = 'ChangeID'; // 定义主键
    protected static $columns = [           // 定义获取的字段
        '*'    => '*',
        'one'  => 'Mac,IP,DeviceID',
    ];
    
    /**
     * 定义返回where条件语句，主要在delete、getCount、getList、getSingle、updatePatch方法中调用，根据条件删除，查询，修改数据。
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
    
#### 2、根据主键获取一条数据：getOne，如DeviceModel::getOne($设备ID)
#### 3、根据条件获取一条数据：getSingle，如DeviceModel::getSingle(['DeviceID' => $设备ID])
#### 4、插入数据：insert，如DeviceModel::insert(['DeviceID' => $设备ID])
#### 批量插入数据：insertPatch，如DeviceModel::insertPatch([['DeviceID' => $设备ID1], ['DeviceID' => $设备ID2]]);
#### 5、调用存储过程：callFunction，如DeviceModel::callFunction('proc_ReCreateView');，原则同样存储过程主要是操作哪个表，就用哪个表对应的model去调用。
#### 6、修改数据：update，如DeviceModel::update(5, ['DeviceID' => $设备ID])，第一个参数为主键的值。
#### 根据条件批量修改数据：updatePatch，如DeviceModel::updatePatch(['Mac' => $mac], ['IP' => $IP]);
#### 7、根据条件获取多条数据：getList，如DeviceModel::getList(['Mac' => $mac], false, 0, 20)
#### 根据条件获取数据条数：getCount，如DeviceModel::getCount(['Mac' => $mac])
#### 8、根据条件删除数据：delete，如DeviceModel::delete(['Mac' => $mac])