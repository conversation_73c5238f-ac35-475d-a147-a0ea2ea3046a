<?php

/**
 * Description: 设备所有IP，MAC表
 * User: <EMAIL>
 * Date: 2021/05/25 10:32
 * Version: $Id
 */

class DevAllIPMACModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevAllIPMAC';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'hand' => 'IsHandType,IsHandDevName',
        'name' => 'ComputerName',
        'one'  => 'Mac,IP,DeviceID',
        'dasc' => 'DAscID, OrigID',
        'info' => 'DevName, ComputerName, IP, Mac, SwitchIP, SwitchPort, UserName, DepartId, DeviceID',
        'role' => 'RoleID, DepartID, IP, Tel, EMail',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if(isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if(isset($cond['Mac'],  $cond['Mac'])) {
            $where .= "AND  Mac = ".self::setData($cond['Mac']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
