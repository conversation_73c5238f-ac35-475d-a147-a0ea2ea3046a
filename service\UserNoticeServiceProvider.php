<?php
/**
 * Description: 用户消息相关的逻辑
 * User: <EMAIL>
 * Date: 2023/10/30 15:53
 * Version: $Id: UserNoticeServiceProvider.php
 */

class UserNoticeServiceProvider extends BaseServiceProvider
{

    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'user_notice';

    /**
     * 修改设备信息
     *
     * @param $userId
     * @param $start
     * @param $limit
     * @param int $isSetRead
     * @return array
     * @throws Exception
     */
    public static function getNoticeList($userId, $start, $limit, int $isSetRead = 0): array
    {
        //判断用户id是否正确
        $userInfo = AuthUserModel::getSingle(['ID' => intval($userId)]);
        if (empty($userInfo)) {
            // 如果token不存在或者用户不存在，此处不反回错误信息，直接返回空 T(21120061);
            return ['total' => 0, 'unReadCount' => 0, 'userId' => $userId, 'list' => []];
        }
        $cond = ['UserID' => $userId];
        $allCount = UserNoticeModel::getCount($cond);
        $list = UserNoticeModel::getList($cond, "NoticeID DESC", $start, $limit);
        //未读数量
        $unReadCount = UserNoticeModel::getCount(['UserID' => $userId, 'Status' => 0]);
        //目前会将所有的标记为已读
        if ($isSetRead == 1 && $unReadCount > 0) {
            try {
                UserNoticeModel::setNoticeRead([], $userId, ['Status' => 1]);
                $noticeIdArr = array_column($list, "NoticeID");
                //调用admin-api Rp发送消息
                $dataArr = \lib_yar::clients('backend', 'sendWsMsg', [intval($noticeIdArr[0])], "127.0.0.1", 10 * 1000, 2);
                self::log('RPC调用sendWsMsg结果：' . json_encode($dataArr, JSON_UNESCAPED_UNICODE));
            } catch (\Exception $e) {
                self::log('getNoticeList fuc Err：' . $e->getMessage());
            }
        }
        return ['total' => $allCount, 'unReadCount' => $unReadCount, 'userId' => $userId, 'list' => $list];
    }

    /**
     * 设置消息已读
     *
     * @param $noticeIdArr
     * @param $userId
     * @param int $type 1全部已读，0根据id设置已读
     * @return bool
     * @throws Exception
     */
    public static function setNoticeRead($noticeIdArr, $userId, int $type = 1): bool
    {
        //判断用户是否存在
        $userInfo = AuthUserModel::getSingle(['ID' => intval($userId)]);
        if (empty($userInfo)) {
            T(21120061);
        }
        //判断是全部标记还是部分标记
        if ($type == 1) {
            return UserNoticeModel::setNoticeRead([], $userId, ['Status' => 1]);
        }
        return UserNoticeModel::setNoticeRead($noticeIdArr, 0, ['Status' => 1]);
    }


}
