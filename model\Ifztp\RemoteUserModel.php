<?php

/**
 * Description: 远程应用的账号
 * Version: $Id$
 */

class RemoteUserModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TRemoteUser';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'info' => 'UserID,RemoteUserName,RemotePwd'
    ];


    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        if (isset($cond['WinRunRemote'])) {
            $where .= "AND WinRunRemote = ".self::setData($cond['WinRunRemote']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
