<?php

/**
 * Description: 安检结果信息 原item_result_info.class.php
 * User: <EMAIL>
 * Date: 2021/07/23 15:53
 * Version: $Id: xml_check_item_result.php 151146 2021-07-23 10:03:16Z duanyc $
 */


/**
 * ***************************************** 解析安检结果信息 **********************************************
 */
class xml_check_item_result extends cls_xml
{
    /**
     * 构造函数
     *
     * @param $str
     *
     * @throws Exception
     */
    public function __construct($str)
    {
        $str = str_replace(array('\"','&#61204;'), array('"','μT'), $str);
        $str = stripslashes(stripslashes($str));
        parent::__construct($str);
    }

    /**
     * 解析检查项公共信息
     */
    public function parseHead()
    {
        $this->res['InsideName'] = $this->xml->CheckType->InsideName;
        $this->res['OutsideName'] = $this->xml->CheckType->OutsideName;
        $this->res['Result'] = $this->xml->CheckType->Result;
        $this->res['Message'] = $this->xml->CheckType->Message;
        $this->res['DllVersion'] = $this->xml->DllVersion;
    }

    /**
     * 解析xml
     * @return mixed
     */
    public function parseXml()
    {
        $this->parseHead ();
        $this->convertEncode ();
        return $this->res;
    }
}
