<?php

/**
 * Description: Msep联动回复通知表
 * User: <EMAIL>
 * Date: 2024/04/08 10:32
 */

class LinkAgeReplyModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TLinkageReply';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = " . self::setData($cond['Type']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
