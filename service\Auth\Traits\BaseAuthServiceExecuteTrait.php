<?php
/**
 * Description: 认证中代码块 只能BaseAuthService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: BaseAuthServiceExecuteTrait.php 167278 2022-01-14 07:28:57Z duanyc $
 */

namespace Services\Auth\Traits;

use DeviceModel;
use DictModel;
use Exception;
use lib_yar;
use RelationComputerModel;

trait BaseAuthServiceExecuteTrait
{
    /**
     * 获取认证信息
     *
     * @param $userInfo
     * @param $businessInfo
     *
     * @return array
     * @throws Exception
     */
    public function getAuthInfo($userInfo, $businessInfo)
    {
        $aResult = $this->getAuthDataFromDevice($userInfo['RoleID']);
        $this->params['factorAuth'] = $aResult['FactorAuth'] = $businessInfo['FactorAuth'];
        $this->params['userName'] = $aResult["UserName"] = $businessInfo['UserName'];
        $this->params['beforeAuthType'] = $aResult["BeforeAuthType"] = $businessInfo['BeforeAuthType']; //双因子前一步的认证方式
        $this->params['authType'] = $aResult['AuthType'] = $businessInfo['AuthType'];//认证结果里面带当前的认证方式
        $this->params['syncToDevice'] = $businessInfo['SyncToDevice'];// 是否同步用户数据到设备
        $this->params['sendMessage'] = $businessInfo['SendMessage'];// 是否同步用户数据到设备
        return $this->mergeResult($userInfo, $aResult);
    }

    /**
     * 获取认证类型
     * @return mixed
     */
    public function getAuthType()
    {
        return $GLOBALS['AUTH_CONFIG']['AuthType'][$this->params['authType']] ?? $this->params['authType'];
    }

    /**
     * 合并返回结果
     *
     * @param $userInfo
     * @param $result
     *
     * @return array
     */
    public function mergeResult($userInfo, $result): array
    {
        $aResult = array_merge($userInfo, $result);
        $aResult['Tel'] = $userInfo['Tel'] ?: $aResult['Tel'];
        $aResult['EMail'] = $userInfo['EMail'] ?: $aResult['EMail'];
        return $aResult;
    }

    /**
     * 是否设备角色优先
     *
     * @param $RoleID
     * @param $AuthParam
     *
     * @return bool
     */
    public function deviceAuthPriority($RoleID, $AuthParam)
    {
        if (in_array($this->params['authType'], [AUTH_TYPE_GUEST, AUTH_TYPE_NOAUTH])) {
            return false;
        }

        if ($RoleID == ROLE_ID_TRUST && $AuthParam['IsTrustUser'] == '1') {
            return false;
        }

        // AuthPriority 0为设备角色优先，1为账户角色优先
        if (!(int)$AuthParam['AuthPriority']) {
            return true;
        }

        return false;
    }

    /**
     * 是否可信认证
     *
     * @param $RoleID
     * @param $AuthParam
     *
     * @return bool
     */
    public function isTrustAuth($RoleID, $AuthParam)
    {
        // 设备角色优先时，
        if (!(int)$AuthParam['AuthPriority']) {
            if (in_array($this->params['authType'], [AUTH_TYPE_GUEST, AUTH_TYPE_NOAUTH])) {
                return false;
            }
        }

        if ($RoleID == ROLE_ID_TRUST && $AuthParam['IsTrustUser'] == '1') {
            return true;
        }

        return false;
    }

    /**
     * 获取设备的认证信息 原getAuthDataFromDevice
     *
     * @param string $defRoleId
     * @throws Exception
     * @return array
     */
    public function getAuthDataFromDevice($defRoleId = '')
    {
        cutil_php_log("getAuthDataFromDevice:{$defRoleId}", $this->logFileName);
        //如果是双因子认证 则不返回结果 依然使用用户名认证的结果
        if (!empty($this->params['twoAuthType']) && $this->params['twoAuthType'] === 'TwoFactor') {
            return array('TwoFactor' => true);
        }
        $AuthParam = DictModel::getAll('AUTHPARAM');
        if (!(int)$AuthParam['AuthPriority']) {
            //认证后优先采用设备角色 如果是不认证则默认采用用户优先角色
            lib_yar::clients('net', 'UpdateDeviceRole', ['deviceId' => $this->deviceId]);
        }
        $isDeviceRole = $this->deviceAuthPriority($defRoleId, $AuthParam);
        cutil_php_log("deviceAuthPriority:{$isDeviceRole}", $this->logFileName);
        $aResult = DeviceModel::getJoinRelationComputer($this->deviceId, 'relationScene');
        if ($isDeviceRole) {
            $defRoleId = $aResult['RoleID'];
        }
        // 获取默认角色：账户角色->设备角色-》部门角色-》缺省。
        $isTrustAuth = $this->isTrustAuth($defRoleId, $AuthParam);
        if ($isTrustAuth) {
            $this->credibleUserToCredibleDevice();
        }
        cutil_php_log("isTrustAuth:{$isTrustAuth}", $this->logFileName);
        if (empty($defRoleId)) {
            // 设备角色
            if (empty($aResult['RoleID'])) {
                // 部门角色
                $aResult = DeviceModel::getDepartRole($this->deviceId);
                // 缺省角色
                if (!(int)$aResult['RoleID']) {
                    $aResult['RoleID'] = '1';  //缺省角色
                    $aResult['DepartID'] = '0';
                }
            }
        } else {
            // 账户角色
            $aResult['RoleID'] = $defRoleId;
        }
        cutil_php_log("getRoleInfo RoleID:{$aResult['RoleID']},SceneID:{$aResult['SceneID']}", $this->logFileName);
        $aResult['SceneID'] = (int)$aResult['SceneID'] > 0 ? (int)$aResult['SceneID'] : 1;
        // 这里获取的场景信息不是最新的，返回的是上一次的数据
        // TODO 返回的字段不确定客户端或服务端是否还有使用，使用了则数据存在不正确
        $aResult1 = $this->getSceneInfoById($aResult['SceneID'], $this->params['newMobile']);
        $aResult = array_merge($aResult, $aResult1);
        if ($isDeviceRole && !empty($defRoleId)) {
            $aResult['RoleID'] = $defRoleId;
        }

        $aResult['IsSceneChange'] = $this->getSceneChange($aResult['SceneID']);
        $aResult["DeviceID"] = (string)$this->deviceId;//认证结果里面带设备ID，按业务来说，必须先有设备才能有认证
        $aResult['IsDeviceRole'] = $isDeviceRole;// 是否设备角色优先
        return $aResult;
    }

    /**
     * @如果是可信账户认证通过之后则终端为可信设备 原credibleUserToCredibleDevice方法
     * @throws Exception
     */
    public function credibleUserToCredibleDevice()
    {
        if ($this->deviceInfo['Registered'] != 1) {
            include_function('device');
            if (getDevNumIn() == 0) {
                T(21103012);
            }
        }
        $data = ['IsTrustDev' => STRING_TRUE, 'CutOffStopTime' => DEFAULT_TIME];
        RelationComputerModel::update($this->deviceId, $data);
        DeviceModel::update($this->deviceId, ['RoleID' => ROLE_ID_TRUST]);
    }

    /**
     * 判断场景是否改变
     *
     * @param $sceneId
     *
     * @return int|string
     */
    public function getSceneChange($sceneId)
    {
        if (!empty($this->params['twoAuthType']) && $this->params['twoAuthType'] === 'TwoFactor') {
            return '0';
        }

        // 场景变了通过返回值告知小助手，防止切换来宾，可信角色时，用之前的策略进行安检
        $lastSceneID = isset($this->onlineDeviceInfo['SceneID']) ? (int)$this->onlineDeviceInfo['SceneID'] : 0;
        return $lastSceneID !== (int)$sceneId ? '1' : '0';
    }
}
