<?php
/**
 * Description: 用户相关逻辑service
 * User: <EMAIL>
 * Date: 2021/06/18 10:02
 * Version: $Id: UserServiceProvider.php 165120 2021-12-16 09:55:17Z duanyc $
 */
use Services\Common\Services\DepartService;

class UserServiceProvider extends BaseServiceProvider
{
    /**
     * 修改密码
     *
     * @param $type
     * @param $username
     * @param $oldpass
     * @param string $newpass 加密新密码
     * @param string $newpassword 明文新密码
     *
     * @return array
     * @throws Exception
     */
    public static function changePassword($type, $username, $oldpass, $newpass, $newpassword)
    {
        $userinfo = AuthServiceProvider::getUserByUserName($type, $username);
        $roleId = $userinfo['RoleID'] ? $userinfo['RoleID'] : 0;
        $aparams = [];
        $aparams['AuthType'] = 2;
        $aparams['RoleID'] = $roleId;
        $aparams['UserName'] = $username;
        $aparams['DeviceID'] = 0;
        if (empty($userinfo) || $userinfo['auth_password'] != $oldpass) {
            self::addUserActionRecord($aparams, L(21137003), 0);
            T(21137003);
        }
        if (!in_array($type, ['User', 'UKey'])) {
            self::addUserActionRecord($aparams, L(21137004), 0);
            T(21137004);
        }
        //hlp_check::checkPasswd($newpassword); bug:4192 去除中文校验
        $return = array();
        $Password2 = doXorEncrypt($newpassword, "infogoasm");
        $Password2 = stringTohex($Password2);
        $uparams = [];
        $uparams['PassWord'] = $newpass;
        $uparams['Password2'] = $Password2;
        $uparams['MustChange'] = 0;
        $uparams['PassUpdateTime'] = 'now()';
        //$result = AuthUserModel::updateByUsername($type, $username, $uparams);
        \AuthUserModel::update($userinfo['ID'], $uparams);
        $result = \AccountModel::updateByUsername($type, $username, ['auth_password' => $newpass, 'auth_credential' => $Password2]);
        if ($result !== false) {
            cutil_exec_cmd(PATH_ASM . "sbin/get_vpn_user &");//通知vpn密码被修改
            $return['ok'] = L(********);
            self::addUserActionRecord($aparams); //用户行为记录
        }
        return $return;
    }

    /**
     * 添加用户行为记录
     *
     * @param $params
     * @param string $failLog
     * @param int $isSuccess
     * @param int $checkTid
     *
     * @return int
     */
    public static function addUserActionRecord($params, $failLog = '', $isSuccess = 1, $checkTid = 0)
    {
        $params['InsertTime'] = 'now()';
        $params['OffLineTime'] = 'now()';
        $params['FailReason'] = $failLog;
        $params['IsSuccess'] = $isSuccess;
        $params['CheckTID'] = $checkTid;
        return UserActionRecordModel::insert($params);
    }

    /**
     * 注册账号
     *
     * @param $uparams
     * @param $DeviceID
     *
     * @return bool
     * @throws Exception
     */
    public static function registerUser($uparams, $DeviceID)
    {
        if (empty($uparams['Type']) || empty($uparams['UserName'])) {
            return false;
        }
        // 判断名称是否用过了
        $accountInfo = \AccountModel::getOneByUserName('', $uparams['UserName']);
        $userinfo = \AuthUserModel::getSingle(['UserName' => $uparams['UserName'], 'column' => 'one']);
        if (!empty($accountInfo) || !empty($userinfo)) {
            T(********);
        }
        $uparams['LifeTime'] = '99999';
        $uparams['Remark'] = L(********);
        $uparams['CreateUser'] = '';
        $uparams['RoleID'] = ROLE_ID_DEFAULT;
        $ip = $uparams['IP'];
        unset($uparams['IP']);//bugID:1600
        $userId = AuthUserModel::insert($uparams);
        //后台执行
        sendSocketFunc('127.0.0.1', '36532', PATH_ASM . "sbin/get_vpn_user &");
        $deviceInfo = \DeviceModel::getJoinComputerAndDepart($DeviceID);
        $deviceInfo['UserName'] = $uparams['UserName'];
        $deviceInfo['TrueNames'] = $uparams['TrueNames'];
        $deviceInfo['IP'] = isset($deviceInfo['IP']) && !empty($deviceInfo['IP']) ? $deviceInfo['IP'] : $ip;
        lib_alarm::createRegisterAlarm($deviceInfo);
        return $userId;
    }

    /**
     * 保存用户信息
     *
     * @param $uparams
     *
     * @return bool|int
     */
    public static function saveUser($uparams)
    {
        if (empty($uparams['Type']) || empty($uparams['UserName'])) {
            return false;
        }

        // 判断名称是否用过了
        $userinfo = AuthUserModel::getOneByUserName($uparams['Type'], $uparams['UserName'], '*');

        if (!empty($userinfo)) {
            $userId = $userinfo['ID'];
            foreach ($uparams as $column => $val) {
                if ($val == $userinfo[$column]) {
                    unset($uparams[$column]);
                }
            }
            if (!empty($uparams)) {
                AuthUserModel::update($userId, $uparams);
            }
        } else {
            $userId = AuthUserModel::insert($uparams);
        }
        return $userId;
    }

    /**
     * 保存部门信息
     *
     * @param $uparams
     *
     * @return bool|int
     */
    public static function saveDepart($uparams)
    {
        if (!isset($uparams['UpID']) || empty($uparams['DepartName'])) {
            return false;
        }

        // 判断名称是否用过了
        $cond = ['UpID' => $uparams['UpID'], 'DepartName' => $uparams['DepartName'], 'column' => '*'];
        $depart = DepartModel::getSingle($cond);

        if (!empty($depart)) {
            $departID = $depart['DepartID'];
            foreach ($uparams as $column => $val) {
                if ($val == $depart[$column]) {
                    unset($uparams[$column]);
                }
            }
            if (!empty($uparams)) {
                DepartModel::update($departID, $uparams);
            }
        } else {
            $departID = DepartModel::insert($uparams);
        }
        return $departID;
    }

    /**
     * 获取用户分布式ID
     *
     * @return int
     * @throws Exception
     */
    public static function getSequenceId($type)
    {
        $ID = SequenceIdModel::insert(['Value' => $type]);

        if (empty($ID)) {
            T(********);
        }

        switch ($type) {
            case 'user':
                $data = AuthUserModel::getSingle(['column' => 'max']);
                break;
            case 'depart':
                $data = DepartModel::getSingle(['column' => 'max']);
                break;
            case 'account':
                $data = AccountModel::getSingle(['column' => 'max']);
                break;
        }

        if (!empty($data['MaxID']) && $data['MaxID'] + 1 > $ID) {
            $MaxID = $data['MaxID'] + 1;
            $ID = SequenceIdModel::insert(['Value' => "{$type}-{$MaxID}", 'ID' => $MaxID]);
        }

        return $ID;
    }

    /**
     * 获取部门信息
     *
     * @param $devip
     *
     * @return array
     */
    public static function getDepartInfo($devip)
    {
        $departList = DepartModel::getList(['column' => 'depart'], 'departlevel asc', 0, 20000);
        $aDepID =array();
        $depinfo = array();
        /* 遍历所有部门，找到符合部门，包括未填写IP段的部门，填写的IP段包含设备IP的部门 */
        foreach ($departList as $key => $value) {
            if (!empty($value['IPSegment'])) {
                /* 获得某部门所有关联IP段,分割后依次判断 */
                $flag = 0;
                $tmp[$key] = explode('|', $value['IPSegment']);
                foreach ($tmp[$key] as $value2) {
                    $tmp2 = explode('-', $value2); //获得起始和结束IP
                    /* 查找设备IP是否在这段起止IP中 */
                    if (SystemServiceProvider::compareIP($devip, $tmp2[0], $tmp2[1])) {
                        $rowData = $departList[$key];
                        $depinfo[$rowData['DepartID']] = $rowData; //将符合的部门信息记录到数组中
                        $flag = 1;
                    }
                }
                /* 部门有IP段，且IP段中不含设备IP，记录下来 */
                if (!$flag) {
                    $aDepID[] = $value['DepartID'];
                    $aDepID = array_unique($aDepID);
                }
            } else {
                // 没有配置IP段，看看上级是否存在(根据层级排序后，上级一定是先处理过的)
                // 如果上级需要显示，则子部门在没有配置IP段的情况下一定需要显示，最顶级的部门默认为0.0.0.0 - *************** 且不可修改
                // update by RC 2020-09-04
                if (array_key_exists($value['UpID'], $depinfo)) {
                    $depinfo[$departList[$key]['DepartID']] = $departList[$key];
                }
            }
        }

        $depinfo = array_values($depinfo);
        // 不知道为什么要单独取下顶级部门的，因为取数据的时候排序了，所以去掉这个复杂的流程 update by RC 2020-09-04
        $depinfo[0]['seldepartid'] = $depinfo[0]['DepartID'];
        $depinfo[0]['seldepartname'] = $depinfo[0]['AllDepartName'];
        return $depinfo;
    }

    /**
     * 获取默认部门信息
     *
     * @param $ip
     *
     * @return int|mixed
     * @throws \Services\Common\Exceptions\AsmException
     */
    public static function getDefaultDepart($ip)
    {
        $return = [];
        $departService = new DepartService();
        $departId = $departService->getDepartByIp($ip);
        $subCount = DepartModel::getCount(['UpID' => $departId]);
        if (!empty($subCount)) {
            $return['selDepartId'] = '';
            $return['selDepartName'] = '';
        } else {
            $return['selDepartId'] = strval($departId);
            $departInfo = DepartModel::getOne($departId, 'depart');
            $return['selDepartName'] = $departInfo['AllDepartName'];
        }
        return $return;
    }

    /**
     * 针对AllDepartName为空的bug做特殊处理
     *
     * @param $departid
     * @param $departinfo
     * @param $CompanyName
     *
     * @return mixed
     */
    public static function getDepartAllDepareinfo($departid, $departinfo, $CompanyName)
    {
        foreach ($departinfo as $key => $value) {
            if ($departinfo[$key]['DepartID'] != $departid) {
                continue;
            }
            if ($departinfo[$key]['AllDepartName'] == null) {
                $AllDepartName = $departinfo[$key]['DepartName'];
                $AllDepartID = $departinfo[$key]['DepartID'];
                $upid = $departinfo[$key]['UpID'];
                if ($upid == 0) {
                    $AllDepartID="0/".$AllDepartID;
                    $AllDepartName=$CompanyName."/".$AllDepartName;
                } else {
                    $AllDepartinfo = self::getDepartAllDepareinfo($upid, $departinfo, $CompanyName);
                    $AllDepartID = $AllDepartinfo['AllDepartID']."/".$AllDepartID;
                    ;
                    $AllDepartName = $AllDepartinfo['AllDepartName']."/".$AllDepartName;
                }
                $departinfo[$key]['AllDepartName'] = $AllDepartName;
                $departinfo[$key]['AllDepartID'] = $AllDepartID;
            }
            return $departinfo[$key];
        }
        return false;
    }

    /**
     * 获取操作员可管理部门id
     *
     * @param string $user_name
     * @return string
     */
    public static function getOperatorRangeDepartID($user_name)
    {
        $aResult = OpDepartLimitModel::getList(['UserName' => $user_name, 'column' => 'depart']);
        $aDepartID = [];
        if (is_array($aResult)) {
            foreach ($aResult as $row) {
                $aDepartID[] = $row ['DepartID'];
            }
        }
        return @implode(",", $aDepartID);
    }

    /**
     * 获取过滤后的部门列表
     *
     * @param $DepartIds
     * @param $CompanyName
     *
     * @return array
     */
    public static function getFilterDepartList($DepartIds, $CompanyName)
    {
        //所有的部门
        $aDepartInfo = DepartModel::getList(['column' => 'tree'], 'OrderIndex', 0, 20000);
        foreach ($aDepartInfo as $key => $value) {
            if (!empty($value['IPSegment'])) {
                //分配了IP段，IP段不在设备所属的部门 则删除
                if (!isset($DepartIds[$value['DepartID']])) {
                    unset($aDepartInfo[$key]);
                }
            } else {
                //如果该部门不是一级部门，没有分配IP段，设备不在所所属IP，则删除
                if ($value['UpID'] != 0 && !isset($DepartIds[$value['DepartID']])) {
                    unset($aDepartInfo[$key]);
                }
            }
            unset($aDepartInfo[$key]['IPSegment']);
        }
        //上面unset了不少项目, 这里重新排index
        $aDepartInfo = array_values($aDepartInfo);
        //针对AllDepartName为空的bug做特殊处理
        foreach ($aDepartInfo as $key => $value) {
            if ($aDepartInfo[$key]['AllDepartName']==null) {
                $AllDepartinfo=self::getDepartAllDepareinfo($aDepartInfo[$key]['DepartID'], $aDepartInfo, $CompanyName);
                $aDepartInfo[$key]['AllDepartName']=$AllDepartinfo['AllDepartName'];
                $aDepartInfo[$key]['AllDepartID']=$AllDepartinfo['AllDepartID'];
            }
        }
        $alldepart=self::departOrder($aDepartInfo);
        return $alldepart;
    }

    /**
     * 获取部门列表
     *
     * @param $cond
     * @param $start
     * @param $limit
     *
     * @return array
     */
    public static function getDepartList($cond, $start = 0, $limit = 100)
    {
        $cond['column'] = 'tree';
        $departList = DepartModel::getList($cond, 'OrderIndex', $start, $limit);
        $returnDepartList = [];

        foreach ($departList as $key => $value) {
            if (isset($cond['devip'])) {
                $IPSegment = trim($value['IPSegment']);
                if (!empty($IPSegment) && $IPSegment != "" && !self::check_deviceip($cond['devip'], $IPSegment)) {
                    continue;
                }
            }
            $returnDepartList[] = $value;
        }
        if (empty($cond['UpID'])) {
            $returnDepartList=self::departOrder($returnDepartList);
        }
        return $returnDepartList;
    }

    /**
     * 获取部门数量
     *
     * @param $cond
     *
     * @return mixed
     */
    public static function getDepartCount($cond)
    {
        return DepartModel::getCount($cond);
    }

    /**
     * 获取下级部门组织架构树高效函数，仅查询一次数据库 原getAllChildDepartInfo方法
     * @param $departID
     * @param $column
     * @return array
     * @throws Exception
     */
    public static function getAllChildDepartInfo($departID = "0", $column = 'depart')
    {
        if ($departID === "") {
            return [];
        }
        $cond = ['column' => $column, 'GteUpID' => 0];
        $order = 'departlevel asc, OrderIndex asc';
        $departList = DepartModel::getList($cond, $order, 0, 20000);
        $array = array();
        $filter_array = array();
        if (count($departList) > 0) {
            foreach ($departList as $row) {
                $array[$row['UpID']][$row['DepartID']] = $row;
            }
            foreach ($array as $key1 => $val1) {
                // 一级部门
                if ($key1 == $departID) {
                    foreach ($val1 as $key2 => $val2) {
                        $filter_array[$key2] = $val2;
                    }
                    continue;
                }
                // 二级部门及以下
                if (array_key_exists($key1, $filter_array)) {
                    foreach ($val1 as $key2 => $val2) {
                        $filter_array[$key2] = $val2;
                    }
                }
            }
        }

        $depinfo = array_values($filter_array);
        return $depinfo;
    }

    /**
     * 获取子树数据
     *
     * @param $departID
     * @param $allDeparts
     * @param string $prefix
     * @param bool $firstLevel
     *
     * @return array|bool
     */
    public static function getChildTreeNode($departID, $allDeparts, $prefix = "  └", $firstLevel = false)
    {
        if (!isset($allDeparts[$departID])) {
            return false;
        }
        $returnDeparts = array();
        foreach ($allDeparts[$departID] as $k => $v) {
            $tmpfix=$prefix;
            if ($firstLevel===false) {
                $v['DepartName'] = $prefix . $v['DepartName'];
                $tmpfix = "　" . $prefix;
            }
            unset($v['departlevel']);
            unset($v['UpID']);
            $returnDeparts[] = $v;
            $currentChild = self::getChildTreeNode($v['DepartID'], $allDeparts, $tmpfix);
            if (is_array($currentChild)) {
                $returnDeparts = array_merge($returnDeparts, $currentChild);
            }
        }
        return $returnDeparts;
    }

    /**
     * @param $devip
     * @param $IPSegment
     * @return bool
     * 判断设备ip是否在规定范围内 true为在范围内，false不在范围内
     */
    public static function check_deviceip($devip, $IPSegment)
    {
        if (!empty($IPSegment) && $IPSegment != "") {
            $tmp = explode("|", $IPSegment); //获得某部门所有关联IP段
            foreach ($tmp as $value) {
                $tmp2 = explode("-", $value); //获得起始和结束IP
                if (FindInIP($devip, $tmp2[0], $tmp2[1])) {
                    return  true;
                }
            }
        }
        return false;
    }

    /**
     * 对所有部门数据按路径深度排序
     * 仅仅是为了将上级部门排到下级部门之前
     * wangjian 20180818
     *
     * @param array $aLastDepart
     * @return array
     */
    public static function departOrder($aLastDepart)
    {
        if (!is_array($aLastDepart)) {
            return array();
        }
        foreach ($aLastDepart as $key => &$depart) {
            $depart['c'] = strval(substr_count($depart['AllDepartName'], '/'));
            $depart['_key'] = $key;
        }
        unset($depart); //for safety

        usort($aLastDepart, function ($a, $b) {
            if ($a['c'] === $b['c']) {
                //由于输入数组是经过orderIndex排序的, 所以需要保持原有排序的稳定
                return $a['_key'] - $b['_key'];
            }
            return $a['c'] - $b['c'];
        });

        //清理_key
        foreach ($aLastDepart as &$depart) {
            unset($depart['_key']);
        }

        return $aLastDepart;
    }

    /**
     * 获取AD域数据
     *
     * @param $domain
     *
     * @return array|bool
     */
    public static function getAdDomain($domain)
    {
        if (empty($domain)) {
            return false;
        }

        return AdDomainModel::getSingle(['Domain' => $domain, 'column' => 'info']);
    }

    /**
     * 校验用户id对应的账户是否是管理员账户
     *
     * @param $userid
     *
     * @return bool
     */
    public static function checkIsManageAccount($userid)
    {
        $AuthUserModel = \AuthUserModel::getOne($userid, '*');
        $CurrentSecretKey = $AuthUserModel['IsManageAccount'];
        return $CurrentSecretKey ? true : false;
    }

    /**
     * 获取指定帐号生成的移动端APP管理平台URL
     *
     * @param $userid
     *
     * @return string
     */
    public static function getAPPManageUrl($userid)
    {
        $AuthUserModel = \AuthUserModel::getOne($userid, '*');
        $CurrentSecretKey = $AuthUserModel['IsManageAccount'];
        if (!$CurrentSecretKey) {
            T(********);
        }
        if (self::checkIsValid($userid)) {
            $Token = self::checkIsValid($userid);
        } else {
            $adminInfo = OperatorModel::getSingle(['UserName' => "admin"]);
            $setTimeOut = cutil_dict_get('ADMINISTRATORINFO', 'SetTimeOut');
            $date = date("Y-m-d H:i:s", (time() + $setTimeOut * 60));
            $params['Login'] = 'admin';
            $params['IP'] = getRemoteAddress();
            $params['Token'] = hlp_common::rundnum();
            $params['Remark'] = '移动端APP管理平台登录成功！';
            $params['Alias'] = $adminInfo['Alias'];
            $params['OffTime'] = $date;
            $params['Browser'] = "Chrome";
            $params['LoginType'] = "APP";
            $params['AuthAccount'] = $userid;
            UserLoginModel::insert($params);
            $Token = $params['Token'];
        }
        $key_json = "token:'".$Token."',login:'admin',sysflag:'1',gLang:'zh',from:'app'";
        $key = Base64EnExt($key_json);
        return "/m/mod/index.html?key=".$key;
    }

    /**
     * 校验帐号ID的token是否还在有效期内
     *
     * @param $userid
     *
     * @return bool|string
     */
    public static function checkIsValid($userid)
    {
        $UserLog = UserLoginModel::getSingle(['AuthAccount' => (string)$userid,'IP' => getRemoteAddress()], 'ID desc');
        $OffTime = $UserLog['OffTime'];
        $Token = $UserLog['Token'];
        return strtotime($OffTime) > time() ? $Token : false;
    }
}
