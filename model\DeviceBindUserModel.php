<?php

/**
 * Description: TDeviceBindUser 设备绑定用户表
 * User: <EMAIL>
 * Date: 2021/05/27 10:32
 * Version: $Id.
 */
class DeviceBindUserModel extends BaseModel
{
    /**
     * 表名.
     *
     * @var string
     */
    public const TABLE_NAME = 'TDeviceBindUser';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'IP,MAC',
        '*' => '*',
    ];

    /**
     * 根据设备ID获取设备绑定的用户.
     *
     * @param $DeviceID
     *
     * @return null|false|mixed
     */
    public static function getBindUserJoinAuthUser($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $where = "WHERE A.AuthUserID = B.ID AND A.Type = 0 AND A.DeviceID = ".self::setData($DeviceID);
        $sql = "SELECT B.Type, B.UserName FROM TDeviceBindUser A, TAuthUser B {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 根据用户名（类型）获取用户绑定的设备.
     *
     * @param string $userName
     * @param string $type
     *
     * @return null|false|mixed
     */
    public static function getBindDevJoinAuthUser(string $userName = '', string $type = '')
    {
        if (empty($userName)) {
            return false;
        }
        self::$data = [];
        $sql = "SELECT A.DeviceID FROM TDeviceBindUser A, TAuthUser B WHERE A.AuthUserID = B.ID AND A.Type = 1 AND  B.UserName = ".self::setData($userName);

        if ($type) {
            $sql .= " AND B.Type = ".self::setData($type);
        }

        return lib_database::getAll($sql, '', false, self::$data);
    }

    /**
     * 获取条件.
     *
     * @param array $cond
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }

        if (isset($cond['AuthUserID'])) {
            $where .= "AND AuthUserID = ".self::setData($cond['AuthUserID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
