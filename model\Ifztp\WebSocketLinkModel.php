<?php
/**
 * Description: WebSocket连接表
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: GateWayModel.php 174952 2022-04-29 13:33:57Z duanyc $
 */

class WebSocketLinkModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TWebSocketLink';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'      => '*',
        'one'    => 'ID',
        'client' => 'LinkID',
        'ip'     => 'ID,GWIP',
        'all'    => 'ID,Name',
        'group'    => 'GWID,GWIP',
    ];

    /**
     * 获取GWID与LinkID映射
     *
     * @return mixed
     */
    public static function getAllGwIds()
    {
        self::$data = [];
        $column = self::$columns['group'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $allIds = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $allIds[$row['GWIP']] = $row['GWID'];
            }
        }
        return $allIds;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['LinkID'])) {
            $where .= "AND LinkID = ".self::setData($cond['LinkID']);
        }

        if (isset($cond['GWIP'])) {
            $where .= "AND GWIP = ".self::setData($cond['GWIP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}