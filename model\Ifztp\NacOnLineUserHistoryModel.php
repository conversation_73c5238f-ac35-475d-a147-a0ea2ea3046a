<?php

/**
 * Description: 用户回话历史记录
 * User: <EMAIL>
 * Date: 2023/11/16 10:32
 * Version: $Id: TNacOnLineUserHistoryModel.php 165094 2021-12-16 08:34:07Z duanyc $
 */

class NacOnLineUserHistoryModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacOnLineUserHistory';
    public const PRIMARY_KEY = 'Token';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['Token'])) {
            $where .= "AND Token = ".self::setData($cond['Token']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
