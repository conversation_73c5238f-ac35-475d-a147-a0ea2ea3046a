<?php

/**
 * Description: AD域
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: AdDomainModel.php 171725 2022-03-23 08:11:59Z huyf $
 */

class AdDomainModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TAdDomain';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'all'       => '*',
        'domain'    => 'Domain',
        'info'      => 'UserName,Password',
        'autoLogin' => 'AutoLogin,Domain',
        '*'         => '*',
    ];

    /**
     * 所有条目
     *
     * @param $column
     * @return mixed
     */
    public static function getAll($column = 'all')
    {
        $column = self::$columns[$column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']}";
        return lib_database::getAll($sql, $table['index']);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['Domain'])) {
            $where .= "AND Domain = ".self::setData($cond['Domain']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
