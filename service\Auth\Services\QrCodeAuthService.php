<?php
/**
 * Description: 二维码认证服务：目前只是高级动态认证的扫码使用
 * User: <EMAIL>
 * Date: 2021/08/03 15:53
 * Version: $Id: QrCodeAuthService.php 162798 2021-11-24 14:40:40Z duanyc $
 */

namespace Services\Auth\Services;

use Exception;
use Services\Auth\Interfaces\AuthServiceInterface;
use Services\Common\Services\DESService;

class QrCodeAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 初始化
     * http://*************/a/resource_protect.html
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        if (empty($this->params['qrCode'])) {
            T(21146002, ['column' => 'qrCode']);
        }
        $result = $this->decryptQrCode($this->params['qrCode']);
        if ($result === false) {
            T(21146002, ['column' => 'qrCode']);
        }
        $this->params['mobileDeviceID'] = $this->deviceId;
        $this->params['deviceID'] = $this->deviceId = $result['deviceID'];
        $this->params['authType'] = $result['authType'];
        $this->writeLog(var_export($this->params, true));
        return $this->params;
    }

    /**
     * 获取验证服务
     *
     * @return array
     * @throws Exception
     */
    public function getAuthServer(): array
    {
        return ['QrCode'];
    }

    /**
     * 解密QrCode
     * @param string $qrCode
     * @return array|false
     */
    public function decryptQrCode(string $qrCode = '')
    {
        if (empty($qrCode)) {
            return false;
        }

        $type = $qrCode[0];
        // A:agent，W:web，O:other
        if (!in_array($type, ['A', 'W', 'O'])) {
            return false;
        }

        // 密文
        $ciphertext = substr($qrCode, 1);
        $deQrCode = DESService::desEcbDecrypt(stringDecode($ciphertext));
        $this->writeLog($deQrCode);
        $deQrCode = explode('|', $deQrCode);
        if (count($deQrCode) < 3) {
            return false;
        }
        return [
            'deviceID' => trim($deQrCode[0]),
            'authType' => trim($deQrCode[1])
        ];
    }

    /**
     * 获取认证的字段
     * @return array
     */
    public function getAuthColumns()
    {
        return ['authType', 'deviceID', 'mobileDeviceID', 'time', 'isResourceAuth'];
    }
}
