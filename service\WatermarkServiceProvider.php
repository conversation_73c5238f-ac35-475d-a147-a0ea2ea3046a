<?php
/**
 * Description: 页面水印service
 * User: <EMAIL>
 * Date: 2022/06/10 10:32
 * Version: $Id$
 */

class WatermarkServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'app';

    /**
     * 判断资源是否开启水印
     * @param $userInfo
     * @return bool
     */
    public static function isOpenWatermark($userInfo): bool
    {
        $typeId = (int)($userInfo['TypeID'] ?? 0);
        $arrayKey = ($typeId <= ACCESS_TYPE_FORM || $typeId == ACCESS_TYPE_MICRO_APP) ? 'AppHost' : 'ResID';
        $redisKey = ($typeId <= ACCESS_TYPE_FORM || $typeId == ACCESS_TYPE_MICRO_APP) ? 'appcfg' : 'terminalcfg';
        $config = AppConfigRedis::getOne($redisKey);
        $resource = json_decode($config[$redisKey], true);
        if (isset($resource[$userInfo[$arrayKey]])) {
            $switch = $resource[$userInfo[$arrayKey]]['Watermark'] ?? '0';
            return $switch === '1';
        }
        return false;
    }

    /**
     * 获取水印内容
     * @param $session
     * @return array
     */
    public static function getWatermarkContent($session): array
    {
        $username = $time = $ip = "";
        $cfgStr = GateConfigRedis::getOne('watermark');
        $cfg = json_decode($cfgStr['Watermark'] ?? '', true);
        $arr = explode(',', $cfg['config'] ?? '');
        if (in_array('username', $arr)) {
            $username = $session['UserName'] ?? '';
        }
        if (in_array('time', $arr)) {
            $time = date('Y-m-d H:i:s');
        }
        if (in_array('ip', $arr)) {
            $ip = $_SERVER['REMOTE_ADDR'];
        }
        return ['switch' => true, 'content' => $cfg['content'], 'username' => $username, 'time' => $time, 'ip' => $ip];
    }
}
