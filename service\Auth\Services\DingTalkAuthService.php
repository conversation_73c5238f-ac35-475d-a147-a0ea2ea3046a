<?php
/**
 * Description: 钉钉认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: DingTalkAuthService.php 174774 2022-04-28 09:10:15Z huyf $
 */

namespace Services\Auth\Services;

use DeviceModel;
use Exception;
use hlp_common;
use Services\Auth\Interfaces\AuthServiceInterface;

class DingTalkAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'DingTalk';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['isAgent'] = request('isAgent', 'request', 0, 'int');
        $this->params['code'] = request('dingtalkcode', 'request');
        return $this->params;
    }

    /**
     * 移动端首页
     */
    public function main()
    {
        $code = request('dingtalkcode', 'request');
        $otherParams = hlp_common::otherAuthParams();
        $url = '/mobile/ui/wel.html?route_type=dingtalk&dingtalkcode=' . $code . "&{$otherParams}";
        $url = strlen($url) > 0 ? $url : '/mobile/ui/wel.html?1=1';
        cutil_php_log('Location:' . $url . '&t=' . time(), 'dingtalk');
        header('Location:' . $url . '&t=' . time());
        exit();
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);
        DeviceModel::update($this->deviceId, ['DepartID' => $userInfo['DepartID']]);
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'dingtalkcode', 'isAgent', 'appid'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'isAgent', 'userName', 'password', 'code', 'fwknopAuth'];
    }
}
