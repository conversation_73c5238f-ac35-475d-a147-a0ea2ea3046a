<?php

/**
 * Description: 用户，角色，部门，位置等交易
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: UserNoticeController.php  $
 */


!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/ZtpController.php";

class UserNoticeController extends ZtpController
{

    public $whiteList = ['getNoticeList' => true];
    public $nocheckZtpUser = ['getNoticeList' => true];
    /**
     * 获取我的消息列表
     * @throws Exception
     */
    public function getNoticeList(): array
    {
        $start = request('start', 'request', 0, 'int');
        $limit = request('limit', 'request', 20, 'int');
        $isSetRead = request('isSetRead', 'request', 0, 'int');
        return UserNoticeServiceProvider::getNoticeList($this->userId, $start, $limit,$isSetRead);
    }


    /**
     * 设置已读，如果带上具体消息ID则设置单条，否则设置全部已读
     *
     * @return array
     * @throws Exception
     */
    public function setNoticeRead(): array
    {
        $noticeId = request('noticeId', 'request', 0, 'array');
        UserNoticeServiceProvider::setNoticeRead($noticeId, $this->userId);
        return [];
    }

}
