<?php

/**
 * Description: redis zset通用redis
 * User: <EMAIL>
 * Date: 2024/05/15 20:32
 * Version: $Id: BaseZsetRedis.php 175039 2024-04-19 20:33:41Z xlj $
 */
class BaseZsetRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASG_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = '';

    /**
     * 获取key
     *
     * @param array $keys
     *
     * @return mixed
     */
    public static function getKey($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }

        return static::TABLE_NAME . ":" . implode('_', $keys);
    }

    /**
     * Returns the score of a given member in the specified sorted set
     *
     * @param mixed $member
     * @param mixed ...$keys
     * @return float|null
     */
    protected static function zScore($member, ...$keys)
    {
        $key = static::getKey($keys);
        return lib_redis::zScore(static::PREFIX, $key, $member);
    }

    /**
     * 计算个数
     * Returns the score of a given member in the specified sorted set
     *
     * @param mixed ...$keys
     * @return float|null
     */
    protected static function zCard(...$keys): ?float
    {
        $key = static::getKey($keys);
        return lib_redis::zCard(static::PREFIX, $key);
    }

    /**
     * 添加元素到有序集
     * @param $score
     * @param $value
     * @param ...$keys
     * @return mixed|null
     */
    protected static function zAdd($score, $value, ...$keys)
    {
        $key = static::getKey($keys);
        cutil_php_log(['zAdd', $key, $value, $score], "model_" . static::TABLE_NAME);
        return lib_redis::zAdd(static::PREFIX, $key, $score, $value);
    }

    /**
     * 从有序集中删除元素
     *
     * @param $member
     * @param array $keys
     *
     * @return false|int|Redis|null
     * @throws RedisException
     */
    protected static function zRem($member, ...$keys)
    {
        $key = static::getKey($keys);
        cutil_php_log(['zRem', $key, $member], "model_" . static::TABLE_NAME);
        return lib_redis::zRem(static::PREFIX, $key, $member);
    }

    /**
     * 获取对应的有序集合中的列表
     * Returns a range of elements from the ordered set stored at the specified key
     * @static
     *
     * @param int $start
     * @param int $stop
     * @param mixed ...$keys
     * @return mixed
     */
    protected static function zRange($start = 0, $stop = -1, ...$keys)
    {
        $key = static::getKey($keys);
        cutil_php_log(['zRange', $key, $keys, $start, $stop], "model_" . static::TABLE_NAME);
        return lib_redis::zRange(static::PREFIX, $key, $start, $stop);
    }

    /**
     * 删除
     *
     * @param array $keys
     *
     * @return boolean
     */
    public static function del(...$keys): bool
    {
        $key = static::getKey($keys);
        cutil_php_log(['del', $key], "model_" . static::TABLE_NAME);
        return lib_redis::del(static::PREFIX, $key);
    }

}
