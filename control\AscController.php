<?php

/**
 * Description: Asc相关
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: AscController.php 153494 2021-08-19 01:25:07Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class AscController extends BaseController
{
    /**
     * 获取asc的ip端口信息，对应老交易 get_asc_ipport
     *
     * @return mixed
     * @throws Exception
     */
    public function ipport()
    {
        $ascid = request('ascid', 'request');
        $deviceid = request('deviceid', 'request');
        return SystemServiceProvider::getAscInfo($ascid, $deviceid);
    }
}
