<?php
/**
 * Description: 零信任访问 基础策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;


class DefaultPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *
     * @return bool
     */
    public function check(): bool
    {
        try {
            \LoginServiceProvider::checkResult($this->params['session']['DeviceID']);
        } catch(\Exception $e) {
            $this->reason = $e->getMessage();
            return false;
        }
        return true;
    }
}
