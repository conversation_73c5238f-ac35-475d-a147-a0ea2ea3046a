<?php
/**
 * Description: 通用服务暴露
 * User: <EMAIL>
 * Date: 2021/06/25 8:53
 * Version: $Id: SystemServiceProvider.php 175451 2022-05-10 03:53:17Z huyf $
 */

use <PERSON><PERSON><PERSON>\Agent\Agent;

class SystemServiceProvider extends BaseServiceProvider
{
    /**
     * windows外的全部类型
     * @var array
     */
    public static $ostypes = [
        OSTYPE_LINUX,
        OSTYPE_MAC,
        OSTYPE_ANDROID,
        OSTYPE_IOS,
        OSTYPE_HARMANYOS,
    ];

    /**
     * 获取已注册数量
     * @return int
     */
    public static function getUseRegNum()
    {
        return DeviceModel::getUseCount();
    }

    /**
     * 获取最大注册数
     * @param $stopdata
     * @return int
     * @throws Exception
     */
    public static function getMaxRegNum(&$stopdata): int
    {
        $MaxRegNum = self::getModuleCount(); //判断设备是否超过最大注册数;

        $cache = lib_redis::get("ASG_ModuleRegist:", "stopData");
        if ($cache !== false) {
            $stopdata[0] = (int)$cache;
        } else {
            $stopdata[0] = cutil_exec_wait(PATH_USR . "bin/getmodulecount 1"); //获取试用到期时间
            lib_redis::set("ASG_ModuleRegist:", "stopData", $stopdata[0], 300);
        }
        if (!isset($stopdata[0]) || !$MaxRegNum) {
            cutil_exec_wait(PATH_USR . "bin/getmodulecount 1 > " . PATH_LOG . "/webreg.log");
            $stopdata[0] = file_get_contents(PATH_LOG . "/webreg.log");
            $MaxRegNum = self::getModuleCount('', true);
        }
        return (int)$MaxRegNum;
    }

    /**
     * 获取注册设备数量是否够，1：表示够，0：表示不够
     * @return int
     * @throws Exception
     */
    public static function getDevNumIn(): int
    {
        $MaxRegNum = self::getModuleCount(); //判断设备是否超过最大注册数;
        $useCount = DeviceModel::getUseCount();
        return ($MaxRegNum - $useCount) >= 0 ? 1 : 0;
    }

    /**
     * 获取授权是否在时间范围内，1：表示在，0：表示否
     *
     * @return int
     * @throws Exception
     */
    public static function getTimeIn(): int
    {
        $cache = lib_redis::get("ASG_ModuleRegist:", "stopData");
        if ($cache !== false) {
            $time = (int)$cache;
        } else {
            $time = cutil_exec_wait(PATH_USR . "bin/getmodulecount 1");
            lib_redis::set("ASG_ModuleRegist:", "stopData", $time, 300);
        }
        return ((int)date("Ymd") - (int)$time) > 0 ? 0 : 1;
    }

    /**
     * @Description:获取授权点数
     * @param string $ip asmip(asc向asm获取授权点数使用)
     * @param boolean $reTry 是否重试
     * @return string|null
     * @throws Exception
     */
    public static function getModuleCount($ip = '', $reTry = false)
    {
        static $maxRegNum = null;
        if ($maxRegNum !== null) {
            return $maxRegNum;
        }
        $devType = 'asm';
        $dev_path = PATH_ETC . "devinfo.ini";
        if (!file_exists($dev_path)) {
            $fpr = fopen($dev_path, 'a+');
            fclose($fpr);
        }
        $cfg = read_inifile($dev_path);
        $devType = $cfg['devtype'] ?? $devType;

        //dasc用/asm/sh/get_authorize_num.sh 1获取授权点数--zjc 2021.1.15
        $cmd = $devType === 'dasc' ? PATH_ASM . 'sh/get_authorize_num.sh 1' : PATH_USR . 'bin/getmodulecount 2';

        if ($reTry) {
            $cmd .= ' > ' . PATH_LOG . '/webregnum.log';
            cutil_exec_wait($cmd);
            $maxRegNum = file_get_contents(PATH_LOG . "/webregnum.log");
        } else {
            $cache = lib_redis::get("ASG_ModuleRegist:", "maxRegNum");
            if (empty($ip) && $cache !== false) {
                $maxRegNum = (int)$cache;
            } else {
                $maxRegNum = !empty($ip) ? cutil_exec_wait($cmd, 10, $ip) : cutil_exec_wait($cmd);
                lib_redis::set("ASG_ModuleRegist:", "maxRegNum", $maxRegNum, 300);
            }
        }

        return (int)$maxRegNum;
    }

    /**
     * 比较ip范围
     *
     * @param $currentIp
     * @param $startip
     * @param $endip
     * @return bool
     */
    public static function compareIP($currentIp, $startip, $endip)
    {
        $currentIpToLong = bindec(decbin(ip2long($currentIp)));
        return bindec(decbin(ip2long($startip))) <= $currentIpToLong &&
            $currentIpToLong <= bindec(decbin(ip2long($endip)));
    }

    /**
     * 是否可新注册
     * @return bool
     * @throws Exception
     */
    public static function isRegistrable()
    {
        $stopdata = [];
        $MaxRegNum = (int)self::getMaxRegNum($stopdata);
        $totalNum = self::getUseRegNum();
        if (IS_INTERNAL && ($totalNum >= ($MaxRegNum + 1) || (int)$stopdata[0] === 0)) {
            return false;
        }

        $stopdata[0] = trim($stopdata[0]); // 防止后面有换行符
        $date = substr($stopdata[0], 0, 4);
        $date .= "-" . substr($stopdata[0], 4, 2);
        $date .= "-" . substr($stopdata[0], -2);
        return !(date("Y-m-d", strtotime(date("Y-m-d"))) > date("Y-m-d", strtotime($date)));
    }

    /**
     * 获取系统引擎
     * @param bool $isTrust 是否获取自定义oem版本
     * @return mixed
     * @throws Exception
     */
    public static function getEngine(bool $isTrust = false)
    {
        $langConfFile = PATH_ETC . "version.ini";
        $ini_arr = read_inifile($langConfFile);
        if (!$isTrust && file_exists(PATH_HTML . '/oem/oem.ini')) {
            $oemEngine = read_inifile(PATH_HTML . '/oem/oem.ini');
            if (empty($oemEngine) || empty($oemEngine['engine'])) {
                $Engine = $ini_arr['ShowEngine'];
            } else {
                $Engine = $oemEngine['engine'];
            }
        } else {
            $Engine = $ini_arr['ShowEngine'];
        }
        return $Engine;
    }

    /**
     * 是否上传目录和路径
     *
     * @param $serverPathFie
     *
     * @return bool
     */
    public static function isUploadDir($serverPathFie)
    {
        if (strpos($serverPathFie, '..') !== false) {
            self::log("{$serverPathFie} forbid upload");
            return false;
        }
        $shortname = lib_upload::get_shortname('file');
        if (!in_array($shortname, lib_upload::$shortnames)) {
            self::log("{$serverPathFie} forbid upload '{$shortname}'");
            return false;
        }
        $whitePathList = [PATH_HTML . '/download/', PATH_LOG . '/'];
        foreach ($whitePathList as $path) {
            $len = strlen($path);
            if (substr($serverPathFie, 0, $len) == $path) {
                return $path;
            }
        }
        self::log("{$serverPathFie} forbid upload");
        return false;
    }

    /**
     * 获取asm信息
     * @param $ascid
     * @param $deviceid
     * @return mixed
     * @throws Exception
     */
    public static function getAscInfo($ascid, $deviceid)
    {
        $devtype = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if ($devtype === 'dasc') {
            if (file_exists(PATH_ETC . 'ascpatch.ini')) {
                $patchArr = read_inifile(PATH_ETC . 'ascpatch.ini');
                if ($patchArr['repairfrom'] === 'ASC') {
                    return false;
                }
                if ($patchArr['repairfrom'] === 'ASM') {
                    $asmArr = read_inifile(PATH_ETC . 'asc.ini');
                    $aResult['IP'] = $asmArr['ManageIp'] !== 'localhost' ? $asmArr['ManageIp'] : '127.0.0.1';
                    $aResult['AscPort'] = $asmArr['ManagePort'];
                    return $aResult;
                }
            }
            return false;
        } else {
            $aValue = ConfigServiceProvider::getDictOne('PatchUpdate', 'AscDownload');
            if ($aValue != '1') {
                return false;
            }
            if (strlen($ascid) > 0) {
                $aResult = DevASCInfoModel::getSingle(['AscID' => $ascid, 'column' => 'info']);
                return $aResult;
            }
            $device = DeviceModel::getOne($deviceid, 'asc');
            if (is_array($device)) {
                $aResult = DevASCInfoModel::getSingle(['AscID' => $device['AscID'], 'column' => 'info']);
                return $aResult;
            }
            return false;
        }
    }

    /**
     * 获取验证码并展示
     *
     * @param $type
     * @param $deviceid
     *
     * @throws Exception
     */
    public static function getValidateCode($deviceid)
    {
        $dict = DictModel::getOneItem('AUTHPARAM', 'verifyCodeType');
        $codetype = $dict['ItemValue'];
        $vcode = new cls_validate_code($codetype);        //实例化一个对象
        $vcode->doimg();
        $code = $vcode->getCode();
        $code = urlencode($code);
        cache_set_info('verifyCode', (int)$deviceid, array("vcode" => $code), 6000);
    }

    /**
     * 获取重定向信息
     *
     * @param $firsturl
     * @param $ascid
     *
     * @return mixed
     * @throws Exception
     */
    public static function getRedirectInfo($firsturl, $ascid)
    {
        $ascid = $ascid == "" ? "11:11:11:11:11:11" : $ascid;
        $logInfo = AscLogUrlModel::getSingle(['AscID' => $ascid]);
        $result = [];
        $logInfo = self::getAscLogInfo($logInfo, $firsturl);
        $result['redirectUrl'] = $logInfo['AccessUrl'];
        $result['redirectTime'] = $logInfo['redirectTime'] ?? 10;
        $result['reAccessUrl'] = self::setSdpDomainUrl();
        return $result;
    }

    /**
     * 获取是否要跳转到外网重定向地址
     * @return mixed
     * @throws Exception
     */
    public static function setSdpDomainUrl()
    {
        $ServerAddr = getServerAddr();
        $arr = explode(':', $ServerAddr);
        $domain = $arr[0];
        $sdp = parse_initfile(PATH_ETC . 'sdp.ini');
        $externaldomain = $sdp['AccessAddress'] ?? '';
        // 内网不跳
        if (IS_INTERNAL) {
            return '';
        }
        // 外网地址符合预期或者没填外网地址则不跳
        if (empty($externaldomain) || $domain === $externaldomain) {
            return '';
        }
        // 开启域名绑定判断的话  填的外网地址域实际地址不一样则跳转到forbidden页面
        $bindDomainSwitch = $sdp['BindDomainSwitch'] ?? 'off';
        return $bindDomainSwitch === "on" ? "https://{$ServerAddr}/mobile/ui/forbidden.html" : '';
    }

    /**
     * 获得控制器 安检后转向地址
     *
     * @param : $logInfo
     * @param : $firsturl
     *
     * @return mixed 使用返回重定向前访问的页面 url地址：返回指定的页面 ""//不做任何操作
     * @throws Exception
     */
    public static function getAscLogInfo($logInfo, $firsturl)
    {
        $firsturl = str_replace('?', '&', $firsturl);
        $firsturl = preg_replace("/&/", '?', $firsturl, 1);
        // 使用全局配置
        if ($logInfo['IsUseRemote'] == '1') {
            $cfg = parse_initfile(PATH_ETC . "asm/tbridge_comm.ini");
            if ($cfg['LogSpecifiedURL'] == "" && !$cfg['LogFirstURL']) {//不做任何操作
                $logUrl = "";
            } elseif ($cfg['LogFirstURL'] == 1) {
                $logUrl = 1;
            } else {
                $logUrl = $cfg['LogSpecifiedURL'];
            }
            $logInfo['redirectTime'] = $logInfo['redirectTime'] ?: 10;
        } else {
            if ($logInfo['IsUseBeforeUrl'] == "0" && $logInfo['LogSpecifiedURL'] == "") {//不做任何操作
                $logUrl = "";
            } elseif ($logInfo['IsUseBeforeUrl'] == '1') { // 返回重定向前访问的页面
                $logUrl = 1;
            } else {
                $logUrl = $logInfo['LogSpecifiedURL'];
            }
        }
        $AccessUrl = $logUrl == "1" ? $firsturl : $logUrl;
        $logInfo['AccessUrl'] = (($AccessUrl != "" && !preg_match("/^http(s)?:\/\//i", $AccessUrl)) ? HTTP_PROTOCOL : "") . $AccessUrl;
        return $logInfo;
    }

    /**
     * 获取移动端配置
     * @return mixed
     */
    public static function getMobileConfig()
    {
        $returnArr = DictModel::getAll("Mobile");
        $returnArr['wechat'] = "c";//c 关闭 o开启 a自动认证
        $returnArr['dingtalk'] = "a";//c 关闭 o开启 a自动认证
        $returnArr['wework'] = "a";//c 关闭 o开启 a自动认证
        $returnArr['feishu'] = "a";//c 关闭 o开启 a自动认证
        //获取钉钉入网配置
        $dingTalkConfig = \AuthServiceProvider::getAuthDomainConfig('DingTalk');
        $returnArr['QRCode'] = $dingTalkConfig['QRCode'] ?? '';
        $returnArr['appIDScanner'] = $dingTalkConfig['appIDScanner'] ?? '';
        $AuthParam = DictModel::getAll("AUTHPARAM");
        $returnArr['verifyCode'] = $AuthParam['verifyCode'];
        $returnArr['verifyCodeType'] = $AuthParam['verifyCodeType'];
        $SMSParam = DictModel::getAll("SMS");
        $returnArr['writeDevtel'] = $SMSParam['write_devtel'];
        $GuestParam = DictModel::getAll("GUESTAUTH");
        $returnArr['GuestIP'] = $GuestParam['GuestApplyIP'];
        $returnArr['guest_sms'] = $GuestParam['SmsState'];
        $returnArr['guest_code'] = $GuestParam['NetCode'];
        $returnArr['guest_qr'] = $GuestParam['QrCode'];
        $returnArr['MobileNetCodeMsg'] = $GuestParam['netCodeMsg'];
        return $returnArr;
    }

    /**
     * 获取浏览器（平台、浏览器、浏览器版本信息）
     *
     * @param $devInfo
     * @param $isClient
     * @param $userAgent
     * @return string
     */
    public static function getDevInfo($devInfo, $isClient = null, $userAgent = ''): string
    {
        if ($isClient === null) {
            $isClient = IS_CLIENT;
        }
        $web_info = [];
        if ($isClient && $devInfo) {
            return $devInfo;
        }
        $agent = new Agent();
        if (!empty($userAgent)) {
            $agent->setUserAgent($userAgent);
        }
        $web_info['platform'] = $agent->platform() ?? '';
        $web_info['browser'] = $agent->browser() ?? '';
        $web_info['version'] = $agent->version($web_info['browser']) ?? '';
        return "{$web_info['platform']} {$web_info['browser']}/{$web_info['version']}";
    }

    /**
     * 获取客户端类型ID
     *
     * @param null $osType
     * @param null $client
     *
     * @return int
     */
    public static function getClientTypeId($osType = null, $client = null): int
    {
        $osTypeId = hlp_common::getSubType($osType ?? OS_TYPE);
        $isClient = ($client ?? IS_CLIENT) ? 10 : 0; // 客户端在web基础上加10
        return $isClient + $osTypeId;
    }

    /**
     * 是否紧急模式
     *
     * @param $hastatus
     *
     * @return bool
     */
    public static function isEmergency($hastatus): bool
    {
        if (in_array($hastatus, [2, 5, 6, 7], false)) {
            return true;
        }
        return false;
    }

    /**
     * 获取签名值
     * @param $time
     * @return string
     */
    public static function getSignValue($time)
    {
        $prefix = substr($time, 0, 5);
        $suffix = substr($time, 5);
        return md5($prefix . '#infogo#' . $suffix);
    }

    /**
     * 获取项目配置
     * @throws Exception
     */
    public static function getProjectConf()
    {
        $data = array(
            'name' => 'base',
            'thirdAuth' => 0
        );
        $iniPath = PATH_HTML . '/project_conf.ini';
        if (file_exists($iniPath)) {
            // 配置文件存在，则根据配置文件判断项目
            $data = read_inifile(PATH_HTML.'/project_conf.ini');
            $sr = DictModel::getOneItem('User', 'AuthServer');
            $ds = DictModel::getOneItem('AdThirdAuth', 'DsUrl');
            if (strpos($sr['ItemValue'], 'Other') !== false) {
                // 如果开启了第三方服务器，则检测通畅性
                $dsr = get_headers($ds['ItemValue'], 1);
                if ($dsr) {
                    // 如果通
                    $data['thirdAuth'] = 1;
                }
            }
        }
        $joint = (int)cutil_dict_get('MSEPConfig', 'joint', '0');
        if ($joint === 1) {
            $windowsUrl = cutil_dict_get('MSEPConfig', 'windowsUrl');
            $data['windowsUrl'] = $windowsUrl;
        }
        $data['joint'] = $joint;
        $data['entrance_version'] = DictModel::getOneItem('CLIENTCHECK', 'entrance_version');
        $data['deviceType'] = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        $data['protocol'] = 'http:';
        $http_port_file = PATH_ETC . "asm/updateport/asm_http_port.ini";
        $port = parse_initfile($http_port_file);
        $data['http_port'] = $port['cur_http_port'];
        $data['https_port'] = $port['cur_https_port'];
        $config = self::getControllerConfig();
        if (stripos($config['URL'], 'https://') === 0) {
            $data['protocol'] = 'https:';
        }
        return $data;
    }

    /**
     * 获取入网提示语言包，支持PC入网/移动入网
     * @return array 入网提示语言包
     */
    public static function getAccessTips()
    {
        $return = [];
        $langData = ['en' => 'parameter_en.json', 'zh' => 'parameter.json'];
        $fileDir = ['clientPush', 'personality'];

        foreach ($langData as $lang => $file) {
            $return[$lang] = [];
            foreach ($fileDir as $configDir) {
                $dir = PATH_HTML . "/download/{$configDir}/";
                $data = "";
                if (is_file($dir . $file)) {
                    $result = file_get_contents($dir . $file);
                    $result = gbkToUtf8($result);
                    $data = json_decode($result, true);
                }
                // $result = dataStrpTag($result); 考虑富文本使用，暂时去掉
                if (!empty($data) && $configDir === 'clientPush') {
                    foreach ($data as $k => $val) {
                        $data[$k] = str_replace(
                            '入网安全检查控件',
                            '小助手',
                            Base64DeExt($val)
                        );
                    }
                }
                if (!is_array($data)) {
                    continue;
                }
                $return[$lang] = array_merge($return[$lang], $data);
            }
        }
        $mobileArr = DictModel::getAll('Mobile');
        $checkArr = DictModel::getAll('CLIENTCHECK');
        $return['en']['oWelcome']['mobile_Web_VPN'] = $mobileArr['mobile_Web_VPN_en'];
        $return['en']['oWelcome']['mobile_Web_CutOff'] = $mobileArr['mobile_Web_CutOff_en'];
        $return['en']['oWelcome']['mobile_Web_WaitAudit'] = $mobileArr['mobile_Web_WaitAudit_en'];
        $return['en']['oWelcome']['mobile_Web_AuditStop'] = $mobileArr['mobile_Web_AuditStop_en'];
        $return['en']['oWelcome']['mobile_Web_OnAccesss'] = $mobileArr['mobile_Web_OnAccesss_en'];
        $return['en']['oWelcome']['mobile_Web_GoWeb'] = $checkArr['mobile_Web_GoWeb_en'];
        $return['zh']['oWelcome']['mobile_Web_VPN'] = $mobileArr['mobile_Web_VPN'];
        $return['zh']['oWelcome']['mobile_Web_CutOff'] = $mobileArr['mobile_Web_CutOff'];
        $return['zh']['oWelcome']['mobile_Web_WaitAudit'] = $mobileArr['mobile_Web_WaitAudit'];
        $return['zh']['oWelcome']['mobile_Web_AuditStop'] = $mobileArr['mobile_Web_AuditStop'];
        $return['zh']['oWelcome']['mobile_Web_OnAccesss'] = $mobileArr['mobile_Web_OnAccesss'];
        $return['zh']['oWelcome']['mobile_Web_GoWeb'] = $checkArr['mobile_Web_GoWeb'];
        return $return;
    }

    /**
     * 计算表达式
     * @param $exp
     * @param $data
     * @return mixed
     */
    public static function calculateExpression($exp, $data)
    {
        //增加判断data数据内容只能为true 或者false这种数据，避免注入
        // 验证$data是否为数组
        if (!is_array($data)) {
            self::log('calculateExpression err:===> calculateExpression $data must be an array.');
            throw new InvalidArgumentException('calculateExpression $data must be an array.');
        }

        // 遍历$data，检查每个值是否为布尔类型
        foreach ($data as $key => $value) {
            if (!is_bool($value)) {
                self::log('calculateExpression err:===>The calculateExpression data value for key' . " '$key' must be a boolean.");
                throw new InvalidArgumentException("The calculateExpression data value for key '$key' must be a boolean.");
            }
        }

        // 规范表达式中的与或
        $exp = str_replace('&&', '&', $exp);
        $exp = str_replace('||', '|', $exp);
        $exp = str_replace('&', '&&', $exp);
        $exp = str_replace('|', '||', $exp);

        // 替换表达式中的变量
        $exp = str_replace(array_keys($data), array_map('var_export', $data, array_fill(0, count($data), true)), $exp);
        self::log('exp:===>' . var_export($exp, true));
        ob_start();
        // 替换表达式中的变量后续考虑替换eval()方法，目前此处触发为命令行，且表达式为后台配置，放入加密文件，风险较小
        $result = eval("return ($exp);");
        ob_end_clean();
        return $result;
    }

    /**
     * 获取系统真实的引擎版本号
     * @return mixed
     * @throws Exception
     */
    public static function getRealEngine()
    {
        $langConfFile = PATH_ETC . "version.ini";
        $ini_arr = read_inifile($langConfFile);
        return 'V' . $ini_arr['engine'];
    }

    /**
     * 当前服务器版本，支持的最小客户端引擎版本号
     * 请勿随意修当前版本号，否则会导致所有未升级的客户端提示重启升级。
     * 修改此函数值需要经过主管和经理评审！！！
     * 修改此函数值需要经过主管和经理评审！！！
     * 修改此函数值需要经过主管和经理评审！！！
     * @return string
     */
    public static function getMiniSupportClientEngine()
    {
        return 'V6.0.6039.3746.R004';
    }

}
