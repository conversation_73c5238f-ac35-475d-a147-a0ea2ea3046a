<?php
/**
 * Description: 补丁相关操作
 * User: <EMAIL>
 * Date: 2021/08/06 09:46
 * Version: $Id: PatchController.php 161740 2021-11-15 08:45:27Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class PatchController extends BaseController
{
    /**
     * 获取最后的下载补丁，对应老交易 make_belong_times
     *
     * @return array|string
     * @throws Exception
     */
    public function belongTimes()
    {
        $params = [];
        $params['DeviceID'] = request('deviceid', 'request', 0, 'int');
        $patchService = PatchServiceProvider::initPathService($params);
        $result = $patchService->getBelongTimes($params['DeviceID']);
        if ($result['code'] === 1) {
            return $result['data'];
        }
        return $result['msg'];
    }

    /**
     * 获取补丁服务器ip和端口，对应老交易 get_patchserver_ipport
     *
     * @return array|string
     * @throws Exception
     */
    public function serverInfo()
    {
        $params = [];
        $patchService = PatchServiceProvider::initPathService($params);
        $result = $patchService->getPatchServerInfo();
        if ($result['code'] === 1) {
            return $result['data'];
        }
        throw new Exception($result['msg']);
    }

    /**
     * 获取补丁详情，对应老交易 get_patch_info
     *
     * @return array|string
     * @throws Exception
     */
    public function detail()
    {
        $params['revisionid'] = request('revisionid', 'request', 0);
        $params['islastbundle'] = request('islastbundle', 'request', 0, 'int');
        $params['isLinuxPatch'] = request('isLinuxPatch', 'request', OS_TYPE === OSTYPE_LINUX ? 1 : 0, 'int');
        $patchService = PatchServiceProvider::initPathService($params);
        $result = $patchService->getPathDetail($params);
        if ($result['code'] === 1) {
            return $result['data'];
        }
        throw new Exception($result['msg']);
    }

    /**
     * 获取补丁安装的记录，对应老交易 update_install_log
     *
     * @return string
     * @throws Exception
     */
    public function installLog()
    {
        $params['deviceid'] = request('deviceid', 'request', 0, 'int');
        $params['revisionid'] = request('revisionid', 'request', 0, 'int');
        $params['installresult'] = request('installresult', 'request', '', 'string');
        $params['belong_times'] = request('belong_times', 'request', '', 'string');
        $patchService = PatchServiceProvider::initPathService($params);
        $result = $patchService->insertInstallLog($params);
        if ($result['code'] === 1) {
            return 'ok';
        }
        throw new Exception($result['msg']);
    }

    /**
     * 获取设备需要安装的补丁信息，对应老交易 get_device_repair_patch
     *
     * @return string
     * @throws Exception
     */
    public function deviceRepairPatch()
    {
        $params['deviceid'] = request('deviceid', 'request', 0, 'int');
        $patchService = PatchServiceProvider::initPathService($params);
        $result = $patchService->deviceRepairPatch($params);
        if ($result['code'] === 1) {
            return $result['data'];
        }
        throw new Exception($result['msg']);
    }
}
