<?php
/**
 * Description: 角色
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: RoleService.php 170149 2022-03-03 01:53:25Z lihao $
 */

namespace Services\Common\Services;

use Services\Common\Exceptions\AsmException;
use Services\Common\Interfaces\DepartServiceInterface;
use Services\Common\Interfaces\RoleServiceInterface;

class RoleService extends CommonService implements RoleServiceInterface
{
    /**
     * 获取角色信息 原方法getRoleInfo
     *
     * @param string $RoleID
     * @param int $Type 识别是否为移动端访问 如果是 1 否则null
     *
     * @return array|bool
     */
    public function getRoleInfo($RoleID, $Type = null)
    {
        if (empty($RoleID)) {
            return false;
        }
        static $RoleInfos = null;
        if (PHP_SAPI !== 'cli' && !empty($RoleInfos[$RoleID])) {
            return $RoleInfos[$RoleID];
        }
        $aResult = \NetRoleModel::getOne($RoleID, 'one');
        if (is_array($aResult)) {
            $roleTDict = \RoleDictModel::getAll($RoleID);
            if (!is_array($roleTDict)) {
                $roleTDict = [];
            }
            $default_roleTDict = \RoleDictModel::getAll(ROLE_ID_DEFAULT);
            foreach ($default_roleTDict as $key => $value) {
                $aResult[$key] = $roleTDict[$key] ?? $value;
            }
            foreach ($roleTDict as $key => $value) {
                if (!isset($aResult[$key])) {
                    $aResult[$key] = $value;
                }
            }
            $aResult ["CheckParam"] = $this->getRoleXmlInfo($aResult);

            if ($aResult ["IsSafeCheck"] == 1 && $aResult ["IsNeedActive"] == 0) {   // 如果要安检就必须装小助手
                $aResult ["IsNeedActive"] = 2;
            }
            $aResult['GETTYPE'] = $Type ? "mobile" : "PC";
            $RoleInfos[$RoleID] = $aResult;
            return $aResult;
        } else {
            return false;
        }
    }

    /**
     * 获取指定角色指定配置
     * @param string|int $RoleID 指定角色ID
     * @param string $ItemName 指定配置名
     * @param string $Groups 指定配置分类
     * @return array|bool
     */
    public function getRoleInfoOne($RoleID,string $ItemName,string $Groups = '')
    {
      return \RoleDictModel::getOneConfig($RoleID,$ItemName,$Groups);
    }

    /*
     * 将角色信息从数组转为xml报文  原方法getRoleXmlInfo
     * @parame array  $roleResult
     * @return xml string
     */
    private function getRoleXmlInfo($roleResult)
    {
        if (!is_array($roleResult)) {
            return "";
        }
        // 安检参数
        $xml = "<?xml version=\"1.0\" encoding=\"gbk\"?>";
        $xml .= "<CheckParam>";
        $xml .= "<CheckIntervalDay>" . $roleResult ['CheckIntervalDay'] . "</CheckIntervalDay>";
        $xml .= "<IsSafeCheck>" . $roleResult ['IsSafeCheck'] . "</IsSafeCheck>";
        $xml .= "<NoPassCutNet>" . $roleResult ['NoPassCutNet'] . "</NoPassCutNet>";

        $xml .= "<ClientCheckTitle>" . $roleResult ['ClientCheckTitle'] . "</ClientCheckTitle>";
        $xml .= "<InstallAssistant>" . $roleResult ['InstallAssistant'] . "</InstallAssistant>";
        $xml .= "<AssistantName>" . $roleResult ['AssistantName'] . "</AssistantName>";
        $xml .= "<HideAssistant>" . $roleResult ['HideAssistant'] . "</HideAssistant>";
        $xml .= "<ReCheck>" . $roleResult ['ReCheck'] . "</ReCheck>";
        $xml .= "<ReCheckTime>" . $roleResult ['ReCheckTime'] . "</ReCheckTime>";
        $xml .= "<Disturb>" . $roleResult ['Disturb'] . "</Disturb>";
        $xml .= "<IsUseMiniAuthWnd>" . get_column($roleResult, 'IsUseMiniAuthWnd') . "</IsUseMiniAuthWnd>";
        $xml .= "<IsOpenNetworkBeforeLogon>" . get_column($roleResult, 'IsOpenNetworkBeforeLogon') . "</IsOpenNetworkBeforeLogon>";
        $xml .= "<DelDevice>" . $roleResult ['DelDevice'] . "</DelDevice>";
        //2013-03-27 yan add
        $xml .= "<AboutMessage>" . str_replace('{CopyrightTime}', '2006-'.date('Y'), $roleResult ['AssistantContent']) . "</AboutMessage>";
        $xml .= "<AboutTitle>" . $roleResult ['AssistantTitle'] . "</AboutTitle>";
        //end
        $xml .= "<Navigation>";
        $xml .= "<IsShow>0</IsShow>";
        $xml .= "</Navigation>";
        $xml .= "<AssExitBtnShow>" . $roleResult ['AssExitBtnShow'] . "</AssExitBtnShow>";
        $xml .= "</CheckParam>";

        return $xml;
    }

    /**
     * 获取角色控制 原getRoleAuthControl方法
     *
     * @param $RoleID
     * @param string $type
     * @param string $findval
     *
     * @return void
     * @throws \Exception
     */
    public function checkRoleAuthControl($RoleID, $type = 'time', $findval = '')
    {
        // 如果为空不做控制
        if ($RoleID == "") {
            return;
        }
        $Authenticate = \DictModel::getAll('Authenticate');
        if ($type == 'time') {
            $split = ["|", "/", "/", "-"];
            $Res = \RoleDictModel::getOneConfig($RoleID, 'customAuthTime');
            if ($Res['Config'] == "") {
                return;
            }
            $split_1 = explode($split[0], $Res['Config']);
            $split_2 = explode($split[1], $split_1[0]);
            if (in_array(date('w'), $split_2)) {
                $split_3 = explode($split[2], $split_1[1]);
                foreach ($split_3 as $time) {
                    $split_4 = explode($split[3], $time);
                    $nowtime = time();
                    if ($nowtime >= strtotime($split_4[0]) && $nowtime <= strtotime($split_4[1])) {
                        return;
                    }
                }
            }
            throw new \Exception($Authenticate['customAuthTimeRemark']);
        } else if ($type == 'ip') {
            if ($findval == "") {
                T(21103014);
            }
            $split = ["|", "-"];
            $Res = \RoleDictModel::getOneConfig($RoleID, 'customAuthIp');
            if ($Res['Config'] == "") {
                return;
            }
            $split_1 = explode($split[0], $Res['Config']);
            foreach ($split_1 as $ips) {
                $split_2 = explode($split[1], $ips);
                $nowip = ip2long($findval);
                if ($nowip >= ip2long($split_2[0]) && $nowip <= ip2long($split_2[1])) {
                    return;
                }
            }
            throw new \Exception($Authenticate['customAuthIpRemark']);
        }
    }
}
