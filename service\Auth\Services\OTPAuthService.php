<?php
/**
 * Description: OTP业务
 * User: <EMAIL>
 * Date: 2022/05/09 16:52
 * Version: $Id: QrcodeServiceProvider.php 175145 2022-05-06 07:53:42Z huyf $
 */

namespace Services\Auth\Services;

use AuthServiceProvider;
use AuthUserModel;
use cls_google_authenticator;
use Exception;
use lib_qrcode;
use Services\Auth\Interfaces\AuthServiceInterface;

class OTPAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'OTP';

    /**
     * 认证配置
     * @var array
     */
    protected $config = [];

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
        $this->config = AuthServiceProvider::getAuthDomainConfig($this->userType);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams(): array
    {
        $this->params['userName'] = Base64DeExt($this->params['userName'], true);
        return $this->params;
    }

    /**
     * 校验帐号是否激活了令牌
     *
     * @param $userid
     */
    public function checkActivationKey($userid)
    {
        $AuthUserModel = AuthUserModel::getOne($userid, 'otp');
        $CurrentSecretKey = $AuthUserModel['CurrentSecretKey'];
        return $CurrentSecretKey ? true : false;
    }

    /**
     * 查询账户对应的当前使用的激活密钥
     *
     * @param $userid
     * @throws Exception
     */
    public function getCurrentSecretKey($userid)
    {
        $AuthUserModel = AuthUserModel::getOne($userid, 'otp');
        if (!$AuthUserModel['CurrentSecretKey']) {
            T(21141001);
        }
        return $AuthUserModel['CurrentSecretKey'];
    }

    /**
     * 获取OTP二维码
     *
     * @param $secret
     */
    public function getCode($secret)
    {
        $ga = new cls_google_authenticator();
        return $ga->getCode($secret);
    }

    /**
     * 获取令牌密钥对应的账户信息
     * @param $secret
     * @throws Exception
     */
    public function getSecretKeyUserInfo($secret)
    {
        $UserInfo = AuthUserModel::getOneByActivationSecretKey($secret, 'base');
        if (!$UserInfo) {
            T(21141002);
        }
        return $UserInfo;
    }

    /**
     * 账户激活令牌密钥
     * @param $userid
     * @param $secret
     * @throws Exception
     */
    public function activateSecretKey($userid, $secret)
    {
        $AuthUserModel = AuthUserModel::getOne($userid, 'otp');
        if ($AuthUserModel['ActivationSecretKey'] !== $secret) {
            T(21141003);
        }
        if ($AuthUserModel['CurrentSecretKey']) {
            T(21141004);
        }
        AuthUserModel::update($userid, ['CurrentSecretKey' => $secret]);
        return true;
    }

    /**
     * 获取OTP二维码
     *
     * @param $userid
     * @param $return
     * @return string|void
     * @throws Exception
     */
    public function getOTPQrcodeImg($userid, $return = false)
    {
        $AuthUserModel = AuthUserModel::getOne($userid, 'otp');
        $ActivationSecretKey = $AuthUserModel['ActivationSecretKey'];
        if (!$ActivationSecretKey) {
            T(21141001);
        }
        if ($this->config['AlgorithmType'] === 'standard') {
            $ga = new cls_google_authenticator();
            $imginfo = $ga->getQRCodeGoogleUrl($AuthUserModel['UserName'], $ActivationSecretKey, 'asm');
        } else {
            $urlPrefix = URL_BASE;
            $data = ['secret_key' => $ActivationSecretKey, 'userid' => $userid, 'userName' => $AuthUserModel['UserName'], 'isOpenUrl' => 1];
            $imginfo = "{$urlPrefix}/mobile/ui/wel.html#/access/OTPDownload?" . http_build_query($data);
        }
        if ($return) {
            return $imginfo;
        }
        lib_qrcode::png($imginfo, false, "L", 6, 1);
    }

    /**
     * 重新设置账户的令牌激活密钥
     * @param $userid
     * @throws Exception
     */
    public function resetActivationSecretKey($userid)
    {
        $AuthUserModel = AuthUserModel::getOne($userid, 'otp');
        if (!empty($AuthUserModel['ActivationSecretKey'])) {
            return;
        }
        $secretLength = $this->config['AlgorithmType'] === 'standard' ? 16 : 8;
        $ga = new cls_google_authenticator();
        $secret = $ga->createSecret($secretLength);
        AuthUserModel::update($userid, ['ActivationSecretKey' => $secret]);
    }

    /**
     * 取消激活令牌
     * @param $secret
     * @throws Exception
     */
    public function cancelActivation($secret)
    {
        $UserInfo = AuthUserModel::getOneByCurrentSecretKey($secret, 'base');
        if (!$UserInfo) {
            T(21141002);
        }
        AuthUserModel::update($UserInfo['ID'], ['CurrentSecretKey' => '']);
        return true;
    }

    /**
     * 校验手机上的密钥是否在服务器上
     * @param $secret
     * @throws Exception
     */
    public function checkSecret($secret)
    {
        $secretArr = explode(',', $secret);
        if (!is_array($secretArr)) {
            T(21141007);
        }
        foreach ($secretArr as $key => $value) {
            $UserInfo = AuthUserModel::getOneByCurrentSecretKey($value, 'base');
            if (!$UserInfo) {
                unset($secretArr[$key]);
            }
        }
        return array_values($secretArr);
    }

    /**
     * 获取认证的字段
     * @return array
     */
    public function getAuthColumns()
    {
        return ['authType', 'userid', 'checkCode'];
    }
}
