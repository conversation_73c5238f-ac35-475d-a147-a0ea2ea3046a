<?php
/**
 * Description: 公共方法
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: hlp_common.php 165900 2021-12-28 07:50:25Z duanyc $
 */

use Services\Resource\Services\ResourcePowerService;

!defined('IN_INIT') && exit('Access Denied');

class hlp_common
{
    /**
     * 根据$item_value得到分表名称
     *
     * @param int $item_value
     * @param string $table
     *
     * @return array
     */
    public static function getSplitTable($item_value, $table)
    {
        return ["name" => $table, 'index' => "0_0"];
        /* 暂未分表，注释掉分表的逻辑
         * switch ($table) {
            case 'test':
                $table_index = '';
                $table_name = $table;
                break;
            default:
                $table_index = '';
                $table_name = $table;
        }

        $index = 0;
        $index_min = 0;
        $section = $GLOBALS['DATABASE']['section'];

        foreach ($section as $k => $v) {
            if (empty($v['table_name']) || in_array($table, $v['table_name'])) {
                $index = $k;
                break;
            }
        }

        if (!empty($table_index) && isset($section[$index]['table_range'])) {
            $group_host_count = $section[$index]["table_range"] / $section[$index]["group_count"];
            $fraction = $item_value % $section[$index]["table_range"];
            $index_min = floor($fraction / $group_host_count);
        }

        return ["name" => $table_name, 'index' => $index . "_" . $index_min];
        */
    }

    /**
     * 获取命令行参数
     *
     * @return array
     */
    public static function getCmdFlag()
    {
        $return = [];
        $argvs = $GLOBALS ['argv'];
        $i = 0;
        for ($i = 0, $iMax = count($argvs); $i < $iMax; $i++) {
            if ($i > 0) {
                $key = trim($argvs [$i]);
                if (preg_match('/^-[a-z0-9A-Z_]+$/', $key)) {
                    $value = '';
                    if (isset($argvs [$i + 1])) {
                        if (preg_match('/^-[a-z0-9A-Z_]+$/', $argvs [$i + 1])) {
                            $value = '';
                        } else {
                            $value = $argvs [$i + 1];
                        }
                    }
                    $key = ltrim($key, '-');
                    $return [$key] = $value;
                }
            }
        }
        return $return;
    }

    /**
     * 转换成首字母大写
     *
     * @param $str
     *
     * @return string
     */
    public static function firstUpper($str): string
    {
        return strtoupper(substr($str, 0, 1)) . substr($str, 1);
    }

    /**
     * 封装iOS的下载地址
     * @return string
     * @throws Exception
     */
    public static function getIosDownloadUrl()
    {
        $path = PATH_ETC . "config-version.ini";
        $aV = read_inifile($path);
        $version = $aV['engine'];
        $safedomain = 'mdm.infogo.com.cn';
        $version = str_replace('.', '_', $version);
        $plist = "asm_ios_ipas/{$version}/asm.plist";
        $url = "https://{$safedomain}/{$plist}?" . time();
        return "itms-services://?action=download-manifest&url={$url}";
    }

    /**
     * 获取引擎版本号
     * @return mixed
     * @throws Exception
     */
    public static function getSystemEngine()
    {
        $ini_arr = read_inifile(PATH_ETC . "version.ini");
        if (file_exists(PATH_HTML . '/oem/oem.ini')) {
            $oemEngine = read_inifile(PATH_HTML . '/oem/oem.ini');
            if (empty($oemEngine) || empty($oemEngine['engine'])) {
                $engine = $ini_arr['ShowEngine'];
            } else {
                $engine = $oemEngine['engine'];
            }
        } else {
            $engine = $ini_arr['ShowEngine'];
        }
        return $engine;
    }

    /**
     * 根据OsName获取设备类型
     *
     * @param $osName
     *
     * @return string
     */
    public static function getOsTypeByOsName($osName)
    {
        $ostype = OSTYPE_WINDOWS;
        if (strpos($osName, 'Windows') !== false) {
            $ostype = OSTYPE_WINDOWS;
        } elseif (strpos($osName, 'MacOS') !== false) {
            $ostype = OSTYPE_MAC;
        } elseif (strpos($osName, 'Android') !== false ||
            strpos($osName, 'Harmony') !== false) {
            // 安卓和鸿蒙的安检项没有区分，这里暂不修改
            $ostype = OSTYPE_ANDROID;
        } elseif (strpos($osName, 'iOS') !== false ||
            strpos($osName, 'iPhone') !== false) {
            $ostype = OSTYPE_IOS;
        } elseif (stripos($osName, 'linux') !== false ||
            stripos(strtolower($osName), 'kylin') !== false ||
            stripos($osName, 'loongson') !== false ||
            stripos($osName, 'Ubuntu') !== false ||
            stripos(strtolower($osName), 'uos') !== false) {
            $ostype = OSTYPE_LINUX;
        }
        return $ostype;
    }

    /**
     * 获取设备子类型
     *
     * @param string $osType
     * @return int
     */
    public static function getSubType(string $osType): int
    {
        switch ($osType) {
            case OSTYPE_WINDOWS:
                return 1;
            case OSTYPE_LINUX:
                return 2;
            case OSTYPE_MAC:
                return 3;
            case OSTYPE_ANDROID:
                return 4;
            case OSTYPE_IOS:
                return 5;
            case OSTYPE_HARMANYOS:
                return 6;
        }
        return 1;
    }

    /**
     * 根据osType判断是否移动端
     *
     * @param $osType
     *
     * @return bool
     */
    public static function isMobile($osType): bool
    {
        if (in_array($osType, [OSTYPE_ANDROID, OSTYPE_IOS, OSTYPE_HARMANYOS], true)) {
            return true;
        }
        return false;
    }

    /**
     * 设置header头
     *
     * @param $code
     * @param $msg
     */
    public static function setHeader($code, $msg): void
    {
        if (IS_CLIENT) {
            return;
        }
        header("HTTP/1.1 {$code} {$msg}", true, $code);
    }

    /**
     * 解析敲门格式数据
     *
     * @param $content
     *
     * @return array
     */
    public static function parseFwknopData($content): array
    {
        if (empty($content)) {
            return [];
        }
        $return = [];
        $arr = explode('|', $content);
        if (!empty($arr)) {
            foreach ($arr as $val) {
                $return[] = Base64DeExt($val, false, true);
            }
        }
        return $return;
    }

    /**
     * 移动端提示页面
     *
     * @param $result
     * @param $code
     */
    public static function showMobileMessage($result, $code)
    {
        $icontype = $result ? "true" : "false";
        $str_zh = L($code, ['language' => 'zh_CN']);
        $str_en = L($code, ['language' => 'en_US']);
        $data = request('tipsData', 'request', [], 'array');
        $query = '';
        if (is_array($data)) {
            $queryData = [];
            foreach ($data as $column => $val) {
                $queryData[] = "{$column}={$val}";
            }
            $query = "?" . implode('&', $queryData);
        }
        $str = get_cururl(true) . "/mobile/ui/wel.html{$query}#/access/guestQRCode?result=" .
            $icontype . "&zh=" . urlencode($str_zh) . "&en=" . urlencode($str_en);
        mobileGotoUrl($str);
    }

    /**
     * 显示飞书页面
     * @param $result
     */
    public static function showFeishuPage($result)
    {
        if (!empty($result)) {
            $url = "/a/images/loading.gif?v=1";
        } else {
            $url = "/a/images/loading.gif?v=2";
        }
        /** 可怕的飞书，非要强制跳转，不学好，钉钉和企业微信都是跨域通讯 **/
        echo <<<STR
            <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
            <html xmlns="http://www.w3.org/1999/xhtml">
            <head>
                <title>loading...</title>
            </head>
            <body>
                <div style="height: 300px;width: 250px;text-align: center;">
                    <img src="{$url}" style="width: 50px;height:50px;margin-top:150px;"/>
                </div>
            </body>
            </html>
STR;
        exit(0);
    }

    /**
     * 生成token
     */
    public static function rundnum()
    {
        $date = microtime(date("Y-m-d H:i:s", time()));
        $date = str_ireplace('.', '', $date);
        $date = substr($date, 10, 4);
        $date = strlen($date) == 4 ? $date : (strlen($date) == 3 ? $date . "0" : (strlen($date) == 2 ? $date . "00" : $date));
        $str = "1,2,3,4,5,6,7,8,9,0,a,b,c,d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T,U,V,W,X,Y,Z";
        $aStr = explode(',', $str);
        $strs = '';
        for ($i = 0; $i < 10; $i++) {
            $rund = rand(0, 41);
            $strs .= $aStr[$rund];
        }
        return $strs . $date;
    }

    /**
     * 拼接第三方认证通用参数
     *
     * @return string
     */
    public static function otherAuthParams(): string
    {
        $return = [];
        $columns = ['twoFactors', 'authFrom', 'resId', 'token', 'userId'];
        foreach ($columns as $column) {
            $val = request($column, 'request');
            $return[] = "{$column}={$val}";
        }
        return implode('&', $return);
    }

    /**
     * 主要用于判断当前脚本是否允许运行
     *
     * @param string $file : 脚本名称
     * @param array $devtypes :设备类型:array("asm", "asc" ,"dblb") ;
     * @param int $ha : 1:主机/单机,2:备机,0:无限制
     *
     * @return bool
     * @throws Exception
     */
    public static function isAccessRun(string $file = "", array $devtypes = ['asm'], int $ha = 1): bool
    {
        $devtype = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if (!in_array($devtype, $devtypes, false)) {
            cutil_php_log($file . " devtype:" . $devtype . ",不符合运行条件[" .
                var_export($devtypes, true) . "],不能执行!", 'cronb');
            return false;
        }
        $hastatus = get_ini_info(PATH_ETC . 'deveth.ini.noback', 'hastatus');
        $iss = ["1", "2", "3", "6"];
        $iss_b = ["4", "5"];
        if ($ha) {
            if ($ha === 1 && !in_array($hastatus, $iss, false)) {
                cutil_php_log('不是主机,不能执行!', 'cronb');
                return false;
            }
            if ($ha === 2 && !in_array($hastatus, $iss_b, false)) {
                cutil_php_log('不是备机,不能执行!', 'cronb');
                return false;
            }
        }
        return true;
    }

    /**
     * 是否部署外网
     * @return bool
     */
    public static function isDeployExternal(): bool
    {
        try {
            $sdp = parse_initfile(PATH_ETC . 'sdp.ini');
        } catch (Exception $e) {
            $sdp = false;
        }
        if (!empty($sdp['AccessAddress'])) {
            return true;
        }
        return false;
    }

    /**
     * 获取语言
     * @return string
     */
    public static function getLang(): string
    {
        return defined('LANG') ? $GLOBALS['CONFIG']['LANG_MAP'][LANG] : 'zh';
    }

    /**
     * 获取端口
     * @param $Protocol
     * @param $Port
     * @return string
     */
    public static function getPortByProtocol($Protocol, $Port)
    {
        if ($Protocol === 'https' && (int)$Port === 443 || $Protocol === 'http' && (int)$Port === 80) {
            return '';
        }
        return ":{$Port}";
    }

    /**
     * 开启系统联动配置
     */
    public static function isOpenThirdServer()
    {
        static $isOpen = null;
        if ($isOpen !== null) {
            return $isOpen;
        }
        try {
            $mode = get_ini_info(PATH_ETC . 'thirdserver.ini', 'MODE');
            $isOpen = ($mode === 'enable');
        } catch (Exception $e) {
            $isOpen = false;
        }
        return $isOpen;
    }

    /**
     * 获取访问类型
     * @param $accessTypeID
     * @return int
     */
    public static function getAccessType($accessTypeID)
    {
        switch ($accessTypeID) {
            case ACCESS_TYPE_COMMON:
            case ACCESS_TYPE_OAUTH2:
            case ACCESS_TYPE_FORM:
            case ACCESS_TYPE_MICRO_APP:
                // web代理资源
                return 0;
            case ACCESS_TYPE_RDP:
            case ACCESS_TYPE_VNC:
            case ACCESS_TYPE_SSH:
            case ACCESS_TYPE_TELENT:
                // 主机服务器资源
                return 1;
            case ACCESS_TYPE_REMOTE_APP:
                // 远程应用资源
                return 2;
            case ACCESS_TYPE_CUSTOM_APP:
                // 自定义应用资源
                return 3;
        }
        return 0;
    }

    /**
     * 是否超出零信任在线用户连接的点数控制
     * @param string $Token
     * @return bool
     */
    public static function ztpUserExceed(string $Token): bool
    {
        $Session = SessionRedis::getOne($Token, 'policy');
        $service = ResourceServiceProvider::initResourceService('Power');
        [$resIds, $ipResIds, $datas, $isNetIds, $diyResIds]= $service->resId($Session);
        $allIds = array_column($datas,'ResID');
        $allIds = array_diff($allIds,$diyResIds);  //自定义应用资源不占用授权点数
        if (!$allIds) { //如果全部是之自定义应用就不需要弹框
            return true;
        }
        return $service->ztpUserOnline($Session, $datas, $isNetIds, $Token, $diyResIds);
    }
}
