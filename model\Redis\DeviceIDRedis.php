<?php

/**
 * Description: 准入设备的redis
 * User: <EMAIL>
 * Date: 2023/11/7 10:32
 * Version: $Id$
 */

class DeviceIDRedis extends BaseRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = '';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'DeviceID';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => ['LastHeartTime' => 'TNacOnLineDevice.LastHeartTime'],
    ];

    /**
     * 获取key
     *
     * @param array $keys
     *
     * @return mixed
     */
    public static function getKey($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }

        return static::TABLE_NAME . "_" . implode('_', $keys);
    }

    /**
     * 单条
     *
     * @param string $deviceId
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($deviceId, $column = 'one')
    {
        $data = self::get($column, $deviceId);
        $return = [];
        foreach (self::$columns[$column] as $key => $val) {
            $return[$key] = $data[$val];
        }
        return $return;
    }
}
