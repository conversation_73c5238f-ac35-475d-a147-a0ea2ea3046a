<?php

/**
 * Description: 策略与资源映射表
 * User: <EMAIL>
 * Date: 2022/05/10 10:32
 * Version: $Id$
 */

class ResPolicyRelationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResPolicyRelation';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'policy' => 'PolicyID',
        'resPolicy' => 'ResID,PolicyID',
        'res' => 'ResID',
        'group' => 'PolicyID,ResID',
    ];

    /**
     * 获取分组的资源ID列表
     *
     * @return mixed
     */
    public static function getPolicyResIDs()
    {
        self::$data = [];
        $column = self::$columns['group'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $groupDatas = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $groupDatas[$row['PolicyID']][] = $row['ResID'];
            }
        }
        return $groupDatas;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['PolicyID'])) {
            $where .= "AND PolicyID = ".self::setData($cond['PolicyID']);
        }

        if (isset($cond['ResID'])) {
            $where .= "AND ResID = ".self::setData($cond['ResID']);
        }

        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
