<?php

/**
 * Description: 场景信息主表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id
 */

class SceneModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TScene';
    public const PRIMARY_KEY = 'SceneID';
    protected static $columns = [
        'info' => 'SceneID,SceneName,Priority,UserType',
        '*'   => '*',
    ];

    /**
     * 获取默认场景的信息
     * @return array
     */
    public static function getDefaultSceneConfig(): array
    {
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $priority = 999;
        $where = "WHERE s.Priority = ".self::setData($priority);
        $sql = "SELECT d.ItemName,d.ItemValue,d.Groups,d.Remark FROM TScene s LEFT JOIN TSceneRelation r ON s.SceneID = r.SceneID LEFT JOIN TSceneDict d ON r.ConfigID = d.ConfigID 
	{$where} GROUP BY d.RID";

        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }
    /**
     * 新增获取安检通过后可访问域
     * @param $sceneID
     * @return array
     */
    public static function getScenePassIpRegion($sceneID)
    {
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE TSceneDict.ConfigID=TSceneRelation.ConfigID AND TSceneDict.ItemName='PassIpRegion' AND TSceneRelation.SceneID=" . self::setData($sceneID) .
            " AND TSceneRelation.Groups='RegionList'";
        $sql = "SELECT ItemValue,SceneID FROM TSceneDict,TSceneRelation {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }
    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['SceneID'])) {
            $where .= "AND SceneID = ".self::setData($cond['SceneID']);
        }

        if (isset($cond['SceneName'])) {
            $where .= "AND SceneName = ".self::setData($cond['SceneName']);
        }

        if (isset($cond['Priority'])) {
            $where .= "AND Priority = ".self::setData($cond['Priority']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
