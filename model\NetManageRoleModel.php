<?php

/**
 * Description: TNetManageRole表
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: NetManageRoleModel.php 160295 2021-10-29 03:36:32Z renchen $
 */

class NetManageRoleModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNetManageRole';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取设备对应的角色的安全域以及入网角色管理中是否是快速入网设备（TNetManageRoleToDevice只要有一条记录则说明是）
     *
     * @param int $deviceID
     * @return array|null
     */
    public static function getIPListIdAndCount(int $deviceID): ?array
    {
        if (!$deviceID) {
            return [];
        }

        self::$data = [];
        $sql = "SELECT count(B.ID) as count,A.OtherRegionId AS IPListId FROM TNetManageRole A INNER JOIN TNetManageRoleToDevice B ";
        $sql .= "ON A.ID = B.NetManageRoleID WHERE B.DeviceID = " . self::setData($deviceID) . " and A.IsPermit = 1 and A.State = 0";
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['RoleID'])) {
            $where .= "AND RoleID = " . self::setData($cond['RoleID']);
        }

        if (isset($cond['IsPermit'])) {
            $IsPermit = (int)$cond['IsPermit'];
            $where .= "AND IsPermit = " . self::setData($IsPermit);
        }

        if (isset($cond['State'])) {
            $State = (int)$cond['State'];
            $where .= "AND State = " . self::setData($State);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
