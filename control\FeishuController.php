<?php

/**
 * Description: 获取飞书用户信息
 * User: <EMAIL>
 * Date: 2021/08/20 15:53
 * Version: $Id: FeishuController.php 167234 2022-01-14 03:46:16Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class FeishuController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['user' => true, 'jump' => true];

    /**
     * 获取飞书用户信息，对应老交易 get_feishu_user
     *
     * @return array
     * @throws Exception
     */
    public function user()
    {
        $deviceType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if ($deviceType === 'out') {
            return $this->userOut();
        }
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $code = request('code', 'request');
        $appId = request('appid', 'request');
        // 准入检查是否扫码成功
        if ($action === 'check') {
            return $this->userCheck($deviceId);
        }
        $action!="verify" && hlp_check::checkEmpty($deviceId);
        hlp_check::checkEmpty($code);
        if ($action=='verify') {
            $time=request('time', 'request');
            return $this->userVerify($code, $time);
        }
        $this->userCallback($deviceId, $code, $appId);
        return [];
    }

    /**
     * 外置服务器
     *
     * @return array
     * @throws Exception
     */
    private function userOut()
    {
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $code = request('code', 'request');
        $time = request('time', 'request');
        hlp_check::checkEmpty($deviceId);
        hlp_check::checkEmpty($time);
        // 准入检查是否扫码成功
        if ($action === 'check') {
            $data = cache_get_info('feishu_out', "{$deviceId}_{$time}");
            if (empty($data['token'])) {
                return ['state' => false, 'type' => 'Prescaned'];
            }
            cache_del_info('feishu_out', "{$deviceId}_{$time}");
            return ['state' => true, 'type' => 'Scaned', 'token' => $data['token'], 'username' => ''];
        }
        hlp_check::checkEmpty($code);
        $data = ['token' => $code];
        $res = cache_set_info('feishu_out', "{$deviceId}_{$time}", $data, 300);
        return ['res' => $res];
    }
    /**
     * 用户校验
     * @param $code
     * @param $time
     * @return array
     * @throws Exception
     */
    public function userVerify($code, $time): array
    {
        $config = cache_get_info("asm_database_", "AuthDomainTest_FeiShu_" . $time);
        hlp_check::checkEmpty($config['appid']);
        hlp_check::checkEmpty($config['secret']);
        $tmpConfig=['appid' => $config['appid'], 'appsecret' => $config['secret'],'appsecret_1'=>$config['secret'],'from'=>'backend'];
        $data = ['servicePrefix' => 'FeiShu', 'code' => $code, 'isAgent' => false,'tmpConfig'=>$tmpConfig];
        $data['redirectUri'] = Base64EnExt(request('redirect_uri', 'request'));
        $userinfo = AuthServiceProvider::callbackRpcAuthServer($data);
        if (is_array($userinfo) && isset($userinfo['ID'])) {
            cache_set_info("asm_database_", "AuthDomainTest_FeiShu_" . $time, ['user' => $userinfo['ID']], 300);
        }
        return [];
    }
    /**
     * 检查
     *
     * @param $deviceId
     * @return array
     */
    private function userCheck($deviceId)
    {
        $return = ['state' => false];
        try {
            if (empty($deviceId)) {
                T(21145004);
            }
            $cond = ['LikeToken' => '|||', 'DeviceID' => $deviceId, 'Type' => 'FeiShu'];
            $userList = QrcodeServiceProvider::getQrcodeUserList($cond);
            if (empty($userList)) {
                T(21145003);
            }
            foreach ($userList as $user) {
                if (!empty($user['UserID'])) {
                    $return['state'] = true;
                    $return['type'] = "Scaned";
                    $return['token'] = $user['Token'];
                    $return['username'] = AuthServiceProvider::getUserNameById($user['UserID']);
                    break;
                }
            }
            if ($return['state'] == false) {
                QrcodeServiceProvider::deleteQrcodeUser($deviceId);
                T(21145005);
            }
        } catch (Exception $e) {
            $typeConfig = [21145004 => 'EmptyDeviceID', 21145003 => 'NoSuchDeviceToken',
                           21145005 => 'Prescaned'];
            $code = $e->getCode();
            $return['type'] = isset($typeConfig[$code]) ? $typeConfig[$code] : '';
            $return['message'] = $e->getMessage();
        }
        return $return;
    }

    /**
     * 回调
     *
     * @param $deviceId
     * @param $code
     * @param $appId
     *
     * @throws Exception
     */
    private function userCallback($deviceId, $code, $appId)
    {
        $GLOBALS['HeaderContentType'] = 'text/html';
        $code = trim($code);
        $data = ['servicePrefix' => 'FeiShu', 'code' => $code, 'appId' => $appId];
        $data['redirectUri'] = Base64EnExt(request('redirect_uri', 'request'));
        $authUser = AuthServiceProvider::callbackRpcAuthServer($data);
        $params = ['DeviceID' => $deviceId, 'Type' => 'FeiShu'];
        if (!empty($authUser)) {
            /* 记录二维码扫描 */
            $token = $authUser['openid'];
            $params['Token'] = $token;
            $params['UserID'] = $authUser['ID'];
            QrcodeServiceProvider::addQrcodeUser($params);
        } else {
            $params['Token'] = '|||';
            QrcodeServiceProvider::addQrcodeUser($params);
            T(21145003);
        }
    }

    /**
     * 飞书跳转，对应老交易 /a/getotheruser_int/feishu/feishu.php
     *
     * @throws Exception
     */
    public function jump(): void
    {
        $authKey = request('authKey', 'request', '', 'string');
        $code = request('code', 'request', '', 'string');
        $state = request('state', 'request', '', 'string');
        $closewindow = request('close', 'request', 0, 'int');
        $index = request('index', 'request', '', 'string');
        $originOtherParams = hlp_common::otherAuthParams();
        //针对漏扫进行个简单判断，是否有注入风险
        $otherParams = @htmlspecialchars($originOtherParams);
        $otherParams = str_replace("&amp;", "&", $otherParams); // 排除&符号的影响

        if (!empty($originOtherParams) && $originOtherParams != $otherParams) {
            header('Status: 401 Unauthorized', true, 401);
            exit();
        }
        if (!empty($authKey)) {
            QrcodeServiceProvider::saveAuthCode($authKey, $code);
            gotoUrl("/mobile/ui/wel.html?route_type=feishu&authKey={$authKey}#/access/authorizeSuccess");
        }

        if ($index === 'main') {
            $redirect = URL_BASE . "/a/getotheruser_int/feishu/feishu.php?{$otherParams}";
            $url = "https://open.feishu.cn/open-apis/authen/v1/index?app_id={$state}&state={$state}&redirect_uri=".urlencode($redirect);
            header("location: {$url}");
            exit();
        }

        $formUrl = "/a/getotheruser_int.php?route_type=feishu&t=feishu&state={$state}&{$otherParams}";
        hlp_page::showFeiShu($code, $closewindow, $formUrl);
    }
}
