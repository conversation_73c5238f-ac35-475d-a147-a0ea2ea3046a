<?php
/**
 * Description: 资源权限相关
 * User: duanyc
 * Date: 2023/10/26 21:51
 * Version: $Id: PatchService.php 157413 2021-09-22 08:11:28Z duanyc $.
 */

namespace Services\Resource\Services;

use AuthUserModel;
use DeviceIDRedis;
use Exception;
use GateWayExpendModel;
use GateWayModel;
use hlp_net;
use lib_database;
use NacOnLineDeviceModel;
use OtherAppUserTokenRedis;
use PolicyServiceProvider;
use ResConfigListModel;
use ResIPListModel;
use ResourceModel;
use ResourceServiceProvider;
use ResRemoteModel;
use Services\Common\Services\CommonService;
use Services\Common\Services\DESService;
use SessionRedis;
use OnlineZtpUserRedis;
use TokenRedis;
use UserInfoRedis;
use AuthorizeRedis;

class ResourcePowerService extends CommonService
{
    /**
     * 资源权限
     * @param $params
     */
    public function __construct($params)
    {
        parent::__construct();
        $this->logFileName = 'ResourcePower';
        $this->writeLog(!is_string($params) ? json_encode($params) : $params);
    }

    /**
     * 获取资源权限
     * @param $Session
     * @param $address
     * @return array
     */
    public function checkResPrivilege($session, $address): array
    {
        //访问权限检查
        $checkData = ['Result' => false, 'Summary' => L(21148001), 'Details' => L(21148063)];
        $returnArr = ['returnArr' => $checkData];

        //根据用户获取自己有的权限资源ID，判断是否有权限
        $authorizationIDs = AuthUserModel::getAuthorizationGroup($session['Uuid'], $session['DeviceID']);
        list($resIds, $ipResIds, $ResList) = ResourceServiceProvider::getUserResIds(
            $session['Uuid'],
            $session['DeviceID'],
            $session['DepartIDs'],
            $session['RoleID'],
            $authorizationIDs,
            '1'
        );

        //根据地址找到对应的资源，web资源/访问域/主机服务,访问域资源需要特殊处理下，可能会有交差
        $resInfo = $this->getResByAddress($address, $ipResIds);
        $resId = $resInfo['ResID'] ?? 0;
        $returnArr['resData'] = $resInfo;

        //如果没有找到，则直接返回没有权限
        if (empty($resId)) {
            return $returnArr;
        }

        if (!$this->checkAccessType($resInfo['AccessTypeID'] ?? 0)) {
            $checkData['Details'] = L(21148037);
            $returnArr['returnArr'] = $checkData;
            return $returnArr;
        }


        if (in_array($resId, $resIds) || in_array($resId, $ipResIds)) {
            $checkData['Result'] = true;
            $checkData['Summary'] = L(21148061);
            $checkData['Details'] = L(21148062);
            $returnArr['returnArr'] = $checkData;
        }

        return $returnArr;
    }

    /**
     * 获取资源权限
     * @param $address
     * @param $ipResIds
     * @return array
     */
    public function getResByAddress($address, $ipResIds): array
    {
        //解析地址参数构成，带端口与不带端口 如：example.com:8080
        $urlParts = parse_url($address);
        $ipOrDomain = $urlParts['host'] ?? ($urlParts['path'] ?? '');
        $port = $urlParts['port'] ?? '';
        //判断是否是远程主机服务类型，如果url中带有RDP的地址
        if (strpos($address, 'access_token') !== false) {
            // 解析token使用parse_str函数解析查询字符串，并将结果存储在数组中
            parse_str($urlParts['query'], $queryParameters);
            // 获取access_token的值
            $accessToken = $queryParameters['access_token'];
            $url_str = DESService::desEcbDecrypt(stringDecode($accessToken));
            parse_str($url_str, $data);
            $ResID = $data['resId'] ?? '';
            $resInfoArr = ResourceModel::getResourceList(['ResID' => $ResID]);
            if (!empty($resInfoArr) && !empty($ResID)) {
                //返回数据
                return $resInfoArr[0] ?? [];
            }
            return [];
        }

        //查找web应用，使用访问地址域名去查询
        $resInfoArr = ResourceModel::getResourceList(['URL' => $ipOrDomain]);
        if (!empty($resInfoArr) && !empty($ipOrDomain)) {
            //返回数据
            return $resInfoArr[0] ?? [];
        }

        //查找访问域资源，此处只能循环获取ip字段，判断是否为范围，以及对应的是否有端口与协议
        $resInfoArr = ResourceModel::getResourceList([], 1);
        if (!empty($resInfoArr)) {
            //循环处理IP资源，判断是否匹配，匹配则返回对应的值回来
            return $this->isInIpRes($ipOrDomain, $port, $resInfoArr, $ipResIds);
        }

        return [];
    }

    /**
     * 判断一个IP与端口是否属于对应的IP资源配置范围
     * @param $ipOrDomain
     * @param $port
     * @param $ipResInfoArr
     * @return array
     */
    public function isInIpRes($ipOrDomain, $port, $ipResInfoArr, $ipResIds): array
    {
        //循环判断是否符合条件，符合则返回对应的资源数据
        $returnResData = [];
        if (empty($ipResInfoArr)) {
            return $returnResData;
        }
        foreach ($ipResInfoArr as $ipResInfo) {
            //获取IP字段，##切割*******-********##******* TCp/123
            if (empty($ipResInfo['IP'])) {
                continue;
            }
            $ipArr = explode("##", $ipResInfo['IP']);
            foreach ($ipArr as $ipStr) {
                //单个IP
                if ($ipStr == $ipOrDomain) {
                    $returnResData = $ipResInfo;
                    $returnResData['ip_match'] = $ipStr;
                    $returnResData['ip_match_type'] = 1;
                    break;
                }

                //如果是IP范围段，则是默认所有端口与协议打开
                if (strpos($ipStr, "-") !== false && VpnIpRange($ipOrDomain, $ipStr, '##')) {
                    $returnResData = $ipResInfo;
                    $returnResData['ip_match'] = $ipStr;
                    $returnResData['ip_match_type'] = 2;
                    break;
                }

                // //如果是ip 协议/端口，则需要判断对应的端口是否匹配
                if (strpos($ipStr, " ") !== false) {
                    $ipStrArr = explode(" ", $ipStr);
                    $ipStr1 = $ipStrArr[0] ?? '';
                    $isIpIn = $isPortIn = false;
                    $ipMatchType = 0;
                    if (!empty($ipStr1) && strpos($ipStr1, "-") !== false && VpnIpRange($ipOrDomain, $ipStr1, '##')) {
                        $ipMatchType = 3;
                        $isIpIn = true;
                    }
                    if (!empty($ipStr1) && strpos($ipStr1, "-") === false && $ipOrDomain == $ipStr1) {
                        $ipMatchType = 4;
                        $isIpIn = true;
                    }
                    $portStr = $ipStrArr[1] ?? '';
                    if ($isIpIn && !empty($portStr)) {
                        $portStr = strtoupper($portStr);
                        if (strpos($portStr, "-") === false && in_array($portStr, ['TCP/' . $port, 'UDP/' . $port, $port])) {
                            //判断端口TCP/123
                            $ipMatchType = 5;
                            $isPortIn = true;
                        }

                        if (strpos($portStr, "-") !== false) {
                            //替换掉协议
                            $portStr = str_replace('TCP', '', $portStr);
                            $portStr = str_replace('UDP', '', $portStr);
                            //判断端口范围TCP/123-443
                            $portStartEndArr = explode("-", $portStr);
                            $startPort = $portStartEndArr[0] ?? '';
                            $endPort = $portStartEndArr[1] ?? '';
                            if (intval($startPort) > 0 && intval($port) >= intval($startPort) && intval($port) <= intval($endPort)) {
                                $ipMatchType = 6;
                                $isPortIn = true;
                            }
                        }
                    }

                    //未带端口的情况下只匹配IP
                    if (empty($port) || empty($portStr)) {
                        $returnResData['ip_match_type'] = 2;
                        $isPortIn = true;
                    }

                    if ($isIpIn && $isPortIn) {
                        $returnResData = $ipResInfo;
                        $returnResData['ip_match_type'] = $ipMatchType;
                        $returnResData['ip_match'] = $ipStr;
                        break;
                    }
                }
            }

            //如果当前匹配到的资源没有权限，则需要继续循环往下看到最后一个ip资源
            if (in_array($returnResData['ResID'] ?? -1, $ipResIds)) {
                break;
            }
        }

        return $returnResData;
    }

    /**
     * 检查授权
     * @param $AccessType
     * @return bool
     */

    public function checkAccessType($AccessType): bool
    {
        //基础授权
        if (!getmoduleregist(11)) {
            return false;
        }
        switch ($AccessType) {
            case ACCESS_TYPE_RDP:
            case ACCESS_TYPE_VNC:
                if (!getmoduleregist(12)) {
                    return false;
                }
                break;
            case ACCESS_TYPE_REMOTE_APP:
                if (!getmoduleregist(28)) {
                    return false;
                }
                break;
            case ACCESS_TYPE_SSH:
            case ACCESS_TYPE_TELENT:
                if (!getmoduleregist(13)) {
                    return false;
                }
                break;
        }

        return true;
    }

    /**
     * 批量刷新在线设备用户权限
     * @return void
     * @throws Exception
     */
    public function batchSetUserPower(): void
    {
        $cond = ['column' => '*', 'groupby' => 'Token'];
        $list = NacOnLineDeviceModel::getList($cond, false, 0, 50000);
        $size = 50;
        $start = 0;
        $changePowerDeviceArr = [];
        if (!empty($list)) {
            foreach ($list as $row) {
                if (empty($row['Token'])) {
                    continue;
                }
                $session = SessionRedis::getOne($row['Token'], 'check');
                //增加个判断如果一个周期内，也就是俩分钟内刚计算过则不计算，避免单次已经触发了的，批量继续计算
                if (!empty($session['LastUpdateTime']) && abs(time() - intval($session['LastUpdateTime'])) < 120) {
                    $this->writeLog("batchSetUserPower LastUpdateTime ignore {$row['Token']}: {$session['LastUpdateTime']}");
                    continue;
                }
                if (empty($session['Uuid']) || (isset($session['SessionStatus']) && $session['SessionStatus'] != '0')) {
                    $this->writeLog("batchSetUserPower ignore {$row['Token']}: {$session['SessionStatus']}");
                    continue;
                }
                $start++;

                $setRes = $this->setUserPower($row['Token'], 'batchSetUserPower');
                if (isset($setRes['ResPowerIds'])) {
                    $changePowerDeviceArr[$row['DeviceID'] ?? 0] = $row['Token'];
                }

                if ($start % $size === 0) {
                    lib_database::closeMysql();
                    //避免cpu飙升，50个，睡1秒
                    sleep(1);
                }
            }
        }

        $count = OnlineZtpUserRedis::hlen('Users');
        $date = date('Y-m-d');
        $max = \OnlineMaxUsersRedis::getOne($date);
        $max = $max ?: 0;
        if ($max != $count) {
            $max = max($max, $count);
            \OnlineMaxUsersRedis::setOne($date, $max, 10 * 24 * 3600);
        }

        // 处理微应用登录的相关的权限计算
        if (OtherAppUserTokenRedis::count() > 0) {
            //获取出来对应的token数据，然后判断是否存在session
            $otherList = OtherAppUserTokenRedis::zRangeData(0, -1);
            if (!empty($otherList)) {
                foreach ($otherList as $token) {
                    if (empty($token)) {
                        continue;
                    }
                    $session = SessionRedis::getOne($token, 'check');
                    if (empty($session['Uuid'])) {
                        $this->writeLog("OtherAppUserTokenRedis ignore and zRemMember {$token}: {$session['Uuid']}");
                        // 如果session不正常，则直接从当前zset中移除当前key
                        OtherAppUserTokenRedis::zRemMember($token);
                        continue;
                    }
                    //微应用不匹配终端策略相关
                    $this->setUserPower($token, 'batchSetUserPower');
                }
            }
        }

        //处理完成后增加清理过期动态匹配策略的数据
        $service = PolicyServiceProvider::getPolicyService('DynamicTactic', []);
        $service->clearDirtyHistoryData($changePowerDeviceArr);
        $this->writeLog("batchSetUserPower changePowerDeviceArr");
    }

    /**
     * 计算用户权限
     *
     * @param $ResIds
     * @param $Session
     * @param $ipResIds
     * @return array
     */
    public function setResourcePolicyResult($ResIds, $Session, $ipResIds): array
    {
        if (empty($ResIds)) {
            return PolicyServiceProvider::setUserPolicy($Session['Token'], [], []);
        }
        [$PolicyIDs, $PolicyResults] = ResourceServiceProvider::patchCheckPolicy($Session, $ResIds);
        $ResPolicys = [];
        foreach ($ipResIds as $ResId) {
            $ResPolicys[$ResId] = $PolicyResults[$ResId] ?? true;
        }
        // 没有策略，则设置为通过
        $pResIds = [];
        foreach ($ResIds as $ResId) {
            if (!isset($PolicyIDs[$ResId])) {
                $pResIds[] = $ResId;
            }
        }
        if (!empty($pResIds)) {
            try {
                PolicyServiceProvider::checkNoPolicy($Session, $pResIds);
            } catch (Exception $e) {
                $this->writeLog("{$Session['Uuid']}: :" . $e->getMessage());
            }
        }
        $PolicyIDs = array_unique($PolicyIDs);
        return PolicyServiceProvider::setUserPolicy($Session['Token'], $PolicyIDs, $ResPolicys);
    }

    /**
     * 计算权限前检查
     * @param $Token
     * @return bool|mixed
     */
    private function userPowerCheck($Token)
    {
        $Session = SessionRedis::getOne($Token, 'policy');
        if (empty($Session['Uuid'])) {
            $this->writeLog("setUserPower Session not exist {$Token}");
            return false;
        }
        $UserInfo = UserInfoRedis::getOne($Session['Uuid'], 'ZtpUser');

        //如果是设备ID小于0的，微应用等产生的临时设备ID，需要触发下设置ZtpUser
        if (intval($Session['DeviceID']) < 0 && empty($UserInfo['ZtpUser'])) {
            //查询一下数据库刷新一下设备是否为ztpUser
            $userRes = AuthUserModel::getOne($Session['Uuid'], 'ztpuser');
            if (!empty($userRes['ZtpUser'])) {
                UserInfoRedis::setOne($Session['Uuid'], ['ZtpUser' => 1]);
                $UserInfo['ZtpUser'] = 1;
            }
        }

        if (empty($UserInfo['ZtpUser'])) {
            // 如果是非零信任用户，但是有零信任资源，需要清理权限。
            if ($Session['ResPowerIds']) {
                $this->writeLog(" {$Token} setUserPower is not ZtpUser {$UserInfo['ZtpUser']} need clear resource!");
                $powerData = ['AuthApp' => [], 'AuthAppID' => [], 'vpnIpList' => '', 'vpnIpRangleAll' => [], 'ResPowerIds' => [], 'DeviceID' => $Session['DeviceID'],
                    'vpnRouteListAll' => [], 'AgentGate' => [], 'IpListGate' => [], 'GateDomain' => [], 'AuthAppGate' => json_encode([]), 'AuthAppAll' => json_encode([])];
                $powerData['LastMD5'] = md5(json_encode($powerData));
                $powerData['LastUpdateTime'] = time();
                SessionRedis::setOne($Token, $powerData);
            } else {
                $this->writeLog(" {$Token} setUserPower is not ZtpUser {$UserInfo['ZtpUser']}");
            }
            return false;
        }

        return $Session;
    }

    /**
     * 解析IP资源
     * @param $datas
     * @param $Session
     * @param $ipResIds
     * @return array
     */
    private function parseIpResIds($datas, $Session, $ipResIds)
    {
        //获取远程应用资源ids
        $remoteResIds = array();
        $remoteBroResIds = array();
        foreach ($datas as $res_id => $res_row) {
            if ($res_row['ResType'] == ResourceServiceProvider::RES_TYPE_REMOTE_APP) {
                $remoteResIds[] = $res_id;
                if ($res_row['ActionType'] == 1) {
                    $remoteBroResIds[] = $res_id;
                }
            }
        }
        //判断设备类型过滤远程应用$ipResIds
        if ($Session['OsType'] == OSTYPE_WINDOWS) {
            $ipResIds = array_diff($ipResIds, $remoteBroResIds);
        } else {
            $ipResIds = array_diff($ipResIds, $remoteResIds);
        }
        return $ipResIds;
    }

    /**
     * @param array $session
     * @param array $datas 所有的资源ID
     * @param array $isNetIds 内网直连的资源
     * @param $Token
     * @param array $diyResIds 自定义应用资源
     * @param string $from
     * @return bool  是否没有超过零信任授权点数  false 已超过  true还能继续使用
     */
    public function ztpUserOnline($session, $datas, $isNetIds, $Token, $diyResIds, $from = 'common'): bool
    {
        $info = AuthorizeRedis::getOne('11');
        $count = OnlineZtpUserRedis::hlen('Users');
        //如果此时授权的key 不存在或者过期了，则从新刷新一次缓存生成。
        if ($count <= 0 && $from == 'common') {
            // 如果是获取资源权限列表的时候，发现没有对应的权限key（理论上应该存在，此时可能过期导致），从在线表中获取所有设备刷新一次权限（异步）
            // 由于普通定时的最近2分钟有计算过的权限不参与计算，但是此处会忽略掉，所以不会重复计算，对性能影响较小
            $this->refreshAllOnlineToken();
        }
        $allIds = array_column($datas, 'ResID');
        $allIds = array_diff($allIds, $diyResIds);  //自定义应用资源不占用授权点数
        if (!$allIds) {
            OnlineZtpUserRedis::delColumn('Users', $Token);
            return true; //没有分配零信任资源的账户不占用点数
        }
        //只有内网直连的资源 并且是在内网下 不占点数 IsInternal true是内网
        if (isset($session['IsInternal']) && $session['IsInternal'] && count($isNetIds) === count($allIds)) {
            OnlineZtpUserRedis::delColumn('Users', $Token);
            return true;
        }
        //没装客户端、只有访问域资源不占用点数
        if (isset($session['Client']) && !$session['Client']) {
            $temp = [];
            foreach ($datas as $item) {
                if ($item['ResType'] == '1') {
                    $temp[] = $temp;
                }
            }
            if (count($temp) === count($allIds)) {
                OnlineZtpUserRedis::delColumn('Users', $Token);
                return true;
            }
        }
        //零信任核心模块授权数量
        $AuthExceeded = true;

        if (!isset($info['SumNum'])) {
            return false;
        }

        if ($count < $info['SumNum'] || ((int)$info['SumNum'] === 0 || (string)$info['SumNum'] === '')) {
            if (!OnlineZtpUserRedis::getOne('Users', [$Token])) {
                $onlineDataArr = [$Token => 1];
                // 设置一个有效期，最多俩分钟空窗期，在获取列表的时候补全
                if ($count <= 0) {
                    $onlineDataArr['LifeTimeNotInKey'] = 86400;
                }
                OnlineZtpUserRedis::setOne('Users', $onlineDataArr);
            }
        } else {
            $AuthExceeded = false;
        }

        if (OnlineZtpUserRedis::getOne('Users', [$Token])) {
            return true;
        }
        return $AuthExceeded;
    }

    /**
     * 异步刷新所有在线用户token的权限，及授权点数
     * @return void
     */
    public function refreshAllOnlineToken()
    {
        $list = NacOnLineDeviceModel::getList(['column' => 'token', 'groupby' => 'Token'], false, 0, 50000);
        $size = 100;
        $start = 0;
        $tmpTokenArr = [];
        if (!empty($list)) {
            foreach ($list as $row) {
                if (empty($row['Token'])) {
                    continue;
                }
                $tmpTokenArr[] = $row['Token'];
                $start++;
                if ($start % $size === 0) {
                    //避免cpu飙升，100个一批放入
                    TokenRedis::lMultiPushData($tmpTokenArr, 'setUserPower');
                    unset($tmpTokenArr);
                }
            }
            // 不满足100的批量入一次
            !empty($tmpTokenArr) && TokenRedis::lMultiPushData($tmpTokenArr, 'setUserPower');
            // 调用触发下实时计算权限
            \lib_queue::addJob('COMMON_RUN', ['run_func' => 'setUserPower']);
        }
    }

    /**
     * 返回已经分配了的资源权限ID
     * @param $Session
     * @return array|array[]|mixed
     */
    public function resId($Session)
    {
        $authorizationIDs = AuthUserModel::getAuthorizationGroup($Session['Uuid'], $Session['DeviceID']);
        return ResourceServiceProvider::getUserResIds(
            $Session['Uuid'],
            $Session['DeviceID'],
            $Session['DepartIDs'],
            $Session['RoleID'],
            $authorizationIDs
        );
    }

    /**
     * 设置用户资源权限
     *
     * @param $Token
     * @param string $From
     * @return mixed
     */
    public function setUserPower($Token, $From = '')
    {
        $this->writeLog("setUserPower {$Token} {$From}");
        $Session = $this->userPowerCheck($Token);
        if (empty($Session)) {
            return false;
        }
        $Session['From'] = $From;
        [$resIds, $ipResIds, $datas, $isNetIds, $diyResIds] = $this->resId($Session);
        $resPowerIds = array_merge($resIds, $ipResIds);
        //记录零信任用户在线占用点数
        $AuthExceeded = $this->ztpUserOnline($Session, $datas, $isNetIds, $Token, $diyResIds, 'ResourcePowerService');
        if (!$AuthExceeded) { //超过了点数
            //1、自定义的应用还是可以访问
            //2、内网环境下、内网直连的资源的资源依然可以访问
            $canVisitID = $diyResIds;
            if (isset($Session['IsInternal']) && $Session['IsInternal']) {
                $canVisitID = array_unique(array_merge($diyResIds, $isNetIds));
            }
            //不在可访问的资源id 集合中的数据  都删掉
            foreach ($resIds as $k => $v) {
                if (!in_array($v, $canVisitID, true)) {
                    unset($resIds[$k]);
                }
            }
            foreach ($ipResIds as $k => $v) {
                if (!in_array($v, $canVisitID, true)) {
                    unset($ipResIds[$k]);
                }
            }
            foreach ($datas as $k => $v) {
                if (!in_array($v['ResID'], $canVisitID, true)) {
                    unset($datas[$k]);
                }
            }
        }
        $AuthApp = [];
        $AuthAppGate = [];
        $AuthAppAll = [];
        $AuthAppID = [];
        $ipResIds = $this->parseIpResIds($datas, $Session, $ipResIds);

        $AgentGate = [];
        $GateDomain = [];
        [$GwGroupIps] = GateWayModel::getGroupColumns('ID');
        if (!empty($resIds)) {
            $cond = ['column' => 'power', 'InResID' => $resIds];
            $data = ResConfigListModel::getList($cond);
            $remoteData = ResRemoteModel::getRelationList($cond);
            if (is_array($remoteData)) {
                $data = array_merge($remoteData, $data);
            }
            if (!empty($data)) {
                foreach ($data as $val) {
                    $AgentGateTmp = $this->setGatewayIds($val, $GwGroupIps, $GateDomain);
                    $AgentGate = array_merge($AgentGate, $AgentGateTmp);
                    switch ($val['AccessTypeID']) {
                        case ACCESS_TYPE_TELENT:
                        case ACCESS_TYPE_RDP:
                        case ACCESS_TYPE_SSH:
                        case ACCESS_TYPE_VNC:
                        case ACCESS_TYPE_REMOTE_APP:
                            $AuthAppID[] = $val['ResID'];
                            break;
                        default:
                            $AuthApp[$val['URL']] = $val['ResID'];
                            if (!empty($val["GateWayID"])) {
                                if ($val['AccessTypeID'] != ACCESS_TYPE_MICRO_APP && $val['AccessTypeID'] != ACCESS_TYPE_CUSTOM_APP) {
                                    $AuthAppGate[] = $val["GateWayID"];
                                }
                                $AuthAppAll[$val["GateWayID"]][$val["ResID"]] = $val['URL'];
                            }
                    }
                }
            }
        }
        $auth = getmoduleregist(14);
        $vpnIpRangle = [];
        $vpnRouteList = [];
        $vpnIpListTmp = [];
        $IpListGateArr = [];
        $policyData = $this->setResourcePolicyResult(array_keys($datas), $Session, $ipResIds);
        $this->writeLog("setUserPower Vpn {$Token}:{$auth}:" . json_encode($ipResIds));
        if (!empty($ipResIds) && $auth) {
            $cond = ['column' => 'power', 'InResID' => $ipResIds];
            $zVpn = cutil_dict_get('ZTP', 'VpnType');
            $ipDatas = ResIPListModel::getList($cond);
            if ($zVpn !== 'vpn') {
                $ipDatas = self::parseProxyIpList($ipDatas);
            }

            $remoteData = ResRemoteModel::getRelationList($cond);
            if (is_array($remoteData)) {
                $ipDatas = array_merge($remoteData, $ipDatas);
            }
            $this->writeLog("setUserPower VpnInfo {$Token}");
            if (!empty($ipDatas)) {
                foreach ($ipDatas as $val) {
                    $resGateIds = $this->setGatewayIds($val, $GwGroupIps);
                    $this->writeLog("setUserPower GateInfo {$Token}");
                    foreach ($resGateIds as $gwId) {
                        $vpnIpRangle[$gwId] = $vpnIpRangle[$gwId] ?? [];
                        $vpnRouteList[$gwId] = $vpnRouteList[$gwId] ?? [];
                        // 策略不通过过滤掉
                        if (empty($policyData['IpResPolicys'][$val['ResID']])) {
                            continue;
                        }

                        //是否为内网
                        $isInternal = !empty($Session['IsInternal']);
                        $resInfo = $datas[$val['ResID']] ?? [];
                        //判断是否为内网，如果为内网且开启了内网访问实际地址，则直接返回实际地址
                        if (!empty($resInfo) && $resInfo['IsNat'] == 1 && $isInternal) {
                            $this->writeLog("this is IsInternal IsNat {$Token}" . json_encode($vpnRouteList));
                            continue;
                        }
                        $IpListGateArr[] = $gwId;
                        $vpnIpListTmp[] = $val['ResID'];
                        $vpnIpRangle[$gwId][$val['ResID']] = $val['IP'];
                        $vpnRouteList[$gwId][$val['ResID']] = $val['Route'];
                    }
                }
            }
        }
        $vpnIpList = implode(',', $vpnIpListTmp);
        $access = parse_ini_file(PATH_ETC . "access.ini");
        $powerData = ['AuthApp' => $AuthApp, 'AuthAppID' => $AuthAppID, 'vpnIpList' => $vpnIpList, 'vpnIpRangleAll' => $vpnIpRangle, 'DeviceID' => $Session['DeviceID'], 'ResPowerIds' => arrayUnique($resPowerIds),
            'vpnRouteListAll' => $vpnRouteList, 'AgentGate' => arrayUnique($AgentGate), 'IpListGate' => arrayUnique($IpListGateArr), 'GateDomain' => $GateDomain, 'AuthAppGate' => json_encode(arrayUnique($AuthAppGate)), 'AuthAppAll' => json_encode($AuthAppAll)];
        // 控制哪些字段变动需要下发通知
        $changeKey = [$access['ServerDomainIPv4In'], $access['ServerIPv4Ex'], $access['WebPortEx'], $access['WebPortIn'], $powerData['AgentGate']];
        $powerData['LastMD5'] = md5(json_encode($changeKey));
        $deviceRedis = DeviceIDRedis::getOne($Session['DeviceID']);
        //此处如果设备ID缓存key中的时间落后，比如微应用等会出现一直续期的问题。
        if ((!isset($Session['LastHeartTime']) && $Session['DeviceID'] > 0) || (!empty($deviceRedis['LastHeartTime']) && $deviceRedis['LastHeartTime'] !== $Session['LastHeartTime'])) {
            $powerData['LastHeartTime'] = $deviceRedis['LastHeartTime'];
            $powerData['LifeTime'] = $Session['LifeTime'];
        }
        $powerData['ztpUserExceed'] = $AuthExceeded ? '1' : '0';
        $powerData['LastUpdateTime'] = time();
        return SessionRedis::setOne($Token, array_merge($policyData, $powerData));
    }

    /**
     * 增加新隧道的数据处理
     * @param $ipDatas
     * @return array
     */
    private static function parseProxyIpList($ipDatas)
    {
        $aIpRes = [];
        if (is_array($ipDatas)) {
            foreach ($ipDatas as $item) {
                $aIpRes[$item['ResID']] = $item;
            }
        }
        $aIpRess = [];
        $aGwEx = GateWayExpendModel::getList(['column' => 'list', 'Type' => 1], 'RID');
        foreach ($aGwEx as $item) {
            if (isset($aIpRes[$item['RID']])) {
                $aIpRes[$item['RID']]['GateWayID'] = $item['GwID'];
                $aIpRes[$item['RID']]['GateWayType'] = $item['GwType'];
                $aIpRess[] = $aIpRes[$item['RID']];
            }
        }
        $ipDatas = $aIpRess;
        return $ipDatas;
    }

    /**
     * 组装$GatewayIds
     *
     * @param $resource
     * @param $GwGroupIps
     * @param array $DomainGatewayIds
     * @return array
     */
    public function setGatewayIds($resource, $GwGroupIps, &$DomainGatewayIds = []): array
    {
        $resGateIds = [];
        if ($resource["GateWayType"] === '1') {
            $resGateIds[] = $resource["GateWayID"];
        } elseif ($resource["GateWayType"] === '2') {
            if (empty($GwGroupIps[$resource["GateWayID"]])) {
                return [];
            }
            foreach ($GwGroupIps[$resource["GateWayID"]] as $ID) {
                $resGateIds[] = $ID;
            }
        }
        foreach ($resGateIds as $gateId) {
            if (!empty($resource['IsParseDomain'])) {
                $DomainGatewayIds[$gateId] = $DomainGatewayIds[$gateId] ?? [];
                $DomainGatewayIds[$gateId][] = $resource['URL'];
                //如果有依赖站点开启，则需要将依赖站点的对应域名都放入域名解析，并去重
                if (!empty($resource['DependSite'])) {
                    is_string($resource['DependSite']) && $resource['DependSite'] = json_decode($resource['DependSite'], true);
                    $isOpen = $resource['DependSite']['isOpen'] ?? 0;
                    if ($isOpen == 1 && !empty($resource['DependSite']['accessUrls'])) {
                        $accessUrlArr = explode(PHP_EOL, $resource['DependSite']['accessUrls']);
                        foreach ($accessUrlArr as $accUrl) {
                            $DomainGatewayIds[$gateId][] = hlp_net::getDomainByUrl($accUrl);
                        }
                        $DomainGatewayIds[$gateId] = array_unique($DomainGatewayIds[$gateId]);
                    }
                }
            }
        }
        return $resGateIds;
    }
}
