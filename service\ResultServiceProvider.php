<?php
/**
 * Description: 安检相关逻辑service
 * User: <EMAIL>
 * Date: 2021/07/19 15:53
 * Version: $Id: ResultServiceProvider.php 165504 2021-12-22 06:58:57Z duanyc $
 */

use Services\Result\Services;

class ResultServiceProvider extends BaseServiceProvider
{
    /**
     * 获取规范
     *
     * @param $policy_id
     *
     * @return bool|mixed
     */
    public static function getPolicy($policy_id)
    {
        if (empty($policy_id)) {
            return false;
        }

        $policyInfo = NacPolicyListModel::getOne($policy_id, 'info');

        if (empty($policyInfo) || $policyInfo['PolicyTradecode'] !== 'SafeCheck') {
            return false;
        }

        return $policyInfo;
    }

    /**
     * 解析获取PolicyBody
     *
     * @param $ostype
     * @param $PolicyBody
     *
     * @return mixed
     * @throws Exception
     */
    public static function getPolicyBody($ostype, $PolicyBody)
    {
        $params['servicePrefix'] = 'Base';
        $resultService = self::initResultService($params);
        return $resultService->getPolicyBody($ostype, $PolicyBody);
    }

    /**
     * 获取安检项
     *
     * @param $itemId
     *
     * @return bool|mixed
     */
    public static function getCheckItem($itemId)
    {
        if (empty($itemId)) {
            return false;
        }

        return NacCheckItemModel::getOne($itemId, 'info');
    }

    /**
     * 初始化认证后上报结果服务
     *
     * @param $params
     *
     * @return object|Services\MobileResultService|Services\PcResultService|Services\BaseResultService|Services\AutoResultService
     * @throws ReflectionException
     */
    public static function initResultService($params)
    {
        $getType = $params['servicePrefix'];
        $serviceClass = '\\Services\\Result\\Services\\' . $getType . 'ResultService';
        return (new ReflectionClass($serviceClass))->newInstance($params);
    }

    /**
     * 认证成功生成钥匙 默认有效期1个小时
     *
     * @param $deviceId
     * @param $expire
     *
     * @return mixed
     */
    public static function generateSubmitKey($deviceId, $expire = 3600)
    {
        $key = str_pad($deviceId, 5, '0', STR_PAD_LEFT);
        $SubmitKey = str_replace('.', '', microtime(true)) . $key;
        $data = ['AuthTime' => time(), 'SubmitKey' => $SubmitKey];
        cache_set_info('ASM_AuthSuccess:', "{$deviceId}_".IS_CLIENT, $data, $expire);
        return $SubmitKey;
    }

    /**
     * 保存上次设备信息
     * @param $deviceId
     * @param $deviceInfo
     */
    public static function saveLastDeviceInfo($deviceId, $deviceInfo): void
    {
        $columns = ['CheckResult', 'LastFaultTime', 'Registered', 'DeviceID'];
        $lastResult = [];
        foreach ($columns as $column) {
            $lastResult[$column] = $deviceInfo[$column];
        }
        cutil_php_log($lastResult, 'net_auth');
        if (empty($deviceId)) {
            return;
        }

        cache_set_info('ASM_LastResult:', "{$deviceId}", $lastResult, 300);
    }

    /**
     * 获取ip违规状态
     *
     * @param $ip
     *
     * @return string
     * @throws Exception
     */
    public static function getIrregularState($ip)
    {
        //查找当前网络技术
        $aNetSafe_NetSecurity = read_inifile(PATH_ETC . "SwInfoCollect.ini");
        if ($aNetSafe_NetSecurity['IpMacBind'] != "1") {
            return "false";
        }
        if ($aNetSafe_NetSecurity['IsIPControlSwitch'] != "1") {
            return "false";
        }
        if ($aNetSafe_NetSecurity['ControlIPRange'] == "") {
            return "false";
        }
        $aNetSafe_NetSecurity['ControlIPRange'] = explode(",", $aNetSafe_NetSecurity['ControlIPRange']);
        $iscontrolip = false;
        foreach ($aNetSafe_NetSecurity['ControlIPRange'] as $value) {
            $iparea = explode("-", $value);
            $ip0 = $iparea[0];
            $ip1 = $iparea[1];
            //比较IP大小,开始IP小于结束IP返回真
            if (CompareIP($ip0, $ip) && CompareIP($ip, $ip1)) {
                $iscontrolip = true;
                break;
            }
        }
        if ($iscontrolip) {
            return "true";
        } else {
            return "false";
        }
    }

    /**
     * 检查钥匙
     *
     * @param $deviceId
     * @param $SubmitKey
     *
     * @throws Exception
     */
    public static function resultCheck($deviceId, $SubmitKey)
    {
        $cache = cache_get_info('ASM_AuthSuccess:', "{$deviceId}_".IS_CLIENT);
        if (empty($cache)) {
            T(21104017);
        }
        if ($cache['SubmitKey'] != $SubmitKey) {
            T(21104018);
        }
    }

    /**
     * 钥匙清理
     *
     * @param $deviceId
     * @param $SubmitKey
     *
     * @throws Exception
     */
    public static function resultClear($deviceId)
    {
        cache_del_info('ASM_AuthSuccess:', "{$deviceId}_".IS_CLIENT);
    }

    /**
     * 安检修复上报 $InsideName为数组时支持批量
     *
     * @param $deviceId
     * @param $policyId
     * @param $InsideName
     *
     * @throws Exception
     */
    public static function securityFix($deviceId, $policyId, $InsideName)
    {
        $policyInfo = self::getPolicy($policyId);
        if (!is_array($policyInfo)) {
            T(21104002);
        }
        $device = DeviceModel::getOne($deviceId, 'one');
        if (empty($device)) {
            T(21103006);
        }
        $policyTime = $policyInfo['ChangeTime'] != '' ? $policyInfo['ChangeTime'] : $policyInfo['InsertTime'];
        $params = ['InsideName' => $InsideName, 'ChangeTime' => $policyTime,
                   'DeviceID' => $deviceId];
        $result = lib_yar::clients('safecheck', 'securityFix', $params);
        if (empty($result['state'])) {
            throw new Exception($result['message'], 21100002);
        }
    }

    /**
     * 获取安检开关
     *
     * @param  $DeviceID
     * @return bool
     */
    public static function getIsSafeCheck($DeviceID)
    {
        $devObj = NacOnLineDeviceModel::getSingle(['DeviceID' => $DeviceID]);

        if (empty($devObj['RoleID'])) {
            cutil_php_log("设备{$DeviceID}无法获取场景", "isolation");
            return false;
        }

        $pdata = SceneDictModel::getOneConfig((int)$devObj['SceneID'], 'IsSafeCheck', 'PolicyList');
        cutil_php_log("设备{$DeviceID}: {$devObj['SceneID']}是否要安检：{$pdata['Config']}", "isolation");
        return $pdata['Config'];
    }

    /**
     * 根据当前状态隔离逻辑处理
     *
     * @param $DeviceID
     */
    public static function isolationDeviceByCurr($DeviceID)
    {
        $IsSafeCheck = self::getIsSafeCheck($DeviceID);
        $computer = ComputerModel::getSingle(['DeviceID' => $DeviceID]);
        $StartTime = $computer['RegTime'];
        $CheckResult = '';
        $rcomputer = RelationComputerModel::getSingle(['DeviceID' => $DeviceID]);
        if (!empty($rcomputer['CheckResult'])) {
            $StartTime = $rcomputer['CheckTime'];
            $CheckResult = $rcomputer['CheckResult'];
        }
        $isIsolation = !empty($IsSafeCheck) && (empty($CheckResult) || $CheckResult == 'false');
        self::isolationDevice($isIsolation, $DeviceID, $CheckResult, $StartTime);
    }

    /**
     * 隔离处理
     *
     * @param bool   $isIsolation 是否隔离
     * @param int    $DeviceID    设备ID
     * @param string $CheckResult 安检结果
     * @param string $StartTime   隔离时间
     */
    public static function isolationDevice($isIsolation, $DeviceID, $CheckResult = '', $StartTime = 'now()')
    {
        cutil_php_log("设备隔离{$DeviceID}:{$isIsolation}:{$CheckResult}:{$StartTime}", "isolation");
        $isolation = IsolationLogModel::getSingle(['DeviceID' => $DeviceID]);
        if ($isIsolation) {
            if (empty($isolation)) {
                IsolationLogModel::insert(array(
                    'DeviceID' => $DeviceID,
                    'CheckResult' => $CheckResult,
                    'StartTime' => $StartTime,
                    'hadSafeCheck' => !empty($CheckResult) ? 1 : 0
                ));
            } elseif (empty($isolation['hadSafeCheck'])) {
                $cond = ['DeviceID' => $DeviceID];
                IsolationLogModel::updatePatch($cond, array(
                    'CheckResult' => $CheckResult,
                    'StartTime' => $StartTime,
                    'hadSafeCheck' => !empty($CheckResult) ? 1 : 0
                ));
            }
        } else {
            if (!empty($isolation)) {
                IsolationLogModel::delete(['DeviceID' => $DeviceID]);
            }
        }
    }
}
