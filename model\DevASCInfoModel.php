<?php

/**
 * Description: ASC基础信息表
 * User: <EMAIL>
 * Date: 2021/05/26 10:32
 * Version: $Id
 */

class DevASCInfoModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevASCInfo';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'LinkIP,IsUseRemote',
        'info'  => 'IP,AscPort',
        'use' => 'A.IsUseRemote',
        '*'   => '*',
    ];

    /**
     * 所有条目
     *
     * @param array $cond
     * @param string $Column
     * @return mixed
     */
    public static function getAll(array $cond = [],$Column = 'one')
    {
        self::$data = [];
        $column = self::$columns[$Column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = self::getWhere($cond);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取连表查询设备数据
     *
     * @param $deviceID
     * @param $column
     *
     * @return array|bool
     */
    public static function getJoinDevice($deviceID, $column = 'use')
    {
        if (empty($deviceID)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns[$column];
        $sql = "SELECT {$column} FROM TDevASCInfo A left join TDevice B on A.AscID=B.AscID  WHERE B.DeviceID = ".self::setData($deviceID);
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['AscID'])) {
            $where .= "AND AscID = ".self::setData($cond['AscID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
