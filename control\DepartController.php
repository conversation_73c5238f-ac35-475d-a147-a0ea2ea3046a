<?php

/**
 * Description: 部门相关交易
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: DepartController.php 158596 2021-10-11 02:28:06Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class DepartController extends BaseController
{

    /**
     * 获取部门信息，对应老交易 get_device_depart
     *
     * @return array|string
     * @throws Exception
     */
    public function info()
    {
        /* 直接获取连接IP，有代理服务器即获取代理服务器的IP */
        $devip = getRemoteAddress();
        $depinfo = UserServiceProvider::getDepartInfo($devip);
        return !empty($depinfo) ? $depinfo : 'none';
    }

    /**
     * 获取默认选中部门
     *
     * @return int|mixed
     * @throws \Services\Common\Exceptions\AsmException
     */
    public function default()
    {
        $devip = request('devip', 'request');
        $ip = getRemoteAddress();
        $devip = $ip!='' ? $ip : $devip;
        $return = UserServiceProvider::getDefaultDepart($devip);
        return $return;
    }

    /**
     * 获取部门树，对应老交易 get_depart_tree
     *
     * @return array|string
     * @throws Exception
     */
    public function tree()
    {
        //组织架构树的配置
        $tree_conf = ConfigServiceProvider::getDictAll("treeConfig");
        $conpany = ConfigServiceProvider::getDictAll("ADMINISTRATORINFO");
        $CompanyName = $conpany['CompanyName'];
        $devip = request('devip', 'request');
        $ip = getRemoteAddress();
        $devip = $ip!='' ? $ip : $devip;
        $depinfos = UserServiceProvider::getAllChildDepartInfo();

        // 对IP进行过滤
        $depinfos = array_filter($depinfos, function ($val2) use ($devip) {
            $IPSegment = trim($val2['IPSegment']);
            if (!empty($IPSegment) && $IPSegment != "") {
                if (UserServiceProvider::check_deviceip($devip, $IPSegment)) {
                    $rowData = $val2;
                    $rowData["seldepartid"] = $rowData["DepartID"];
                    return true; //将符合的部门信息记录到数组中
                }
            } else {
                return true;
            }
            return false;
        });

        $DepartIds=array();
        foreach ($depinfos as $depinfo) {
            $DepartIds[$depinfo['DepartID']] = 1;
        }
        $alldepart = UserServiceProvider::getFilterDepartList($DepartIds, $CompanyName);
        $arr = ['alldepart'=>$alldepart,'tree_conf'=>$tree_conf,'CompanyName'=>$CompanyName];
        if ($arr) {
            return $arr;
        }
        return 'none';
    }

    /**
     * 部门列表
     * @throws Exception
     */
    public function list()
    {
        $parentDepartId = request('parentDepartId', 'request', 0, 'int');
        $keyword = request('keyword', 'request');
        $start = request('start', 'request', 0, 'int');
        $limit = request('limit', 'request', 100, 'int');
        $ip = getRemoteAddress();
        $devip = $ip != '' ? $ip : request('devip', 'request');
        $conpany = ConfigServiceProvider::getDictAll("ADMINISTRATORINFO");
        $companyName = $conpany['CompanyName'];
        $cond = ['devip' => $devip];
        if ($parentDepartId >= 0) {
            $cond['UpID'] = $parentDepartId;
        } else {
            $cond['GteUpID'] = 0;
        }
        if (!empty($keyword)) {
            $cond['Keyword'] = $keyword;
        }
        $alldepart = UserServiceProvider::getDepartList($cond, $start, $limit);
        $allCount = UserServiceProvider::getDepartCount($cond);
        return ['total' => $allCount, 'list' => $alldepart, 'CompanyName' => $companyName];
    }

    /**
     * 获取子部门信息，对应老交易 get_child_depart
     *
     * @return mixed
     * @throws Exception
     */
    public function child()
    {
        $depart_id = request('depart_id', 'request', 0, 'int');
        $result = UserServiceProvider::getAllChildDepartInfo($depart_id, 'child');
        $tmp = array();
        $aDepart = array();
        if (!empty($result)) {
            foreach ($result as $row) {
                $tmp[$row['UpID']][$row['DepartID']] = $row;
            }
            $aDepart = UserServiceProvider::getChildTreeNode($depart_id, $tmp, "  └", true);
        }
        return !empty($aDepart) ? $aDepart : "";
    }
}
