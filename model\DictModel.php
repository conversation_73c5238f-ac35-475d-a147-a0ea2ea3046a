<?php

/**
 * Description: 字典表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: DictModel.php 158348 2021-09-29 15:19:29Z duanyc $
 */

class DictModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDict';
    public const PRIMARY_KEY = 'Type';
    protected static $columns = [
        'one' => 'ItemName, ItemValue, Remark',
        'all' => 'Type, ItemName, ItemValue, Remark',
        'list' => 'Type, ItemName, ItemValue',
    ];

    /**
     * 单条
     *
     * @param string $Type
     * @param string $ItemName
     *
     * @return mixed
     */
    public static function getOneItem(string $Type, string $ItemName)
    {
        if (empty($Type) || empty($ItemName)) {
            return false;
        }
        $cache = DictHashRedis::getOne($Type, [$ItemName]);
        if (isset($cache[$ItemName])) {
            return ['ItemName' => $ItemName, 'ItemValue' => $cache[$ItemName], 'Remark' => ''];
        }

        self::$data = [];
        $column = self::$columns['one'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE Type = ".self::setData($Type)." AND ItemName = ".self::setData($ItemName);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 该类型的所有条目 (getTDictInfoByType)
     *
     * @param string $Type
     *
     * @return mixed
     */
    public static function getAll(string $Type)
    {
        if (empty($Type)) {
            return false;
        }

        $cache = DictHashRedis::getOne($Type, null);
        if ($cache) {
            return $cache;
        }

        static $data = [];
        if (isset($data[$Type])) {
            return $data[$Type];
        }

        self::$data = [];
        $column = self::$columns['all'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE Type = ".self::setData($Type);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        $aData = lib_database::getAll($sql, $table['index'], false, self::$data);
        $aResult = [];

        if (is_array($aData) && count($aData) > 0) {
            foreach ($aData as $row) {
                $aResult [$row ['ItemName']] = $row ['ItemValue'];
            }
            if (PHP_SAPI !== 'cli') {
                $data[$Type] = $aResult;
            }
            return $aResult;
        }

        return false;
    }

    /**
     * 该类型的所有条目 (getTDictInfoByType)
     *
     * @param array $Types
     *
     * @return mixed
     */
    public static function getAllByTypes(array $Types)
    {
        if (empty($Types)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns['all'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE Type IN (".self::setArrayData($Types).")";
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        $aData = lib_database::getAll($sql, $table['index'], false, self::$data);
        $aResult = [];

        if (is_array($aData) && count($aData) > 0) {
            foreach ($aData as $row) {
                $aResult[$row['Type']][$row['ItemName']] = $row['ItemValue'];
            }
            return $aResult;
        }

        return false;
    }

    /**
     * 修改
     *
     * @param string $Type
     * @param string $ItemName
     * @param string $ItemValue
     * @param string $Remark
     *
     * @return boolean
     */
    public static function updateItem(string $Type, string $ItemName, string $ItemValue, $Remark = '')
    {
        if (empty($Type) || empty($ItemName)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = " Type = ".self::setData($Type)." AND ItemName = ".self::setData($ItemName);
        $key_values = ['ItemValue' => $ItemValue];
        if (!empty($Remark)) {
            $key_values['Remark'] = $Remark;
        }
        cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $key_values]), "model_{$table['name']}");
        DictHashRedis::setOne($Type, [$ItemName => $ItemValue]);
        return lib_database::update($key_values, $where, $table['name'], $table['index'], self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }

        if (isset($cond['ConditionPatchServer'])) {
            $where .= "AND (ItemName = 'PatchServerIP' OR ItemName = 'PatchServerPort' OR ItemName = 'ServerStatus')";
        }

        if (isset($cond['Time'])) {
            $where .= "AND (InsertTime >= ".self::setData($cond['Time']) .
                "OR UpdateTime >= ".self::setData($cond['Time']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
