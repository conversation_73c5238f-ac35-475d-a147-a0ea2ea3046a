<?php
/**
 * Description: 802.1X MAC 认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: MacAuthService.php 151036 2021-07-22 08:47:48Z duanyc $
 */

namespace Services\Auth\Services;

use Exception;
use Services\Auth\Interfaces\AuthServiceInterface;

class MacAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'Mac';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'userName'];
    }
}
