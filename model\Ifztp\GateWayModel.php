<?php

/**
 * Description: 网关表
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: GateWayModel.php 174952 2022-04-29 13:33:57Z duanyc $
 */

class GateWayModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGateWay';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'one'     => 'NetworkIP,NetworkPort,IP,HttpPort',
        'group'   => 'ID,Name,GroupID,IP,VpnPort,NetworkIP,OutVpnPort',
        'status'  => 'SysStatus, IsEnable',
        'app'   => 'ID,IP,GroupID',
    ];

    /**
     * 获取分组的网关IP
     *
     * @param string $columnNmae
     *
     * @return mixed
     */
    public static function getGroupColumns($columnNmae)
    {
        self::$data = [];
        $column = self::$columns['group'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $groupIps = [];
        $allIps = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $groupIps[$row['GroupID']][] = $row[$columnNmae];
                $allIps[$row['ID']] = $row[$columnNmae];
            }
        }
        return [$groupIps, $allIps];
    }

    /**
     * 获取分组的网关
     * @param string $column
     *
     * @return mixed
     */
    public static function getAppGroupColumns($column)
    {
        self::$data = [];
        $column = self::$columns[$column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $groupIps = [];
        $allIps = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $groupIps[$row['GroupID']][] = $row;
                $allIps[$row['ID']] = $row;
            }
        }
        return [$groupIps, $allIps];
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (!empty($cond['InGatewayID'])) {
            $where .= "AND ID IN (" . self::setArrayData($cond['InGatewayID']) . ")";
        }

        if (isset($cond['GroupID'])) {
            $where .= "AND GroupID = ".self::setData($cond['GroupID']);
        }
        if (isset($cond['IsEnable'])) {
            $where .= "AND IsEnable = ".self::setData($cond['IsEnable']);
        }

        if (isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * updated
     * a = a + 1
     * a = a - 1
     * **/
    public static function updated($ids, $type = 1)
    {
        self::$data = [];
        $pa = '+';
        $wh = '';
        if ($type == 0) {
            $pa = '-';
            $wh = ' and IpUseNum > 0';
        }
        $sql = 'update TGateWay set IpUseNum = IpUseNum '.$pa.' 1 where GwVirState = 1 and ID in ('.self::setArrayData($ids).')'.$wh;
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        lib_database::query($sql, $table['index'], false, self::$data);
    }
}
