<?php
/**
 * Description: 指纹认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: FingerAuthService.php 161644 2021-11-12 13:26:30Z duanyc $
 */

namespace Services\Auth\Services;

use Exception;
use Services\Auth\Interfaces\AuthServiceInterface;

class FingerAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'Finger';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['nID'] = request('nID', 'request', '0');
        $this->params['factory'] = request('factory', 'request');
        //识别指纹识厂家默认中正
        $this->params['factory'] = $this->params['factory'] ? $this->params['factory'] : 'ZZ';
        $this->params['strFingerTemplate'] = Base64DeExt(request('strFingerTemplate', 'request'));
        $this->params['jasFingerData'] = request('jasFingerData', 'request');
        return $this->params;
    }

    /**
     * 获取敲门认证的参数
     *
     * @param $Data
     *
     * @return mixed
     * @throws Exception
     */
    public function getFwknopData($Data)
    {
        $params = parent::getFwknopData($Data);
        switch($params['factory']) {
            case 'JAS':
                $params['jasFingerData'] = $params['key'];
                unset($params['key']);
                break;
            case 'ZZ':
                $params['strFingerTemplate'] = $params['key'];
                unset($params['key']);
                break;
        }
        return $params;
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'factory', 'nID', 'key'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'nID', 'factory', 'strFingerTemplate', 'jasFingerData'];
    }
}
