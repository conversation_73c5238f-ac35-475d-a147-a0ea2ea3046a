<?php
/**
 * Description: 匹配方法
 * User: <EMAIL>
 * Date: 2022/05/10 10:32
 * Version: $Id$
 */

!defined('IN_INIT') && exit('Access Denied');

class hlp_match
{
    /**
     * 检查多个允许或禁止，如部门
     *
     * @param $string
     * @param $vals
     * @param string $type 0表示允许，1表示禁止
     *
     * @return bool true：表示符合，false：表示不符合
     */
    public static function checkIntersect($string, $vals, $type = '0'): bool
    {
        if (empty($string)) {
            return true;
        }
        $datas = explode(',', $string);
        $sameDadas = array_intersect($datas, $vals);
        if ($type === '0') {
            if (empty($sameDadas)) {
                // 当前用户部门不在允许的部门里面
                return false;
            }
        } else {
            if (!empty($sameDadas)) {
                // 当前用户部门在禁止的部门里面
                return false;
            }
        }
        return true;
    }

    /**
     * 检查单个允许或禁止，如角色，用户类型
     *
     * @param $string
     * @param string $val
     * @param string $type 0表示允许，1表示禁止
     *
     * @return bool true：表示符合，false：表示不符合
     */
    public static function checkContain($string, $val, $type = '0'): bool
    {
        if (empty($string)) {
            return true;
        }
        $datas = explode(',', $string);
        if ($type === '0') {
            if (!in_array($val, $datas)) {
                // 当前用户角色不在允许的角色里面
                return false;
            }
        } else {
            if (in_array($val, $datas)) {
                // 当前用户角色在禁止的角色里面
                return false;
            }
        }
        return true;
    }

    /**
     * 检查当前时间是否在哪些星期几里面，
     *
     * @param string $week 1为星期一，2为星期二 ... ，0为星期日,逗号分隔。
     *
     * @return bool
     */
    public static function checkWeek($week): bool
    {
        if (empty($week)) {
            return false;
        }
        $weeks = explode(',', $week);
        $curWeek = date("w");
        return in_array($curWeek, $weeks);
    }

    /**
     * 检查当前时间是否在日期范围内
     *
     * @param $startDate
     * @param $endDate
     *
     * @return bool
     */
    public static function checkDate($startDate, $endDate): bool
    {
        $curTime = time();
        $startTime = strtotime($startDate);
        $endTime = strtotime($endDate);
        return $curTime >= $startTime && $curTime <= $endTime;
    }

    /**
     * 检查当前时分秒是否在区间内
     *
     * @param array $times
     *
     * @return bool
     */
    public static function checkDay(array $times): bool
    {
        foreach($times as $row) {
            $time = date('His');
            $startTime = str_replace(':', '', $row['StartTime']);
            $endTime = str_replace(':', '', $row['EndTime']);
            if ($time >= $startTime && $time <= $endTime) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查资源授权
     *
     * @param $AccessType
     *
     * @throws Exception
     */
    public static function checkAccessType($AccessType): void
    {
        if (!getmoduleregist(11)) {
            T(21148037);
        }
        switch ($AccessType) {
            case ACCESS_TYPE_RDP:
            case ACCESS_TYPE_VNC:
                if (!getmoduleregist(12)) {
                    T(21148037);
                }
                break;
            case ACCESS_TYPE_REMOTE_APP:
                if (!getmoduleregist(28)) {
                    T(21148037);
                }
                break;
            case ACCESS_TYPE_SSH:
            case ACCESS_TYPE_TELENT:
                if (!getmoduleregist(13)) {
                    T(21148037);
                }
                break;
        }
    }
}
