<?php
/**
 * Description: 网关
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: GatewayServiceProvider.php 174952 2022-04-29 13:33:57Z duanyc $
 */

class GatewayServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'gateway';

    /**
     * 定义个固定敲门端口配置
     * tcp/80      http入网   （会修改）
     * tcp/443		https入网   （会修改）
     * tcp/17443	web代理的端口   （会修改）
     * tcp/37527   AsmSvr业务端口
     * tcp/8443	管理后台端口
     * tcp/448		安卓NAT穿越端口
     * tcp/1723	pptp端口
     * udp/500		iOS NAT穿越端口
     * udp/4500	iOS NAT穿越端口
     * tcp/6969	终端P2P文件下载端口
     * tcp/16886   远程维护安卓手机端口
     * tcp/44346   IPv6网络下HTTPS管理服务端口
     * tcp/53      DNS端口
     * udp/53      DNS端口
     * udp/17443   VPN连接端口     （会修改）
     * udp/36533   小助手心跳检测端口
     * @var array
     */
    private static $openPorts = [
        'http'       => 'tcp/80',
        'https'      => 'tcp/443',
        'webVpn'     => 'tcp/17443',
        'vpn'        => 'udp/17443',
        'asmSvr'     => 'tcp/37527',
        'webManage'  => 'tcp/8443',
        'androidNat' => 'tcp/448',
        'pptp'       => 'tcp/1723',
        'iosNat'     => 'udp/500',
        'iosNat1'    => 'udp/4500',
        'p2p'        => 'tcp/6969',
        'android'    => 'tcp/16886',
        'ipv6'       => 'tcp/44346',
        'dns'        => 'tcp/53',
        'dns1'       => 'udp/53',
        'isa'        => 'udp/36533',
        'proxy'      => 'tcp/17442'
    ];

    /**
     * @Description:获取敲端口对应的字符串
     * User: <EMAIL>
     * Date: 2023/8/3 14:48
     */
    public static function getOpenPorts($vpnPort = '', $proxyPort = ''): string
    {
        $openPorts = self::$openPorts;
        //当前使用http/https 协议
        $protocol = ServerServiceProvider::getControllerProtocol();
        $ServerPort = getHttpPort($protocol == 'https');
        //修改入网对应的敲门端口
        $openPorts[$protocol] = 'tcp/'.$ServerPort;
        //修改VPN对应的敲门端口
        if (!empty($vpnPort)) {
            $openPorts['vpn'] = 'tcp/'.$vpnPort;
            $openPorts['webVpn'] = 'udp/'.$vpnPort;
            $openPorts['proxy'] = 'tcp/'.$proxyPort;
        }
        return implode(',', $openPorts);
    }

    /**
     * 获取控制器地址信息
     * @return array
     * @throws Exception
     */
    public static function getControlHostInfo(): array
    {
        $ini_arr = read_inifile(PATH_ETC . "/workerman.ini");
        if ($ini_arr['remotehost'] === '127.0.0.1') {
            $ini_arr['remotehost'] = getServerAddr();
        }
        return ['RemoteHost' => $ini_arr['remotehost'],
                'RemotePort' => $ini_arr['remoteport'],
                'RemoteProtocol' => 'https'
        ];
    }

    /**
     * 获取网关IP信息 原app_rdp_access_url信息
     *
     * @param $Resource
     *
     * @return bool|mixed
     */
    public static function getGateIpInfo($Resource)
    {
        if ($Resource['GateWayType'] === GATEWAY_TYPE_COMMON) {
            $GateInfo = GateWayModel::getOne($Resource['GateWayID'], 'one');
        } else {
            $GateInfo = GateWayGroupModel::getOne($Resource['GateWayID'], 'one');
        }
        if (empty($GateInfo)) {
            return false;
        }
        // 增加返回外网配置
        $GateInfo['NetworkIPEx'] = $GateInfo['NetworkIP'];
        $GateInfo['NetworkPortEx'] = $GateInfo['NetworkPort'];
        // 若为内网，则获取内网的IP和443端口
        if (empty($GateInfo['NetworkIP']) || IS_INTERNAL) {
            $GateInfo['NetworkIP'] = $GateInfo['IP'];
            $GateInfo['NetworkPort'] = $GateInfo['HttpPort'];
        }
        return $GateInfo;
    }

    /**
     * 获取网关IP信息 原app_rdp_access_url信息
     *
     * @param $Resource
     *
     * @return bool|mixed
     */
    public static function getGateGroupHostIpInfo($Resource)
    {
        if ($Resource['GateWayType'] === GATEWAY_TYPE_COMMON) {
            $GateInfo = GateWayModel::getOne($Resource['GateWayID'], 'one');
        } else {
            $GateInfo = GateWayGroupModel::getOne($Resource['GateWayID'], 'one');
            $cond = ['GroupID' => $Resource['GateWayID'], 'column' => '*'];
            $gatewayList = GateWayModel::getList($cond, 'SysStatus Asc');
            $GateInfo['IP'] = $gatewayList[0]['IP']; // 默认赋值防止为空
            foreach ($gatewayList as $gateway) {
                if ($gateway['SysStatus'] === '3') {
                    $GateInfo['IP'] = $gateway['IP'];
                    break;
                }
            }
        }
        if (empty($GateInfo)) {
            return false;
        }
        // 增加返回外网配置
        $GateInfo['NetworkIPEx'] = $GateInfo['NetworkIP'];
        $GateInfo['NetworkPortEx'] = $GateInfo['NetworkPort'];
        // 若为内网，则获取内网的IP和443端口
        if (empty($GateInfo['NetworkIP']) || IS_INTERNAL) {
            $GateInfo['NetworkIP'] = $GateInfo['IP'];
            $GateInfo['NetworkPort'] = $GateInfo['HttpPort'];
        }
        return $GateInfo;
    }

    /**
     * 获取外置服务器
     *
     * @return string
     * @throws Exception
     */
    public static function getOutServer(): string
    {
        $sdp = read_inifile(PATH_ETC . "sdp.ini");
        if (empty($sdp['RunMode'])) {
            return '';
        }
        if ($sdp['RunMode'] === '2' && !empty($sdp['OutsideSvr'])) {
            return $sdp['OutsideSvr'];
        }
        return '';
    }

    /**
     * 获取是否隐身的例外
     *
     * @return string
     * @throws Exception
     */
    public static function getIPIsInException(): string
    {
        $sdp = read_inifile(PATH_ETC . "sdp.ini");
        if (empty($sdp['RunMode']) || $sdp['RunMode'] !== '2') {
            return '1';
        }
        $currIp = getRemoteAddress();
        $trust_iprange_enable = get_ini_info(PATH_ETC . 'iptables.ini', 'trust_iprange_enable');
        return self::isInTrustIprange($trust_iprange_enable, $currIp);
    }

    /**
     * 是否在信任的ip范围
     *
     * @param $trust_iprange_enable
     * @param $currIp
     *
     * @return string
     */
    public static function isInTrustIprange($trust_iprange_enable, $currIp): string
    {
        if (!empty($trust_iprange_enable)) {
            $trust_ips = explode(',', $trust_iprange_enable);
            foreach ($trust_ips as $trust_ip) {
                $ips = explode('-', $trust_ip);
                $startIp = $ips[0];
                $endIp = $ips[1] ?? $ips[0];
                $live = FindInIP($currIp, $startIp, $endIp);
                if ($live === 1) {
                    return '1';
                }
            }
        }
        return '0';
    }

    /**
     * 获取host信息(控制器和网关都会调用)
     *
     * @return array
     * @throws Exception
     */
    public static function getHostInfo(): array
    {
        $controlInfo = self::getControlHostInfo();
        $config = GateConfigRedis::getOne('one');
        $isContrl = 0;
        $devtype = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if (!empty($config['AccessAddress']) && !IS_INTERNAL) {
            $Protocol = $config['Protocol'] ?: "https";
            $AccessPort = $config['AccessPort'] ?: "443";
            $urlPrefix = "{$Protocol}://{$config['AccessAddress']}:{$AccessPort}";
            if ($devtype !== DEVTYPE_ASG) {
                $isContrl = 1;
            }
        } else {
            $host = $controlInfo["RemoteHost"] . ":" . $controlInfo["RemotePort"];
            if ($devtype !== DEVTYPE_ASG) {
                $isContrl = 1;
                $urlPrefix = ServerServiceProvider::getGotoPrefixUrl();
            } else {
                $urlPrefix = "https://{$host}";
            }
        }
        return [$urlPrefix, $isContrl];
    }

    /**
     * 网关上获取控制器url前缀
     *
     * @return string
     * @throws Exception
     */
    public static function getControlUrlPrefix(): string
    {
        $GateConfig = GateConfigRedis::getOne('control');
        if (!empty($GateConfig)) {
            if (IS_INTERNAL) {
                return $GateConfig['ControlUrlIn'];
            }
            return $GateConfig['ControlUrl'];
        }
        return '';
    }

    /**
     * 获取防火墙
     *
     * @param $ChangeTrustIprange
     *
     * @return mixed
     * @throws Exception
     */
    public static function getFirewallConfig($ChangeTrustIprange)
    {
        $files = [
            PATH_ETC . "sdp.ini",
            PATH_ETC . "iptables.ini",
        ];
        $filepaths = [];
        foreach ($files as $filepath) {
            if (!file_exists($filepath)) {
                continue;
            }
            $filepaths[] = ['path' => $filepath, 'content' => base64_encode(trim(file_get_contents($filepath)))];
        }
        $data['action'] = 'Firewall';
        $data['filepaths'] = $filepaths;
        if ($ChangeTrustIprange) {
            // 执行防火墙策略
            cutil_exec_no_wait(PATH_HTML."/bin/asm_access_control.php &");
        }
        return $data;
    }

    /**
     * 获取配置文件下发
     * @return mixed
     */
    public static function getCertificateConfig($param = "")
    {
        $files = [
            PATH_ETC . "pki/tls/certs/gateway.crt",
            PATH_ETC . "pki/tls/private/gateway.key",
            PATH_ETC . "fwknop/private_key_pkcs8.der",
            PATH_ETC . "fwknop/private_key.pem",
            PATH_ETC . "fwknop/public_key.pem"
        ];
        $filepaths = [];
        foreach ($files as $filepath) {
            if (!file_exists($filepath)) {
                continue;
            }
            if (stripos($filepath, "gateway.crt") !== false && !empty($param['crt']) && is_file($param['crt'])) {
                // 自定义证书
                $content = file_get_contents($param['crt']);
            } elseif (stripos($filepath, "gateway.key") !== false && !empty($param['key']) && is_file($param['key'])) {
                // 自定义证书
                $content = file_get_contents($param['key']);
            } else {
                $content = file_get_contents($filepath);
            }
            $filepaths[] = ['path' => $filepath, 'content' => base64_encode(trim($content))];
        }
        $data['action'] = 'Fwknop';
        $data['filepaths'] = $filepaths;
        return $data;
    }

    /**
     * 获取VPN配置文件下发
     * @return mixed
     * @throws Exception
     */
    public static function getVpnCertificateConfig()
    {
        $files = [
            PATH_ETC . "gatewayvpn/EasyRSA/pki/ca.crt",
            PATH_ETC . "gatewayvpn/EasyRSA/pki/dh.pem",
            PATH_ETC . "gatewayvpn/EasyRSA/pki/issued/ztpserver.crt",
            PATH_ETC . "gatewayvpn/EasyRSA/pki/private/ztpserver.key"
        ];
        $filepaths = [];
        foreach ($files as $filepath) {
            if (!is_readable($filepath)) {
                cutil_exec_wait("chmod 777 {$filepath}");
            }
            $filepaths[] = ['path' => $filepath, 'content' => base64_encode(file_get_contents($filepath))];
        }
        $data['action'] = 'Gatevpn';
        $data['filepaths'] = $filepaths;
        return $data;
    }

    /**
     * 获取网关数据
     *
     * @param $params
     *
     * @return array|bool
     * @throws Exception
     */
    public static function getGatewayData($params)
    {
        $gatewayList = GateWayModel::getList();
        if (empty($gatewayList)) {
            return false;
        }
        $aCerts = CertificateListModel::getList(['Type' => 2]);
        $aCertInfo = [];
        if (is_array($aCerts)) {
            foreach ($aCerts as $item) {
                $aCertInfo[$item['ID']] = $item;
            }
        }
        $gwips = $params['GWInfo'] ?? [];
        $ChangeRunMode = $params['ChangeRunMode'] ?? false;
        $ChangeTrustIprange = $params['ChangeTrustIprange'] ?? false;
        $sdp = read_inifile(PATH_ETC . "sdp.ini");
        $groupList = GatewayGroupServiceProvider::getGroupGatewayList($gatewayList);
        $groupIps = GateWayGroupModel::getAllIp();
        $der_path = PATH_ETC . "fwknop/private_key_pkcs8.der";
        $priv_path = PATH_ETC . "fwknop/private_key.pem";
        $pub_path = PATH_ETC . "fwknop/public_key.pem";
        $fwknop_path = PATH_ETC . "fwknop/fwknopd.conf";
        // 由于该配置文件会出现经常修改会出现截断，与服务端刘志刚确认该文件他们不会改，所以我们此处维护所有字段
        $fwknopConfig = ['filepath' => $fwknop_path, 'Separator' => ' ',
            'FWKNOP_CONF_DIR' => '/opt/SJRJ/InG/etc/fwknop;',
            'ACCESS_FILE' => '/opt/SJRJ/InG/etc/fwknop/access.conf;',
            'FWKNOP_RUN_DIR' => '/opt/SJRJ/InG/var/run/fwknop;',
            'ENABLE_SDP' => strtoupper((int)$sdp['RunMode'] === 2 ? 'y' : 'n') . ";",
            'PCAP_INTF' => true,
            'MAX_SPA_PACKET_AGE' => '3000;',
            'VERBOSE' => '1;',
            'FLUSH_IPT_AT_INIT' => 'N;',
            'FLUSH_IPT_AT_EXIT' => 'N;',
            'PCAP_DISPATCH_COUNT' => '1000;',
            'PCAP_LOOP_SLEEP' => '100;',
        ];
        $certConfig = self::getCertificateConfig();
        $vpnCertConfig = self::getVpnCertificateConfig();
        $firewallConfig = self::getFirewallConfig($ChangeTrustIprange);
        $aFwknopConf = []; // 敲门配置
        $aCertConf = []; // 证书
        $aVpnCertConf = []; // vpn证书
        $aVpnConf = []; // vpn配置
        $aGatewayConf = []; // 网关配置
        $aFirewallConf = []; // 防火墙配置
        $urlPrefixIn = ServerServiceProvider::getGotoPrefixUrl();
        $Protocol = $sdp['Protocol'] ?: "https";
        $urlPrefix = ServerServiceProvider::getAsmControlUrl();
        foreach ($gatewayList as $gateway) {
            if (empty($gateway["IP"]) || (!empty($gwips) && !in_array($gateway["IP"], $gwips, true))) {
                continue;
            }
            self::saveGatewayCache($gateway["ID"], $gateway);
            $key = $gateway["IP"];
            $isOpenVpn = ((int)$gateway['IsVpn'] === 1) ? "Y" : "N";
            $del = ((int)$gateway['CtoC'] === 1) ? "" : "delkey";
            $aVpnConf[$key] = [
                "client-to-client" => $del,
                "Separator"        => " ",
                "enable-vpn"       => $isOpenVpn,
                "filepath"         => PATH_ETC . "gatewayvpn/gateway_vpn.conf",
                "runCmd"    => [PATH_ASM . "etc/init.d/gatewayvpnd restart"],
            ];
            $fwknopConfig['PCAP_FILTER'] = 'udp port ' . $gateway['SpaPort'] . ';';
            $aCertConf[$key] = !empty($aCertInfo[$gateway['CertID']]['Path']) ? self::getCertificateConfig(['crt' => $aCertInfo[$gateway['CertID']]['Path'],'key' => $aCertInfo[$gateway['CertID']]['KeyPath']]) : $certConfig;
            $aVpnCertConf[$key] = $vpnCertConfig;
            $aFwknopConf[$key] = $fwknopConfig;
            $aFirewallConf[$key] = $firewallConfig;
            $Gateways = GatewayGroupServiceProvider::getGatewayList($gateway, $groupList);
            $watermark = DictModel::getOneItem('ZTP', 'Watermark');
            $httpPort = $gateway['HttpPort'] ?? 17443;
            $ProxyPort = $gateway['ProxyPort'] ?? 17442;
            $InternalIPs = $gateway['InternalIPs'] ?? '';
            //生成放开敲门端口字符串
            $openPorts = self::getOpenPorts($httpPort, $ProxyPort);

            $aGatewayConf[$key] = [
                "ControlUrl"            => $urlPrefix,
                "ControlUrlIn"          => $urlPrefixIn,
                "GwID"                  => $gateway["ID"],
                "MyiP"                  => $gateway["IP"],
                "VirtualIP"             => $gateway['VirtualIP'] ?? $gateway["IP"], // 虚拟IP
                "DNS"                   => $gateway["DNS"],
                "StdDNS"                => $gateway["StdDNS"],
                "RdpAddr"               => $gateway['NetworkIP'] ?? $gateway["IP"],
                "RdpPort"               => $gateway['NetworkPort'] ?? 443,
                "HttpPort"              => $httpPort,
                "OutSpaPort"            => $gateway['OutSpaPort'],
                "ErrorUrl"              => $urlPrefix . "/access/sdpErr",
                "ErrorUrlIn"            => $urlPrefixIn . "/access/sdpErr",
                "AddJsUrl"              => "",
                "RsaPrivateKey"         => $priv_path,
                "RsaPublicKey"          => $pub_path,
                "RsaPrivateKeyPkcs8Der" => $der_path,
                "AccessAddress"         => $sdp['AccessAddress'],
                "AccessPort"            => $sdp['Port'],
                "Protocol"              => $Protocol,
                "OpenPorts"             => $openPorts, //sdp.ini增加一个敲门对应的端口配置字符串
                "Gateways"              => $Gateways,
                "RunMode"               => $sdp['RunMode'],
                "ChangeRunMode"         => $ChangeRunMode,
                "RouteCmd"              => $gateway["RouteCmd"], //隧道模式下 NAT模式 : 路由模式 配置
                "State"                 => (int)$gateway["SysStatus"],
                "IsEnable"              => (int)$gateway["IsEnable"],
                "ExpireTime"            => '99999999',
                "HaIP"                  => $groupIps[$gateway["GroupID"]] ?? '',
                "HaMode"                => (int)$gateway["IsLoad"] <= 0 ? "SINGLEHOST" : "DUALHOST",//双机状态 SINGLEHOST,DOUBLE
                "Priority"              => $gateway["Priority"],//权值
                "ClipboardCopy"         => 0,//是否复制  RDP全局开关
                "ClipboardPaste"        => 0,//是否黏贴 RDP全局开关
                "TerminalHeartbeat"     => 60,//单位 秒 心跳间隔 RDP全局开关
                "IpAllocation"          => $gateway['IpAllocation'] ?? 'proxy',
                "GwVirState"            => $gateway['GwVirState'] ? 1 : 0,
                "disVirIp"              => $gateway['disVirIp'] ?? 1,
                "RecoveHours"           => $gateway['RecoveHours'] ?? 0,
                "ProxyPort"             => $gateway['ProxyPort'] ?? 17442,
                "PriorityProx"          => $gateway['PriorityProx'] ?? 1,
                "RecoveVirIp"            => $gateway['RecoveVirIp'] ?? 1,
                "Watermark"             => $watermark['ItemValue'] ?? '{"content":"\u6d4b\u8bd5\u6c34\u5370","config":"username,ip,time"}',
                "InternalIPs"           => $InternalIPs
            ];
        }

        // key为tradecode
        $return = [];
        $return[] = ['TradeCode' => 'overridefile', 'AsgList' => $aFirewallConf];
        $return[] = ['TradeCode' => 'fileConfig', 'AsgList' => $aVpnConf];
        $return[] = ['TradeCode' => 'overridefile', 'AsgList' => $aVpnCertConf];
        $return[] = ['TradeCode' => 'overridefile', 'AsgList' => $aCertConf];
        $return[] = ['TradeCode' => 'fileConfig', 'AsgList' => $aFwknopConf];
        $return[] = ['TradeCode' => 'gwconfig', 'AsgList' => $aGatewayConf];
        return $return;
    }

    /**
     * 保存网关缓存
     * @param $gatewayId
     * @param $gateway
     * @return bool
     */
    private static function saveGatewayCache($gatewayId, $gateway)
    {
//        cutil_php_debug(var_export($gatewayId, true), 'gateway');
        if (empty($gatewayId)) {
            return false;
        }
//        cutil_php_debug(var_export($gateway, true), 'gateway');
        GatewayRedis::setOne($gatewayId, [
            'ID' => $gatewayId,
            'Name' => $gateway['Name'],
            'Priority' => $gateway['PriorityProx'],
            'IpIn' => $gateway['IP'],
            'IpEx' => !empty($gateway['NetworkIP']) ? $gateway['NetworkIP'] : $gateway['IP'],
            'PortIn' => $gateway['VpnPort'],
            'PortEx' => $gateway['OutVpnPort'],
            'GwVirState' => $gateway['GwVirState'], // 0:nat 1:virip
            'disVirIp' => $gateway['disVirIp'], // 1:nat 2:禁用
            'ProxyPort' => $gateway['ProxyPort'], // 代理端口
            'RecoveHours' => $gateway['RecoveHours'],  //回收时间
            'RecoveVirIp' => $gateway['RecoveVirIp'],  //回收时间
        ]);
        return true;
    }

    /**
     * 修改配置文件
     *
     * @param $params
     *
     * @throws Exception
     */
    public static function updateConfigFile($params): void
    {
        $filepath = $params['filepath'];
        self::log("update---------::".var_export($params, true));
        if (!hlp_check::addLock('updateConfigFile', $filepath)) {
            self::log("updateConfigFile LOCK");
            return;
        }
        $runCmd = $params['runCmd'] ?? '';
        unset($params['filepath'], $params['runCmd']);
        if (!empty($params['PCAP_INTF'])) {
            $ipinfo = get_ini_info(PATH_ETC . 'deveth.ini.noback', ['manager']);
            $params['PCAP_INTF'] = ($ipinfo['manager'] ?? '') . ';';
        }
        $aOldFile = $fileContent = "";
        if (!file_exists($filepath)) {
            file_put_contents($filepath, "");
            writeFile($filepath, $params, 0, $params["Separator"]);
        } else {
            $aFile = file_get_contents($filepath);
            $aOldFile = $aFile;
            $aFile = explode("\n", $aFile);
            if (is_array($aFile)) {
                // 删除key
                foreach ($aFile as $item) {
                    if (trim($item) === "") {
                        continue;
                    }
                    foreach ($params as $key => $val) {
                        if (strpos(trim($item), $key) !== false) {
                            $item = ($val === "delkey") ? "" : $key . $params["Separator"] . $val;
                            break;
                        }
                    }
                    if (stripos($item, "delkey") === false) {
                        $fileContent .= $item . "\n";
                    }
                }
                self::log("fileContent---------::".$fileContent);
                foreach ($params as $key => $val) {
                    $flag = 1 ;
                    if ($key === "Separator") {
                        continue;
                    }
                    foreach ($aFile as $item) {
                        if (strpos(trim($item), $key) !== false) {
                            $flag = 0;
                            break;
                        }
                    }
                    if ($flag === 1 && stripos($val, "delkey") === false) {
                        $fileContent .= $key . $params["Separator"] . $val . "\n";
                    }
                }
            }
            if (!is_writable($filepath)) {
                cutil_exec_wait("chmod 666 ".$filepath);
            }
            file_put_contents($filepath, $fileContent);
        }
        hlp_check::delLock('updateConfigFile', $filepath);
        //判断条件是否文件有变动，变动就执行
        if ($aOldFile == $fileContent) {
            return ;
        }
        if (!empty($runCmd)) {
            foreach ($runCmd as $cmd) {
                self::log("fileConfig cmd: {$cmd}.");
                cutil_exec_no_wait($cmd);
            }
        }
    }

    /**
     * 修改路由配置
     *
     * @param $content
     * @param $restartFirewall
     *
     * @throws Exception
     */
    public static function updateRouteConfig($content, &$restartFirewall = false): void
    {
        if (empty($content)) {
            return;
        }
        if (!hlp_check::addLock('updateRouteConfig', 'gateway_vpn')) {
            self::log("VpnInfo LOCK");
            return;
        }
        $VpnInfo = Base64DeExt($content);
        self::log("VpnInfo: {$VpnInfo}");
        $ishavpush = 0;
        $ishavser = 0;
        if (stripos($VpnInfo, "push") !== false) {
            $ishavpush = 1;
        }
        if (stripos($VpnInfo, "server") !== false) {
            $ishavser = 1;
        }
        $aVpnInfo = explode("\n", $VpnInfo);
        $push_new = "";
        $aPush = [];
        $server_new = "";
        $route_conf = "";
        foreach ($aVpnInfo as $istr) {
            if (stripos($istr, "push ") !== false && $ishavpush) {
                $push_new .= $istr . "\n";
                $aPush[] = $istr;
            } elseif (stripos($istr, "server ") !== false && $ishavser) {
                $server_new .= $istr . "\n";
                $route_conf .= $istr . "\n";
            }
        }
        self::log("route_conf: {$route_conf}");
        //修改gateway_nat_route.conf
        if (!empty($route_conf)) {
            $routePath = PATH_ETC . "gatewayvpn/gateway_nat_route.conf";
            $aRouConf = explode("\n", $route_conf);
            $cmds = "";
            foreach ($aRouConf as $item) {
                if (trim($item) !== "") {
                    $route = trim(substr($item, 6));
                    $aIp = explode(" ", trim($route));
                    $mask = hlp_net::netmaskTo(trim($aIp[1]));
                    if ($aIp[0] !== "" && $mask !== "") {
                        $cmds .= empty($cmds) ? $aIp[0] . "/" . $mask : "," . $aIp[0] . "/" . $mask;
                    }
                }
            }
            $aFile = get_ini_info($routePath);
            $aFile["gateway_nat_route"] = $cmds;
            writeFile($routePath, $aFile);
        }
        //修改gateway_vpn.conf
        $path = PATH_ETC . "gatewayvpn/gateway_vpn.conf";
        $info = file_get_contents($path);
        $aInfo = explode("\n", $info);
        $push = "";
        $server = "";
        foreach ($aInfo as $istr) {
            if (stripos($istr, "push ") !== false) {
                $push .= $istr . "\n";
            } elseif (stripos($istr, "server ") !== false && $ishavser) {
                $server .= $istr . "\n";
            }
        }
        if (!empty($server_new)) {
            if (!empty($server)) {
                $info = str_replace($server, $server_new, $info);
            } else {
                $info .= $server_new;
            }
        }
        if (!empty($push)) {
            $info = str_replace($push, $push_new, $info);
        } else {
            $info .= $push_new;
        }
        $cmd = 'echo ' . "'" . $info . "'" . ' > ' . $path;
        cutil_exec_wait($cmd);
        if (!empty($aPush)) {
            foreach ($aPush as $cmds) {
                if (!empty($cmds)) {
                    cutil_exec_wait($cmds);
                }
            }
        }
        hlp_check::delLock('updateRouteConfig', 'gateway_vpn');
        //改了配置 必须重启
        $cmd = PATH_ASM . "etc/init.d/gatewayvpnd restart";
        self::log("setVpnRoute {$cmd}");
        cutil_exec_no_wait($cmd);
        $restartFirewall = true;
    }

    /**
     * 同步资源数据到网关
     *
     * @param $ResList
     * @throws Exception
     */
    public static function syncResList($ResList): void
    {
        if (empty($ResList)) {
            return;
        }
        $ResIds = [];
        $appcfg = [];
        foreach ($ResList as $Resource) {
            ResIPModel::duplicate($Resource);
            $ResIds[] = $Resource['ResID'];
            $appOne = ResourceServiceProvider::getOneProxyConfig($Resource['IP']);
            $appOne['resID'] = (int)$Resource['ResID'];
            $appcfg[] = $appOne;
        }
        ResIPModel::delete(['NotInResID' => $ResIds]);
        ProxyConfigRedis::setOne(['appnum' => count($appcfg), 'appcfg' => $appcfg]);
    }

    /**
     * 下发网关配置
     *
     * @param $params
     * @throws Exception
     */
    public static function setGatewayConfig($params): void
    {
        $params['AsmRootPath'] = PATH_SYSTEM;
        $oldGateConfig = GateConfigRedis::getOne('gateway');
        GateConfigRedis::setOne($params);
        $deviceType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');

        if ($deviceType === DEVTYPE_ASG && isset($params["MyiP"])) {
            $path = PATH_ETC . "ha.ini";
            $aHaInfo = get_ini_info($path);
            $aHaInfo['Mode'] = empty($params["HaMode"]) ? "SINGLEHOST" : $params["HaMode"];
            $aHaInfo['Priority'] = (int)$params["Priority"] <= 0 ? "120" : (int)$params["Priority"];
            if ($aHaInfo['Mode'] === "SINGLEHOST") {
                $params["HaIP"] = $params["MyiP"];
            }
            $aDevInfo = get_ini_info(PATH_ETC . 'deveth.ini.noback', ['manager', 'managernetmask']);
            $aHaInfo['Virifname'] = ($aHaInfo['Mode'] === "SINGLEHOST") ? $aDevInfo["manager"]."|" :
                ($aDevInfo["manager"]."|".$params["HaIP"]."/".netmaskto($aDevInfo["managernetmask"]));
            $aHaInfo["MANAGER_NAME"] = $aDevInfo["manager"];
            $aHaInfo["MANAGER_REAL_IP"] = $params["MyiP"];
            $aHaInfo["MANAGER_VIRTUAL_IP"] = $params["HaIP"];
            $aHaInfo["MANAGER_REAL_IPv6"] = "";
            $aHaInfo["MANAGER_VIRTUAL_IPv6"] = "";
            write_localfile($path, arrayToIniString($aHaInfo));
        }
        // 由于修改入网端口与协议时需要修改敲门端口，故将该部分提前,但是避免升级被覆盖为空，此处如果端口未变动不修改。
        if (!empty($params['OpenPorts'])) {
            $openPorts = cache_get_info("ASG_Cache_", 'Gateway', 'OpenPorts');
            if ($openPorts !== $params['OpenPorts']) {
                self::updateFwknopOpenPorts('OPEN_PORTS', $params['OpenPorts']);
            }
        }
        if (!empty($params['HttpPort'])) {
             self::updateListenPort($params['HttpPort'], $params['OpenPorts']);
        }

        // 增加启用/禁用配置
        if (isset($params['IsEnable'])) {
            $asgInfo = get_ini_info(PATH_ETC . "asg.ini");
            if (!isset($asgInfo['IsEnable']) || $asgInfo['IsEnable'] != $params['IsEnable']) {
                $asgInfo['IsEnable'] = $params['IsEnable'];
                write_localfile(PATH_ETC . "asg.ini", array_to_inistr($asgInfo), 'w', true);
                $cmd = PATH_ASM . 'sh/ztpgateway.restart.sh';
                cutil_exec_no_wait($cmd);
            }
        }
        // 修改隐身模式
        if (isset($params['RunMode'])) {
            $path = PATH_ETC . "sdp.ini";
            $sdpInfo = get_ini_info($path);
            if ($sdpInfo['RunMode'] !== $params['RunMode']) {
                $sdpInfo['RunMode'] = $params['RunMode'];
                write_localfile($path, arrayToIniString($sdpInfo));
            }
            self::log("setRunMode {$sdpInfo['RunMode']}");
            cutil_exec_no_wait(PATH_HTML."/bin/asm_access_control.php &");
        }
        //修改DNS
        if (isset($params['DNS']) || isset($params['StdDNS'])) {
            self::saveDns($params);
        }
        $restartFirewall = !empty($params['ChangeRunMode']) ? true : false;
        $isChange = 0 ;
        // RouteCmd有变化才修改vpn配置文件
        if (GateConfigRedis::isChange($params, $oldGateConfig, 'RouteCmd')) {
            $isChange = 1 ;
            self::updateRouteConfig($params['RouteCmd'], $restartFirewall);
        }
        if ($restartFirewall) {
            $cmd = PATH_ASM . "etc/init.d/firewall restart";
            self::log("setVpnRoute {$cmd}");
            cutil_exec_wait($cmd);
        }
        $configFile = PATH_ETC . "asmproxy.ini";
        $asmproxyInfo = get_ini_info($configFile);
        if (empty($oldGateConfig['ProxyPort']) || $oldGateConfig['ProxyPort'] != $params['ProxyPort'] || $params['ProxyPort'] != $asmproxyInfo['ProxyPort']) {
            if (!empty($params['ProxyPort'])) {
                self::setProxyPort($params);
            }
        }
        if (!$isChange&&!empty($oldGateConfig['VirtualIP'])&&!empty($params['VirtualIP'])&&$oldGateConfig['VirtualIP']!=$params['VirtualIP']) {
            $cmd = PATH_ASM . "etc/init.d/gatewayvpnd restart";
            self::log("setVpn VirtualIP {$cmd}");
            cutil_exec_no_wait($cmd);
        }
    }

    /**
     * 修改网关监听端口
     *
     * @param $newPort
     * @param $openPortsStr
     * @return bool
     * @throws Exception
     */
    public static function updateListenPort($newPort, $openPortsStr)
    {
        $asgInfo = get_ini_info(PATH_ETC . "asg.ini");
        $oldPort = $asgInfo['HttpPort'] ?? '17443';

        // 如果本次端口未发生变化不需要处理，避免升级时候触发的动作导致敲门配置文件或者其他配置文件被覆盖
        if ((int)$oldPort === (int)$newPort || in_array((int)$newPort, [80, 443], false) ||
            in_array((int)$oldPort, [80, 443], false)) {
            self::log("updateListenPort the port not change; newport " . $newPort . ",oldPort" . $oldPort);
            return false;
        }

        // 检查新的端口是否被占用
        $status = self::checkPrivPort($newPort);
        if (!$status) {
            self::log("updateListenPort: the port ".$newPort." is in use!");
            return true;
        }
        $asgInfo['HttpPort'] = $newPort;
        write_localfile(PATH_ETC . "asg.ini", array_to_inistr($asgInfo), 'w', true);
        $nginxFile = PATH_ETC . "nginx/vhost_asm/{$oldPort}.conf";
        $newNginxFile = PATH_ETC . "nginx/vhost_asm/{$newPort}.conf";
        //todo() 此处逻辑会有个问题，暂未修改，如果客户已经自定义过其他端口,重复升级会导致17443.conf 默认配置会同时存在
        self::moveNginxFile($nginxFile, $newNginxFile, $oldPort, $newPort);
        cutil_exec_wait(PATH_ETC."init.d/nginx reload");
        //修改vpn端口并重启
        $aVpnConf = [
            "Separator" => " ",
            "port"      => $newPort,
            "filepath"  => PATH_ETC . "gatewayvpn/gateway_vpn.conf",
            "runCmd"    => [PATH_ASM . "etc/init.d/gatewayvpnd restart"],
        ];
        self::updateConfigFile($aVpnConf);
        return  true;
    }

    /**
     * 修改ASG敲门的端口
     *
     * @param $key
     * @param $openPortStr
     * @return bool
     * @throws Exception
     */
    public static function updateFwknopOpenPorts($key, $openPortStr): bool
    {
        self::log("updateFwknopOpenPorts: " . $key . ':' . $openPortStr);
        if (empty($openPortStr)) {
            return false;
        }
        $configFile = PATH_ETC . "fwknop/access.conf";
        //读取旧内容，做对比日志
        $content = file_get_contents($configFile);
        self::log("old_content OPEN_PORTS: " . $content);
        //拼接替换行内容
        $new_content = "OPEN_PORTS " . $openPortStr;
        self::log("new_content OPEN_PORTS: " . $new_content);
        repalce_localfile($configFile, 'OPEN_PORTS', $new_content);
        cache_set_info("ASG_Cache_", 'Gateway', ['OpenPorts' => $openPortStr], 3600);
        //write_localfile($configFile, $new_content, 'w', true);
        // 重启敲门
        cutil_exec_no_wait(PATH_ASM . "etc/init.d/fwknopd restart");
        return true;
    }


    /**
     * 保存备选dns
     *
     * @param $params
     * @throws Exception
     */
    private static function saveDns($params): void
    {
        $aDNS = [];
        $curDns = getDNS();
        //判断一下是否修改首选DNS
        if (isset($params['DNS'])) {
            $aDNS[0] = $params['DNS'];
            $aDNS[1] = $curDns['B'];
        }
        if (isset($params['StdDNS'])) {
            $aDNS[0] = !isset($aDNS[0]) ? $curDns['F'] : $aDNS[0];
            $aDNS[1] = $params['StdDNS'];
        }
        //判断一下是否修改备选DNS
        if ($curDns['F'] === $aDNS[0] && $curDns['B'] === $aDNS[1]) {
            return;
        }
        $s_content = "";
        $dnsArr = [];
        if (!empty($aDNS[0])) {
            $s_content .= "nameserver " . $aDNS[0];
            $dnsArr[] = $aDNS[0];
        }
        if (!empty($aDNS[1])) {
            $s_content .= "\nnameserver " . $aDNS[1];
            $dnsArr[] = $aDNS[1];
        }
        $dnsStr = !empty($dnsArr) ? implode(' ', $dnsArr) : '***************';
        $n_content = "resolver {$dnsStr};";
        write_localfile(PATH_ETC . "resolv.conf", $s_content);
        cutil_exec_no_wait("cp " . PATH_ETC . "resolv.conf   /etc/resolv.conf");
        write_localfile(PATH_ETC . "nginx/resolver.conf", $n_content, 'w', true);
    }

    /**
     * 修改网关表的dns
     *
     * @param $gwip
     * @param $dns
     *
     * @return mixed
     */
    public static function updateGatewayDns($gwip, $dns, $stDns)
    {
        if (empty($gwip)) {
            return false;
        }
        return GateWayModel::updatePatch(['IP' => $gwip], ['DNS' => $dns, 'StdDNS' => $stDns]);
    }

    /**
     * 网关策略检查
     *
     * @param $token
     * @param $resId
     *
     * @throws Exception
     */
    public static function checkGatewayPolicy($token, $resId): void
    {
        if (empty($token) || empty($resId)) {
            T(21148024);
        }
        $resPolicy = UserResourceRedis::getOne($token, $resId);
        if (!empty($resPolicy) && $resPolicy['Result'] !== '1') {
            throw new Exception($resPolicy['Reason']);
        }
    }

    /**
     * 记住资源密码
     * @param $params
     * @return void
     */
    public static function saveRememberPassword($params): void
    {
        RememberPasswordModel::insert($params);
    }

    /**
     * 清除记住密码
     * @param $params
     * @return void
     */
    public static function removeRememberPassword($params): void
    {
        RememberPasswordModel::delete($params);
    }

    /**
     * 新的联动清理网关数据
     */
    public static function clearGatewayData()
    {
        ModuleOnlineRedis::deleteOne('Proxy');
        ModuleOnlineRedis::deleteOne('Users');
        ModuleOnlineRedis::deleteOne('WebOnline');
        SessionRedis::deleteAll();
    }

    /**
     * 获取域名解析的网关
     * @param $GateDomain
     * @return array
     */
    private static function getDomainGate($GateDomain)
    {
        if (empty($GateDomain)) {
            return [];
        }
        $domainGate = json_decode($GateDomain, true);
        if (empty($domainGate) || !is_array($domainGate)) {
            return [];
        }
        return array_map('strval', array_keys($domainGate));
    }

    /**
     * 根据token获取网关列表
     * @param string $token
     * @return false|true|array
     * **/
    public static function getGateway($token)
    {
        if (strlen($token)!=32) {
            return [];
        }
        $aSess = SessionRedis::getOne($token, 'gwvirip');
        $ipListGate = json_decode($aSess['IpListGate'], true);
        $ipGate = !empty($ipListGate) ? $ipListGate : [];
        $domainGate = self::getDomainGate($aSess['GateDomain']);
        if (!empty($domainGate)) {
            $ipGate = array_unique(array_merge($ipGate, $domainGate));
        }
        if (empty($ipGate)) {
            return [];
        }
        $aGwErr = [];
        if (!empty($aSess['VirIpInfo'])) {
            $aVirIpInfo = json_decode($aSess['VirIpInfo'], true);
            foreach ($ipGate as $gwId) {
                if (isset($aVirIpInfo[$gwId]['disVirIp']) && $aVirIpInfo[$gwId]['disVirIp'] == 2) {
                    $aGwErr[$gwId] = $aVirIpInfo[$gwId]['disVirIp'];
                }
            }
        }
        $aNoIpGw = self::getGwVirIpStatus($aSess);
        $aVirIpInfo = "";
        $aSess = "";
        // 134
        if (!empty($ipGate)) {
            $aGateway = GateWayModel::getList(['InGatewayID'=>$ipGate,"IsEnable"=>"1"]);
            $aData = [];
            if (is_array($aGateway)) {
                foreach ($aGateway as $item) {
                    $gwD = [];
                    $gwD['Name'] = $item['Name'];
                    $gwD['ID'] = $item['ID'];
                    $gwD['SysStatus'] = in_array($item['SysStatus'], [1,3,4]) ? 0 : 1;
                    $gwD['GwVirState'] = "0";
                    $gwD['ErrMsg'] = "";
                    if (isset($aGwErr[$item['ID']]) && $aGwErr[$item['ID']] == 2 || in_array($item['ID'], $aNoIpGw['noIpPermitGw'])) {
                        // 0:正常,1:地址池满了,转为NAT, 2: 地址池满了,禁止连接
                        $gwD['GwVirState'] = $aGwErr[$item['ID']];
//                    $gwD['ErrMsg'] = $aGwErr[$item['ID']] == 2 ? "IP地址池满了,禁止连接,请联系管理员!" : "IP地址池满了,转为NAT模式!";
                        $gwD['ErrMsg'] = L(21148059);
                        $gwD['SysStatus'] = 1;
                        $gwD['ErrCode'] = '21148059';
                    }
                    $aData[] = $gwD;
                }
            }
            return $aData;
        }
        $aGateway = "";
        return [];
    }
    /**
     * 合并vpn数据和web代理数据
     * @param $vpnIpRangleAll
     * @param $AuthAppAll
     * @return array
     * **/
    public static function getGwVirIpStatus($Session, $type = 0)
    {
        $aVpnR = [];
        if (!empty($Session['vpnIpRangleAll'])&&$Session['vpnIpRangleAll']!=[]) {
            $aVpnR = json_decode($Session['vpnIpRangleAll'] ?? '', true);
        }
        return self::makeGwResStatus($aVpnR);
    }
    /**
     * 计算token对应的网关是否因为未分配虚拟IP被禁用
     * @param $aUserSession
     * @return array
     * **/
    private static function makeGwResStatus($aVpnR)
    {
        $aIpResGw = [];
        $aGw = [];
        if (is_array($aVpnR)) {
            foreach ($aVpnR as $gwId => $aRes) {
                $aGwRedis = GatewayRedis::getOne($gwId, 'ipPermit');
                foreach ($aRes as $resId => $val) {
                    if (!isset($aIpResGw[$resId])) {
                        $aIpResGw[$resId] = $aGwRedis['noIpPermit'] ?? 0; //不禁用
                    } else {
                        $aIpResGw[$resId] = $aIpResGw[$resId] == 0 ? $aIpResGw[$resId] : ($aGwRedis['noIpPermit'] ?? 0) ;
                    }
                }
                if (isset($aGwRedis['noIpPermit']) && $aGwRedis['noIpPermit'] == 1 && !in_array($gwId, $aGw)) {
                    $aGw[] = $gwId;
                }
            }
        }
        self::log("getGwVirIpStatus::".var_export([$aIpResGw,$aGw], true));
        return ['IpResGw'=>$aIpResGw, 'noIpPermitGw' => $aGw];
    }

    /**
     * 下发网关配置
     * 新隧道端口
     * @param array $aQuery
     * return true
     * *
     * @return bool|void
     * @throws Exception
     */
    public static function setProxyPort($aQuery)
    {
        if (!empty($aQuery['ProxyPort']) && $aQuery['ProxyPort'] > 0) {
            $status = self::checkPrivPort($aQuery['ProxyPort']);
            if (!$status) {
                self::log("setProxyPort: the port ".$aQuery['ProxyPort']." is in use!");
                return false;
            }
            $configFile = PATH_ETC . "asmproxy.ini";
            if (is_file($configFile)) {
                $aFile = get_ini_info($configFile);
                $aFile['ProxyPort'] = $aQuery['ProxyPort'];
                writeFile($configFile, $aFile);
            } else {
                $aFile['ProxyPort'] = $aQuery['ProxyPort'];
                writeFile($configFile, $aFile);
            }
            cutil_exec_no_wait(PATH_ASM . "etc/init.d/asmproxyd restart");
        }
        return true;
    }
    /**
     * 根据token获取IP资源对应的网关ID
     * @param string $token
     * @return false|true|array
     * **/
    public static function getResGateway($token, int $resId)
    {
        if (strlen($token)!=32 || empty($resId)) {
            return false;
        }
        $aSess = SessionRedis::getOne($token, 'usergw');
        if (empty($aSess['vpnIpRangleAll']) || $aSess['vpnIpRangleAll'] == '[]') {
            return false;
        }
        $aVpnIpRangleAll = json_decode($aSess['vpnIpRangleAll'], true);
        $ipGate = [];
        if (is_array($aVpnIpRangleAll)) {
            foreach ($aVpnIpRangleAll as $gwId => $item) {
                if (isset($item[$resId])) {
                    $ipGate[] = (string)$gwId;
                }
            }
        }
        return $ipGate;
    }
    /**
     * 判断端口是否被占用
     * @param int $port
     * @return false|true
     * **/
    public static function checkPrivPort($port)
    {
        $testFile = PATH_LOG . "netstat_test_".$port.".log";
        cutil_exec_wait('netstat -nap | grep -e ":'.$port.' " | grep LISTEN > '.$testFile);
        $status = file_get_contents($testFile);
        if (!empty($status)) {
            return false;
        }
        return true;
    }

    /**
     * 生成端口文件
     * @param $oldFile
     * @param $newFile
     * @param $newPort
     * @param $newPort
     * @throws Exception
     */
    public static function moveNginxFile($oldFile, $newFile, $oldPort, $newPort)
    {
        // 1.尝试执行 mv 命令，最多尝试 3 次
        $move_num = 0;
        do {
            cutil_exec_wait("mv {$oldFile} {$newFile}");
            sleep(1);
            if (file_exists($newFile)) {
                cutil_exec_wait("sed -i 's/listen\s*\<{$oldPort}\>/listen {$newPort}/g' {$newFile}");
                cutil_exec_wait("sed -i 's/listen\s*\[::\]:\<{$oldPort}\>/listen [::]:{$newPort}/g' {$newFile}");
                return;
            }
            $move_num++;
        } while ($move_num < 3);
        self::log('create ' . $newFile . ' mv faild 3 times' . $oldFile . ' to ' . $newFile);
        // 2.尝试通过标准模板文件生成newFile
        $templateFile = PATH_ETC . 'nginx/web_proxy_port_template.conf.tmp';
        if (file_exists($templateFile)) {
            cutil_exec_wait("cp {$templateFile} {$newFile}");
        }
        if (!file_exists($newFile)) {
            self::log('create ' . $newFile . ' faild by template');
        } else {
            cutil_exec_wait("sed -i 's/listen\s*\<17443\>/listen {$newPort}/g' {$newFile}");
            cutil_exec_wait("sed -i 's/listen\s*\[::\]:\<17443\>/listen [::]:{$newPort}/g' {$newFile}");
            if (is_file($oldFile)) {
                cutil_exec_wait("rm -f {$oldFile}");
            }
        }
    }
    /**
     * 记住资源密钥证书、密码
     * @param $params
     * @return void
     */
    public static function saveRememberPasswordCer($params): void
    {
        RememberPasswordCerModel::insert($params);
    }
}
