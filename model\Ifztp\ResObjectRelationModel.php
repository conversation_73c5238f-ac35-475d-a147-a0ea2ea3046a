<?php

/**
 * Description: 资源与对象关系
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: ResObjectRelationModel.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class ResObjectRelationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResObjectRelation';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'user' => 'ResID',
        'object' => 'UserID,DepartID,RoleID,DeviceID,ObjectType'
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getPowerWhere($cond = []): string
    {
        $where = [];

        if (isset($cond['UserID'])) {
            $where[] = "UserID = ".self::setData($cond['UserID']);
        }

        if (isset($cond['DepartID'])) {
            $where[] = "DepartID IN (".self::setArrayData($cond['DepartID']) . ")";
        }

        if (!empty($cond['AuthorizationID'])) {
            if (!is_array($cond['AuthorizationID'])) {
                $cond['AuthorizationID'] = explode(',', $cond['AuthorizationID']);
            }
            $where[] = "AuthorizationID IN (" . self::setArrayData($cond['AuthorizationID']) . ")";
        }

        if (isset($cond['RoleID'])) {
            $where[] = "RoleID = ".self::setData($cond['RoleID']);
        }

        if (isset($cond['DeviceID'])) {
            $where[] = "DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? implode(" OR ", $where) : "";
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";
        $powerWhere = self::getPowerWhere($cond);

        if (!empty($powerWhere)) {
            $where .= " AND ({$powerWhere})";
        }

        if (isset($cond['ResID'])) {
            $where .= " AND ResID = ".self::setData($cond['ResID']);
        }

        if (isset($cond['InResID'])) {
            $where .= " AND ResID IN (".self::setArrayData($cond['InResID']). ")";
        }
        $where .= " AND EndTime >= " . self::setData(date('Y-m-d H:i:s'));

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
