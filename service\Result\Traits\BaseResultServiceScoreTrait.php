<?php
/**
 * Description: 结果判别/分数相关 只能BaseResultService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: BaseResultServiceScoreTrait.php 153522 2021-08-19 03:22:08Z duanyc $
 */

namespace Services\Result\Traits;

trait BaseResultServiceScoreTrait
{

    /**
     * 判断设备本次检查结果
     * @throws \Exception
     */
    public function judgeDeviceCheckResult()
    {
        if (empty($this->params['NoPassItemID'])) {
            // 检查通过
            $this->checkResult['Res'] = self::CHECK_RESULT_SUCCESS;
            $this->updateDeviceInfo($this->checkResult['Res']);
            $this->repairCheckResult();
            $this->checkResult['Desc'] = $this->clientCheck['PromptWords1'];
        } elseif (count(@array_intersect($this->params['NoPassItemID'], $this->params['KeyItemID'])) > 0) {
            // 检查不通过
            $this->checkResult['Res'] = self::CHECK_RESULT_false;
            $this->updateDeviceInfo($this->checkResult['Res']);
            $this->checkResult['Desc'] = $this->deviceInfo['IsTrustDev'] == '1' ?
                $this->clientCheck['PromptWords2'] : $this->clientCheck['PromptWords3'];
            $PrefixMsg = $this->deviceInfo['IsTrustDev'] == '1' ? L(21104013):L(21104014);
            $this->checkResult['Warning'] = $this->getWarnningMsg($PrefixMsg);
        } else {
            // 存在安全隐患
            $this->checkResult['Exceed_Expectations'] = "NO";//是否超期
            if ($this->params['RepairInterval'] > 0) {
                // 存在修复期限
                $lastFaultTime = strtotime(substr($this->deviceInfo['LastFaultTime'], 0, 10));
                $repairTime = strtotime("+" . $this->params['RepairInterval'] . " day", $lastFaultTime);
                $remainDate = ($repairTime - strtotime(func_time_getNow("Y-m-d")) ) / 86400;

                if (in_array($this->deviceInfo['CheckResult'], array("success", "false"))) {
                    if ($this->deviceInfo['CheckResult'] == 'false' && $remainDate <= 0) {
                        $this->judgeCheckResultFalse();
                    } else {
                        $this->judgeCheckResultFault($this->params['RepairInterval']);
                    }
                } else {
                    if ($this->deviceInfo['LastFaultTime'] === DEFAULT_TIME) {
                        $this->judgeCheckResultFault($this->params['RepairInterval']);
                    } else {
                        if ($remainDate > 0) {
                            $this->judgeCheckResultFault($remainDate);
                        } else {
                            $this->judgeCheckResultFalse();
                        }
                    }
                }
            } else {
                $this->checkResult['Res'] = self::CHECK_RESULT_fault;
                $this->updateDeviceInfo($this->checkResult['Res']);
                $this->checkResult['Desc'] =$this->clientCheck['PromptWords5'];
                $this->checkResult['Warning'] = $this->getWarnningMsg();
            }
        }
        // 计算安全得分
        $this->checkResult['Rate'] = $this->getCheckResultRate();
        $this->checkResult['LastCheckTID'] = $this->params['TimesID'];

        // 更新设备检查结果
        $cparams = ['CheckResult' => $this->checkResult['Res'], 'CheckScore' => $this->checkResult['Rate'],
                    'ActivexCabVersion' => \hlp_common::getSystemEngine(), 'RoleID' => $this->params['roleId']];
        \NacCheckTimesModel::update($this->params['TimesID'], $cparams);
    }

    /**
     * 修复安检结果
     */
    public function repairCheckResult()
    {
        if (in_array($this->deviceInfo['CheckResult'], array("false", "fault"))) {
            // 插入修复记录
            $rparams = ['DeviceID' => $this->deviceInfo["DeviceID"], 'CheckTID' => $this->params['TimesID'],
                        'InsertTime' => 'now()'];
            \NacRepairLogModel::insert($rparams);
        }
    }

    /**
     * 必须安检项不通过
     */
    public function judgeCheckResultFalse()
    {
        $this->checkResult['Res'] = self::CHECK_RESULT_false;
        $this->checkResult['Desc'] =$this->clientCheck['PromptWords4'];
        $this->updateDeviceInfo($this->checkResult['Res'], 0);
        $this->checkResult['Warning'] = $this->getWarnningMsg(L(21104015));
        $this->checkResult['Exceed_Expectations'] = "Yes";
    }

    /**
     * 非必须安检项不通过
     * @param $interval
     */
    public function judgeCheckResultFault($interval)
    {
        $this->checkResult['Res'] = self::CHECK_RESULT_fault;
        $repairMsg = L(21104009, ["interval" => $interval]);
        $this->checkResult['Desc'] = $this->clientCheck['PromptWords5']."，".$repairMsg;
        $this->checkResult['Warning'] = $this->getWarnningMsg(L(21104016, ['day' => $this->params['RepairInterval']]));
        if ($this->params['RepairInterval'] >= 30) {
            $this->checkResult['Desc'] = $this->clientCheck['PromptWords5']."，". L(21104010);
            $this->checkResult['Warning'] = $this->getWarnningMsg(L(21104010));
        }
        $this->updateDeviceInfo($this->checkResult['Res']);
    }

    /**
     * 更新设备检查结果信息
     *
     * @param string $check_result 检查结果
     * @param int $type 1为更新LastFaultTime
     */
    public function updateDeviceInfo($check_result, $type = 1)
    {
        $params = ['LastCheckTID' => $this->params['TimesID'], 'CheckResult' => $check_result,
                   'CheckTime' => 'now()'];
        if ($type == 1) {
            $params['LastFaultTime'] = 'now()';
        }
        \RelationComputerModel::update($this->deviceId, $params);
    }

    /**
     * 计算安全得分
     *
     * @return float|int
     */
    public function getCheckResultRate()
    {
        if ($this->checkResult['Res'] == self::CHECK_RESULT_SUCCESS) {
            $Rate = 100 ;
        } else if ($this->checkResult['Res'] != self::CHECK_RESULT_false) {
            $Rate = @ceil($this->params['PassItemRate'] / $this->params['AllItemRate'] * 100);
        } else {
            $Rate = @ceil($this->params['PassItemRate'] / $this->params['AllItemRate'] * 60);
        }
        return $Rate;
    }
}
