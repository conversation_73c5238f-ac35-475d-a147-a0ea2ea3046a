<?php

/**
 * Description: 安检项TNacCheckItem表
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: NacCheckItemModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class NacCheckItemModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacCheckItem';
    public const PRIMARY_KEY = 'ItemID';
    protected static $columns = [
        'info' => 'Content, IsRepairItem',
        'submit' => 'A.ItemID, A.Type, A.ItemName, A.Content as Xml,  A.Rate, A.<PERSON>, B.Content AS Template, A.OptionBtn,A.IsRepairItem,A.IssuedLevel',
        '*'    => '*',
    ];

    /**
     * 获取安检数据
     *
     * @param $itemid
     * @param string $Column
     *
     * @return array|bool
     */
    public static function getJoinTemplate($itemid, $Column = 'one')
    {
        if (empty($itemid)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns[$Column];
        $sql = "SELECT {$column} FROM TNacCheckItem A LEFT JOIN TCheckTemplate B ON 
            A.ItemName = B.ItemName WHERE A.ItemID = ".self::setData($itemid);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['ItemID'])) {
            $where .= "AND ItemID = ".self::setData($cond['ItemID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
