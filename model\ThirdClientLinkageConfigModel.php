<?php

/**
 * Description: Msep联动配置表
 * User: <EMAIL>
 * Date: 2024/04/08 10:32
 */

class ThirdClientLinkageConfigModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TThirdClientLinkageConfigs';
    public const PRIMARY_KEY = 'id';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['type'])) {
            $where .= "AND type = " . self::setData($cond['type']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
