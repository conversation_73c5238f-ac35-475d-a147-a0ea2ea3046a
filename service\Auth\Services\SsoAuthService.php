<?php
/**
 * Description: Sso认证服务
 * User: <EMAIL>
 * Date: 2022/02/22 10:52
 * Version: $Id$
 */

namespace Services\Auth\Services;

use AccountModel;
use AuthUserModel;
use casClientService;
use Exception;
use lib_otheruser;
use oauthClientService;
use Services\Auth\Interfaces\AuthServiceInterface;
use ssoCommon;

class SsoAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'WebAuth';

    /**
     * sso操作对象
     * @var casClientService|oauthClientService
     */
    private $sso = null;

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        $params['isRecordErr'] = $params['isRecordErr'] ?? 1;
        if (isset($params['isHttps']) && $params['isHttps'] !== '') {
            $params['isHttps'] = $params['isHttps'] === '1';
        } else {
            $params['isHttps'] = isHttps();
        }
        parent::__construct($params);
        $this->sso = lib_otheruser::getInstance('sso', $params);
        $this->fanwei = \lib_otheruser::getInstance("fanwei");
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams(): array
    {
        $this->params['userName'] = addslashes(Base64DeExt($this->params['userName']));
        $this->params['serverUrl'] = ssoCommon::getServerUrl($this->params['isHttps']);
        return $this->params;
    }

    /**
     * 获取验证服务
     *
     * @return array
     */
    public function getAuthServer(): array
    {
        return ['Sso'];
    }

    /**
     * 判断SSO账户是否存在
     * @param $userId
     * @return bool|array
     */
    public function ssoUserIsExist($userId)
    {
        if (empty($userId)) {
            return false;
        }
        $openId = $userId . '|||' . 'sso' . '|||';
        $user = AccountModel::getOneByOpenId($openId);
        if (is_array($user) && (int)$user['ID'] > 0) {
            return $user;
        }
        return false;
    }

    /**
     * 保存账户信息
     * @param $userInfo
     * @return false|int|mixed|null
     * @throws Exception
     */
    public function actionDB($userInfo)
    {
        // 如果未同步到UserName
        if (empty($userInfo['userId'])) {
            T(********);
        }
        $openId = $userInfo['userId'] . '|||' . 'sso' . '|||';
        $user = $this->ssoUserIsExist($userInfo['userId']);
        $param = [
            'TrueNames' => $userInfo['TrueNames'],
            'Tel'       => $userInfo['Tel'],
            'Email'     => $userInfo['Email'],
            'AuthTime'  => date("Y-m-d H:i:s"),
        ];
        if (is_array($user) && $user['ID'] > 0) {
            AuthUserModel::update($user['ID'], $param);
            AccountModel::update($user['AccountID'], ['auth_name' => $userInfo['UserName']]);
        } else {
            $param['UserName'] = $userInfo['UserName'];
            $param['Type'] = $this->userType;
            $param['openid'] = $openId;
            $param['Remark'] = L(********);
            $param['LifeTime'] = 99999;
            $param['RoleID'] = ROLE_ID_DEFAULT;
            $user['ID'] = AuthUserModel::insert($param);
        }
        /* 部门需要特殊处理 */
        $loginRes = ssoCommon::cacheGet($this->params['deviceId'], 'loginRes');
        $loginResJson = json_decode($loginRes, true);
        $loginResJson = $loginResJson['attributes'];
        $maxDep = \DepartModel::getSingle(['column' => 'max']);

        ssoCommon::recordLog($loginResJson);
        $departid = $this->getAsmDepartID($loginResJson, 0);
        if ($departid > $maxDep['MaxID']) {
            cutil_exec_no_wait(PATH_HTML . '/bin/setAllDepartName.php');
        }
        \AuthUserModel::update($user['ID'], ['DepartID' => $departid]);

        return $user['ID'];
    }

    /**
     * ASM部门同步
     * @param $loginResJson
     * @param $index
     * @return int|mixed
     */
    public function getAsmDepartID($loginResJson, $index = 0)
    {
        $depart_list = $this->fanwei->getDepartInfo($loginResJson['id'], $loginResJson['departmentid']);

        ssoCommon::recordLog(var_export($depart_list, true));
        // 判断全路径是否存在
        $departInfo = \DepartModel::getOneByOtherSysID("fanwei_" . $depart_list[0]['id']);
        if (!empty($departInfo)) {
            return $departInfo['DepartID'];
        }
        // 判断上层组织架构是否存在
        $res = \DepartModel::getSingle(['DepartName' => "OA", 'UpID' => 0]);
        ssoCommon::recordLog(var_export($res, true));
        if (empty($res)) {
            $index = \DepartModel::insert(['DepartName' => "OA", 'UpID' => 0, 'RoleID' => 1, 'OrderIndex' => 0]);
            $this->departService->updateOperatorManagerScope(0, $index);
        } else {
            $index = $res['DepartID'];
        }


        $departLen = count($depart_list);
        $idx = 1;
        while ($idx <= $departLen) {
            $departName = $depart_list[count($depart_list) - $idx]['name'];
            $departTmp = \DepartModel::getSingle(['DepartName' => $departName, 'UpID' => $index]);
            ssoCommon::recordLog($departTmp);
            if (!$departTmp) {
                // 插入部门
                $upDepID = $index;
                $index = \DepartModel::insert(['DepartName' => $departName, 'UpID' => $upDepID, 'RoleID' => 1, 'OrderIndex' => 0, "OtherSysID" => "fanwei_" . $depart_list[count($depart_list) - $idx]['id']]);
                //更新操作员部门管理范围
                $this->departService->updateOperatorManagerScope($upDepID, $index);
            }
            $idx++;
        }
        return $index;
    }

    /**
     * 登录
     * 未登录则跳转SSO
     * SSO回调，缓存token
     * @return bool
     * @throws Exception
     */
    public function login(): bool
    {
        try {
            $ssoConfig = ssoCommon::getSSOConfig();
            if ($ssoConfig['basicConfig']['State'] !== 1) {
                T(21147002);
            }
            $this->checkSsoStatus();

            // 未登录自动跳转
            if (!$this->sso->login($this->params)) {
                return false;
            }

            $userInfo = $this->sso->getUserInfo();
            if ($userInfo !== false) {
                $this->actionDB($userInfo);
            }
            $url = 'Location:/index.html?skipStatus=1';
            if ($this->params['isClient'] === 1) {
                $url .= '&isOpenClient=1';
            }
            $url .= '&fromSSO=1&resId=' . $this->params['resId'] . '&resurl=' . urlencode($this->params['resurl']);
            header($url);
            return true;
        } catch (Exception $e) {
            // 记录认证失败日志
            $this->setDeviceInfo();
            $this->recordAuthErrLog($this->userType, $e->getMessage());
            //            throw $e;
            ssoCommon::cacheDelete($this->params['deviceId']);
            if (in_array((int)$e->getCode(), [21147006, 21147008, 21147009])) {
                $this->clearSsoStatus();
            }
            self::showSsoMessage(false, $e->getMessage());
            return false;
        }
    }

    /**
     * 登出
     * @return bool|string
     * @throws Exception
     */
    public function logout()
    {
        return $this->sso->logout();
    }

    /***
     * 检测是否已经单点登录
     * @return array|false|void
     * @throws Exception
     */
    public function checkLoginState()
    {
        $this->checkSsoStatus();
        return $this->sso->checkLoginState();
    }

    /**
     * 检查是否与SSO连接是否正常
     * @throws Exception
     */
    private function checkSsoStatus(): void
    {
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        if (isset($basicConfig['ServerPort'])) { // 兼容项目不配置端口的情况
            return;
        }
        if (isset($basicConfig['ServerAddress'], $basicConfig['ServerPort']) &&
            !ssoCommon::ping($basicConfig['ServerAddress'], $basicConfig['ServerPort'], 10)) {
            T(21147009);
        }
    }

    /**
     * 清除连接是否正常缓存
     * @throws Exception
     */
    private function clearSsoStatus(): void
    {
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        if (isset($basicConfig['ServerAddress'], $basicConfig['ServerPort'])) {
            $key = "{$basicConfig['ServerAddress']}_{$basicConfig['ServerPort']}";
            ssoCommon::cacheDelete('Server_' . $key);
        }
    }

    /**
     * 移动端提示页面
     *
     * @param $result
     * @param $message
     */
    private static function showSsoMessage($result, $message): void
    {
        $iconType = $result ? "true" : "false";
        $str = file_get_contents(PATH_HTML . "/a/err.html");
        $str = str_replace(
            array('"<--icontype-->"', '<--spaninfo-->', '<--local_lguage_set-->'),
            array($iconType, $message, $GLOBALS['CONFIG']['LANG_MAP'][LANG]),
            $str
        );
        exit($str);
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'userName', 'deviceId', 'isHttps', 'serverUrl'];
    }

}
