<?php
/**
 * Description: OTP相关
 * User: <EMAIL>
 * Date: 2022/05/09 15:48
 * Version: $Id:
 */

use Services\Auth\Services\OTPAuthService;

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class OTPController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['qrcode' => true, 'iosQrcode' => true, 'androidQrcode' => true];

    /**
     * OTP服务
     * @var OTPAuthService
     */
    private $OTPService;

    /**
     * OTP参数
     * @var array
     */
    public $params = [];

    public function __construct()
    {
        parent::__construct();
        // 手动处理phpcas需要参数
        $_GET = [];
        $_POST = [];

        $this->params['servicePrefix'] = 'OTP';
        $this->OTPService = AuthServiceProvider::initAuthService($this->params);
    }

    /**
     * 校验帐号是否激活了令牌
     * @return array
     * @throws Exception
     */
    public function checkActivationKey()
    {
        $userid = request('userid', 'request', 0, 'int');
        hlp_check::checkEmpty($userid);
        $return = [];
        $return['state'] = $this->OTPService->checkActivationKey($userid);
        return $return;
    }

    /**
     * 生成激活令牌的二维码
     * @return array
     * @throws Exception
     */
    public function qrcode()
    {
        $return = [];
        $userid = request('userid', 'request', 0, 'int');
        hlp_check::checkEmpty($userid);
        $this->OTPService->resetActivationSecretKey($userid);  //重新设置一个令牌激活密钥
        $this->OTPService->getOTPQrcodeImg($userid);   //生成新的令牌激活密钥的二维码
        return $return;
    }

    /**
     * 获取ios客户端下载二维码
     * @return array
     * @throws Exception
     */
    public function iosQrcode()
    {
        $return = [];
        QrcodeServiceProvider::getIosQrcodeImg();   //生成新的令牌激活密钥的二维码
        return $return;
    }

    /**
     * 获取安卓客户端下载二维码
     * @return array
     * @throws Exception
     */
    public function androidQrcode()
    {
        $return = [];
        QrcodeServiceProvider::getAndroidQrcodeImg();   //生成新的令牌激活密钥的二维码
        return $return;
    }

    /**
     * 校验账户令牌的口令是否正确
     * @return array
     * @throws Exception
     */
    public function verifyCode()
    {
        $return = [];
        $userid = request('userid', 'request', 0, 'int');
        $code = request('code', 'request');

        hlp_check::checkEmpty($userid);
        hlp_check::checkEmpty($code);
        $secretKey = $this->OTPService->getCurrentSecretKey($userid);
        // 校验
        $params = ['servicePrefix' => 'OTP'];
        $params['code'] = $code;
        $params['secret'] = $secretKey;
        $return['state'] = AuthServiceProvider::callbackRpcAuthServer(['servicePrefix' => 'OTP', 'code' => $code]);
        return $return;
    }

    /**
     * 获取指定帐号的当前时间戳的6位数字口令
     * @return array
     * @throws Exception
     */
    public function getCode()
    {
        $return = [];
        $secretKey = request('secretKey', 'request');
        hlp_check::checkEmpty($secretKey);
        $secretKeys = explode(',', $secretKey);
        $codes = [];
        foreach ($secretKeys as $key) {
            $codes[] = $this->OTPService->getCode($key);
        }
        $return['code'] = implode(',', $codes);
        return $return;
    }

    /**
     * 获取令牌密钥对应的账户信息
     * @return array
     * @throws Exception
     */
    public function getSecretKeyUserInfo()
    {
        $return = [];
        $secretKey = request('secretKey', 'request');
        hlp_check::checkEmpty($secretKey);
        $userInfo = $this->OTPService->getSecretKeyUserInfo($secretKey);
        $return['userid'] = $userInfo['ID'];
        $return['username'] = $userInfo['UserName'];
        return $return;
    }

    /**
     * 账户激活令牌密钥
     * @return array
     * @throws Exception
     */
    public function activateSecretKey()
    {
        $return = [];
        $secretKey = request('secretKey', 'request');
        $userid = request('userid', 'request', '0', 'int');
        hlp_check::checkEmpty($userid);
        hlp_check::checkEmpty($secretKey);
        $this->OTPService->activateSecretKey($userid, $secretKey);
        $this->errmsg = L(21141005);
        return $return;
    }

    /**
     * 取消激活令牌
     * @return array
     * @throws Exception
     */
    public function cancelActivation()
    {
        $return = [];
        $secretKey = request('secretKey', 'request');
        hlp_check::checkEmpty($secretKey);
        $return['state'] = $this->OTPService->cancelActivation($secretKey);
        return $return;
    }

    /**
     * 校验手机上的密钥是否在服务器上
     * @return array
     * @throws Exception
     */
    public function checkSecret()
    {
        $return = [];
        $secretKey = request('secretKeyArr', 'request', '', 'string');
        hlp_check::checkEmpty($secretKey);
        $return['result'] = $this->OTPService->checkSecret($secretKey);
        return $return;
    }
}
