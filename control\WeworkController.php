<?php

/**
 * Description: 获取企业微信用户信息
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: WeworkController.php 167234 2022-01-14 03:46:16Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class WeworkController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['user' => true, 'jump' => true];

    /**
     * 获取企业微信用户信息，对应老交易 get_wework_user
     *
     * @return array
     * @throws Exception
     */
    public function user()
    {
        $deviceType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
        if ($deviceType === 'out') {
            return $this->userOut();
        }
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $code = request('code', 'request');
        // 准入检查是否扫码成功
        if ($action === 'check') {
            return $this->userCheck($deviceId);
        }
        $action!="verify" && hlp_check::checkEmpty($deviceId);
        hlp_check::checkEmpty($code);
        if ($action=='verify') {
            $time=request('time', 'request');
            return $this->userVerify($code, $time);
        }
        $this->userCallback($deviceId, $code);
        return [];
    }

    /**
     * 外置服务器
     *
     * @return array
     * @throws Exception
     */
    private function userOut()
    {
        $action = request('action', 'request');
        $deviceId = request('deviceid', 'request', 0, 'int');
        $code = request('code', 'request');
        $time = request('time', 'request');
        hlp_check::checkEmpty($deviceId);
        hlp_check::checkEmpty($time);
        // 准入检查是否扫码成功
        if ($action === 'check') {
            $data = cache_get_info('wework_out', "{$deviceId}_{$time}");
            if (empty($data['token'])) {
                return ['state' => false, 'type' => 'Prescaned'];
            }
            cache_del_info('wework_out', "{$deviceId}_{$time}");
            return ['state' => true, 'type' => 'Scaned', 'token' => $data['token'], 'username' => ''];
        }
        hlp_check::checkEmpty($code);
        $data = ['token' => $code];
        $res = cache_set_info('wework_out', "{$deviceId}_{$time}", $data, 300);
        return ['res' => $res];
    }
    /**
     * 用户校验
     * @param $code
     * @param $time
     * @return array
     * @throws Exception
     */
    public function userVerify($code, $time): array
    {
        $config = cache_get_info("asm_database_", "AuthDomainTest_WeWork_" . $time);
        hlp_check::checkEmpty($config['appid']);
        hlp_check::checkEmpty($config['secret']);
        $tmpConfig=['corpId' => $config['appid'], 'secret' => $config['secret'],'from'=>'backend'];
        $data = ['servicePrefix' => 'WeWork', 'code' => $code, 'isAgent' => false,'tmpConfig'=>$tmpConfig];
        $userinfo = AuthServiceProvider::callbackRpcAuthServer($data);
        if (is_array($userinfo) && isset($userinfo['ID'])) {
            cache_set_info("asm_database_", "AuthDomainTest_WeWork_" . $time, ['user' => $userinfo['ID']], 300);
        }
        return [];
    }
    /**
     * 检查
     *
     * @param $deviceId
     * @return array
     */
    private function userCheck($deviceId)
    {
        $return = ['state' => false];
        try {
            if (empty($deviceId)) {
                T(21136008);
            }
            $cond = ['LikeToken' => '|||', 'DeviceID' => $deviceId, 'Type' => 'WeWork'];
            $userList = QrcodeServiceProvider::getQrcodeUserList($cond);
            if (empty($userList)) {
                T(21136007);
            }
            foreach ($userList as $user) {
                if (!empty($user['UserID'])) {
                    $return['state'] = true;
                    $return['type'] = "Scaned";
                    $return['token'] = $user['Token'];
                    $return['username'] = AuthServiceProvider::getUserNameById($user['UserID']);
                    break;
                }
            }
            if ($return['state'] == false) {
                QrcodeServiceProvider::deleteQrcodeUser($deviceId);
                T(21136009);
            }
        } catch (Exception $e) {
            $typeConfig = [21136008 => 'EmptyDeviceID', 21136007 => 'NoSuchDeviceToken',
                           21136009 => 'Prescaned'];
            $code = $e->getCode();
            $return['type'] = isset($typeConfig[$code]) ? $typeConfig[$code] : '';
            $return['message'] = $e->getMessage();
        }
        return $return;
    }

    /**
     * 回调
     *
     * @param $deviceId
     * @param $code
     *
     * @throws Exception
     */
    private function userCallback($deviceId, $code)
    {
        $GLOBALS['HeaderContentType'] = 'text/html';
        $code = trim($code);
        $authUser = AuthServiceProvider::callbackRpcAuthServer(['servicePrefix' => 'WeWork', 'code' => $code]);
        $params = ['DeviceID' => $deviceId, 'Type' => 'WeWork'];

        if (!empty($authUser)) {
            /* 记录二维码扫描 */
            $token = $authUser['openid'];
            $params['Token'] = $token;
            $params['UserID'] = $authUser['ID'];
            QrcodeServiceProvider::addQrcodeUser($params);
        } else {
            $params['Token'] = '|||';
            QrcodeServiceProvider::addQrcodeUser($params);
            T(21136007);
        }
    }
}
