<?php

/**
 * Description: 证书缓存字段
 * User: <EMAIL>
 * Date: 2024/04/22 23:32
 * Version: $Id: CertRedis.php 175039 2024-04-22 07:33:41Z xielj $
 */

class CertRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Cert';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'KeyContent,CrtContent,LifeTime';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = 'KeyContent,CrtContent,LifeTime';


    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['KeyContent', 'CrtContent'],
    ];

    /**
     * 单条
     *
     * @param $cid *证书id
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($cid, string $column = 'one')
    {
        return self::get($column, $cid);
    }


    /**
     * 单条
     *
     * @param  $cid
     * @param array $data
     *
     * @return bool
     */
    public static function setOne($cid, array $data): bool
    {
        return self::set($data, $cid);
    }

    /**
     * 修改,此处覆盖父方法，简化实现写方法，不然有缓存证书没法推送到独立网关ASG上去
     * 证书为比较重要的内容，不管栏位是否修改变化，都覆盖一遍redis
     * @param array $key_values
     * @param array $keys
     *
     * @return boolean
     */
    protected static function set($key_values = [], ...$keys)
    {
        if (empty($key_values)) {
            return false;
        }
        static::$changeDatas = [];
        static::$oldDatas = [];
        $expire = null;
        if (isset($key_values['LifeTime'])) {
            $expire = $key_values['LifeTime'];
        }
        $columns = explode(',', static::$localColumns);
        $vals = [];
        foreach ($columns as $column) {
            if (isset($key_values[$column])) {
                $vals[$column] = isset(static::$jsonColumns[$column]) ?
                    json_encode($key_values[$column]) : (string)$key_values[$column];
            }
        }
        if (isset($key_values['UpdateLifeTime'])) {
            $vals['LifeTime'] = $key_values['UpdateLifeTime'];
        }
        $key = static::getKey($keys);
        if (empty($vals)) {
            return false;
        }
        if (!empty(static::$remoteColumns)) {
            static::push($key, $vals);
        }
        cutil_php_log(['set', $key, $vals], "model_" . static::TABLE_NAME);
        $redoCount = $key_values['redoCount'] ?? 3;
        for ($i = 0; $i < $redoCount; $i++) {
            $return = lib_redis::hMSetEx(static::PREFIX . $key, $vals, $expire);
            if ($return) {
                break;
            }
            cutil_php_log(['setErr', $key, $return], "model_" . static::TABLE_NAME);
        }
        return $return;
    }

    /**
     * 删除
     *
     * @param $cid
     * @return bool
     */
    public static function deleteOne($cid): bool
    {
        return self::del($cid);
    }

}
