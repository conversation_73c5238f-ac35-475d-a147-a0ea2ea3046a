<?php
/**
 * Description: 针对和MSEP对接的签名SDK
 * User: <EMAIL>
 * Date: 2024/04/07 17:02
 */
include "cls_msep.php";

$sdk = new cls_msep();
$ts = time();
$deviceIds = "1,2,3";

//正常验签
$sign = $sdk->genSign($ts, $deviceIds);
if ($sdk->verifySign($ts, $deviceIds, $sign)) {
    echo "Test1 Pass" . PHP_EOL;
}

//时间戳过期
$ts = time() - 500; //模拟过期时间戳
$deviceIds = "3,2,1";
$sign = $sdk->genSign($ts, $deviceIds);
if ($sdk->verifySign($ts, $deviceIds, $sign) === false) {
    echo "Test2 Pass" . PHP_EOL;
}
