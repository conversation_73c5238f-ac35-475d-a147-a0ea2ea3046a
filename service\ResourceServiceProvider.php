<?php
/**
 * Description: 资源
 * User: <EMAIL>
 * Date: 2021/08/20 15:53
 * Version: $Id: ResourceServiceProvider.php 175145 2022-05-06 07:53:42Z huyf $
 */

use Services\Common\Services\DESService;
use Services\Resource\Services;

class ResourceServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'resource';

    /**
     * 资源类型
     */
    public const RES_TYPE_COMMON = '0'; // 普通资源 包含网站代理、SDP等
    public const RES_TYPE_IP = '1'; // IP资源
    public const RES_TYPE_GROUP = '2';  // 资源组
    public const RES_TYPE_REMOTE_APP = '3';  // 远程应用资源

    /**
     * 初始化资源服务
     * @param $Type
     * @param $params
     * @return null|Object|Services\ResourceAgentService|Services\ResourcePowerService|Services\ResourceInfoService
     */
    public static function initResourceService($Type, $params = [])
    {
        try {
            $serviceClass = '\\Services\\Resource\\Services\\' . 'Resource' . $Type . 'Service';
            return (new ReflectionClass($serviceClass))->newInstance($params);
        } catch (Exception $e) {
            self::log("initResourceService error:" . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取配置
     * @return mixed
     * @throws Exception
     */
    public static function getConfig()
    {
        $cond = ['Type' => 0, 'ItemName' => 'SPolicy_CriticalResourceProtect'];
        $resourceConfig = TacticsItemModel::getSingle($cond);
        $config = new xml_critical_resource_protect($resourceConfig['Content']);
        return $config->parseXml();
    }

    /**
     * 获取表单代填数据
     * @param $host
     * @param $url
     * @param $userInfo
     * @return mixed
     * @throws Exception
     */
    public static function getFormFillContent($host, $url, $userInfo)
    {
        $config = AppConfigRedis::getOne('appcfg');
        if ($config['appcfg']) {
            $config = json_decode($config['appcfg'], true);
        }
        self::checkFullFillValue($config, $host, $url);
        $ztpCenterInfo = GatewayServiceProvider::getControlHostInfo();
        $params = ['UserId' => $userInfo['Uuid'], 'ResId' => $userInfo['ResID']];
        $yar_res = lib_yar::clients('ztpCenter', 'getResUserPassword', $params, $ztpCenterInfo['RemoteHost']);
        $resAccountInfo = $yar_res['data'];
        $res = json_decode($config[$host]['OtherField'], true);
        $res['PwdDataValue'] = $resAccountInfo['Password'] ?? '';
        $res['AccountDataValue'] = $resAccountInfo['UserName'] ?? '';
        $res['APPType'] = $config[$host]['APPType'];
        $res['APPSubType'] = $config[$host]['APPSubType'];
        $res['autoAccountInputArr'] = array('account', 'name', 'mobile', 'email');
        $res['autoPwdInputArr'] = array('password', 'pwd', 'passwd');
        $res['autoSubmitInputArr'] = array('submit', 'login');
        return $res;
    }


    /**
     * 验证表单代填值
     * @param $config
     * @param $host
     * @param $url
     * @throws Exception
     */
    public static function checkFullFillValue($config, $host, $url)
    {
        if (!isset($config[$host])) {
            T(********);
        }
        $config = $config[$host];
        if ($config['APPType'] != ACCESS_TYPE_FORM || !isset($config['OtherField'])) {
            T(********);
        }
        $UrlData = parse_url($url);
        $loginUrlData = parse_url($config['LoginURL']);
        if ($UrlData['path'] != $loginUrlData['path']) {
            T(********);
        }
    }

    /**
     * 根据授权返回 不符合的资源 类型ID
     * @return array|string[]
     * @throws Exception
     */
    public static function notAuthAccessTypeID(): array
    {
        $rdp = getmoduleregist(12);
        $ssh = getmoduleregist(13);
        if (!$rdp || !$ssh) {
            $notInArr = [];
            if (!$rdp) {
                $notInArr = [ACCESS_TYPE_RDP, ACCESS_TYPE_VNC];
            }
            if (!$ssh) {
                $notInArr = array_merge($notInArr, [ACCESS_TYPE_SSH, ACCESS_TYPE_TELENT]);
            }
            $notInArr[] = ACCESS_TYPE_MICRO_APP;
            return $notInArr;
        }
        return [ACCESS_TYPE_MICRO_APP];
    }

    /**
     * 获取当前认证用户名
     * @param $deviceId
     * @return mixed
     * @throws Exception
     */
    public static function getOnlineData($deviceId)
    {
        if (empty($deviceId)) {
            return false;
        }

        return NacOnLineDeviceModel::getOne($deviceId, 'name');
    }

    /**
     * 获取认证地址
     *
     * @param $deviceId
     * @param $resourceAddr
     * @param $AuthType
     *
     * @return string
     * @throws Exception
     */
    public static function getAuthUrl($deviceId, $resourceAddr, $AuthType)
    {
        $code = $deviceId . "|" . $AuthType . '|0';
        $qrCode = DESService::desEcbEncrypt($code);
        $qrCode = 'W' . $qrCode;
        $imgInfo = URL_BASE . "/mobile/ui/wel.html?isOpenUrl=0&qrCodeType=senior&qrCode=" . $qrCode . '&resourceAddr=' . $resourceAddr;
        $imgInfo = urlencode($imgInfo);
        return $imgInfo;
    }

    //--------------------------------------零信任资源开始-------------------------------------------

    /**
     * 获取用户资源权限
     *
     * @param $UserID
     * @param $DeviceID
     * @param $DepartIDs
     * @param $RoleID
     * @param $ResID
     *
     * @return mixed
     */
    public static function getUserResourcePower($UserID, $DeviceID, $DepartIDs, $RoleID, $ResID)
    {
        $cond = ['column' => 'user'];
        $cond['UserID'] = (int)$UserID;
        $cond['DeviceID'] = (int)$DeviceID;
        $cond['RoleID'] = (int)$RoleID;
        $cond['DepartID'] = array_map('intval', explode(',', $DepartIDs));
        $cond['AuthorizationID'] = AuthUserModel::getAuthorizationGroup($UserID, $DeviceID);
        //此处增加一个判断$ResID是否为资源组，如果是资源组则修则将该资源ID对应的资源组也放进来查询只要有资源组的权限也放过
        $resCond = ['GtGroupResID' => 0, 'InResID' => is_array($ResID) ? $ResID : [$ResID], 'column' => 'index'];
        $resGroupList = ResourceModel::getList($resCond);
        if (!empty($resGroupList)) {
            $resGroupIds = array_column($resGroupList, "GroupResID");
            if (is_array($ResID)) {
                $ResID = array_merge($ResID, $resGroupIds);
            } else {
                $ResIDTmp = [$ResID];
                $ResID = array_merge($ResIDTmp, $resGroupIds);
            }
        }
        if (is_array($ResID)) {
            $cond['InResID'] = $ResID;
        } else {
            $cond['ResID'] = (int)$ResID;
        }
        $list = ResObjectRelationModel::getList($cond);
        if (empty($list)) {
            return [];
        }
        return $list[0];
    }

    /**
     * 获取资源信息
     *
     * @param $ResID
     * @param $DetailColumn
     * @return array|bool|mixed
     */
    public static function getResource($ResID, $DetailColumn = false)
    {
        if (empty($ResID)) {
            return false;
        }
        $result = ResourceModel::getOne($ResID, 'one');
        if (empty($result['IsInternet'])) {
            return false;
        }

        unset($result['IsInternet']);
        if (!empty($DetailColumn) && !empty($result)) {
            switch ($result['ResType']) {
                case self::RES_TYPE_COMMON:
                    $resource = ResConfigListModel::getOne($ResID, $DetailColumn);
                    $result = array_merge($result, $resource);
                    break;
                case self::RES_TYPE_REMOTE_APP:
                    $resource = ResRemoteModel::getRelationOne($ResID, $DetailColumn);
                    $result = array_merge($result, $resource);
                    break;
                case self::RES_TYPE_IP:
                    $resource = ResIPListModel::getOne($ResID, $DetailColumn);
                    $result = array_merge($result, $resource);
                    break;
            }
        }
        return $result;
    }

    /**
     * 检查资源
     *
     * @param $UserID
     * @param $Token
     * @param $ResID
     *
     * @return mixed
     * @throws Exception
     */
    public static function checkResource($UserID, $Token, $ResID)
    {
        $Resource = self::getResource($ResID, 'one');
        if (empty($Resource)) {
            T(21148002);
        }
        //检查网关是否启用
        if ($Resource['AccessTypeID'] !== ACCESS_TYPE_CUSTOM_APP) {
            self::checkGateWayEnable($Resource['GateWayID'], $Resource['GateWayType']);
        }
        //授权检查
        hlp_match::checkAccessType($Resource['AccessTypeID']);
        // 检查表单代填
        self::checkFullForm($UserID, $Resource);
        // 检查资源强化认证
        LoginServiceProvider::checkAddAuth($UserID, $Token, $Resource);
        return $Resource;
    }

    /**
     * 获取用户资源列表
     *
     * @param $Token
     * @param $datas
     *
     * @param string $queryStr
     * @return mixed
     * @throws Exception
     */
    public static function getUserResourceList($Token, &$datas, $queryStr = '')
    {
        $Session = SessionRedis::getOne($Token, 'policy');
        if (empty($Session)) {
            return ['returnLists' => [], 'ipResLists' => []];
        }
        $DepartIDs = $Session['DepartIDs'];
        $RoleID = $Session['RoleID'];
        $DeviceID = $Session['DeviceID'];
        $UserID = $Session['Uuid'];
        $authorizationIDs = AuthUserModel::getAuthorizationGroup($UserID, $DeviceID);

        list($resIds, $ipResIds, $datas) = self::getUserResIds($UserID, $DeviceID, $DepartIDs, $RoleID, $authorizationIDs, '0');
        $list = $listDefault = [];
        if (!empty($resIds)) {
            $cond = ['column' => 'list', 'InResID' => $resIds, 'notAuthAccessTypeID' => self::notAuthAccessTypeID()];
            $data = ResConfigListModel::getList($cond);
            // 需要判断远程应用授权，授权后添加远程应用的数据
            $isAuthRemote = getmoduleregist(28);
            if ($isAuthRemote) {
                $cond = ['column' => 'list', 'InResID' => $resIds];
                $remote_data = ResRemoteModel::getList($cond);
                if (is_array($remote_data)) {
                    $data = array_merge($data, $remote_data);
                }
            }
            if (!empty($data)) {
                $groupNames = ResGroupModel::getAllName();
                foreach ($data as $key => $val) {
                    $ResID = $val['ResID'];
                    if ($datas[$ResID]['IsInternet'] !== '1') {
                        continue;
                    }
                    //按名称模糊搜索
                    if (!empty($queryStr) && !self::getIsQueryString($queryStr, $datas[$ResID]['ResName']) && !self::getIsQueryString($queryStr, $val['Remark'])) {
                        continue;
                    }
                    $result = UserResourceRedis::getOne($Session['Token'], $ResID);
                    $result = self::formatReasonResult($result);
                    $val['Result'] = $result['Result'] ?? '1';
                    $val['IsNat'] = $datas[$ResID]['IsNat'];
                    $val['Reason'] = $result['Reason'] ?? '';
                    $groupResId = $datas[$ResID]['GroupResID'];
                    $val['ResName'] = $datas[$ResID]['ResName'];
                    $val['AppIcon'] = !empty($val['Icon']) ? "/backend/{$val['Icon']}" : '';
                    unset($val['Icon']);
                    $val['ResType'] = $datas[$ResID]['ResType'];
                    $val['ActionType'] = $datas[$ResID]['ActionType'];
                    $val['IsCopy'] = $datas[$ResID]['IsCopy'];
                    $val['Expired'] = self::getDiffTime(date("Y-m-d H:i:s"), $datas[$ResID]['EndTime'] ?? '');
                    //如果是默认资源组，需要排序放前面
                    $tmpGroupName = $groupNames[$groupResId] ?? '';
                    if ($groupResId == 1 || $tmpGroupName == '默认资源组') {
                        $listDefault[$groupResId]['GroupName'] = $tmpGroupName;
                        $listDefault[$groupResId]['GroupResId'] = $groupResId;
                        $listDefault[$groupResId]['Data'][] = $val;
                    } else {
                        $list[$groupResId]['GroupName'] = $tmpGroupName;
                        $list[$groupResId]['GroupResId'] = $groupResId;
                        $list[$groupResId]['Data'][] = $val;
                    }
                }
                if (!empty($listDefault)) {
                    foreach ($listDefault as $gVal) {
                        $list[] = $gVal;
                    }
                }
                $list = array_values($list);
            }
        }
        $ipResLists = [];
        $auth = getmoduleregist(14);

        if (!empty($ipResIds) && $auth) {
            // 计算虚拟IP禁用
            $aIpResGw = GatewayServiceProvider::getGwVirIpStatus($Session, 1);
            $cond = ['column' => 'list', 'InResID' => $ipResIds];
            $ipList = ResIPListModel::getList($cond);
            foreach ($ipList as $key => $val) {
                $ResID = $val['ResID'];
                if ($datas[$ResID]['IsInternet'] !== '1') {
                    continue;
                }
                //按名称模糊搜索
                if (!empty($queryStr) && !self::getIsQueryString($queryStr, $datas[$ResID]['ResName']) && !self::getIsQueryString($queryStr, $val['Remark']) && !self::getIsQueryString($queryStr, $val['IP'])) {
                    continue;
                }
                $ipList[$key]['AppIcon'] = !empty($val['Icon']) ? "/backend/{$val['Icon']}" : '';
                unset($ipList[$key]['Icon']);
                $ipList[$key]['ResName'] = $datas[$ResID]['ResName'];
                $result = UserResourceRedis::getOne($Session['Token'], $ResID);
                $result = self::formatReasonResult($result);
                $ipList[$key]['Result'] = $result['Result'] ?? '1';
                $ipList[$key]['Reason'] = $result['Reason'] ?? '';
                $ipList[$key]['Expired'] = self::getDiffTime(date("Y-m-d H:i:s"), $datas[$ResID]['EndTime'] ?? '');
                if (
                    (
                        (isset($result['VirIpResult']) && $result['VirIpResult'] == 0) // 虚拟IP直接计算
                        || (isset($aIpResGw['IpResGw'][$ResID]) && $aIpResGw['IpResGw'][$ResID] == 1 && empty($Session['VirIp'])) // 通过网关计算
                    )
                    && $result['Result'] == 1
                ) {
                    $ipList[$key]['Result'] = "0";
                    $ipList[$key]['Reason'] = $result['VirIpReason'] ?? '';
                    $result['ErrCode'] = '21148059';
                }
                if (isset($result['ErrCode']) && $ipList[$key]['Result'] == 0) {
                    $ipList[$key]['ErrCode'] = $result['ErrCode'];
                }
                $ipResLists[] = $ipList[$key];
            }
        }
        return ['returnLists' => $list, 'ipResLists' => $ipResLists];
    }

    /**
     *
     * 此处针对一些特殊策略检查，错误编码做个兼容处理
     *
     * @param $result
     * @return mixed
     */
    public static function formatReasonResult($result)
    {
        if (!empty($result['ErrCode']) && $result['ErrCode'] == '21148004') {
            $result['Result'] = 1;
        } else {
            $isAddAuthReason = mb_stripos($result['Reason'] ?? '', L(21148004));
            if ($isAddAuthReason !== false) {
                $result['Result'] = 1;
            }
        }
        return $result;
    }

    /**
     * 设置用户资源权限
     *
     * @param $Token
     * @param string $from
     * @return mixed
     * @throws Exception
     */
    public static function setUserPower($Token, $from = '')
    {
        $powerService = self::initResourceService('Power');
        //需要实时触发当前条目是否需要回收匹配数据
        $res = $powerService->setUserPower($Token, $from);
        // 策略变更与权限变更的时候需要触发动态策略回收
        if (isset($res['ResPowerIds']) || $from == "UpdatePolicyPower") {
            //处理完成后增加清理过期动态匹配策略的数据,此处性能影响不大应该
            $service = PolicyServiceProvider::getPolicyService('DynamicTactic', []);
            $service->changePowerDeviceRecovery([$Token], true);
        }
        return $res;
    }

    /**
     * 批量检查策略
     *
     * @param $Session
     * @param $ResIds
     *
     * @return array
     */
    public static function patchCheckPolicy($Session, $ResIds): array
    {
        $PolicyRelations = ResPolicyRelationModel::getList(['column' => 'resPolicy', 'InResID' => $ResIds]);
        $PolicyIDs = [];
        $PolicyResults = [];
        //优化处理下多个资源关联同一个策略，多次检查的问题
        $isHadCheckPolicy = [];
        if (!empty($PolicyRelations)) {
            foreach ($PolicyRelations as $row) {
                $PolicyIDs[$row['ResID']] = $row['PolicyID'];
                if (isset($isHadCheckPolicy[$row['PolicyID']])) {
                    $PolicyResults[$row['ResID']] = $isHadCheckPolicy[$row['PolicyID']];
                    continue;
                }
                try {
                    PolicyServiceProvider::checkPolicy($Session, $row['PolicyID']);
                    $PolicyResults[$row['ResID']] = true;
                    $isHadCheckPolicy[$row['PolicyID']] = true;
                } catch (Exception $e) {
                    self::log("{$Session['Uuid']}: {$row['PolicyID']}:" . $e->getMessage());
                    $PolicyResults[$row['ResID']] = false;
                    $isHadCheckPolicy[$row['PolicyID']] = false;
                }
            }
        }
        return [$PolicyIDs, $PolicyResults];
    }

    /**
     * 按普通资源和IP资源获取资源ID列表
     *
     * @param $UserID
     * @param $DeviceID
     * @param $DepartIDs
     * @param $RoleID
     * @param $authorizationIds
     * @param string $IsInternet 1表示只包含上架的，0表示全部的
     * @return array|array[]
     */
    public static function getUserResIds($UserID, $DeviceID, $DepartIDs, $RoleID, $authorizationIds, $IsInternet = '1')
    {
        $ztpInfo = UserInfoRedis::getOne($UserID, 'ZtpUser');
        if (empty($ztpInfo['ZtpUser'])) {
            return [[], [], [], [], []];
        }
        $cond = [];
        $cond['UserID'] = (int)$UserID;
        $cond['DeviceID'] = (int)$DeviceID;
        $cond['RoleID'] = (int)$RoleID;
        $cond['DepartID'] = array_map('intval', explode(',', $DepartIDs));
        $cond['AuthorizationID'] = $authorizationIds;
        if (!empty($IsInternet)) {
            $cond['IsInternet'] = $IsInternet;
        }
        $list = ResourceModel::getUserResourceList($cond);
        //兼容下资源组，因为资源组默认未上线
        unset($cond['IsInternet']);
        $cond['ResType'] = 2;
        $listResGroup = ResourceModel::getUserResourceList($cond);
        if (!empty($listResGroup)) {
            $list = array_merge($list, $listResGroup);
        }
        $datas = [];
        $resIds = [];
        $ipResIds = [];
        $isNetIds = [];
        $groupResIds = [];
        $diyResIds = [];
        self::dataMerge($list, $resIds, $ipResIds, $datas, $isNetIds, $groupResIds, $diyResIds);
        //组装资源组资源ID
        if (!empty($groupResIds)) {
            $condGroupRes['GroupResID'] = $groupResIds;
            if (!empty($IsInternet)) {
                $condGroupRes['IsInternet'] = $IsInternet;
            }
            $condGroupRes['IgnoreEndTime'] = 1;
            //由于资源组目前不会开放申请，默认的权限有效期是永久
            $listGroups = ResourceModel::getUserGroupResourceList($condGroupRes);
            self::dataMerge($listGroups, $resIds, $ipResIds, $datas, $isNetIds, $groupResIds, $diyResIds);
        }
        $resIds = array_unique($resIds);
        $ipResIds = array_unique($ipResIds);
        return [$resIds, $ipResIds, $datas, array_unique($isNetIds), array_unique($diyResIds)];
    }

    /**
     * 账户拥有资源的数组值 组装
     * @param array $data
     * @param array $resIds
     * @param array $ipResIds
     * @param array $datas
     * @param array $isNetIds
     * @param array $groupResIds
     * @param $diyResIds
     * @return void
     */
    public static function dataMerge(array $data, array &$resIds, array &$ipResIds, array &$datas, array &$isNetIds, array &$groupResIds, &$diyResIds): void
    {
        foreach ($data as $row) {
            if ($row['IsNat']) {
                $isNetIds[] = $row['ResID'];
            }
            if ($row['AccessTypeID'] == ACCESS_TYPE_CUSTOM_APP) {
                $diyResIds[] = $row['ResID'];
            }
            switch ($row['ResType']) {
                case self::RES_TYPE_GROUP:
                    $groupResIds[] = $row['ResID'];
                    continue 2;
                case self::RES_TYPE_REMOTE_APP:
                    $resIds[] = $row['ResID'];
                    $ipResIds[] = $row['ResID'];
                    break;
                case self::RES_TYPE_COMMON:
                    $resIds[] = $row['ResID'];
                    break;
                case self::RES_TYPE_IP:
                    $ipResIds[] = $row['ResID'];
                    break;
            }
            $datas[$row['ResID']] = $row;
        }
    }

    /**
     * 获取用户访问历史资源
     *
     * @param $Session
     * @param bool $ResList
     *
     * @param string $queryStr
     * @return mixed
     * @throws Exception
     */
    public static function getUserHistoryList($Session, $ResList = false, $queryStr = '')
    {
        if (empty($Session)) {
            return [];
        }
        if ($ResList === false) {
            $authorizationIDs = AuthUserModel::getAuthorizationGroup($Session['Uuid'], $Session['DeviceID']);
            list($resIds, $ipResIds, $ResList) = self::getUserResIds(
                $Session['Uuid'],
                $Session['DeviceID'],
                $Session['DepartIDs'],
                $Session['RoleID'],
                $authorizationIDs,
                '0'
            );
        }
        $cond = ['column' => 'history', 'groupby' => 'ResID', 'UserID' => $Session['Uuid']];
        $result = ResVisitLogModel::getList($cond, 'VisitID DESC');
        $resIds = [];
        if (!empty($result)) {
            foreach ($result as $row) {
                $resIds[] = $row['ResID'];
            }
            $cond = ['column' => 'list', 'InResID' => $resIds, 'notAuthAccessTypeID' => self::notAuthAccessTypeID()];
            $list = ResConfigListModel::getList($cond);
            // 需要判断远程应用授权，授权后添加远程应用的数据
            $isAuthRemote = getmoduleregist(28);
            if ($isAuthRemote) {
                $cond = ['column' => 'list', 'InResID' => $resIds];
                $remote_list = ResRemoteModel::getList($cond);
                if (is_array($remote_list)) {
                    $list = array_merge($list, $remote_list);
                }
            }
            $resDatas = ResourceModel::getResDatas($resIds);
            $historyList = [];
            if (!empty($list)) {
                $tmpHistory = [];
                foreach ($list as $key => $val) {
                    $ResID = $val['ResID'];
                    if (!isset($ResList[$ResID]) || $ResList[$ResID]['IsInternet'] !== '1') {
                        continue;
                    }
                    $reasonResult = UserResourceRedis::getOne($Session['Token'], $ResID);
                    $reasonResult = self::formatReasonResult($reasonResult);
                    $list[$key]['Result'] = $reasonResult['Result'] ?? '1';
                    $list[$key]['Reason'] = $reasonResult['Reason'] ?? '';
                    $list[$key]['IsNat'] = $resDatas[$ResID]['IsNat'];
                    $list[$key]['ResName'] = $resDatas[$ResID]['ResName'];
                    $list[$key]['AppIcon'] = !empty($val['Icon']) ? "/backend/{$val['Icon']}" : '';
                    $list[$key]['ResType'] = $resDatas[$ResID]['ResType'];
                    $list[$key]['ActionType'] = $resDatas[$ResID]['ActionType'];
                    $list[$key]['IsCopy'] = $resDatas[$ResID]['IsCopy'];
                    $list[$key]['Expired'] = self::getDiffTime(date("Y-m-d H:i:s"), $ResList[$ResID]['EndTime'] ?? '');
                    unset($list[$key]['Icon']);
                    //$historyList[] = $list[$key];
                    $tmpHistory[$val['ResID']] = $list[$key];
                }
                //针对数组排序后，取最近4条,搜索时也只从最近的4条里面搜索
                $i = 0;
                foreach ($result as $v) {
                    //只取最近4条记录
                    if (isset($tmpHistory[$v['ResID']])) {
                        //按名称与备注模糊搜索
                        $i++;
                        if (!empty($queryStr) && (!self::getIsQueryString($queryStr, $tmpHistory[$v['ResID']]['ResName']) && !self::getIsQueryString($queryStr, $tmpHistory[$v['ResID']]['Remark']))) {
                            continue;
                        }
                        if ($i > 4) {
                            break;
                        }
                        $historyList[] = $tmpHistory[$v['ResID']];
                    }
                }
            }
            return $historyList;
        }
        return [];
    }

    /**
     * 获取用户搜藏的资源
     *
     * @param $Session
     * @param bool $ResList
     *
     * @param string $queryStr
     * @return mixed
     * @throws Exception
     */
    public static function getUserCollectList($Session, $ResList = false, $queryStr = '')
    {
        if (empty($Session)) {
            return [];
        }
        if ($ResList === false) {
            $authorizationIDs = AuthUserModel::getAuthorizationGroup($Session['Uuid'], $Session['DeviceID']);
            list($resIds, $ipResIds, $ResList) = self::getUserResIds(
                $Session['Uuid'],
                $Session['DeviceID'],
                $Session['DepartIDs'],
                $Session['RoleID'],
                $authorizationIDs,
                '0'
            );
        }
        $cond = ['column' => 'collect', 'UserID' => $Session['Uuid']];
        $result = ResCollectModel::getList($cond, 'CollectID DESC');
        $resIds = [];
        if (!empty($result)) {
            foreach ($result as $row) {
                $resIds[] = $row['ResID'];
            }
            $cond = ['column' => 'list', 'InResID' => $resIds, 'notAuthAccessTypeID' => self::notAuthAccessTypeID()];
            $list = ResConfigListModel::getList($cond);
            // 需要判断远程应用授权，授权后添加远程应用的数据
            $isAuthRemote = getmoduleregist(28);
            if ($isAuthRemote) {
                $cond = ['column' => 'list', 'InResID' => $resIds];
                $remote_list = ResRemoteModel::getList($cond);
                if (is_array($remote_list)) {
                    $list = array_merge($list, $remote_list);
                }
            }

            $resDatas = ResourceModel::getResDatas($resIds);
            $collectList = [];
            if (!empty($list)) {
                foreach ($list as $key => $val) {
                    $ResID = $val['ResID'];
                    if (!isset($ResList[$ResID])) {
                        continue;
                    }
                    //按名称模糊搜索
                    if (!empty($queryStr) && !self::getIsQueryString($queryStr, $resDatas[$ResID]['ResName']) && !self::getIsQueryString($queryStr, $val['Remark'])) {
                        continue;
                    }
                    $result = UserResourceRedis::getOne($Session['Token'], $ResID);
                    $result = self::formatReasonResult($result);
                    $list[$key]['Result'] = $result['Result'] ?? '1';
                    $list[$key]['Reason'] = $result['Reason'] ?? '';
                    $list[$key]['AppIcon'] = !empty($val['Icon']) ? "/backend/{$val['Icon']}" : '';
                    unset($list[$key]['Icon']);
                    $list[$key]['ResName'] = $resDatas[$ResID]['ResName'];
                    $list[$key]['IsNat'] = $resDatas[$ResID]['IsNat'];
                    $list[$key]['IsInternet'] = $resDatas[$ResID]['IsInternet'];
                    $list[$key]['ResType'] = $resDatas[$ResID]['ResType'];
                    $list[$key]['ActionType'] = $resDatas[$ResID]['ActionType'];
                    $list[$key]['IsCopy'] = $resDatas[$ResID]['IsCopy'];
                    $list[$key]['Expired'] = self::getDiffTime(date("Y-m-d H:i:s"), $ResList[$ResID]['EndTime'] ?? '');
                    $collectList[] = $list[$key];
                }
            }
            return $collectList;
        }
        return [];
    }

    /**
     * @Description: 判断是否需要过滤掉
     * User: <EMAIL>
     * Date: 2023/5/29 14:14
     * @param $queryStr
     * @param $allStr
     * @return bool
     */
    public static function getIsQueryString($queryStr, $allStr): bool
    {
        //按名称模糊搜索
        if (!empty($queryStr)) {
            $pattern = "/{$queryStr}/u"; // 注意要加上 '/u' 以支持中文匹配
            if (!preg_match($pattern, $allStr)) {
                //没有找到关键词，在判断一下字母大小写的问题
                $lowercaseString = mb_strtolower($allStr, 'UTF-8');
                $lowercaseSubstring = mb_strtolower($queryStr, 'UTF-8');
                if (mb_strpos($lowercaseString, $lowercaseSubstring) !== false) {
                    return true;
                }
                return false;
            } else {
                return true;
            }
        }
        return true;
    }

    /**
     * 获取用户单个收藏的资源
     *
     * @param $UserID
     * @param $ResID
     *
     * @return mixed
     */
    public static function getUserCollect($UserID, $ResID)
    {
        if (empty($UserID) || empty($ResID)) {
            return false;
        }

        $Cond = ['UserID' => $UserID, 'ResID' => $ResID, 'column' => 'collect'];
        return ResCollectModel::getSingle($Cond);
    }

    /**
     * 收藏资源
     *
     * @param $UserID
     * @param $ResID
     *
     * @return bool|int
     * @throws Exception
     */
    public static function collectResource($UserID, $ResID)
    {
        if (empty($UserID) || empty($ResID)) {
            return false;
        }

        $Resource = self::getResource($ResID);
        if (empty($Resource)) {
            T(21148002);
        }

        return ResCollectModel::insert(['UserID' => $UserID, 'ResID' => $ResID]);
    }

    /**
     * 取消收藏资源
     *
     * @param $UserID
     * @param $ResID
     *
     * @return mixed
     */
    public static function cancelCollectResource($UserID, $ResID)
    {
        if (empty($UserID) || empty($ResID)) {
            return false;
        }

        return ResCollectModel::delete(['UserID' => $UserID, 'ResID' => $ResID]);
    }

    /**
     * 表单代填检查：判断是否表单代填，并且没有填过。
     *
     * @param $UserID
     * @param $Resource
     *
     * @return bool
     * @throws Exception
     */
    public static function checkFullForm($UserID, $Resource): bool
    {
        if ($Resource['AccessTypeID'] !== ACCESS_TYPE_FORM) {
            return false;
        }
        AgentServiceProvider::handFirstResourceAccount($UserID, $Resource);
        $Account = AgentServiceProvider::getResourceAccount($UserID, $Resource['ResID']);
        $FormData = json_decode($Resource['OtherField'], true);
        if ($Resource['GrantType'] != 'auto_fill') {
            if (empty($Account['UserName']) || empty($Account['Password'])) {
                T(********);
            }
        }
        return true;
    }

    /**
     * 记录资源访问日志
     *
     * @param $UserID
     * @param $ResID
     * @param array $Resource
     * @param array $session
     * @return bool|int
     */
    public static function addAccessLog($UserID, $ResID, array $Resource = [], array $session = [])
    {
        if (empty($UserID) || empty($ResID)) {
            return false;
        }

        if (isset($Resource['AccessTypeID']) && (int)$Resource['AccessTypeID'] === (int)ACCESS_TYPE_CUSTOM_APP) {
            $CreateTime = date("Y-m-d H:i:s");
            $Content = "【{$session['UserName']}】 在 【{$CreateTime}】 访问了 ";
            $AppName = AgentServiceProvider::$AppNames[$Resource['AccessTypeID']] ?? '';
            $Content .= "【{$AppName}】 【{$Resource['ResName']}】";
            $Client = SystemServiceProvider::getClientTypeId($session['OsType'], $session['Client']);
            $Data = ['Client' => $Client, 'Content' => $Content, 'ResName' => $Resource['ResName']];
            $Data['AccessType'] = hlp_common::getAccessType($Resource['AccessTypeID']);
            self::addUserLog($session, 28, $ResID, $Data);
        }
        return ResVisitLogModel::insert(['UserID' => $UserID, 'ResID' => $ResID]);
    }

    /**
     * 记录用户日志
     *
     * @param $Session
     * @param $LogTypeID
     * @param $ResID
     * @param $Content
     * @param array $Data
     *
     * @return bool|int
     */
    public static function addUserLog($Session, $LogTypeID, $ResID, $Data = [])
    {
        if (empty($Session['Uuid']) || empty($ResID) || empty($LogTypeID) || empty($Data['Content'])) {
            return false;
        }

        $params = ['UserID' => $Session['Uuid'], 'ResID' => $ResID, 'AccountID' => $Session['AccountID'],
            'AccessToken' => $Session['Token'], 'LogTypeID' => $LogTypeID, 'IP' => $_SERVER["REMOTE_ADDR"],
            'Client' => $Data['Client'] ?? 1, 'Content' => $Data['Content'], 'LoginName' => $Session['UserName'],
            'User_Name' => $Session['UserName'], 'True_Name' => $Session['TrueNames']];
        if (isset($Data['Status'])) {
            $params['Status'] = $Data['Status'];
        }
        if (isset($Data['ResName'])) {
            $params['Res_Name'] = $Data['ResName'];
        }
        if (isset($Data['AccessType'])) {
            $params['AccessType'] = $Data['AccessType'];
        }
        $params['ClientInfo'] = $Session['DevInfo'];
        return UserLogModel::insert($params);
    }

    /**
     * 附加认证成功
     *
     * @param $Token
     *
     * @return bool|mixed
     */
    public static function addAuth($Token)
    {
        if (empty($Token)) {
            return false;
        }
        $dict = DictModel::getOneItem('User', 'EffTimeLimit');
        $AuthValid = ($dict['ItemValue'] ?? 120) * 60;
        $Data = ['Status' => 1, 'LifeTime' => $AuthValid];
        self::log("{$Token}:" . json_encode($Data));
        return UserAddAuthRedis::setOne($Token, $Data);
    }

    /**
     * 获取资源策略错误信息
     *
     * @param $token
     * @param $resId
     *
     * @return bool|mixed
     */
    public static function getErrMessage($token, $resId)
    {
        if (empty($token) || empty($resId)) {
            return '';
        }
        $result = UserResourceRedis::getOne($token, $resId);
        if (!empty($result['Result'])) {
            return '';
        }
        return $result['Reason'];
    }

    /**
     * 是否windows独立运行
     * @param $Resource
     * @return bool
     */
    public static function isWinRunRemote($Resource): bool
    {
        return $Resource['ResType'] == 3 && $Resource['ActionType'] == 0;
    }

    /**
     * 获取资源的账户密码信息
     *
     * @param $userId
     * @param $resId
     * @param $accessType
     *
     * @return mixed
     * @throws Exception
     */
    public static function getResAccountInfo($userId, $resId, $accessType, $granType)
    {
        if (empty($userId) || empty($resId)) {
            return false;
        }
        if ($accessType === ACCESS_TYPE_REMOTE_APP) {
            $params = ['UserId' => $userId];
            $result = lib_yar::clients('ztpCenter', 'getRemotePassword', $params);
            $account = !empty($result['state']) ? ($result['data'] ?? false) : false;
        } elseif ($accessType === ACCESS_TYPE_SSH && $granType === 'authorized_key') {
            $ztpCenterInfo = GatewayServiceProvider::getControlHostInfo();
            $params = ['ResId' => $resId, 'UserId' => $userId];
            $result = lib_yar::clients('ztpCenter', 'getRememberPasswordCer', $params, $ztpCenterInfo['RemoteHost']);
            $account = !empty($result['state']) ? ($result['data'] ?? false) : false;
        } else {
            $params = ['ResId' => $resId, 'UserId' => $userId];
            $result = lib_yar::clients('ztpCenter', 'getRememberPassword', $params);
            $account = !empty($result['state']) ? ($result['data'] ?? false) : false;
        }
        return $account;
    }

    /** 检查网关是否启用，目前只检查独立网关
     * @param $gateWayId
     * @param $gateWayType
     * @return bool
     * @throws Exception
     */
    public static function checkGateWayEnable($gateWayId, $gateWayType)
    {
        if (empty($gateWayId) || empty($gateWayType)) {
            return false;
        }
        if ($gateWayType == 1) {
            $gateway = GateWayModel::getOne($gateWayId, "status");
            if ($gateway['IsEnable'] == 0) {
                T(********);
            }
        }
        return true;
    }

    /**
     * 根据ip域资源生成对应的redis缓存
     * 类型: json
     * 字段: ASM_IpResource:{ID}
     * 格式: {
     * "resIpContent": "*******##*******-******* tcp/80-8000",
     * "resDomainContent": "www.test.com##*infogo.com tcp/80-8000",
     * "resID": "1",
     * "fakeIpEnable":"1",       // 是否启用fakeIP的的功能，欺骗成fakeIpv4里面的地址 默认1
     * "gatewayID": [
     * "1",
     * "2"
     * ]
     * }
     * @param $param
     * @return bool
     */
    public static function makeRedisResource($param)
    {
        // 删除key
        if (isset($param['doType']) && $param['doType'] == 'del') {
            if (is_array($param['ResID'])) {
                foreach ($param['ResID'] as $resid) {
                    ResourceRedis::deleteOne($resid);
                }
            }
            return true;
        }
        // 增加key
        $cond = (isset($param['ResID']) && $param['ResID']) ? ['column' => 'list', 'ResID' => $param['ResID']] : ['column' => 'list'];
        $aResList = ResIPListModel::getList($cond);
        $cond['column'] = 'power';
        $remoteData = ResRemoteModel::getRelationList($cond);
        if (is_array($remoteData)) {
            $aResList = array_merge($remoteData, $aResList);
        }
        self::log("makeRedisResource--------------ResList------:" . json_encode($aResList));
        if (empty($aResList)) {
            return false;
        }
        foreach ($aResList as $aRes) {
            if (empty($aRes['IP'])) {
                continue ;
            }

            $aAppConfig = self::getOneProxyConfig($aRes['IP']);
            $aAppConfig['gatewayID'] = [];
            $aGw = GateWayExpendModel::getList(['column' => 'list','RID' => [$aRes['ResID']],'Type' => 1]);
            if (empty($aGw)) {
                $aGw = [['GwType' => $aRes['GateWayType'], 'GwID' => $aRes['GateWayID']]];
            }
            self::setGatewayIDs($aGw, $aAppConfig);
            //            $aAppConfig['gatewayID'] = array_unique($aAppConfig['gatewayID']);
            $aAppConfig['resID'] = $aRes['ResID'] ?? '';
            $aAppConfig['fakeIpEnable'] = "1";
            ResourceRedis::setOne($aRes['ResID'], json_encode($aAppConfig));
        }
        return true;
    }

    /**
     * 获取解析的配置
     * @param $ips
     * @return array
     */
    public static function getOneProxyConfig($ips)
    {
        $aIp = explode("##", $ips);
        $aAppConfig = [];
        $aAppConfig['resIpContent'] = "";
        $aAppConfig['resDomainContent'] = "";
        foreach ($aIp as $ip) {
            $ips = $ip;
            $num = stripos($ip, ' ');
            if ($num !== false) {
                $ips = substr($ip, 0, $num);
            }
            $f_num = stripos($ips, '-');
            if ($f_num !== false) {
                $ips = substr($ip, 0, $f_num);
            }
            if (isIP($ips)) {
                $aAppConfig['resIpContent'] .= "##".$ip;
            } else {
                $aAppConfig['resDomainContent'] .= "##".$ip;
            }
        }
        $aAppConfig['resIpContent'] = $aAppConfig['resIpContent'] != "" ? substr($aAppConfig['resIpContent'], 2) : "";
        $aAppConfig['resDomainContent'] = $aAppConfig['resDomainContent'] != "" ? substr($aAppConfig['resDomainContent'], 2) : "";
        return $aAppConfig;
    }

    /**
     * 设置资源的网关ID
     * @param $aGw
     * @param $aAppConfig
     */
    private static function setGatewayIDs($aGw, &$aAppConfig)
    {
        foreach ($aGw as $item) {
            if ($item['GwType'] == 2) {
                $aGwList = GateWayModel::getList(['GroupID' => $item['GwID']]);
                if (is_array($aGwList)) {
                    foreach ($aGwList as $aIt) {
                        if (!in_array($aIt['ID'], $aAppConfig['gatewayID'])) {
                            $aAppConfig['gatewayID'][] = $aIt['ID'];
                        }
                    }
                }
            } else {
                if (!in_array($item['GwID'], $aAppConfig['gatewayID'])) {
                    $aAppConfig['gatewayID'][] = $item['GwID'];
                }
            }
        }
    }

    /**
     * 获取用户资源列表
     *
     * @param $Token
     * @param string $queryStr
     * @param int $groupResID
     * @param int $isReturnGroup
     * @return array[]
     */
    public static function getUserApplyResourceList($Token, string $queryStr = '', int $groupResID = 0, $isReturnGroup = 0): array
    {
        $Session = SessionRedis::getOne($Token, 'policy');
        if (empty($Session)) {
            return ['returnLists' => [], 'ipResLists' => []];
        }
        $departIDs = $Session['DepartIDs'];
        $roleID = $Session['RoleID'];
        $deviceID = $Session['DeviceID'];
        $userID = $Session['Uuid'];
        //授权组从用户表单里面获取一次,避免更新不及时
        $authorizationIDs = AuthUserModel::getAuthorizationGroup($userID, $deviceID);

        //获取自己已经拥有权限的资源列表
        list($resIds, $ipResIds, $datas) = self::getUserResIds($userID, $deviceID, $departIDs, $roleID, $authorizationIDs, '1');
        $noInResId = array_merge($resIds, $ipResIds);
        //获取已经在申请的资源列表,该部分暂时先不屏蔽
        //$hasApplyRes = ResourceServiceProvider::getResApplyList($userID);
        //$hasApplyResId = array_column($hasApplyRes['list'], "ResID");
        //$noInResId = array_merge($noInResId, $hasApplyResId);
        //排除不可以申请的resId,得到最终可以申请的资源权限列表ID
        $resApplyList = ResourceModel::getAllApplyResource(['noInResId' => $noInResId, 'queryStr' => $queryStr, 'groupResID' => $groupResID], 0);
        $resIpApplyList = ResourceModel::getIpApplyResource(['noInResId' => $noInResId, 'queryStr' => $queryStr, 'groupResID' => $groupResID]);
        $resRemoteApplyList = ResourceModel::getAllApplyResource(['noInResId' => $noInResId, 'queryStr' => $queryStr, 'groupResID' => $groupResID], 3);
        if (!empty($resIpApplyList)) {
            $resApplyList = array_merge($resApplyList, $resIpApplyList);
        }
        if (!empty($resRemoteApplyList)) {
            $resApplyList = array_merge($resApplyList, $resRemoteApplyList);
        }
        //$returnRes["noInResId"] = $noInResId;
        $returnRes["resApplyList"] = $resApplyList;
        if (!empty($isReturnGroup)) {
            $returnRes["resGroupList"] = ResGroupModel::getAllName();
        }
        return $returnRes;
    }

    /**
     * 获取已经在申请的资源列表
     *
     * @param $userId
     * @param int $status
     * @param int $start
     * @param int $limit
     * @return array[]
     */
    public static function getResApplyList($userId, int $status = 0, $start = 0, $limit = 1000): array
    {
        $cond = ['column' => 'log', 'UserID' => $userId];
        if ($status != -1) {
            $cond['Status'] = $status;
        }
        $returnRes['list'] = ResApplyModel::getList($cond, "ApplyID DESC", $start, $limit);
        $returnRes['total'] = ResApplyModel::getCount($cond);
        return $returnRes;
    }

    /**
     * 获取已经在申请的资源列表
     *
     * @param $token
     * @param $resId
     * @param $remark
     * @param $applyDays
     * @return false|int
     * @throws Exception
     */
    public static function apply($token, $resId, $remark, $applyDays)
    {
        //检查申请天数是否为整数，且大于0 ,备注是否超过200个字符
        if (intval($applyDays) < 1 || empty($resId)) {
            T(21100002);
        }
        //查询资源是否存在，根据类型获取对应的参数
        $Resource = self::getResource($resId, 'one');
        if (empty($Resource) || empty($Resource['IsApply'])) {
            T(21148058);
        }
        $userInfo = SessionRedis::getOne($token, 'policy');
        $userId = $userInfo['Uuid'] ?? 0;
        $userName = $userInfo['UserName'] ?? '';
        if (empty($userId)) {
            T(21148023);
        }
        //查询资源是否存在申请列表
        $hasApply = ResApplyModel::getList(['column' => '*', 'UserID' => $userId, 'ResID' => $resId, 'Status' => 0], "ApplyID DESC");
        if (!empty($hasApply)) {
            T(21148057);
        }

        //判断是否已经拥有该权限
        $DepartIDs = $userInfo['DepartIDs'] ?? '';
        $RoleID = $userInfo['RoleID'] ?? 0;
        $DeviceID = $userInfo['DeviceID'] ?? 0;
        $UserID = $userInfo['Uuid'] ?? 0;
        $AuthorizationIDs = $userInfo['AuthorizationIDs'];
        list($resIds, $ipResIds, $datas) = self::getUserResIds($UserID, $DeviceID, $DepartIDs, $RoleID, $AuthorizationIDs, '0');
        if (!empty($datas)) {
            foreach ($datas as $key => $data) {
                if ($key == $resId) {
                    T(21148058);
                }
            }
        }

        //此处做个base64解码，不然特殊字符过不了token检查验证
        if (!empty($remark)) {
            $remarkDecode = base64_decode($remark);
            if (!empty($remarkDecode)) {
                $remark = $remarkDecode;
            }
        }
        if (mb_strlen($remark) > 200) {
            T(21100002);
        }
        $result = ResApplyModel::insert(['UserID' => $userId, 'ResID' => $resId, 'ResName' => $Resource['ResName'] ?? '', 'Content' => $remark, 'ResParams' => json_encode($Resource), 'ApplyDays' => $applyDays, 'UserName' => $userName]);
        //如果开启自动审核
        if ($result && !empty($Resource['IsAutoPass'])) {
            //调用admin-api Rpc自动通过
            $dataArr = \lib_yar::clients('backend', 'autoPassResApply', ['ApplyID' => $result, 'UserID' => $userId, 'ResID' => $resId], "127.0.0.1", 10 * 1000, 2);
            self::log('RPC调用autoPassResApply结果：' . json_encode($dataArr, JSON_UNESCAPED_UNICODE));
        }
        return $result;
    }

    /**
     * 获取俩个日期时间之间的间隔 xx天xx小时xx分
     *
     * @param $start
     * @param $end
     * @return string
     * @throws Exception
     */
    public static function getDiffTime($start, $end): string
    {
        $returnRes = "";
        if (empty($end) || empty($start)) {
            return $returnRes;
        }
        $startTime = new DateTime($start);
        $endTime = new DateTime($end);
        $interval = $startTime->diff($endTime);
        $days = $interval->days;
        $hours = $interval->h;
        $minutes = $interval->i;
        //$seconds = $interval->s;
        if ($days > 360) {
            return $returnRes;
        }
        if ($days > 0) {
            $returnRes .= "{$days}天";
        }
        if ($hours > 0) {
            $returnRes .= "{$hours}小时";
        }
        if ($minutes > 0) {
            $returnRes .= "{$minutes}分";
        }

        //如果时分秒都为0则表示小于一分钟
        if ($days <= 0 && $hours <= 0 && $minutes <= 0) {
            $returnRes .= "小于1分钟";
        }

        return $returnRes;
    }

    /**
     * 资源数据重新组装
     * @param $aResIpList
     * @return array|string
     * **/
    public static function getAllGwResource($aResIpList)
    {
        if (!is_array($aResIpList)) {
            return $aResIpList;
        }
        $aGw = GateWayExpendModel::getList(['column' => 'list', 'Type' => 1]);
        if (is_array($aGw)) {
            $aGwInfo = [];
            $aResIps = [];
            foreach ($aGw as $item) {
                if (!isset($aGwInfo[$item['RID']])) {
                    $aGwInfo[$item['RID']] = [];
                }
                if ($item['GwType'] == 2) {
                    $aGw = GateWayModel::getList(['GroupID' => $item['GwID']]);
                    if (is_array($aGw)) {
                        foreach ($aGw as $aIt) {
                            if (!in_array($aIt['ID'], $aGwInfo[$item['RID']])) {
                                $aGwInfo[$item['RID']][] = $aIt['ID'];
                            }
                        }
                    }
                } else {
                    if (!in_array($item['GwID'], $aGwInfo[$item['RID']])) {
                        $aGwInfo[$item['RID']][] = $item['GwID'];
                    }
                }
            }
            foreach ($aResIpList as $item) {
                $aResIp[$item['ResID']] = $item;
            }
            foreach ($aGwInfo as $ResID => $aGwID) {
                if (!empty($aResIp[$ResID])) {
                    foreach ($aGwID as $gwid) {
                        $aResIps[] = [
                            "IP" => $aResIp[$ResID]['IP'],
                            "ResID" => $ResID,
                            "GateWayID" => $gwid,
                            "GateWayType" => '1'
                        ];
                    }
                }
            }
            $aResIpList = $aResIps;
        }
        return $aResIpList;
    }

    /**
     * 获取资源策略信息
     *
     * @param $token
     * @param $resId
     *
     * @return bool|mixed
     */
    public static function getUserResourcePolicy($token, $resId)
    {
        if (empty($token) || empty($resId)) {
            return '';
        }
        $result = UserResourceRedis::getOne($token, $resId);
        if (!empty($result['Result'])) {
            return '';
        }
        return $result;
    }

    /**
     * 同步资源策略
     *
     * @param $token
     * @param $resid
     * @return void
     */
    public static function syncUserResource($token, $resid): void
    {
        if (empty($token) || empty($resid)) {
            return;
        }
        UserResourceRedis::handPush($token, $resid);
    }
}
