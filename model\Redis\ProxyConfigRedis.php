<?php

/**
 * Description: 隧道配置
 * User: <EMAIL>
 * Date: 2024/05/13 10:32
 * Version: $Id$
 */

class ProxyConfigRedis extends AppConfigRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'proxyConfigs';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'appnum,appcfg';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['appnum'],
        'appcfg' => ['appcfg']
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['appcfg' => true];

    /**
     * 发布内容定义
     * @var string
     */
    protected static $publish = 'all';
}
