<?php

/**
 * Description: 存储错误信息
 * User: <EMAIL>
 * Date: 2022/05/16 10:32
 * Version: $Id$
 */

class MessageRedis extends BaseStringRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Message';

    /**
     * 单条
     *
     * @param string $deviceId
     * @param string $error_code
     *
     * @return mixed
     */
    public static function getOne($deviceId, $error_code)
    {
        return self::get($deviceId, $error_code);
    }

    /**
     * 单条
     *
     * @param string $deviceId
     * @param string $error_code
     * @param string $message
     *
     * @return mixed
     */
    public static function setOne($deviceId, $error_code, $message)
    {
        return self::set($message, 86400, $deviceId, $error_code);
    }
}