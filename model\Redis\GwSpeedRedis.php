<?php

/**
 * Description: 网关配置 读数据
 * User: <EMAIL>
 * Date: 2022/06/06 10:32
 * Version: $Id$
 */

class GwSpeedRedis extends BaseRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'GwSpeed';

    /**
     * 控制中心的网关数据
     * @var string
     */
    protected static $localColumns = 'priority,upTrafficLimit,downTrafficLimit';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'    => ['priority','upTrafficLimit', 'downTrafficLimit'],
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['upTrafficLimit', 'downTrafficLimit'];

    /**
     * 单条
     *
     * @param string $gatewayId
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($gatewayId, $column = 'one')
    {
        return self::get($column, $gatewayId);
    }

    /**
     * 单条
     *
     * @param string $gatewayId
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($gatewayId, $data)
    {
        return self::set($data, $gatewayId);
    }
}
