<?php

/**
 * Description: 配置TRoleDict表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: RoleDictModel.php 170149 2022-03-03 01:53:25Z lihao $
 */

class RoleDictModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TRoleDict';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one' => 'ItemName, ItemValue,ConfigID,TRoleDict.Groups',
        'config' => 'TRoleDict.ItemValue as Config',
        '*'   => '*',
    ];

    /**
     * 多条 原getRoleTDictInfo方法
     *
     * @param int $RoleID 角色ID
     *
     * @return mixed
     */
    public static function getAll(int $RoleID)
    {
        if (empty($RoleID)) {
            return false;
        }

        static $data = null;
        if (PHP_SAPI !== 'cli' && !empty($data[$RoleID])) {
            return $data[$RoleID];
        }

        self::$data = [];
        // imc与asm融合，获取的所有角色信息不再单独区分移动端,全部走pc端
        $where = " and TRoleDict.Groups NOT LIKE 'Mobile%'";
        $column = self::$columns['one'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE ConfigID IN (SELECT ConfigID FROM `TRoleRelation` where RoleID = ".
                 self::setData($RoleID)." {$where} ) AND ItemValue NOT LIKE '%<?xml%' ";
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        $aData = lib_database::getAll($sql, $table['index'], false, self::$data);
        $aResult = [];

        if (!empty($aData)) {
            foreach ($aData as $row) {
                $aResult [$row ['ItemName']] = $row ['ItemValue'];
            }
        }

        $data[$RoleID] = $aResult;
        return $aResult;
    }

    /**
     * 获取配置信息
     *
     * @param $RoleID
     * @param $ItemName
     * @param $Groups
     *
     * @return array|bool
     */
    public static function getOneConfig($RoleID, $ItemName, $Groups = '')
    {
        if (empty($RoleID) || empty($ItemName)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns['config'];
        $where = "where TRoleDict.ItemName = ".self::setData($ItemName).
                 " and TRoleRelation.RoleID = ".self::setData($RoleID)." and TRoleRelation.ConfigID=TRoleDict.ConfigID";

        if (!empty($Groups)) {
            $where .= " and TRoleDict.Groups = '{$Groups}'";
        }

        $sql = "select {$column} from TRoleDict, TRoleRelation {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['Groups'])) {
            $where .= "AND TRoleDict.Groups = ".self::setData($cond['Groups']);
        }

        if (isset($cond['ItemName'])) {
            $where .= "AND ItemName = ".self::setData($cond['ItemName']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
