<?php



class IpPoolExpendModel extends BaseModel
{
    public const TABLE_NAME = 'TGwIpPoolExpend';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'list'   => 'ID,GwID,PoolID,IpPool'
    ];

    /**
     * 获取新的资源信息,关联多个网关
     * **/

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        if (isset($cond['PoolID'])) {
            $where .= "AND PoolID = ".self::setData($cond['PoolID']);
        }
        if (isset($cond['GwID'])) {
            $where .= "AND GwID = ".self::setData($cond['GwID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
