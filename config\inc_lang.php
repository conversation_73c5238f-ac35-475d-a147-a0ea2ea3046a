<?php

/**
 * Description: 语言配置
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: inc_lang.php 170454 2022-03-07 07:11:45Z huyf $
 */
/* 访问控制 */
!defined('IN_INIT') && exit('Access Denied');
/* 初始化语言配置 */

// 语言文件配置 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号
$GLOBALS['CONFIG']['LANG_FILE'] = array(
    '11100' => 'Program',   // 程序错误
    '21100' => 'Common',    // 公共提示
    '21101' => 'System',    // 系统
    '21102' => 'Net',       // 网络
    '21103' => 'Device',    // 设备
    '21104' => 'Safecheck', // 安检
    '21105' => 'Register',  // 注册
    '21106' => 'AdLdap', // AD域和Ldap公共服务
    '21107' => 'UnitTest',    // 单元测试
    '21120' => 'Auth',      // 认证公共
    '21121' => 'AdAutoAuth',  // AD域单点登录认证
    '21122' => 'AdDomainAuth', // AD域认证
    '21123' => 'DingTalkAuth', // 钉钉认证
    '21124' => 'EmailAuth', // 邮箱认证
    '21125' => 'FingerAuth', // 指纹认证
    '21126' => 'GuestAuth',  // 来宾认证
    '21127' => 'LdapAuth',  // Ldap认证
    '21128' => 'MacAuth',   // MAC认证
    '21129' => 'RadiusAuth', // radius认证
    '21130' => 'SelfGuestAuth', // 自助申请
    '21131' => 'SmsAuth', // 短信认证
    '21132' => 'UkeyAuth', // Ukey认证
    '21133' => 'UserAuth', // 用户认证
    '21134' => 'WebAuth', // web认证
    '21135' => 'WechatAuth', // 微信认证
    '21136' => 'WeWorkAuth', // 企业微信认证
    '21137' => 'User', // 账号
    '21138' => 'Qrcode', // 二维码
    '21139' => 'Topspeed', // 极速入网
    '21140' => 'Vpn', // vpn
    '21141' => 'OTP', // otp
    '21145' => 'FeiShu', // 飞书
    '21146' => 'Resource', // 高级动态认证
    '21147' => 'SsoAuth', // sso认证
    '21148' => 'ZtpResource', // 零信任资源
);
