<?php
/**
 * Description: 认证前代码块 只能BaseAuthService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: BaseAuthServiceBeforeTrait.php 167035 2022-01-12 12:56:47Z renchen $
 */

namespace Services\Auth\Traits;

use AuthUserModel;
use ComputerModel;
use DeviceModel;
use DictModel;
use Exception;
use SceneModel;
use lib_yar;
use NacOnLineDeviceModel;
use RelationComputerModel;
use ServerServiceProvider;

trait BaseAuthServiceBeforeTrait
{
    /**
     * 获取验证服务
     *
     * @return array
     */
    public function getAuthServer(): array
    {
        return [];
    }

    /**
     * 解析参数
     * @return array
     */
    public function parseParams()
    {
        return $this->params;
    }

    /**
     * 获取设备信息 原GetDeviceInfo
     * @throws Exception
     */
    public function setDeviceInfo()
    {
        if (empty($this->params['mac'])) {
            $deviceInfo = DeviceModel::getOne($this->deviceId, '*');
        } else {
            $deviceInfo = DeviceModel::getOneByMac($this->params['mac'], '*');
        }
        if (empty($deviceInfo)) {
            T(21120003);
        }

        $computerInfo = ComputerModel::getOne($this->deviceId, 'info');
        if (empty($computerInfo)) {
            T(21120003);
        }

        $this->deviceInfo = array_merge($deviceInfo, $computerInfo);
        $reComputer = RelationComputerModel::getOne($deviceInfo['DeviceID'], 'auth');

        $ipamConfig = ServerServiceProvider::ipamConfig();

        $sceneInfo = SceneModel::getOne($reComputer['SceneID']);
        $this->deviceInfo = array_merge($this->deviceInfo, $sceneInfo);
        // 当前设备场景是否可用 如果是802.1x直接往下走不返回失败
        if ($this->params['callfrom'] != AUTH_FROM_8021X && $sceneInfo && $sceneInfo['IsEnable'] != 1) {
            RelationComputerModel::update($deviceInfo['DeviceID'], ['LastUserType' => '']);
            T(21120068);
        }

        if (!empty($reComputer) && $reComputer['CutOffStopTime'] >= date("Y-m-d H:i:s") && $ipamConfig['linkage'] != 1 && $ipamConfig['dev_regulation_access_on_off'] != 1) {
            T(21120029);
        }
        // 设备禁用来宾不允许来宾认证
        if(!empty($reComputer) && $reComputer['ForbidGuest'] == 1 && $this->params['servicePrefix'] == 'Guest') {
            T(21120070);
        }

        $this->deviceInfo = array_merge($this->deviceInfo, $reComputer);
        $this->onlineDeviceInfo = NacOnLineDeviceModel::getOne($this->deviceId);
        $this->deviceInfo['DevInfo'] = $this->getDevInfo();
    }

    /**
     * 获取终端信息
     * @return string
     */
    private function getDevInfo()
    {
        $info = $this->deviceInfo['DevName'] ?? $this->deviceInfo['IEVersion'];
        return $this->deviceInfo['OSName'] . " " . $info;
    }

    /**
     * 认证前校验
     * @throws Exception
     */
    public function authBefore()
    {
        $this->setDeviceInfo();
        $this->checkMobileAuth();
        $this->check8021x();
    }

    /**
     * 高级动态认证前校验
     * @throws Exception
     */
    public function resourceAuthBefore()
    {
        $this->setDeviceInfo();
    }

    /**
     * 验证802.1x认证来源是否合法
     * @return void
     * @throws Exception
     */
    public function check8021x()
    {
        // 如果是802.1x发起的认证则强制验证认证方式是否在管理后台配置
        if ($this->params['callfrom'] == AUTH_FROM_8021X) {
            //User|UKey|Mobile|Finger|Guest,入网流程管理可以灵活控制的认证方式
            $allAuthType = array('User', 'UKey', 'Mobile', 'Finger', 'Guest', 'NoAuth');
            //这里只考虑入网流程管理能配置的，不能配置的不管，手机端认证不管
            if (!strlen($this->params['authType']) || $this->params['newMobile'] || $this->params['isMobile']
                || !in_array($this->params['authType'], $allAuthType)) {
                return;
            }
            $AuthParam = DictModel::getAll('AUTHPARAM');
            $allowAuthTypeArr = explode('|', $AuthParam['AllowAuthType']);
            //1.管理后台入网流程管理没开认证，统一当做免认证处理
            if ($this->params['sceneId'] <= 0) {
                $res = RelationComputerModel::getSingle(['DeviceID' => $this->deviceId, 'column' => 'scene']);
                $this->params['sceneId'] = $res['SceneID'] ?? 0;
            }
            $sceneConfig = $this->getSceneInfoById($this->params['sceneId'], $this->params['newMobile']);
            # 员工场景判断NeedAuth为无需认证 来宾场景判断AccessType为NoAuth 即为免认证
            if (isset($sceneConfig['sceneinfo'])
                && (($sceneConfig['sceneinfo']['UserType'] == '1' && $sceneConfig["needAuth"] != '1')
                || $sceneConfig['sceneinfo']['UserType'] == '2' && $sceneConfig['sceneinfo']['AccessType'] == AUTH_TYPE_NOAUTH)) {
                $this->writeLog("由于没开认证，认证方式" . $this->params['authType'] . "切换到免认证NoAuth");
                $this->params['authType'] = AUTH_TYPE_NOAUTH;
                $allowAuthTypeArr[] = AUTH_TYPE_NOAUTH;
            }
            //2.是否在免认证IP段内,在也直接认证成功
            // 场景化后没有免认证IP段的概念了这里取消 renchen 2023/02/16

            //3.是否在来宾专属IP段内,在也直接认证成功
            $ip = getRemoteAddress();
            $isNoAuthSeq = $this->getGuestApplyIP($ip);
            if ($isNoAuthSeq) {
                return;
            }
            //4.管理后台入网流程管理是否配置了此认证方式，没配置此认证方式，直接返回错误
            if (count($allowAuthTypeArr) > 0 && !in_array($this->params['authType'], $allowAuthTypeArr)) {
                T(21120041);
            }
        }
    }

    /**
     * 判断指定的IP是否在来宾专属IP段内
     * @param string $ip ip
     * @return bool true：存在 false:不存在
     */
    public function getGuestApplyIP($ip)
    {
        //获取是否在来宾专属IP段内,在也直接认证成功
        $GuestAuth = DictModel::getAll('GUESTAUTH');
        $GuestApplyIP = $GuestAuth['GuestApplyIP'];
        $isGuestApplyIP = false;
        if (!empty($GuestApplyIP) && $GuestAuth['State'] == '1' && $GuestAuth['GuestIPState'] == '1') {
            $authIP = explode(",", $GuestApplyIP);
            foreach ($authIP as $value) {
                $authIPSeg = explode("-", $value); //获得起始和结束IP
                if (FindInIP($ip, $authIPSeg[0], $authIPSeg[1])) {
                    $isGuestApplyIP = true;
                    break;
                }
            }
        }
        return $isGuestApplyIP;
    }

    /**
     * 检查移动端认证开关
     * @throws Exception
     */
    public function checkMobileAuth()
    {
        if ($this->deviceInfo['Type'] == '103') {
            $clientAuth = DictModel::getAll('Mobile');
            if ($clientAuth['onmobileclientauth'] == '1' && $this->params['isMobile'] == "1") {
                if ($clientAuth['onmobilewebauth'] == '1') {
                    T(21120039);
                } else {
                    T(21120038);
                }
            } elseif ($clientAuth['onmobilewebauth'] == '1' && $this->params['isMobile'] != "1") {
                if ($clientAuth['onmobileclientauth'] == '1') {
                    T(21120039);
                } else {
                    T(21120040);
                }
            }
        }
    }

    /**
     * 获取用户
     *
     * @param $userId
     * @return array|bool
     * @throws \Exception
     */
    public function getOneUserinfo($userId)
    {
        if (empty($userId)) {
            return false;
        }
        if (empty($this->userInfo)) {
            $this->userInfo = AuthUserModel::getOne($userId, 'user');
        }
        return $this->userInfo;
    }

    /**
     * 验证设备用户绑定检查 原CheckDeviceBindUser方法
     *
     * @param $authType
     * @param $userId
     * @throws Exception
     */
    public function checkDeviceBindUser($authType, $userId)
    {
        $compuer = RelationComputerModel::getBindUser($this->deviceId);
        $this->userInfo = $userInfo = AuthUserModel::getOne($userId);

        //设备绑定用户
        $res = false;
        if ($compuer['IsBindUser'] == '1') {
            $userList = AuthUserModel::getBindUserList($this->deviceId);
            foreach ($userList as $row) {
                if ($userInfo['ID'] == $row['ID']) {
                    $res = true;
                }
            }
            cutil_php_log(['BindUser', $res, $userList], $this->logFileName);
            if ($res !== true && count($userList) > 0) {
                T(21120007, ['userName' => $this->params['userName']]);
            }
        }

        // 用户绑定设备
        $res = false;
        if ($userInfo['IsBindDevice'] > 0) {
            $deviceList = AuthUserModel::getBindDeviceList($userInfo['ID']);
            foreach ($deviceList as $row) {
                if ($row['DeviceID'] == $this->deviceId) {
                    $res = true;
                }
            }
            cutil_php_log(['BindDevice', $res, $deviceList], $this->logFileName);
            if ($res !== true) {
                $AuthParam = DictModel::getAll('AUTHPARAM');
                // 关闭自动绑定时，已绑定设备的账号，不能在其他设备认证
                $limit = ($AuthParam['UserBindDev'] != 1) ? 1 : ((int)$userInfo['UserBindDevNum'] ?: (int)$AuthParam['AutoBindUserNum']);
                if (count($deviceList) >= $limit) {
                    T(21120008, ['userName' => $this->params['userName']]);
                }
            }
        }

        // 分布式场景优先本地数据查询 再去DASM上查询
        if (get_server_type() === DEVTYPE_DASC && get_dasc_status()) {
            if ((int)$compuer['IsBindUser'] === 1 || $userInfo['IsBindDevice'] > 0) {
                // RPC远程调用DASM接口获取信息
                $manageIP = get_dasm_ip();
                $cfg = read_inifile(PATH_ETC . 'asc.ini');
                $rpcParams = [
                    'deviceID' => $this->deviceId,
                    'userID' => $userId,
                    'userName' => $this->params['userName'],
                    'authType' => $authType,
                    'qrlogin' => $this->params['qrlogin'],
                    'roamFlag' => $this->deviceInfo['RoamFlag'],
                    'AscID' => $cfg['AscID']
                ];
                $dataArr = lib_yar::clients('dasm', 'deviceBindUser', $rpcParams, $manageIP, 10 * 1000, 2);
                $this->writeLog('RPC调用deviceBindUser结果：' . json_encode($dataArr, JSON_UNESCAPED_UNICODE));
                if (!$dataArr['state']) {
                    throw new Exception($dataArr['message'], $dataArr['code']);
                }
                return;
            }
        }
    }
}
