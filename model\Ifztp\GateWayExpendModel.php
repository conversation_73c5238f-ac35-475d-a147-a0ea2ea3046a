<?php



class GateWayExpendModel extends BaseModel
{
    public const TABLE_NAME = 'TGateWayExpend';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'list'   => 'ID,GwID,RID,GwType,Type,Remark'
    ];

    /**
     * 获取新的资源信息,关联多个网关
     * **/

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";
        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }
        if (isset($cond['GwType'])) {
            $where .= "AND GwType = ".self::setData($cond['GwType']);
        }

        if (isset($cond['GwID'])) {
            $where .= "AND GwID = ".self::setData($cond['GwID']);
        }
        if (isset($cond['RID'])) {
            $where .= "AND RID in (".self::setArrayData($cond['RID']).")";
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
