<?php
/**
 * Description: 自助申请
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: SelfGuestAuth.php 165939 2021-12-28 13:12:36Z duanyc $
 */

$GLOBALS['LANG'][21130] = [
    21130001 => 'Authentication failed, guest self-service application parameter error!',
    21130002 => 'Guest user [{user}], device [IP:{ip}] generates guest internet code [{pw}] through self-service application, creation time is {nowTime}',
    21130003 => 'The request failed, the guest authentication method or the guest self-service application function has not been activated yet!',
    21130004 => 'Guest name',
    21130005 => 'Host name',
    21130006 => 'Guest unit',
    21130007 => 'Receptionist phone',
    21130008 => 'Guest phone',
    21130009 => 'Visiting reason',
    21130010 => 'Approval failed, you are offline!',
    21130011 => 'Apprize',
    21130012 => 'Terminal',
    21130013 => 'Employee [{userName}] denied guest access, denied time: {date}',
    21130014 => 'Employee [{userName}] approves guest access, the approval time is: {date}',
    21130015 => 'Authentication failed. The guest\'s independent application has not been approved or rejected!',
    21130016 => 'Authentication failed. The guest has applied for authentication independently. Please apply again!',
    21130017 => 'Authentication failed. The device does not exist or is deleted. Please apply again!',
    21130018 => 'The corresponding guest relationship record cannot be found!',
    ******** => 'The corresponding guest access record cannot be found!',
    ******** => 'Visitors cancel guest application,the cancel time is: {date}',
    ******** => 'Guest Code',
    ******** => 'Self Apply',
    ******** => 'QR Code',
    ******** => 'SMS',
    ******** => 'This email approval has expired and cannot receive visitors for network access',
    ******** => 'The approval of this email has been completed. Please do not repeat the approval!',
    ******** => 'Approval failed. Your account has been deleted!',
    ******** => 'The current account role is not within the range of assignable guest internet access code roles. Please contact the administrator!',
    ******** => 'The currently assigned network domain is not within the range of scene assignable and accessible network domains. Please contact the administrator!',
    ******** => 'Request failed, guest self-service application function has not been activated yet!',
    ******** => 'Approval failed, the application has been approved and completed!',
    ******** => 'Approval failed, the access method {accesstype} has not been enabled yet!',
    ******** => 'Unable to find the corresponding approver, please check if the name or phone number of the approver is correct!',
    ******** => 'Approval failed. We cannot proceed without security clearance',
    ******** => 'NO_AUTH',
    ******** => 'The request failed, the guest user was offline by the host or same device is re-authenticated in a different browser or client, please re-enter the network',
    ******** => 'The same device is re-authenticated in a different browser or client!'

];
