<?php

/**
 * Description: 认证账号
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: AccountModel.php 166879 2022-01-11 04:50:15Z duanyc $
 */

class AccountModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TAccount';
    public const PRIMARY_KEY = 'AccountID';
    protected static $columns = [
        'all'    => 'AccountID,user_id,auth_type,auth_name,auth_password,open_id,union_id,ServerID',
        'one'   => 'AccountID,user_id as ID,auth_type,auth_name',
        'json' => "CONCAT('{',group_concat('\"',auth_type,'\":1'),'}') as relation",
        'max' => 'max(AccountID) as MaxID',
    ];

    /**
     * 根据用户名获取账号数据
     * @param $Type
     * @param $UserName
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByUserName($Type, $UserName, $Column = 'one')
    {
        if (empty($UserName)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);

        if (!empty($Type)) {
            $where = 'WHERE auth_type = ' . self::setData($Type) . ' AND auth_name = ' . self::setData($UserName);
        } else {
            $where = 'WHERE auth_name = ' . self::setData($UserName);
        }

        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据openid获取账号数据
     * @param $openid
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByOpenId($openid, $Column = 'one')
    {
        if (empty($openid)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE open_id = ".self::setData($openid);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据unionid获取账号数据
     * @param $unionid
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByUnionId($unionid, $Column = 'one')
    {
        if (empty($unionid)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE union_id = ".self::setData($unionid);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 插入
     *
     * @param array $key_values
     *
     * @return int
     */
    public static function insert($key_values = [])
    {
        if (empty($key_values) || empty($key_values['auth_type'])) {
            return false;
        }

        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $dasmIp = get_pub_dasm_ip();
        $accountID = 0;
        if (!empty($dasmIp)) {
            try {
                $result = lib_yar::clients('duser', 'getAccountId', [], $dasmIp);
                if (!empty($result['state'])) {
                    $accountID = $key_values['AccountID'] = $result['data']['accountId'];
                    single_table_pub(static::TABLE_NAME, $key_values, 'insert');
                }
            } catch (Exception $e) {
                cutil_php_log(json_encode(['model_insert', $e->getMessage()]), "model_{$table['name']}");
            }
        }

        $result = lib_database::duplicate($key_values, $table['name'], $table['index']);
        cutil_php_log(json_encode(['model_insert', $table, $key_values]), "model_{$table['name']}");

        if ($result) {
            return $accountID ?: lib_database::insertId();
        }

        return $result;
    }

    /**
     * 修改
     *
     * @param string $type
     * @param string $userName
     * @param array $key_values
     *
     * @return boolean
     */
    public static function updateByUsername($type, $userName, $key_values = [])
    {
        if (empty($type) || empty($userName) || empty($key_values)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = " auth_type = ".self::setData($type)." AND auth_name = ".self::setData($userName);
        cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $key_values]), "model_{$table['name']}");
        return lib_database::update($key_values, $where, $table['name'], $table['index'], self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";
        if (isset($cond['user_id'])) {
            $where .= "AND user_id = " . self::setData($cond['user_id']);
        }

        if (isset($cond['auth_type'])) {
            $where .= "AND auth_type = " . self::setData($cond['auth_type']);
        }

        if (isset($cond['AccountID_in'])) {
            $where .= "AND AccountID in (" . self::setArrayData($cond['AccountID_in']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
