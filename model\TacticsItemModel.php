<?php

/**
 * Description: TNacAuthLog认证记录表
 * User: <EMAIL>
 * Date: 2021/05/12 09:21
 * Version: $Id: TacticsItemModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class TacticsItemModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TTacticsItem';
    public const PRIMARY_KEY = 'ItemID';
    protected static $columns = [
        '*'    => '*',
        'one'    => 'Content',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }

        if (isset($cond['ItemName'])) {
            $where .= "AND ItemName = ".self::setData($cond['ItemName']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
