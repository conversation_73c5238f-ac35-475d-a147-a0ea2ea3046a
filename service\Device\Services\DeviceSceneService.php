<?php

declare(strict_types=1);
/**
 * Description: 设备场景信息服务
 * User: renchen
 * Date: 2022/8/26 10:39
 * Version: $Id$.
 */

namespace Services\Device\Services;

use Common\Facades\SceneServiceFacade;
use Exception;
use Services\Common\Services\CommonService;

class DeviceSceneService extends CommonService
{
    /**
     * Redis缓存场景信息的前缀
     */
    public const REDIS_PREFIX_KEY = 'SceneInfo:';

    /**
     * 授权设备ID
     * @var int
     */
    private $permitDeviceId = 0;

    public function __construct()
    {
        parent::__construct();
        $this->logFileName = 'DevScene';
    }

    /**
     * 根据设备id获取设备场景信息
     * ps：数据有做缓存处理，如果后台增加，修改，删除场景信息需要删除掉场景相关的缓存 SceneInfo:xx
     * @param int $deviceId
     * @return array
     */
    public function getDevSceneInfo(int $deviceId, string $UserType = ''): array
    {
        try {
            $deviceId = $this->checkIsPermitDevice($deviceId);
            // 设备ID，终端类型，IP，终端所属部门，角色（目前确定的是这五种）
            $deviceInfo = \DeviceModel::getJoinRelationComputer($deviceId, 'relationScene');
            if (empty($deviceInfo)) {
                T(21103006);
            }
            $serverRange = '';
            // 获取当前设备所在的ASCID
            $devtype = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
            // 根据设备类型 如果是dasc dasm上请求取用
            if (!empty($devtype) && in_array(strtolower($devtype), ['dasm', 'dasc'])) {
                $serverRange = get_ini_info(PATH_ETC . 'deveth.ini.noback', 'ascid');
            }
            $this->writeLog('into usertype::' . var_export($UserType, true));
            $onlineDevice = \NacOnLineDeviceModel::getSingle(['DeviceID' => $deviceId, 'column' => 'role']);
            $UserType = ($onlineDevice['RoleID'] > 0 && $UserType === '') ? $deviceInfo['LastUserType'] : $UserType; // 如果前端没有传$UserType并且在线则使用上次认证类型
            // 不要修改key的名称，这里是grpc接口定义的
            $params = [
                'device_id' => $deviceId,
                'depart_id' => $deviceInfo['DepartID'],
                'dev_ip' => $deviceInfo['GateIP'] ?: $deviceInfo['IP'],
                'devtype_id' => $deviceInfo['Type'] . '-' . $deviceInfo['SubType'],
                'role_id' => $onlineDevice['RoleID'] ?? 0,
                'server' => $serverRange
            ];
            if ($UserType !== '') {
                $params['usertype'] = $UserType; // 当usertype不为空时候传给grpc,为空则不传表示根据以往逻辑处理
            } else {
                # 如果小助手版本号小于3746.R004,则默认使用员工场景usertype = 1
                if ((int)$deviceInfo['InstallClient'] == 1 && $deviceInfo['AgentVersion']) {
                    $version = $this->getRealEngine($deviceInfo['AgentVersion']);
                    if (strcmp($version, 'V6.0.6039.3746.R004') < 0) {
                        $this->writeLog('agent version:: ' . var_export($deviceInfo['AgentVersion'], true));
                        $this->writeLog('mapping version:: ' . var_export($version, true));
                        $params['usertype'] = 1;
                    }
                }
            }
            $this->writeLog('grpc param ::'.var_export($params, true));
            $cacheSceneKey = "";
            if (ENABLE_SCENE_CACHE) {
                $cacheSceneKey = $this->createSceneKey($params);
                $cacheSceneID = \lib_redis::get(self::REDIS_PREFIX_KEY, 'Device:'.$cacheSceneKey);
                // 应用对象无变化，无需重新获取
                if ($cacheSceneID && $cacheSceneInfo = \lib_redis::get(self::REDIS_PREFIX_KEY, 'Config:'.$cacheSceneID)) {
                    $this->return['data'] = json_decode($cacheSceneInfo, true);
                    // 是否安装控件或小助手，是否启用安检在具体规范中配置，修改不会通知重新加载场景配置，如果缓存了configId这里重新获取一下，没有就用缓存的值
                    if (isset($this->return['data']['agentInstallConfigId']) && $this->return['data']['agentInstallConfigId'] > 0) {
                        $this->return['data']['IsInstallClient'] = (int)\SceneDictModel::getValue($this->return['data']['agentInstallConfigId'], 'IsNeedActive', 'AgentConfig');
                    }
                    if (isset($this->return['data']['checkPolicyConfigId']) && $this->return['data']['checkPolicyConfigId'] > 0) {
                        $this->return['data']['IsSafeCheck'] = (int)\SceneDictModel::getValue($this->return['data']['checkPolicyConfigId'], 'IsSafeCheck', 'PolicyList');
                    }
                    $this->return['data']['IsInstallClient'] = $this->checkIsNeedInstallClient($this->return['data']['IsSafeCheck'], $this->return['data']['IsInstallClient']);
                    // 如果缓存中的场景id和数据库中的不一致，更新数据库中的场景id(设备下线缓存还在再次获取场景信息时维护数据库场景ID)
                    if ($deviceInfo['SceneID'] != $this->return['data']['SceneID'] || (isset($onlineDevice['SceneID']) && $onlineDevice['SceneID'] != $this->return['data']['SceneID'])) {
                        $this->updateDeviceScene($deviceId, (int)$this->return['data']['SceneID'], $cacheSceneKey);
                    }
                    $this->return['data']['DeviceID'] = $deviceId;
                    $this->writeLog('从缓存中获取场景信息:'.var_export($this->return['data'], true));
                }
            }

            // 没有开启缓存或者缓存中没有数据
            if (empty($this->return['data'])) {
                $params['param_seq'] = time(); // 请求序列号可用于日志跟踪等
                // 场景查询支持多设备查询，这里只需要查一台设备的也只需要返回有一台设备的相关场景(10s)
                $response = SceneServiceFacade::withOptions(['timeout' => 10*1000000])->QueryDeviceScene([$params]);
                $sceneConfig = $response[0] ?? [];
                // 这里先抛出异常，也可以返回默认场景 --修改为未获取场景
                if (empty($sceneConfig)) {
                    T(21103018);
                }
                $this->return['data'] = $this->getSceneConfig($sceneConfig);
                $this->return['data']['DeviceID'] = $deviceId;
                // 维护TRelationComputer与TNacOnlineDevice表的场景信息
                $this->updateDeviceScene($deviceId, (int)$this->return['data']['SceneID'], $cacheSceneKey);
                $this->writeLog('Grpc 获取的场景信息:'.var_export($this->return['data'], true));
            }
            $sceneInfosign = \SceneModel::getOne($this->return['data']['SceneID'], 'info');
            $this->writeLog('场景类型userType:' . $sceneInfosign['UserType']);
            // 场景需要审核则判断最近认证方式是否需要审核
            if ($sceneInfosign['UserType'] == 2 &&  $this->return['data']['IsAudit'] != 0 && $this->return['data']['DeviceID']) {
                $AuditAccessType = \SceneDictModel::getValue((int)$this->return['data']['SceneID'], 'AuditAccessType', 'GuestAuditConfig');
                $this->writeLog('来宾审核接入方式:' . $AuditAccessType);
                if ($AuditAccessType) {
                    $guestInfo = \GuestSelfApplyModel::getSingle(['DeviceID' => $this->return['data']['DeviceID']], "ID desc");
                    $this->writeLog('来宾最近认证ID:' . $guestInfo['ID']);
                    $guestInfo['AccessType'] = $guestInfo['AccessType'] ? fieldMapping($guestInfo['AccessType']) : '';
                    $this->writeLog('来宾最近认证方式情况:' . $guestInfo['AccessType']);
                    $this->return['data']['IsAudit'] = stripos($AuditAccessType, $guestInfo['AccessType']) !== false ? 1 : 0;
                } else {
                    $this->return['data']['IsAudit'] = 0;// 无接入方式需要审核
                }
            }
        } catch (Exception $e) {
            $this->recordErrorMessage($e);
            // 这里要转成系统内的错误码 211xxxxxx 为系统定义业务码
            $this->code = $e->getCode() < 21100000 ? 21103017 : $e->getCode();
        }

        return  $this->return;
    }

    /**
     * 根据场景ID获取场景配置详情
     * @param int $sceneId
     * @param null $Type
     * @return array
     */
    public function getSceneInfoById(int $sceneId, $Type = null): array
    {
        if (!$sceneId) {
            return [];
        }
        static $RoleInfos = null;
        if (\PHP_SAPI !== 'cli' && !empty($RoleInfos[$sceneId])) {
            return $RoleInfos[$sceneId];
        }
        $aResult = \SceneModel::getOne($sceneId, 'info');
        if (\is_array($aResult)) {
            $sceneDict = \SceneDictModel::getAll($sceneId);
            if (!\is_array($sceneDict)) {
                $sceneDict = [];
            }
            $default_roleTDict = \SceneDictModel::getAll(DEFAULT_SCENE_ID);
            foreach ($default_roleTDict as $key => $value) {
                $aResult[$key] = $sceneDict[$key] ?? $value;
            }
            foreach ($sceneDict as $key => $value) {
                if (!isset($aResult[$key])) {
                    $aResult[$key] = $value;
                }
            }
            $aResult ["CheckParam"] = $this->getSceneXmlInfo($aResult);

            if ((int)$aResult ["IsSafeCheck"] === 1 && (int)$aResult ["IsNeedActive"] === 0) {   // 如果要安检就必须装小助手
                $aResult["IsNeedActive"] = 2;
            }
            $aResult["IsAuth"] = $aResult["needAuth"] ?? 0;
            $aResult['GETTYPE'] = $Type ? "mobile" : "PC";
            $RoleInfos[$sceneId] = $aResult;
            // 查询group为 接入方式 入口,接待 审核配置
            $groupslist = ['AccessType', 'EntryConfig' ,'ReceiveConfig', 'GuestAuditConfig'];
            $itemNameList = ['GuestEntry' ,'receiveAccess', 'AccessType', 'allocationRegion', 'UserEntry', 'DefaultEntry', 'AuditAccessType', 'ReceptType'];
            $sceneInfo =  \SceneDictModel::getList(['ConfigID' => $sceneId , 'Groupslist' => $groupslist]);
            $resultSceneinfo = [];
            //初始化
            foreach ($itemNameList as $initk => $initv) {
                $resultSceneinfo[$initv] = '';
            }
            $resultSceneinfo['UserType'] = $aResult['UserType'];
            foreach ($sceneInfo as $k => $v) {
                if (in_array($v['ItemName'], $itemNameList)) {
                    $resultSceneinfo[$v['ItemName']] = $v['ItemValue'];
                }
            }
            // 判断来宾预约与团队接待是否有授权
            if (!getmoduleregist(31) && !empty($resultSceneinfo['receiveAccess'])) {
                $receiveAccessArray = explode(',', $resultSceneinfo['receiveAccess']);
                foreach ($receiveAccessArray as $k => $v) {
                    if ($v == 'IsNeedAppoint' || $v =='AllowGuestTeam') {
                        unset($receiveAccessArray[$k]);
                    }
                }
                $resultSceneinfo['receiveAccess'] = implode(',', $receiveAccessArray);
            }

            $isAuditConfig = \SceneDictModel::getOneConfig($sceneId, 'IsNeedAuto', 'DevAudit');
            $isAudit = $isAuditConfig['Config'] ?? 0;
            // 接待人可分配给来宾的安全域
            if (!empty($resultSceneinfo['allocationRegion'])) {
                $allocationRegionArr = explode(',', $resultSceneinfo['allocationRegion']);
                $resultSceneinfo['allocationRegionData'] = \IpRegionModel::getList(array("RIDs" => $allocationRegionArr, 'column' => 'one'));
            } else {
                $resultSceneinfo['allocationRegionData'] = '';
            }
            $resultSceneinfo['AuditAccessType'] = $isAudit == 1 ? $resultSceneinfo['AuditAccessType']  : "";
            $aResult['sceneinfo'] = $resultSceneinfo;
            return $aResult;
        }

        return [];
    }

    /**
     * 将角色信息从数组转为xml报文  原方法getRoleXmlInfo
     * @param array $sceneResult
     * @return string
     */
    private function getSceneXmlInfo(array $sceneResult): string
    {
        // 安检参数
        $xml = "<?xml version=\"1.0\" encoding=\"gbk\"?>";
        $xml .= "<CheckParam>";
        $xml .= "<CheckIntervalDay>" . $sceneResult ['CheckIntervalDay'] . "</CheckIntervalDay>";
        $xml .= "<IsSafeCheck>" . $sceneResult ['IsSafeCheck'] . "</IsSafeCheck>";
        $xml .= "<NoPassCutNet>" . $sceneResult ['NoPassCutNet'] . "</NoPassCutNet>";

        $xml .= "<ClientCheckTitle>" . $sceneResult ['ClientCheckTitle'] . "</ClientCheckTitle>";
        $xml .= "<InstallAssistant>" . $sceneResult ['InstallAssistant'] . "</InstallAssistant>";
        $xml .= "<AssistantName>" . $sceneResult ['AssistantName'] . "</AssistantName>";
        $xml .= "<HideAssistant>" . $sceneResult ['HideAssistant'] . "</HideAssistant>";
        $xml .= "<ReCheck>" . $sceneResult ['ReCheck'] . "</ReCheck>";
        $xml .= "<ReCheckTime>" . $sceneResult ['ReCheckTime'] . "</ReCheckTime>";
        $xml .= "<Disturb>" . $sceneResult ['Disturb'] . "</Disturb>";
        $xml .= "<IsUseMiniAuthWnd>" . get_column($sceneResult, 'IsUseMiniAuthWnd') . "</IsUseMiniAuthWnd>";
        $xml .= "<IsOpenNetworkBeforeLogon>" . get_column($sceneResult, 'IsOpenNetworkBeforeLogon') . "</IsOpenNetworkBeforeLogon>";
        $xml .= "<DelDevice>" . $sceneResult ['DelDevice'] . "</DelDevice>";
        //2013-03-27 yan add
        $xml .= "<AboutMessage>" . str_replace('{CopyrightTime}', '2006-'.date('Y'), $sceneResult ['AssistantContent']) . "</AboutMessage>";
        $xml .= "<AboutTitle>" . $sceneResult ['AssistantTitle'] . "</AboutTitle>";
        //end
        $xml .= "<Navigation>";
        $xml .= "<IsShow>0</IsShow>";
        $xml .= "</Navigation>";
        $xml .= "<AssExitBtnShow>" . $sceneResult ['AssExitBtnShow'] . "</AssExitBtnShow>";
        $xml .= "</CheckParam>";

        return $xml;
    }

    /**
     * 生成场景缓存的key值（MD5）.
     * @param array $params
     * @return string
     */
    private function createSceneKey(array $params): string
    {
        if (isset($params['usertype'])) {
            if (!isset($params['device_id'],$params['depart_id'],$params['dev_ip'],$params['devtype_id'],$params['role_id'],$params['server'])) {
                return '';
            }
            return md5($params['device_id'].$params['depart_id'].$params['dev_ip'].$params['devtype_id'].$params['role_id'].$params['server'].$params['usertype']);
        } else {
            if (!isset($params['device_id'],$params['depart_id'],$params['dev_ip'],$params['devtype_id'],$params['role_id'],$params['server'])) {
                return '';
            }
            return md5($params['device_id'].$params['depart_id'].$params['dev_ip'].$params['devtype_id'].$params['role_id'].$params['server']);
        }
    }

    /**
     * 更新设备场景信息，维护缓存信息.
     * @param int $deviceId
     * @param int $sceneId
     * @param string $sceneKey
     * @return void
     */
    public function updateDeviceScene(int $deviceId, int $sceneId, string $sceneKey): void
    {
        if (!$deviceId || !$sceneId) {
            return;
        }
        // 如果是访客，则不统计场景信息，将相关表数据重置为0 (陈盼)
        //        $isGuest = \GuestOnLineDeviceModel::getSingle(['DeviceID' => $deviceId,'column'=>'one']);
        //        if (!empty($isGuest)) {
        //            $sceneId = 0;
        //        }
        // 更新场景信息
        \NacOnLineDeviceModel::update($deviceId, ['SceneID' => $sceneId]);
        \RelationComputerModel::update($deviceId, ['SceneID' => $sceneId]);

        // 如果是授权接入的设备，由于是用授权设备获取的场景，这里更新一下原设备场景信息
        if ($this->permitDeviceId) {
            // 授权接入的设备没有写在线表，这里只维护TRelationComputer
            \RelationComputerModel::update($this->permitDeviceId, ['SceneID' => $sceneId]);
        }

        if (ENABLE_SCENE_CACHE && !empty($sceneKey) && $sceneId) {
            // 维护缓存信息(缓存3天)
            $expireTime = 3 * 24 * 3600;
            $uniqueKey = \lib_redis::get(self::REDIS_PREFIX_KEY, 'Config:'.$sceneId);
            if (!$uniqueKey) {
                return;
            }
            \lib_redis::set(self::REDIS_PREFIX_KEY, 'Device:' . $sceneKey, $sceneId.'_'.$uniqueKey, $expireTime);
        }
    }

    /**
     * 获取场景配置信息
     * @param array $data
     * @return array
     * @throws Exception
     */
    private function getSceneConfig(array $data): array
    {
        // 从场景服务中获取的配置信息去查询(根据grpc接口返回的值进行加工)
        if (!empty($data)) {
            // TSceneDict  ItemName: needAuth  Groups: AuthConfig 默认身份认证设置，0：不开启认证，1开启认证
            // TSceneDict  ItemName: IsNeedActive  Groups: AgentConfig 是否安装控件或小助手0:无需安装任何控件或者程序，1:要求安装安全控件,2:要求安装小助手程序
            // TSceneDict  ItemName: IsAccess  Groups: DevAccess 允许接入设置，0：禁止接入，1：允许接入
            // TSceneDict  ItemName: IsNeedReg  Groups: DevReg 默认注册设置,0:自动注册，1：手功注册
            // TSceneDict  ItemName: IsNeedAuto  Groups: DevAudit 默认审核设置,0:自动审核，1：手功审核
            // TSceneDict  ItemName: PolicyID  Groups: PolicyList 安检规范ID
            // TSceneDict  ItemName: IsSafeCheck  Groups: PolicyList 是否启用安检
            $return = [
                'SceneID' => $data['sceneId'],
                'SceneName' => $data['sceneName'],
                'Priority' => $data['priority'],
                'IsAccess' => isset($data['accessConfigId']) ? (int)\SceneDictModel::getValue($data['accessConfigId'], 'IsAccess', 'DevAccess') : 1,
                'IsRegistered' => (int)\SceneDictModel::getValue($data['deviceRegConfigId'], 'IsNeedReg', 'DevReg'),
                'IsAudit' => (int)\SceneDictModel::getValue($data['auditConfigId'], 'IsNeedAuto', 'DevAudit'),
                'PolicyID' => (int)\SceneDictModel::getValue($data['checkPolicyConfigId'], 'PolicyID', 'PolicyList'),
                'IsInstallClient' => (int)\SceneDictModel::getValue($data['agentInstallConfigId'], 'IsNeedActive', 'AgentConfig'),
                'IsSafeCheck' => (int)\SceneDictModel::getValue($data['checkPolicyConfigId'], 'IsSafeCheck', 'PolicyList'),
                'checkPolicyConfigId' => $data['checkPolicyConfigId'],
                'agentInstallConfigId' => $data['agentInstallConfigId']
              ];
            $accessType = isset($data['accessTypeId']) ? \SceneDictModel::getValue($data['accessTypeId'], 'AccessType', 'AccessType') : '';
            $sceneInfosign = \SceneModel::getOne($data['sceneId'], 'info');
            if ((int)$sceneInfosign['UserType'] === 2) {
                if (stripos($accessType, 'NoAuth') !== false) {
                    $return['IsAuth'] = 0;
                } else {
                    $return['IsAuth'] = 1;
                }
            } else {
                // 是否开启身份认证，是否注册，是否审核，策略ID都是在场景编辑页面配置，修改都会通知重新加载场景配置
                $return['IsAuth'] = (int)\SceneDictModel::getValue($data['authConfigId'], 'needAuth', 'AuthConfig');
            }
        } else {
            // 没有获取到场景信息，给出默认的场景配置 (当前不会走到这个逻辑先保留这部分代码)
            $sceneInfo = \SceneModel::getSingle(['Priority' => 999, 'column' => 'info']);
            if (empty($sceneInfo)) {
                T(21103017);
            }

            $sceneConfig = \SceneModel::getDefaultSceneConfig();
            $return = [
                'SceneID' => $sceneInfo['SceneID'],
                'SceneName' => $sceneInfo['SceneName'],
                'Priority' => $sceneInfo['Priority'],
                'IsAccess' => (int)self::getSceneValue($sceneConfig, 'IsAccess', 'DevAccess'),
                'IsAuth' => (int)self::getSceneValue($sceneConfig, 'needAuth', 'AuthConfig'),
                'IsInstallClient' => (int)self::getSceneValue($sceneConfig, 'IsNeedActive', 'AgentConfig'),
                'IsRegistered' => (int)self::getSceneValue($sceneConfig, 'IsNeedReg', 'DevReg'),
                'IsAudit' => (int)self::getSceneValue($sceneConfig, 'IsNeedAuto', 'DevAudit'),
                'PolicyID' => (int)self::getSceneValue($sceneConfig, 'PolicyID', 'PolicyList'),
                'IsSafeCheck' => (int)self::getSceneValue($sceneConfig, 'IsSafeCheck', 'PolicyList'),
            ];
        }

        // 如果场景配置中要安检，不要安装小助手，则纠正配置，以需要安检为最高优先级
        // 场景化适配修改之后没有控件模式，接口IsInstallClient取值范围调整为0,1
        $return['IsInstallClient'] = $this->checkIsNeedInstallClient($return['IsSafeCheck'], $return['IsInstallClient']);

        // 写入缓存
        if (ENABLE_SCENE_CACHE && !empty($data)) {
            $cacheSceneInfo = json_encode($return, JSON_UNESCAPED_UNICODE);
            $uniqueKey = \lib_redis::get(self::REDIS_PREFIX_KEY, 'Config:'.$data['sceneId']) ?: uniqid();

            $expireTime = 3 * 24 * 3600;
            \lib_redis::set(self::REDIS_PREFIX_KEY, 'Config:'.$data['sceneId'], $uniqueKey, $expireTime);
            \lib_redis::set(self::REDIS_PREFIX_KEY, 'Config:'.$data['sceneId'].'_'.$uniqueKey, $cacheSceneInfo, $expireTime);
        }

        return $return;
    }

    /**
     * 获取指定场景指定配置
     * @param int $sceneID 指定场景ID
     * @param string $ItemName 指定配置名
     * @param string $Groups 指定配置分类
     * @return array|bool
     */
    public function getSceneInfoOne(int $sceneID, string $ItemName, string $Groups = '')
    {
        return \SceneDictModel::getOneConfig($sceneID, $ItemName, $Groups);
    }

    /**
     * 获取场景配置项的值
     * @param array $sceneConfig
     * @param string $itemName
     * @param string $groups
     * @return string
     */
    private static function getSceneValue(array $sceneConfig, string $itemName, string $groups): string
    {
        foreach ($sceneConfig as $item) {
            if (isset($item['ItemName'],$item['Groups']) && $item['ItemName'] === $itemName && $item['Groups'] === $groups) {
                return $item['ItemValue'];
            }
        }

        return '';
    }

    /**
     * 安全规范是否包含补丁安检项
     * @param int $policyID 安全规范ID
     * @return string
     */
    public function patchIsInPolicy(int $policyID): string
    {
        $policyConfig = \NacPolicyListModel::getOne($policyID, 'info');
        if ($policyConfig && $policyConfig['PolicyBody']) {
            if (OS_TYPE === OSTYPE_LINUX) {
                // 国产化无授权则不展示
                if (!getmoduleregist(35)) {
                    return '0';
                }
                $checkStr = '<InsideName>CheckPatch_linux</InsideName>';
            } else {
                $checkStr = '<InsideName>CheckPatch</InsideName>';
            }
            if (strpos($policyConfig['PolicyBody'], $checkStr) !== false) {
                return "1";
            }
        }
        return "0";
    }

    /**
     * 检查是否是授权接入设备
     * 如果是授权接入的设备，则通过授权设备查找场景信息
     * @param int $deviceID
     * @return int
     */
    private function checkIsPermitDevice(int $deviceID): int
    {
        if ($deviceID <= 0) {
            return $deviceID;
        }

        $res = \PermitDeviceModel::getSingle(['DeviceID' => $deviceID, 'column' => 'one']);
        if (!empty($res) && isset($res['BindID'])  && (int)$res['BindID'] > 0) {
            $this->permitDeviceId = $deviceID;

            return (int)$res['BindID'];
        }
        return  $deviceID;
    }

    /**
     * 检查是否需要安装客户端
     * @param int $isSafeCheck 是否需要安检
     * @param int $isInstallClient 是否需要安装客户端，来自与控件安装配置：0:无需安装任何控件或者程序，1:要求安装安全控件,2:要求安装小助手程序
     * @return int
     */
    private function checkIsNeedInstallClient(int $isSafeCheck, int $isInstallClient): int
    {
        // 如果场景配置中要安检，不要安装小助手，则纠正配置，以需要安检为最高优先级
        // 场景化适配修改之后没有控件模式，接口IsInstallClient取值范围调整为0,1
        if ($isInstallClient === 1) {
            return 1;
        }
        if ($isSafeCheck === 1 || $isInstallClient === 2) {
            return 1;
        }
        return 0;
    }

    /**
     * 通过小助手上报的版本号获取内部的开发版本号
     * 从3746.R004开始版本号分为内部版本号和外部版本号，使用TSystemEngine进行记录，内部版本号用于开发和升级管理，外部版本号用于系统界面显示以及小助手上报
     *  内部版本号                外部版本号
     * V6.0.6039.3746.R001      V6.0.6039.3746.R001
     * V6.0.6039.3746.R002      V6.0.6039.3746.R002
     * V6.0.6039.3746.R003      V6.0.6039.3746.R003
     * V6.0.6039.3746.R004      V6.0.6039.3746.LTS02
     * @return string
     */
    private function getRealEngine($AgentVersion): string
    {
        $version = strtoupper($AgentVersion);
        # 兼容移动端 6.0.6039.3746.LTS02
        if (strpos($version, 'V') !== 0) {
            $version = 'V'.$version;
        }
        # 兼容移动端 V6.0.6039.3746.LTS02.04
        $versionArr = explode('.', $version);
        if (count($versionArr) > 5) {
            $version = implode('.', array_slice($versionArr, 0, 5));
        }
        $engine = \SystemEngineModel::getOneByShowEngine($version);
        if ($engine) {
            return $engine['Engine'];
        }
        return $AgentVersion;
    }
}
