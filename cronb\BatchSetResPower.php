<?php
/**
 * Description: vpn心跳检测
 * User: <EMAIL>
 * Date: 2022/7/19 17:07
 * Version: $Id$
 */

/* 定义关键常量 */
define('PATH_CUR', __DIR__);

/** 初始化 */
require PATH_CUR . '/../init.php';


class BatchSetResPower
{
    /**
     * 执行
     * @throws Exception
     */
    public function handle(): void
    {
        if (!getmoduleregist(11)){
            cutil_php_log("BatchSetResPower start!没有零信任授权、不需要执行", 'cronb');
            return;
        }
        echo 'BatchSetResPower start:' . PHP_EOL;
        cutil_php_log("BatchSetResPower start!", 'cronb');
        //循环获取在线token，刷新对应的权限控制
        $powerService = ResourceServiceProvider::initResourceService('Power');
        $powerService->batchSetUserPower();
        cutil_php_log("BatchSetResPower handle finish!", 'cronb');
    }
}

try {
    $obj = new BatchSetResPower();
    $obj->handle();
} catch (Exception $e) {
    echo "code: " . $e->getCode() . ", message: ". $e->getMessage();
}
