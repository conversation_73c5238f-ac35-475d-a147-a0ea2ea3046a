<?php
/**
 * Description: 设备信息ip获取mac服务
 * User: <EMAIL>
 * Date: 2021/07/2 15:53
 * Version: $Id: DeviceInfoIpGetMacService.php 167484 2022-01-17 09:32:45Z renchen $
 */

namespace Services\Device\Services;

class DeviceInfoIpGetMacService extends DeviceInfoService
{
    /**
     * 判断设备是否存在.
     *
     * @return mixed
     * @throws \Exception
     */
    public function isDeviceExist()
    {
        $result = $this->isDeviceExistByIpGetMac();

        if ($result === false) {
            $this->writeLog('通过['.$this->params['gettype'].']方式，没有获取到设备信息');
        }
        return $result;
    }

    /**
     * 通过IP获取Mac判断是否是否存在.
     *
     * @return bool|mixed
     * @throws \Exception
     */
    private function isDeviceExistByIpGetMac()
    {
        try {
            $DevIp = $this->params['linkIP'];
            $this->mac = $mac = $this->getMacByIp($DevIp);

            if (!$mac) {
                $this->writeLog("isDeviceExistByIpGetMac调用getMacByIp没有取到Mac", 'ERROR');
                return false;
            }

            if ($this->isDeviceExistByMac($mac)) {
                return true;
            }

            // 外网用伪造hard
            if (IS_INTERNAL) {
                $hard = $mac;
            } else {
                $hard = $this->params['fakeHard'] ?: $mac;
            }
            $getDeviceIDRes = $this->deviceOperationService->getDeviceID($hard, $mac, $DevIp);
            $resDeviceId = $getDeviceIDRes['code'] === 1 ? $getDeviceIDRes['data']['DeviceID'] : 0;
            $this->writeLog("根据{$hard},{$mac},{$DevIp}查询到的设备id：".var_export($resDeviceId, true));

            if (!is_numeric($resDeviceId) || (int)$resDeviceId < 0) {
                $this->writeLog("getMacByIp调用getDeviceID服务查询出错：{$mac}, {$mac}, {$DevIp}", 'ERROR');

                return false;
            }

            if ((int)$resDeviceId > 0) {
                $this->deviceId = (int)$resDeviceId;
                //判断是否漫游设备，如果是则把数据漫游到本地
                $this->changeRoamToLocal([
                    'Ip' => $DevIp,
                    'Mac' => $this->mac,
                    'BrowserFingerprint' => $this->params['BrowserFingerprint']
                ]);
                $this->writeLog("通过ip获取Mac判断设备存在：DeviceID=" . $this->deviceId);
                if (!$this->devExistButIPChange) {
                    $res = \DeviceModel::getOne($this->deviceId, 'ip');
                    if (is_array($res) && $res['IP'] !== $DevIp) {
                        $this->devExistButIPChange = 1;
                    }
                }
                // 如果ip变化了，这里需要更新一下
                if ($this->devExistButIPChange) {
                    $updateData = array(
                        'IP' => $DevIp,
                        'DeviceID' => $this->deviceId,
                        'TComputer' => array(
                            'GateIP' => $DevIp
                        ),
                    );
                    $isNeedUpdatePolicyVersion = false;
                    if (!empty($this->params['ascid'])) {
                        $updateData['TDevice']['AscID'] = $this->params['ascid'];
                        $deviceInfo = \DeviceModel::getOne($this->deviceId, 'ascID');
                        if (is_array($deviceInfo) && $deviceInfo['AscID'] !== $this->params['ascid']) {
                            $isNeedUpdatePolicyVersion = true;
                        }
                    }
                    $this->deviceOperationService->updateDevice($updateData);
                    $this->writeLog("设备IP变化更新IP为：".$DevIp);
                    if ($isNeedUpdatePolicyVersion) {
                        $this->updatePolicyVersion($this->deviceId, 'IpGetMac:AscID');
                    }
                }
                $this->updateDeviceType($this->deviceId, $this->params['deviceType'], '-ipgetmac');

                return true;
            }

            if (0 === (int)$resDeviceId) {
                // 非本地管理器校验漫游设备的指纹信息识别设备，漫游设备直接放开网络
                if ($this->isDeviceExistByWalkAsm($mac)) {
                    $this->isWalkDevice = 1;
                    return true;
                }
                if (get_server_type() === 'dasc' && get_dasc_status() /*&& $this->aQuery['roamflag'] == '1'*/) {
                    /* 如果当时设备是DASC, 并且和ASM联动完成，尝试从ASM获取ASM信息 20191126 李湘要求去掉漫游标记 asc升级dasc*/
                    $manageIP = get_dasm_ip();
                    if ($manageIP) {
                        $this->writeLog("当前是DASC设备，尝试通过无客户端设备漫游使用浏览器指纹从ASM上获取设备信息,manageIP：" . $manageIP);
                        $this->params['manageIP'] = $manageIP;
                        $this->params['Ip'] = $DevIp;
                        $this->params['Mac'] = $this->mac;
                        $result = $this->roamDevInsertDasc($this->params);
                        if ($result['code'] === 1) {
                            return true;
                        }
                    }
                }
                if (!$this->insertDeviceByIpGetMac($DevIp, $mac, $hard)) {
                    return false;
                }
                $this->isNewDevice = 1;
                $this->writeLog("通过ip获取Mac判断设备不存在，插入设备：新设备DeviceID=".$this->deviceId);

                return true;
            }
        } catch (\Exception $e) {
            $this->writeLog('isDeviceExistBygetMacByIp 出错了，错误信息为：'.$e->getMessage().PHP_EOL.'错误行数：'.$e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 根据设备漫游信息获取设备信息
     * @param string $mac mac地址
     * @return bool
     */
    private function isDeviceExistByWalkAsm(string $mac): bool
    {
        try {
            // 校验设备是否开启设备漫游,没有开启设备漫游走伪造mac逻辑
            $path = PATH_ETC . "WalkASM.ini";
            $aWalkAsm = is_file($path) ? read_inifile($path) : [];
            if (isset($aWalkAsm['OpenWalk']) && $aWalkAsm['OpenWalk'] === '1') {
                // 根据mac查询TOtherAsmRegion是否看是否放通网络
                if (!RelativelyIsFake($mac)) {
                    $res = \OtherAsmRegionModel::getList(['MAC' => $mac, 'column' => 'all'], false, 0, 100);
                    $this->writeLog('根据Mac#'.$mac.'查到的漫游设备数据：'.json_encode($res, JSON_UNESCAPED_UNICODE));
                    if ($res) {
                        foreach ($res as $v) {
                            // 放开网络并修改漫游设备的ip
                            $this->walkAsmOpen($v['IP'], $v['IPListId'], $mac, $v['AsmIP'], $v['DeviceID']);
                        }
                        return true;
                    }
                } else {
                    $this->writeLog('OtherAsm伪造Mac#'.$mac.'不查询漫游设备数据！');
                }

                // 存在指纹信息对比指纹信息,读取漫游的服务器信息
                /* 浏览器指纹太不靠谱了   先切掉！！！
                if ($this->params['BrowserFingerprint'] && isset($aWalkAsm['OtherIP']) && $aWalkAsm['OtherIP']) {
                    $aIp = explode(",", $aWalkAsm['OtherIP']);
                    foreach ($aIp as $item) {
                        // 对比指纹查询文件
                        $path = PATH_HTML . "/walkasm/{$item}/browserFingerPrint.json";
                        $browserFinger_arr = file_exists($path) ? json_decode(file_get_contents($path), true) : false;
                        if (isset($browserFinger_arr['BrowserFingerprint'][$this->params['BrowserFingerprint']])) {
                            // 放开网络并修改漫游设备的ip
                            $this->walkAsmOpen($this->params['linkIP'], 1, $mac, $item, $browserFinger_arr['BrowserFingerprint'][$this->params['BrowserFingerprint']]);
                            return true;
                        }
                    }
                }*/
            } else {
                $this->writeLog("未开启漫游");
                return false;
            }
        } catch (\Exception $e) {
            $this->writeLog('isDeviceExistByWalkasm 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 漫游放开网络并修改漫游服务器的ip
     * @param $linkIP
     * @param $IPListId
     * @param $mac
     * @param $asmIP
     * @param $DeviceID
     * @return bool
     * @throws \Exception
     */
    private function walkAsmOpen($linkIP, $IPListId, $mac, $asmIP, $DeviceID): bool
    {
        // 放通网络并修改ip
        $aResult = \DevASCInfoModel::getAll();
        foreach ($aResult as $v) {
            $cmd = PATH_ASM . "sbin/set_client_one {$linkIP} {$IPListId} {$mac} ";
            cutil_exec_no_wait($cmd, 10, $v['LinkIP']);
        }

        $post_data = array(
            'ip' => $linkIP,
            'mac' => $mac,
            'DeviceID' => $DeviceID
        );
        // 通知漫游服务器修改设备ip
        \lib_yar::clients('dasm', 'setDevInfo', $post_data, $asmIP);
        return true;
    }
}
