<?php

/**
 * Description: DhcpToDevice
 * User: <EMAIL>
 * Date: 2021/05/25 10:32
 * Version: $Id
 */

class DhcpToDeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDhcpToDevice';
    public const PRIMARY_KEY = 'TDhcpID';
    protected static $columns = [
        'one'  => 'Mac',
        '*'    => '*',
    ];

    /**
     * 根据IP获取最后条
     *
     * @param string $IP
     * @param string $Column
     *
     * @return mixed
     */
    public static function getOneByIp(string $IP, $Column = 'one')
    {
        if (empty($IP)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns[$Column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE IP = ".self::setData($IP)." ORDER BY LeaseStartTime DESC ";
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if(isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if(isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
