<?php
/**
 * Description: 针对和MSEP对接的签名SDK
 * User: <EMAIL>
 * Date: 2024/04/07 17:02
 */

class cls_msep
{
    private $secretKey;

    public function __construct($secretKey = "")
    {
        $this->secretKey = $secretKey ?: "62e09a8d5e9b497d006f2413aeae753abe649dd9";
    }

    /**
     * @description 生成签名
     * @param $timestamp //时间戳
     * @param $data //加密数据
     * @return string
     */
    public function genSign($timestamp, $data)
    {
        // Concatenate timestamp and data
        $message = $timestamp . $data;

        // Generate HMAC-SHA256 hash
        return hash_hmac('sha256', $message, $this->secretKey);
    }

    /**
     * @description 验证签名
     * @param $timestamp
     * @param $data
     * @param $signature
     * @param $expiryTime
     * @return bool
     */
    public function verifySign($timestamp, $data, $signature, $expiryTime = 300)
    {
        // Check if timestamp is within expiry time
        if (abs(time() - $timestamp) > $expiryTime) {
            return false;
        }

        // Generate HMAC-SHA256 hash using provided data and timestamp
        $expectedSignature = $this->genSign($timestamp, $data);

        // Compare generated signature with provided signature
        if (hash_equals($expectedSignature, $signature)) {
            return true;
        }

        return false;
    }

}
