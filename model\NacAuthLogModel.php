<?php

/**
 * Description: TNacAuthLog认证记录表
 * User: <EMAIL>
 * Date: 2021/05/12 09:21
 * Version: $Id: NacAuthLogModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class NacAuthLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TNacAuthLog';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        '*'    => '*',
        'one'  => 'RoleID',
        'twoFactor'  => 'RID, DeviceID, AuthCode',
        'offline'    => 'RID, OffLineTime',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['RID'])) {
            $where .= "AND RID = ".self::setData($cond['RID']);
        }

        if (isset($cond['RIDs'])) {
            $where .= "AND RID IN (" . self::setArrayData($cond['RIDs']) . ")";
        }

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        if (isset($cond['IsSuccess'])) {
            $where .= "AND IsSuccess = ".self::setData($cond['IsSuccess']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
