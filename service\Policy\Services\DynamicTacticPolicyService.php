<?php
/**
 * Description: 零信任动态策略项服务
 * User: <EMAIL>
 * Date: 2024/04/10 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use AuthServiceProvider;
use AuthUserModel;
use Common\Facades\EventBrokerFacade;
use DepartModel;
use DeviceTokenRedis;
use DynamicPolicyRedis;
use DynamicSafeHistoryModel;
use DynamicSafePolicyModel;
use DynamicSafePolicyRelationModel;
use DynamicSafeResultModel;
use Exception;
use LoginServiceProvider;
use NacOnLineDeviceModel;
use ResultRedis;
use Services\Policy\Interfaces\PolicyServiceInterface;
use SessionRedis;
use Srv\Nacservice\V1\Device;
use SystemServiceProvider;
use TokenRedis;

class DynamicTacticPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**todo 参数组合：A&B
     * A：感染病毒/病毒类型 包含 木马；
     * B：感染病毒/病毒类型 包含 木马、蠕虫。病毒数量大于10（实际数量2）；
     * C: 感染病毒/病毒数量 大于 10（实数量11）；
     */
    protected $resultMessage = '';
    protected $resultTypeMap = [
        'IDT_ADWARE' => '广告软件', 'IDT_MALWARE' => '恶意程序', 'IDT_UNKNOWN' => '未知病毒', 'IDT_VIRWARE' => '病毒软件', 'IDT_RISKWARE' => '风险软件',
        'IDT_TROJWARE' => '木马', 'IDT_BLACKLIST' => '自定义病毒', 'IDT_UNKNOWN_M' => '恶意对象', 'IDT_ATTACHMENT' => '邮件附件',
        'AntiVirusType' => '病毒类型', 'AntiVirusNum' => '病毒数量', 'IDT' => '病毒感染/'
    ];
    protected $operatorMap = ['>' => '大于', '=' => '等于', '<' => '小于', '<=' => '小于等于', '>=' => '大于等于', '!=' => '不等于', 'all' => '全部'];

    protected $cutOffDeviceArr = [];

    protected $ClearTokenRedisKey = 'Clear';

    /**
     * 覆盖父级初始化函数
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        $this->params = $params;
        static $devices = null;
        //避免rpc调用时批量恢复策略
        if (!empty($this->params['DeviceID']) && intval($this->params['DeviceID']) > 0 && empty($devices[$this->params['DeviceID']])) {
            $device = \DeviceModel::getJoinComputer($this->params['DeviceID'], 'policy');
            if (empty($device)) {
                T(21103006);
            }
            $devices[$this->params['DeviceID']] = $device;
            $this->device = $devices[$this->params['DeviceID']];
        } else {
            $this->device = [];
        }
        $this->cutOffDeviceArr = [];
    }

    /**
     * 动态策略检查是否通过,true 通过，false不通过
     *
     * @return bool
     * @throws Exception
     */
    public function check(): bool
    {
        //cutil_php_log('DynamicPolicy check start params:==》' . json_encode($this->params), 'DynamicPolicy');
        //需要安装客户端
        if (empty($this->device['InstallClient']) && empty($this->params['InstallClient'])) {
            return true;
        }
        // 策略ID与设备ID不能为空
        if (empty($this->params['DeviceID']) || empty($this->params['PolicyID'])) {
            return true;
        }
        $this->params['SessionStatus'] = 0;
        if (!empty($this->params['session'])) {
            $this->params['Uuid'] = $this->params['session']['Uuid'] ?? 0;
            $this->params['UserName'] = $this->params['session']['UserName'] ?? 0;
            if (empty($this->params['Token'])) {
                $this->params['Token'] = $this->params['session']['Token'] ?? '';
            }
            $this->params['From'] = isset($this->params['session']['From']) ? $this->params['session']['From'] : '';
            $this->params['AuthTime'] = $this->params['session']['AuthTime'] ?? time();
            $this->params['SessionStatus'] = $this->params['session']['SessionStatus'] ?? 2;
        }

        // 通过资源策略ID，查询到关联该策略ID的动态策略ID（TDynamicSafePolicyRelation通过该表）
        $dPolicyIDList = DynamicSafePolicyRelationModel::getList(['PolicyID' => $this->params['PolicyID']]);
        if (empty($dPolicyIDList)) {
            return true;
        }
        $result = true;
        $dPolicyIDArr = array_column($dPolicyIDList, 'DPolicyID');
        // 通过获取到的动态策略ID，拿到对应的策略详情，配置参数及运算表达式（TDynamicSafePolicy表）
        $dPolicyInfoList = DynamicSafePolicyModel::getList(['DPolicyID' => $dPolicyIDArr, 'Status' => 2]);
        try {
            // 循环动态策略列表，执行匹配，计算A&B...
            if (!empty($dPolicyInfoList)) {
                foreach ($dPolicyInfoList as $dPolicyInfo) {
                    // 如果状态是不是启用的话，则直接跳过，不生效
                    if ($dPolicyInfo['Status'] != 2) {
                        continue;
                    }
                    $this->resultMessage = '';
                    //所有动态策略表达式合规才算通过
                    $tmpResult = $this->checkDPolicy($dPolicyInfo);
                    $result = $tmpResult && $result;
                }
            }
        } catch (Exception $exception) {
            cutil_php_log('DynamicTacticPolicyService err::' . $exception->getTraceAsString(), 'DynamicPolicy');
        }

        if (!empty($this->cutOffDeviceArr)) {
            $this->cutOffDevice(array_unique($this->cutOffDeviceArr));
        }
        return $result;
    }

    /**
     * 单个动态策略计算
     * @return false
     * @throws Exception
     */
    function checkDPolicy($dPolicyInfo): bool
    {
        $result = true;
        // 判断是否存在匹配记录 TDynamicSafeResult
        $dynamicSafeResultList = DynamicSafeResultModel::getList(['DPolicyID' => $dPolicyInfo['DPolicyID'], 'NoDelStatus' => 1, 'DeviceID' => $this->params['DeviceID']]);
        //获取redis中的缓存值，如果没有需要继续匹配一次,如果当前策略id可能没有缓存，但是其他策略有缓存结果不能覆盖，此处先查询缓存
        $dynamicPolicyCache = DynamicPolicyRedis::getone($this->params['DeviceID']);
        !is_array($dynamicPolicyCache) && $dynamicPolicyCache = json_decode($dynamicPolicyCache, true);
        if (!empty($dynamicSafeResultList)) {
            $dynamicSafeResult = $dynamicSafeResultList[0];
            //恢复中的状态处理
            if ($dynamicSafeResult['Status'] == 2) {
                if (date('Y-m-d H:i:s') < $dynamicSafeResult['IgnoreTime']) {
                    // 将状态true 存储到 ASM_DynamicPolicy redis key中
                    $dynamicPolicyCache[$dPolicyInfo['DPolicyID']] = true;
                    DynamicPolicyRedis::setOne($this->params['DeviceID'], $dynamicPolicyCache);
                } else {
                    // 开始匹配
                    $result = $this->checkDPolicyCalculate($dPolicyInfo, $dynamicPolicyCache, $dynamicSafeResult);
                }
            } else {
                // 判断记录对应的IsNeedMatch是否为1
                if ($dynamicSafeResult['IsNeedMatch'] == 1 || !isset($dynamicPolicyCache[$dPolicyInfo['DPolicyID']])) {
                    $result = $this->checkDPolicyCalculate($dPolicyInfo, $dynamicPolicyCache, $dynamicSafeResult);
                } else {
                    // 返回ASM_DynamicPolicy 中的结果
                    $result = $dynamicPolicyCache[$dPolicyInfo['DPolicyID']];

                    // 这种情况下需要继续处理处置动作中的退出登录以及退出所有设备
                    $dealStatus = $dynamicSafeResult['DealStatus'] ?? [];
                    is_string($dealStatus) && $dealStatus = json_decode($dealStatus, true);
                    $dealMethods = $dPolicyInfo['DealMethods'] ?? [];
                    is_string($dealMethods) && $dealMethods = json_decode($dealMethods, true);
                    $this->logOutAction($dealMethods, $dealStatus);

                    // 修改CalculateTime
                    $updateDateArr['CalculateTime'] = date('Y-m-d H:i:s');

                    //退出后切换账号，需要同步修改匹配中对应的账号信息
                    $updateDateArr['UserId'] = $this->params['Uuid'] ?? $dynamicSafeResult['UserId'];
                    if (!empty($dealStatus)) {
                        $updateDateArr['DealStatus'] = json_encode($dealStatus);
                    }
                    \DynamicSafeResultModel::update($dynamicSafeResult['ResultID'], $updateDateArr);
                }
            }
        } else {
            // 开始匹配计算
            $result = $this->checkDPolicyCalculate($dPolicyInfo, $dynamicPolicyCache, []);
        }

        return $result;
    }

    /**
     * 动态策略参数匹配
     * @throws Exception
     */
    function checkDPolicyCalculate($dPolicyInfo, $dynamicPolicyCache, $dynamicSafeResult): bool
    {
        $ret = true;
        // 最新的设备结果存储在hash ASM_Result:10，10为设备ID，AntiVirus：[{AntiVirusType：'Riskware',AntiVirusNum:''}]
        $deviceResult = ResultRedis::getone($this->params['DeviceID']);
        if (!is_array($deviceResult) && !empty($deviceResult)) {
            $deviceResult = json_decode($deviceResult, true);
        }

        // 目前因为只有一个病毒条件，故将该结果提前获取放此处，后续涉及到多个数据来源，将AntiVirus替换为对应的key变量，放入getParamKeyResultMap函数中的参数循环中处理
        $deviceResult = $deviceResult['AntiVirus'] ?? [];
        if (!is_array($deviceResult) && !empty($deviceResult)) {
            $deviceResult = json_decode($deviceResult, true);
        }

        $dealMethods = $dPolicyInfo['DealMethods'];
        !is_array($dealMethods) && $dealMethods = json_decode($dealMethods, true);

        $paramList = $dPolicyInfo['ParamList'];
        !is_array($paramList) && $paramList = json_decode($paramList, true);

        // 计算单个参数，获取参数列表，循环执行
        $paramKeyResultMap = $this->getParamKeyResultMap($paramList, $dPolicyInfo, $deviceResult);

        //表达式
        $paramFormula = $dPolicyInfo['ParamFormula'];
        $result = SystemServiceProvider::calculateExpression($paramFormula, $paramKeyResultMap);
        cutil_php_log('DPolicyID:' . $dPolicyInfo['DPolicyID'] . ',DeviceID:' . $this->params['DeviceID'] . $this->resultMessage . ',result:' . var_export($result, true), 'DynamicPolicy');
        if ($result) {
            $ret = $this->doAction($dealMethods, $dPolicyInfo, $deviceResult, $dynamicSafeResult);
        } else {
            //是否勾选自动恢复
            if (in_array('AutoRecover', $dealMethods) && !empty($dynamicSafeResult)) {
                cutil_php_log('DynamicPolicy AutoRecover DPolicyID:' . $dPolicyInfo['DPolicyID'] . '-DeviceID:' . $this->params['DeviceID'], 'DynamicPolicy');
                $this->recoveryDynamicPolicy($dPolicyInfo['DPolicyID'], $this->params['DeviceID']);
            } elseif (!empty($dynamicSafeResult['ResultID'])) {
                //如果是已经到了忽略期限的，且本次没有匹配上的，理论这次就不需要更新时间，或者直接删除
                if ($dynamicSafeResult['Status'] == 2 && date('Y-m-d H:i:s') > $dynamicSafeResult['IgnoreTime']) {
                    DynamicSafeResultModel::delete(['ResultID' => $dynamicSafeResult['ResultID']]);
                } else {
                    //如果没有勾选自动恢复，勾选禁止访问资源，且在匹配里面，这个时候需要修改下返回结果
                    if (in_array('ForbidResource', $dealMethods)) {
                        $ret = false;
                    }
                    // 修改CalculateTime
                    $safeHistoryInsertData = [
                        'IsNeedMatch' => 0,
                        'UserId' => $this->params['Uuid'] ?? $dynamicSafeResult['UserId'],
                        'CalculateTime' => date('Y-m-d H:i:s'),
                    ];
                    DynamicSafeResultModel::update($dynamicSafeResult['ResultID'], $safeHistoryInsertData);
                }
            }
        }
        // 将状态存储到ASM_DynamicPolicy redis key中
        $dynamicPolicyCache[$dPolicyInfo['DPolicyID']] = $ret;
        DynamicPolicyRedis::setOne($this->params['DeviceID'], $dynamicPolicyCache);
        return $ret;
    }

    /**
     * 获取表达式各参数组的结果 true|false
     * @param $paramList
     * @param $dPolicyInfo
     * @param $deviceResult
     * @return array
     */
    function getParamKeyResultMap($paramList, $dPolicyInfo, $deviceResult): array
    {
        $paramKeyResultMap = [];
        $this->resultMessage = '参数组合：' . $dPolicyInfo['ParamFormula'] . PHP_EOL;
        foreach ($paramList as $paramKey => $paramValue) {
            // 如果对比字段为all时则表示本参数母结果为真，其依赖子参数只看ParamValues中的内容，总结果取决依赖的子条件
            // 目前是为解决病毒类型个数，后续扩展其他的
            $sonParamArr = $paramValue['SonParamID'] ?? '';
            !is_array($sonParamArr) && $sonParamArr = json_decode($sonParamArr, true);
            $parentParamResultTmp = false;
            $sonParamResult = false;
            $this->resultMessage .= $paramKey . '：' . $this->resultTypeMap['IDT'];
            //如果没有子项，则修改病毒数量为设备数量
            $this->resultMessage .= (empty($sonParamArr) ? '设备' : '') . ($this->resultTypeMap[$paramValue['ParamLabel']] ?? '');
            if ($paramValue['ParamCompare'] == 'all') {
                $parentParamResultTmp = true;
                $this->resultMessage .= '包括';
                foreach ($paramValue['ParamValues'] as $pName) {
                    $this->resultMessage .= ($this->resultTypeMap[$pName] ?? '') . '、';
                    //此处判断，如果有一个病毒里不包含在里面则不能匹配为真
                    $isExist = false;
                    if (!empty($deviceResult)) {
                        foreach ($deviceResult as $devOneRes) {
                            if (isset($devOneRes[$paramValue['ParamLabel']]) &&
                                $devOneRes[$paramValue['ParamLabel']] == $pName && $devOneRes['AntiVirusNum'] > 0) {
                                //表示该病毒匹配
                                $isExist = true;
                                break;
                            }
                        }
                    }
                    //只要有一种病毒没包含就表示没有包含全部，为假
                    if (!$isExist) {
                        $parentParamResultTmp = false;
                    }
                }
                //如果数量和配置数量不一致则
                $this->resultMessage = trim($this->resultMessage, '、');
            } else {
                $this->resultMessage .= ($this->operatorMap[$paramValue['ParamCompare']] ?? '') . $paramValue['ParamValues'];
            }
            if (!empty($sonParamArr)) {
                //目前仅支持子条件依赖，没有关系的不支持，即病毒类型包含ParamValues中配置的值
                if (is_array($paramValue['ParamValues']) && !empty($paramValue['ParamValues'])) {
                    foreach ($sonParamArr as $sonParam) {
                        // 子参数循环匹配
                        $sonParamResult = $this->compareParamsResult($sonParam, $deviceResult, $paramValue);
                        if (!$sonParamResult) {
                            //如果有一个子条件没有满足则整体没满足
                            break;
                        }
                    }
                }
            } else {
                //没有子选项直接算
                $sonParamResult = true;
                if ($paramValue['ParamCompare'] != 'all') {
                    $parentParamResultTmp = $this->compareParamsResult($paramValue, $deviceResult, $paramValue, false);
                }
            }
            $paramKeyResultMap[$paramKey] = $parentParamResultTmp && $sonParamResult;
            $this->resultMessage .= ';' . PHP_EOL;
        }
        return $paramKeyResultMap;
    }

    /**
     * 匹配成功执行动作
     * @param $dealMethods
     * @param $dPolicyInfo
     * @param $deviceResult
     * @param $dynamicSafeResult
     * @return bool
     * @throws Exception
     */
    function doAction($dealMethods, $dPolicyInfo, $deviceResult, $dynamicSafeResult): bool
    {
        $ret = true;

        // 执行处置动作
        $dealStatus = [];
        //对整个插入修改模块做个异常捕获处理
        try {
            //退出登录
            $this->logOutAction($dealMethods, $dealStatus);

            // 判断是否勾选了禁止访问资源 ForbidResource
            if (in_array('ForbidResource', $dealMethods)) {
                $ret = false;
                $dealStatus['ForbidResource'] = date('Y-m-d H:i:s');
            }

            //更新或者插入TDynamicSafeResult的记录，status为1，IsNeedMatch为0,修改CalculateTime
            $safeHistoryInsertData = [
                'DeviceID' => $this->params['DeviceID'],
                'DPolicyID' => $dPolicyInfo['DPolicyID'],
                'Result' => json_encode($deviceResult), //策略数值，key为ParamLabel的值，值为对应上报的数值
                'ResultMessage' => $this->resultMessage, // 详情,组装策略内容详情
                'DealStatus' => json_encode($dealStatus),//处置状态，key为ForbidResource、LogoutSelf等需要记录状态的字段，每一项值为处置的时间表示已处理，否则为未处理。
                'Status' => 1,
                'IsNeedMatch' => 0,
                'CalculateTime' => date('Y-m-d H:i:s'),
            ];

            // 如果是存在记录则直接更新，否则插入数据
            if (empty($dynamicSafeResult['ResultID'])) {
                $safeHistoryInsertData['UserId'] = $this->params['Uuid'] ?? 0;
                $safeHistoryInsertData['InsertTime'] = date('Y-m-d H:i:s');
                $safeHistoryInsertData['UpdateTime'] = date('Y-m-d H:i:s');
                \DynamicSafeResultModel::repeatInsertToDb($safeHistoryInsertData, 1, true);
                // 下发通知，判断是否勾选了通知，如果勾选则下发
                $dealNotices = $dPolicyInfo['DealNotices'];
                !is_array($dealNotices) && $dealNotices = json_decode($dealNotices, true);
                if (isset($dealNotices['IsPrompt']) && $dealNotices['IsPrompt'] == 1) {
                    //替换变量${username},${time}, {"Content": "aaaa", "IsPrompt": 1}
                    $dealNotices['Content'] = str_replace('${username}', $this->params['UserName'] ?? '', $dealNotices['Content']);
                    $dealNotices['Content'] = str_replace('${time}', date('Y-m-d H:i:s'), $dealNotices['Content']);
                    // 调用通知接口,此处需要封装通知接口，合并websocket通知，及客户端弹出tips
                    // 如果客户端版本没有升级，且开启了强制升级才能使用，则发消息通知客户端
                    $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>ShowUserOneMsg</WhatToDo><Msg>" . $dealNotices['Content'] . "</Msg></ASM>";
                    $msgSenderInfo = ['DeviceId' => $this->params['DeviceID'], 'Content' => $content, 'IsNull' => true];
                    //Ws通知列表也通知
                    $userNoticeInfo = [
                        'UserID' => $this->params['Uuid'] ?? 0,
                        'UserName' => $this->params['UserName'] ?? '已删除用户',
                        'DeviceID' => $this->params['DeviceID'],
                        'Title' => '动态策略处置通知',
                        'Content' => $dealNotices['Content'],
                        'Type' => 'default',
                        'Status' => 0,
                        'ActionParams' => json_encode(['DPolicyID' => $dPolicyInfo['DPolicyID']]),
                        'SendUser' => "system"
                    ];
                    // 15641 偶现动态消息策略通知下发了一模一样的两条
                    // 做个判断为避免极端情况下会出现后台权限配置与前端触发条件碰到一起，此处增加一个并发锁，锁设置过期时间失效，带个精确到分的时间放key后面
                    $key = md5($this->params['DeviceID'] . $dPolicyInfo['DPolicyID']) . date("YmdHi");
                    //判断当前请求是否存在
                    if (!\lib_redis::setnx('LOCK_', $key, time(), 2)) {
                        cutil_php_log('doAction LOCKED :' . $key . $this->params['DeviceID'] . $dPolicyInfo['DPolicyID'], 'DynamicPolicy');
                        return $ret;
                    }
                    \lib_alarm::createMsgSender($msgSenderInfo, $userNoticeInfo);
                }
            } else {
                $safeHistoryInsertData['UserId'] = $this->params['Uuid'] ?? $dynamicSafeResult['UserId'];
                $safeHistoryInsertData['UpdateTime'] = date('Y-m-d H:i:s');
                \DynamicSafeResultModel::update($dynamicSafeResult['ResultID'], $safeHistoryInsertData);
            }
        } catch (\Exception $exception) {
            cutil_php_log('doAction err:' . $exception->getMessage(), 'DynamicPolicy');
        }

        return $ret;
    }

    /**
     * 执行退出登录参数
     * @param $dealMethods
     * @param $dealStatus
     * @return bool
     */
    function logOutAction($dealMethods, &$dealStatus): bool
    {
        // 此处有bug,登录的时候会触发权限计算，会触发安检，安检如果判断策略属于匹配则退出登录动作，会删除token...
        // 故在此处判断如果是非命令行且LastTime 小于当前时间2分钟内
        if (PHP_SAPI != 'cli' || $this->params['From'] != 'batchSetUserPower') {
            return false;
        }

        //判断认证时间要是俩分钟以前的,如果状态是0则表示安检完成
        if ($this->params['AuthTime'] >= time() - 120 && $this->params['SessionStatus'] != 0) {
            return false;
        }

        $isHaveLoginAllOut = false;
        // 退出该用户所有设备 LogoutAll,其实没有必要再单独退出当前设备
        if (in_array('LogoutAll', $dealMethods)) {
            // 查询当前用户token对应的在线设备ID
            $cond = ['column' => '*', 'UserID' => ($this->params['Uuid'] ?? 0)];
            $onlineDevList = NacOnLineDeviceModel::getList($cond, false, 0, 50000);
            // 循环下线所有设备ID
            foreach ($onlineDevList as $onlineDev) {
                $this->cutOffDeviceArr[] = $onlineDev['DeviceID'];
                AuthServiceProvider::recoveryUser($onlineDev, 'DynamicTacticPolicy');
                TokenRedis::lPushData($onlineDev['Token'], $this->ClearTokenRedisKey);
            }
            $dealStatus['LogoutAll'] = date('Y-m-d H:i:s');
            $isHaveLoginAllOut = true;
        }

        // 退出该用户当前设备 LogoutSelf
        if (in_array('LogoutSelf', $dealMethods)) {
            $dealStatus['LogoutSelf'] = date('Y-m-d H:i:s');
            $this->cutOffDeviceArr[] = $this->params['DeviceID'];
            //已经退出全部的，则无需再退出一次
            if (!$isHaveLoginAllOut) {
                AuthServiceProvider::recoveryUser(['Token' => $this->params['Token'], 'DeviceID' => $this->params['DeviceID']], 'DynamicTacticPolicy');
                TokenRedis::lPushData($this->params['Token'], $this->ClearTokenRedisKey);
            }
        }
        return true;
    }

    /**
     * 对比参数是否满足条件
     * @param $paramInfo
     * @param $deviceResult
     * @param $paramValue
     * @param bool $isSonParam
     * @return bool
     */
    function compareParamsResult($paramInfo, $deviceResult, $paramValue, $isSonParam = true): bool
    {
        $result = false;
        // 目前支持的运算符号
        if (in_array($paramInfo['ParamCompare'], ['>', '=', '<', '!=', '<=', '>='])) {
            //数量内容
            $paramTotalValue = 0;
            if ($isSonParam) {
                $this->resultMessage .= '。' . ($this->resultTypeMap[$paramInfo['ParamLabel']] ?? '');
                $this->resultMessage .= ($this->operatorMap[$paramInfo['ParamCompare']] ?? '') . $paramInfo['ParamValues'];
            }

            if (!empty($deviceResult)) {
                foreach ($deviceResult as $devOneRes) {
                    //判断是否为子参数判断
                    if ($isSonParam) {
                        if (isset($devOneRes[$paramInfo['ParamLabel']]) && in_array($devOneRes[$paramValue['ParamLabel']], $paramValue['ParamValues'])) {
                            //参数匹配，计算类型
                            $paramTotalValue += $devOneRes[$paramInfo['ParamLabel']];
                        }
                    } else {
                        if (isset($devOneRes[$paramInfo['ParamLabel']])) {
                            //参数匹配，计算类型
                            $paramTotalValue += $devOneRes[$paramInfo['ParamLabel']];
                        }
                    }
                }
            }
            switch ($paramInfo['ParamCompare']) {
                case '>':
                    $result = $paramTotalValue > $paramInfo['ParamValues'];
                    break;
                case '=':
                    $result = $paramTotalValue == $paramInfo['ParamValues'];
                    break;
                case '<':
                    $result = $paramTotalValue < $paramInfo['ParamValues'];
                    break;
                case '<=':
                    $result = $paramTotalValue <= $paramInfo['ParamValues'];
                    break;
                case '>=':
                    $result = $paramTotalValue >= $paramInfo['ParamValues'];
                    break;
                case '!=':
                    $result = $paramTotalValue != $paramInfo['ParamValues'];
                    break;
                default:
                    break;
            }
            $this->resultMessage .= '（实际数量' . $paramTotalValue . ')';
        }
        return $result;
    }

    /**
     * 定期清理三个周期之前的数据，也就是6分钟前的没更新的策略数据
     * @param array $changePowerDeviceArr
     * @return bool
     */
    public function clearDirtyHistoryData($changePowerDeviceArr = []): bool
    {
        //注意所有计算基于在线的设备，如果不在线的设备，暂时不处理，如果变动也以退出登录前的状态
        $cond = ['CalculateTime' => date('Y-m-d H:i:s', time() - 360)];
        $resultList = DynamicSafeResultModel::getList($cond);
        $recoveryDataArr = $isNeedMatchArr = [];
        if (!empty($resultList)) {
            foreach ($resultList as $result) {
                //除了在恢复时间中的，时间未到，不能回收，不然上线会恢复中状态有问题，其他状态都可以回收
                if ($result['Status'] != 2 || date('Y-m-d H:i:s') >= $result['IgnoreTime']) {
                    $recoveryDataArr[$result['DPolicyID'] . "-" . $result['DeviceID']] = $result['DeviceID'];
                    //如果该设备对应的需要匹配
                    if ($result['IsNeedMatch'] == 1) {
                        $isNeedMatchArr[$result['DeviceID']] = $result['DPolicyID'];
                    }
                }
            }
        }

        // 此处需要增加判断，如果设备在，且资源有权限，关联动态策略信息还在，则不清理
        if (!empty($recoveryDataArr)) {
            foreach ($recoveryDataArr as $dPolicyIDStr => $deviceID) {
                $dPolicyIDArr = explode("-", $dPolicyIDStr);
                $dPolicyID = $dPolicyIDArr[0] ?? 0;
                // 查询数据是否在线，如果在线且6分钟没有计算则表示取消关联或者没有资源权限了，清理掉
                $isOnline = false;
                //通过设备ID获取到对应的token
                $token = DeviceTokenRedis::getOne($deviceID);
                if (!empty($token)) {
                    $session = SessionRedis::getOne($token, 'policy');
                    if (!empty($session['Uuid'])) {
                        $isOnline = true;
                    }
                }
                if ($isOnline) {
                    $this->recoveryDynamicPolicy($dPolicyID, $deviceID, 2, 'System', '策略变更');
                    continue;
                }
                // 设备是否还存在
                $isExistDev = \DeviceModel::getOne($deviceID);
                //设备已删除，一定要清理掉
                if (empty($isExistDev)) {
                    $this->recoveryDynamicPolicy($dPolicyID, $deviceID, 2, 'System', '策略变更');
                    continue;
                }
                // 处理设备离线，且没有权限，或者取消关联资源或者策略后，没法及时从匹配中回收,此处估计会很耗性能，不处理
                // 如果策略已经停用，则此处需要回收，其他情况不考虑
                $dynamicPolicyInfo = DynamicSafePolicyModel::getOne($dPolicyID);
                if ((empty($dynamicPolicyInfo) && $dPolicyID > 0) || (!empty($dynamicPolicyInfo) && $dynamicPolicyInfo['Status'] != 2)) {
                    $this->recoveryDynamicPolicy($dPolicyID, $deviceID, 2, 'System', '策略变更');
                }
            }
        }

        // 本次权限变化，则立即处理下,此处只能处理在线的设备
        $this->changePowerDeviceRecovery($changePowerDeviceArr);

        //根据token 定期清理些没有Uid的session,从历史表里面查找最近1个小时的token TNacOnLineUserHistory
        while ((int)TokenRedis::lLen($this->ClearTokenRedisKey) > 0) {
            $token = TokenRedis::rPopData($this->ClearTokenRedisKey);
            if (!empty($token)) {
                SessionRedis::deleteOne($token);
            } else {
                break;
            }
        }

        return true;
    }

    /**
     * 设备权限变化的时候，实时回收
     * @param $changePowerDeviceArr
     * @param bool $isOne
     * @return false|void
     */
    public function changePowerDeviceRecovery($changePowerDeviceArr, bool $isOne = false)
    {
        if (empty($changePowerDeviceArr)) {
            return false;
        }

        if ($isOne) {
            //是单个触发是否回收，需要根据token获取对应设备ID
            $oneToke = $changePowerDeviceArr[0] ?? 0;
            $session = SessionRedis::getOne($changePowerDeviceArr[0] ?? 0, 'policy');
            if (!empty($session['Uuid'])) {
                $changePowerDeviceArr[$session['DeviceID']] = $oneToke;
            } else {
                return false;
            }
        }
        foreach ($changePowerDeviceArr as $deviceID => $token) {
            // 查看对应设备的权限，是否已经没有权限，如果有恢复掉
            $cond['DeviceID'] = $deviceID;
            // TDynamicSafePolicyRelation，TResPolicyRelation 查询出来对应的resid,如果对应的ResID在权限中，则不处理，否则回收
            $resultList = DynamicSafeResultModel::getResIdsByDynamicSafeResult($cond);

            // 如果有对应的匹配数据，则进行数据处理
            if (!empty($resultList)) {
                $noRecoveryArr = $hasRecoveryArr = [];
                // 先判断对应的资源ID，是否都在权限中，不在的，就回收
                $session = SessionRedis::getOne($token, 'policy');
                if (!empty($session['Uuid']) && !empty($session['ResPowerIds'])) {
                    $resPowerIds = is_string($session['ResPowerIds']) ? json_decode($session['ResPowerIds'], true) : $session['ResPowerIds'];
                    foreach ($resultList as $result) {
                        //如果有一个设备id和一个动态策略对应多个资源，则只需要有一个资源id还有权限，则不能回收
                        if (!empty($result['ResID']) && in_array($result['ResID'], $resPowerIds)) {
                            //此种情况下对应的策略id对应的result_id不能回收
                            $noRecoveryArr[$result['DPolicyID']] = $result['ResultID'];
                        } else {
                            //做个记录，如果一个动态策略对应多个资源ID，则已经处理过的就不再处理避免浪费性能
                            if (!isset($hasRecoveryArr[$deviceID . '_' . $result['ResultID']])) {
                                $hasRecoveryArr[$deviceID . '_' . $result['ResultID']] = $result['DPolicyID'];
                            }
                        }
                    }

                    //对应策略ID不在不能回收的里面，且在应该回收的里面则回收
                    foreach ($hasRecoveryArr as $dPolicyId) {
                        if (!isset($noRecoveryArr[$dPolicyId])) {
                            $this->recoveryDynamicPolicy($dPolicyId, $deviceID, 2, 'System', '策略变更');
                        }
                    }

                }
                if (!empty($noRecoveryArr)) {
                    // 如果离线，则看设备是否还存在
                    $isExistDev = \DeviceModel::getOne($deviceID);
                    //设备已删除，一定要清理掉
                    if (empty($isExistDev)) {
                        $this->recoveryDynamicPolicy($noRecoveryArr[$deviceID], $deviceID, 2, 'System', '策略变更');
                    }
                }
            }
        }
    }

    /**
     * 恢复动态策略，目前只有删除匹配表记录进入日志表，后续可能会增加其他触发点
     * @return void
     */
    public function recoveryDynamicPolicy($dPolicyID, $deviceID = 0, $RecoverType = 2, $OperateUser = 'System', $RecoverReason = '自动恢复')
    {
        //查询一下对应的Result的数据，
        $cond = ['DPolicyID' => $dPolicyID];
        if (!empty($deviceID)) {
            $cond['DeviceID'] = $deviceID;
        }
        $resultList = DynamicSafeResultModel::getList($cond);
        if (!empty($resultList)) {
            $safeHistoryInsertPatchData = [];
            try {
                foreach ($resultList as $result) {
                    // 插入历史日志记录表TDynamicSafeHistory
                    // 受控资源,根据动态策略ID查找对应的策略ID，根据资源策略ID查找对应关联的资源ID，根据资源ID查询出来对应的资源名称
                    $resourceInfoArr = DynamicSafePolicyRelationModel::getResourceByDid($dPolicyID);
                    $resNameArr = [];
                    if (!empty($resourceInfoArr)) {
                        $resNameArr = array_column($resourceInfoArr, 'ResName');
                    }
                    $dynamicPolicyInfo = DynamicSafePolicyModel::getOne($dPolicyID);
                    $safeHistoryInsertData = [
                        'DeviceID' => $result['DeviceID'] ?? 0,
                        'DPolicyID' => $dPolicyID,
                        'PolicyName' => $dynamicPolicyInfo['PolicyName'] ?? '',
                        'UserId' => $result['UserId'] ?? '',
                        'Resource' => json_encode($resNameArr),
                        'OperateUser' => $OperateUser,
                        'RecoverType' => $RecoverType,
                        'RecoverReason' => $RecoverReason,
                        'TriggerTime' => $result['InsertTime'] ?? date('Y-m-d H:i:s'),
                        'RecoverTime' => date('Y-m-d H:i:s'),
                    ];
                    $safeHistoryInsertDataOther = $this->getSafeResultUserHistory($safeHistoryInsertData['DeviceID'], $result['UserId'] ?? '');
                    $safeHistoryInsertData = array_merge($safeHistoryInsertData, $safeHistoryInsertDataOther);
                    if (!empty($safeHistoryInsertData['IP']) || !empty($safeHistoryInsertData['DepartName']) || !empty($safeHistoryInsertData['UserName'])) {
                        $safeHistoryInsertPatchData[] = $safeHistoryInsertData;
                    }
                }
                DynamicSafeHistoryModel::insertPatch($safeHistoryInsertPatchData);
                // 删除TDynamicSafeResult的记录
                DynamicSafeResultModel::delete($cond);

            } catch (Exception $exception) {
                cutil_php_log('recoveryDynamicPolicy err:' . $exception->getMessage(), 'DynamicPolicy');
            }

        }
    }

    /**
     * 获取要插入的数据
     * @param $deviceId
     * @param int $userId
     * @return mixed
     */
    public function getSafeResultUserHistory($deviceId, $userId = 0)
    {
        //先查redis，如果没查到则去数据库查
        //通过设备ID获取到对应的token
        $token = DeviceTokenRedis::getOne($deviceId);
        $session = [];
        if (!empty($token)) {
            $session = SessionRedis::getOne($token, 'policy');
            if (empty($session['Uuid'])) {
                $session = [];
            }
        }
        //此处如果有性能问题，需要重新设计整体权限策略计算部分。
        if (empty($this->device['DeviceID'])) {
            $device = \DeviceModel::getJoinComputer(intval($deviceId), 'policy');
            $this->device = $device;
        }
        if (!empty($session)) {
            $safeHistoryInsertData['UserId'] = $session['Uuid'] ?? '';
            $safeHistoryInsertData['UserName'] = $session['UserName'] ?? '';
            $safeHistoryInsertData['TrueNames'] = $session['TrueNames'] ?? '';
            $safeHistoryInsertData['IP'] = $session['ConnectIp'] ?? '';
            $safeHistoryInsertData['Mac'] = $session['Mac'] ?? '';
            $safeHistoryInsertData['DevName'] = $session['DevName'] ?? '';
            $safeHistoryInsertData['DevInfo'] = $session['DevInfo'] ?? '';
            //$safeHistoryInsertData['DepartName'] = $session['DepartName'] ?? '';
            //session中的信息可能有延迟，所以此处从新查询数据库，查询设备,bugid:13104 动态安全策略日志中的所属部门字段值不正确
            $departInfo = DepartModel::getOne($this->device["DepartID"] ?? 0, 'one');
            $safeHistoryInsertData['DepartName'] = $departInfo['DepartName'] ?? '';
            $safeHistoryInsertData['Client'] = SystemServiceProvider::getClientTypeId($session['OsType'], $session['Client'] ?? null);
        } else {
            //查询设备
            $departInfo = DepartModel::getOne($this->device["DepartID"] ?? 0, 'one');
            $safeHistoryInsertData['DepartName'] = $departInfo['DepartName'] ?? '';
            //通过当前用户ID查询对应的用户真实姓名
            $userInfo = AuthUserModel::getOne($userId, 'one');
            $info = $this->device['DevName'] ?? ($this->device['IEVersion'] ?? '');
            $safeHistoryInsertData['UserName'] = $this->device["UserName"] ?? '';
            $safeHistoryInsertData['TrueNames'] = $userInfo["TrueNames"] ?? '';
            $safeHistoryInsertData['IP'] = $this->device["IP"] ?? '';
            $safeHistoryInsertData['DevName'] = $this->device["DevName"] ?? '';
            $safeHistoryInsertData['DevInfo'] = ($this->device['OSName'] ?? '') . " " . $info;
            $safeHistoryInsertData['Client'] = ($this->device["SubType"] ?? 0) + 10;
            $safeHistoryInsertData['Mac'] = $this->device["Mac"] ?? '';
        }
        return $safeHistoryInsertData;
    }

    /**
     * 退出登录，强制下线
     * @param $cutOffDeviceArr
     * @return void
     */
    public function cutOffDevice($cutOffDeviceArr)
    {
        cutil_php_log('================cutOffDeviceArr:' . var_export($cutOffDeviceArr, true), 'DynamicPolicy');
        //避免重复断开设备网络，在此处统一处理
        $defaultCutoffOneConfig = [
            'isolation' => 0,
            'is_trust_cut_off' => 0,
            'is_temp_force_cutoff' => 0,
            'is_force_cutoff' => 1,
            'is_isolated_access' => 1, // 0:只能访问隔离域, 1:禁止访问网络,认证安检成功后,放开网络 2:禁止访问网络,禁止认证安检,修复后重新认证安检入网
            'timeout' => 10,
            'show_user_one_message_en' => '',
            'reason_msg' => '动态策略触发退出登录',
            'source' => 0,
            'msg' => '',
            'is_send_cutoff_message' => 1,
            'remark' => 'DynamicTacticPolicy',
            'show_user_one_message' => '您的终端存在异常行为触发动态策略退出登录，请检查终端安全状况或联系管理员处理!',
        ];
        foreach ($cutOffDeviceArr as $cutDeviceId) {
            $deviceArr[] = [
                'device_id' => (int)$cutDeviceId,
                'ip' => '',
                'mac' => '',
                'bas_ip' => '',
                'iplistid' => 0,
                'is_guest' => 0,
            ];
            $event = ["Device" => $deviceArr, 'Config' => $defaultCutoffOneConfig];
            \Common\Facades\EventBrokerFacade::publish("NACCutoff", $event, 'WebAccess:DynamicTacticPolicy');
        }
    }

}