<?php
/**
 * Description: guac心跳检测
 * User: <EMAIL>
 * Date: 2022/7/19 17:07
 * Version: $Id$
 */

/* 定义关键常量 */
define('PATH_CUR', __DIR__);

/** 初始化 */
require PATH_CUR . '/../init.php';


class GuacHearBeat
{
    /**
     * 执行
     */
    public function handle(): void
    {
        echo 'GuacHearBeat start:' . PHP_EOL;
        GuacamoleServiceProvider::checkHeart();
        cutil_php_log("GuacHearBeat handle finish!", 'cronb');
    }
}

try
{
    $obj = new GuacHearBeat();
    $obj->handle();
}
catch (Exception $e)
{
    echo "code: " . $e->getCode() . ", message: ". $e->getMessage();
}