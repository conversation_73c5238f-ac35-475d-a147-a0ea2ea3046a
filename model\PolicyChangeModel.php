<?php

/**
 * Description: TPolicyChange表
 * User: <EMAIL>
 * Date: 2021/08/24 15:53
 * Version: $Id: PolicyChangeModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class PolicyChangeModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TPolicyChange';
    public const PRIMARY_KEY = 'ChangeID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['InDeviceID'])) {
            $where .= "AND DeviceID IN (" .self::setArrayData($cond['InDeviceID']). ")";
        }

        if (isset($cond['RoleID'])) {
            $where .= "AND RoleID = ".self::setData($cond['RoleID']);
        }

        if (isset($cond['PolicyID'])) {
            $where .= "AND PolicyID = ".self::setData($cond['PolicyID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
