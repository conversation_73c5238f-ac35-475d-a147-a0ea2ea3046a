<?php

/**
 * Description: HardToDevID表
 * User: <EMAIL>
 * Date: 2023/11/29 16:42
 * Version: $Id: HardToDevIDModel.php 158181 2022-05-16 16:42:13Z duanyc $
 */

class HardToDevIDModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'THardToDevID';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['MacHard'])) {
            $where .= "AND MacHard = ".self::setData($cond['MacHard']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
