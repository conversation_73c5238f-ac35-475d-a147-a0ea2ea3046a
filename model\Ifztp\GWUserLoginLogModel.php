<?php

/**
 * Description: vpn会话
 * User: <EMAIL>
 * Date: 2023/05/08 11:32
 * Version: $Id: GWUserLoginLogModel.php 174776 2023/05/08 09:18:26Z duanyc $
 */

class GWUserLoginLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGWUserLoginLog';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'     => '*',
        'heart' => 'Cid,AuthToken,ProcessID',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['IsFinished'])) {
            $where .= "AND IsFinished = ".self::setData($cond['IsFinished']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
