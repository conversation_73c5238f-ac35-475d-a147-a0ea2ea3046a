<?php

class OnlineZtpUserRedis extends BaseHashRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Online';

    /**
     * 单条
     *
     * @param string $type
     * @param array $columns
     *
     * @return mixed
     */
    public static function getOne($type, $columns = [])
    {
        if (empty($type)) {
            return false;
        }
        return self::get($columns, $type);
    }

    /**
     * 数量
     *
     * @return int
     */
    public static function getCount(): int
    {
        return self::count();
    }

    /**
     * 单条
     * @param string $type
     * @param array $data
     *
     * @return bool
     */
    public static function setOne(string $type, array $data): bool
    {
        return self::set($data, $type);
    }

    /**
     * 删除
     *
     * @param string $type
     *
     * @return mixed
     */
    public static function deleteOne($type)
    {
        return self::del($type);
    }
}
