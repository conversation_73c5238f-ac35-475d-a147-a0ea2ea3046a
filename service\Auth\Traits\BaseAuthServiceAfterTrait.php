<?php
/**
 * Description: 认证后代码块 只能BaseAuthService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: BaseAuthServiceAfterTrait.php 168702 2022-02-15 06:50:30Z huyf $
 */

namespace Services\Auth\Traits;

use AuthServiceProvider;
use AuthUserModel;
use Common\Facades\NACServiceFacade;
use ComputerModel;
use DepartModel;
use DeviceBindUserModel;
use DeviceModel;
use DictModel;
use Exception;
use GatewayServiceProvider;
use GuestOnLineDeviceModel;
use GuestSelfApplyModel;
use hlp_common;
use lib_queue;
use lib_request;
use lib_yar;
use LoginServiceProvider;
use NacOnLineDeviceModel;
use NacPolicyListModel;
use NacPreOnLineDeviceModel;
use NetManageRoleModel;
use NoticeServiceProvider;
use PermitAuthServiceProvider;
use RelationComputerModel;
use ResourceServiceProvider;
use ResultServiceProvider;
use ServerServiceProvider;
use DictHashRedis;
trait BaseAuthServiceAfterTrait
{
    /**
     * 双因子具体认证方式实现
     * @param $data
     * @param $userInfo
     * @throws Exception
     */
    public function authTwoFactorAfter(&$data, $userInfo)
    {
        $this->checkLimitUser($userInfo);
        $cache = cache_get_info('twoFactor', $this->deviceId);
        if (empty($cache)) {
            T(21120044);
        }
        $data['RoleID'] = $cache['RoleID'];
        $data['ID'] = $cache['UID'] ?? 0;
        $this->params['userName'] = $cache['UserName'];
        $this->params['authType'] = $cache['AuthType'];
        $this->recordDeviceFromRole($data, $userInfo['TrueNames']);
    }

    /**
     * 具体认证方式实现
     * @param $data
     * @param $userInfo
     * @param $authType
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        if ($authType != AUTH_TYPE_NOAUTH && !empty($userInfo['ID'])) {
            /* 记录来宾认证的记录ID */
            $key_values = ['UserID' => $userInfo['ID']];
            if ($authType == AUTH_TYPE_GUEST && !empty($data['guestSelfID'])) {
                $key_values['LastGuestAuthID'] = $data['guestSelfID'];
            }
        } else {
            /* 如果没有用户信息则将UserID更新为0 */
            $key_values = ['UserID' => 0];
        }
        RelationComputerModel::update($this->deviceId, $key_values);
        cutil_php_log([$this->deviceId, $key_values], $this->logFileName);
        // 不是双因子第一步认证，或者第一步认证绕过双因子，则插入在线记录
        if (!$this->params['factorAuth'] || !empty($this->params['noFactorAuth'])) {
            $this->checkLimitUser($userInfo);
            //如果下一步不是双因子认真 则记录
            if ($data['BindAuth'] === 0) { //不进行绑定认证时，才记录
                $this->recordDeviceFromRole($data, $userInfo['TrueNames']);
            }
        } else {
            // 双因子认证时，第一次用户名密码认证通过后，不马上删除上一次的认证，防止断网，等待第二次双因子后进行处理
            $this->checkLimitUser($userInfo, false);
        }
        $AuthParam = DictModel::getAll('AUTHPARAM');
        if ($AuthParam['UserBindDev'] == 1 && ($this->params['authType'] != AUTH_TYPE_NOAUTH && $userInfo['ID'] > 0)) {
            //判断下是否超出最大自动设备绑定数量，如果未超过，则可以继续绑定
            $AutoBindUserNum = $AuthParam['AutoBindUserNum'] ?? 1;
            $maxBindNum = $AutoBindUserNum;
            //此处为判断用户最大绑定设备数量
            if (isset($userInfo['UserBindDevNum'])) {
                $maxBindNum = $userInfo['UserBindDevNum'] > $maxBindNum ? $userInfo['UserBindDevNum'] : $maxBindNum;
            }
            $currBind = $this->canUserBindDev($userInfo["ID"], $maxBindNum);
            if ($currBind) {
                $this->userBindDev($userInfo["ID"], $AutoBindUserNum);
            }
        }
        if (!empty($data['IsSceneChange'])) {
            $this->setDeviceMustCheck($data);
        }
        // 返回注册信息
        // lpc 获取最新的设备注册信息，部分逻辑有中途修改注册信息，导致数据不生效（快速入网）
        $ComputerInfo = ComputerModel::getOne($this->deviceId, 'info');
        $data['Registered'] = $ComputerInfo['Registered'];
        $data['ReRegReason'] = $ComputerInfo['ReRegReason'];
        $data['DepartID'] = $userInfo['DepartID'];
        $this->updateDeviceInfo($data);
        $this->setFastCheck($data);
    }

    /**
     * 设置是否快速入网安检
     *
     * @param $data
     */
    public function setFastCheck(&$data)
    {
        if (!empty($data['PolicyID'])) {
            if ($GLOBALS['DATABASE']['databases']['db_type'] == 'kdb') {
                $policyConfig = NacPolicyListModel::getOne($data['PolicyID'], 'senior_kdb');
            } else {
                $policyConfig = NacPolicyListModel::getOne($data['PolicyID'], 'senior');
            }
            if ($policyConfig && $policyConfig['SafetyMonitor'] == 1 && $policyConfig['FastCheck'] == 1) {
                $data['FastCheck'] = 1;  //是否快速入网安检,入网时直接使用当前检测结果作为安检结果
            }
        }
    }

    /**
     * 更新设备信息
     *
     * @param $data
     * @throws Exception
     */
    public function updateDeviceInfo(&$data)
    {
        $params = [];
        if ($this->deviceInfo['ZtpDevice'] == DeviceModel::UNKNOWZTPDEVICE && !IS_INTERNAL) {
            $params['ZtpDevice'] = DeviceModel::ZTPDEVICE;
        }
        if (in_array($this->deviceInfo['ZtpDevice'], [DeviceModel::ZTPDEVICE, DeviceModel::UNKNOWZTPDEVICE]) && IS_INTERNAL) {
            $params['ZtpDevice'] = 0;
        }
        // 标记来宾设备
        $isGuestDevice = $this->params['authType'] === AUTH_TYPE_GUEST ? 1 : 0;
        if ($isGuestDevice != $this->deviceInfo['IsGuestDevice']) {
            $params['IsGuestDevice'] = $isGuestDevice;
            if ($isGuestDevice) {
                $cmd = 'php ' . PATH_ADMIN . '/artisan search:add-index guestdevice ' . $this->deviceId;
            } else {
                $cmd = 'php ' . PATH_ADMIN . '/artisan search:del-index guestdevice ' . $this->deviceId;
            }
            cutil_exec_no_wait($cmd); //来宾设备变化需要立即更新索引
        }
        $isSyncDevDepart = DictModel::getOneItem('UserDevBind', 'IsUpDepart');
        //设备同步账户所属部门,修改152bug # 42949
        if ((int)$isSyncDevDepart['ItemValue'] === 1 && $this->deviceInfo['DepartId'] != $data["DepartID"] && !empty($data['DepartID'])) {
            $params['DepartId'] = $data["DepartID"];
            $this->deviceInfo['DepartId'] = $data['DepartID'];
        }

        // 如果是自动注册则直接进行注册
        if ((int)$this->deviceInfo['Registered'] === -2 && !empty($this->params['autoAuth'])) {
            $IsNeedReg = $data['IsNeedReg'];
            $IsNeedAuto = $data['IsNeedAuto'];
            if ((int)$IsNeedReg === 0 && (int)$IsNeedAuto === 0) {
                $computerParams = [
                    'Registered' => '1',
                    'RegTime' => 'now()'
                ];
                ComputerModel::update($this->deviceId, $computerParams);
                $data['Registered'] = '1';
                $params['LastTime'] = 'now()';
                $params['Remark'] = L(21103016);
            }
        } elseif (!IS_INTERNAL && $data['Registered'] == -1 && $data['ReRegReason'] == L(21105004)) {
            $data['Registered'] = '1';
            $data['ReRegReason'] = '';
            ComputerModel::update($this->deviceId, ['Registered' => 1, 'ReRegReason' => '']);
        }

        if (IS_CLIENT && $this->deviceInfo['InstallClient'] == '0') {
            $params['InstallClient'] = '1';
        }

        if (!empty($params)) {
            DeviceModel::update($this->deviceId, $params);
        }
    }

    /**
     * 判断认证之后是否必须安检（取消认证时间>安检时间判断后新增逻辑）
     * ps：开启安检之后才需要调用改方法
     * @param array $aResult 认证后获取到的结果信息
     */
    public function setDeviceMustCheck(array $aResult): void
    {
        if ((int)$aResult['IsSafeCheck'] === 1) {
            // 上次认证场景与本次认证的场景不一致且当前场景需要安检，将需要安检之后才能入网
            RelationComputerModel::update($this->deviceId, ['CheckResult' => '']);
            $this->writeLog('Last auth info:' . var_export($this->onlineDeviceInfo, true));
        }
    }

    /**
     * 高级动态认证认证后处理
     * @param $data
     * @throws Exception
     */
    public function resourceAuthAfter(&$data)
    {
        $userId = $data['UserID'] ?? 0;
        $this->saveResourceAuth($userId);
        //分析检查参数
        unset($data['CheckParam'], $data['auth_password'], $data['Password']);
        $data["DeviceID"] = $this->deviceId;
        $data['AuthType'] = $this->params['authType'];
        $data['firstUrl'] = $this->params['resourceAddr'];
        $ip = $this->deviceInfo['IP'];
        $cmd = PATH_ASM . "sbin/set_client_one {$ip} ad_net_auth=1 ";
        cutil_exec_no_wait($cmd, 10);
    }

    /**
     * 证成功后将结果保存在redis中，过期时间设置为单次授权可访问时长
     *
     * @param $UserID
     */
    public function saveResourceAuth($UserID)
    {
        $keyResourceAuthConfig = cache_get_info('resourceAuth_', 'config');
        if (empty($keyResourceAuthConfig)) {
            $keyResourceAuthConfig = DictModel::getAll('KeyResourceAuth');
        }

        $keyPrefix = 'ResourceAuth:Success:' . $UserID . ':';
        cache_set_info($keyPrefix, $this->deviceId, [1], (int)$keyResourceAuthConfig['AuthTime'] * 60);
        $this->writeLog("saveResourceAuth: " . $this->deviceId . ":" . $UserID);
    }

    /**
     * 获取是否提示，
     * @return bool true：表示提示用户，false：表示继续认证
     */
    public function getHintOver()
    {
        $hintOver = true;
        if (isset(lib_request::$requests['hintOver'])) {
            if ($this->params['hintOver'] == 1) {
                $hintOver = false;
            }
        } else {
            //web页面添加autoAuth参数来判断是否自动认证，未传递或值为1时认为自动认证
            if (isset(lib_request::$requests['autoAuth'])) {
                if ($this->params['autoAuth'] == 1) {
                    $hintOver = false;
                }
            } else {
                $hintOver = false;
            }
        }
        return $hintOver;
    }

    /**
     * 限制同一账户最大可认证设备数量 原方法limitUserOnlineDevices
     * @param $data array 用户信息
     * @param $isClear boolean 是否清除上一次入网状态
     * @throws Exception
     */
    public function checkLimitUser($data, $isClear = true)
    {
        if ($isClear) {
            if (!empty($this->onlineDeviceInfo)) {
                AuthServiceProvider::recoveryUser($this->onlineDeviceInfo, 'reAuthRecovery');
                NacOnLineDeviceModel::delete(['DeviceID' => $this->deviceId]);
                if (hlp_common::isOpenThirdServer()) {
                    NacPreOnLineDeviceModel::delete(['DeviceID' => $this->deviceId]);
                }
            }
            $guestOnLineDevice = GuestOnLineDeviceModel::getSingle(["DeviceID" => $this->deviceId]);
            if ($guestOnLineDevice) { //如果有对应的来宾在线条目，则需要更新对应的离线时间
                GuestSelfApplyModel::update($guestOnLineDevice['GuestSelfApplyID'], ['OffLineTime' => 'now()']);
                GuestOnLineDeviceModel::delete(['DeviceID' => $this->deviceId]);
            }
        }
        if (in_array($this->params['authType'], [AUTH_TYPE_GUEST, AUTH_TYPE_NOAUTH])) {
            return;
        }
        $UserBind = DictModel::getAll('UserDevBind');
        /// 闸北教育局限制单个账户使用设备数
        if ((int)$this->userInfo["AuthNums"] > 0) {
            $UserBind['IsUse'] = $this->userInfo["AuthNums"];
        }
        if ($UserBind['IsUse'] <= 0) {
            return;
        }
        //开启了同一账户同一时刻只允许在一台设备上使用
        //查询该用户上次认证设备IP
        $cond = ['AuthType' => $this->params['authType'], 'UserName' => $this->params['userName'], 'DeviceID' => $this->deviceId, 'UserID' => $this->userInfo['ID']];
        $order = 'GROUP BY A.DeviceID ORDER BY A.AuthTime';
        $onlineDeviceList = NacOnLineDeviceModel::getListJoin($cond, $order, 0, 100);
        $Usernum = is_array($onlineDeviceList) ? count($onlineDeviceList) : 0;
        $isHintOver = $this->getHintOver();
        $this->writeLog("isHintOver：" . $isHintOver);

        for ($i = 0; $i < $Usernum; $i++) {
            //比设置的数字多删除一条记录 因为下面逻辑要加入一条新记录
            if (($Usernum - $i) >= $UserBind['IsUse'] && $this->deviceInfo['IP'] != $onlineDeviceList[$i]['IP']) {
                $formatArr = [
                    'num' => $Usernum,
                    'name' => $onlineDeviceList[$i]["DevName"],
                    "ip" => $onlineDeviceList[$i]['IP'],
                ];
                $data = ['Username' => $this->params['userName'], 'AuthType' => $this->params['authType']];
                $isHintOver ? T(21120030, $formatArr, $data) : false;
                $this->writeLog("onlineOutDevice: {$onlineDeviceList[$i]['DeviceID']}");
                $this->onlineOutDevice($onlineDeviceList[$i]['DeviceID']);
            }
        }
    }

    /**
     * 手动下线指定ip的登录
     *
     * @param $DeviceID
     * @param string $remark
     * @return string
     * @throws Exception
     */
    public function onlineOutDevice($DeviceID, string $remark = 'UserLoginDevExceed'): string
    {
        if (empty($DeviceID)) {
            return '';
        }
        $ip = $this->deviceInfo['IP'];
        if (strpos($DeviceID, ",")) {
            $deviceList = DeviceModel::getList(['DeviceIds' => explode(",", $DeviceID)]);
            if (empty($deviceList)) {
                T(21103006);
            }
            $device['IP'] = implode(",", array_column($deviceList, 'IP'));
            $device['DevName'] = implode(",", array_column($deviceList, 'DevName'));
        } else {
            $device = DeviceModel::getOne($DeviceID, 'one');
            if (empty($device)) {
                T(21103006);
            }
        }
        // 终端准入状态服务使用
        $username = $this->params['userName'];
        //删除在线表之前通知MSEP
        NoticeServiceProvider::sendSyncStatus($DeviceID,'deviceToUser');
        $DeviceIDs=explode(",", $DeviceID);
        $oriDeviceID=$DeviceID;
        foreach ($DeviceIDs as $DeviceID){
            switch ($remark) {
                case 'UserLoginDevExceed':
                    $showUserOneMsg = L(21120046, ['curip' => $device['IP'], "ip" => $ip]);
                    $msg = $showUserOneMsg;
                    break;
                case 'LogOut':
                    // 授权接入下线，用户主动下线都用这个
                    $onlineInfo = NacOnLineDeviceModel::getOne($DeviceID);
                    if (empty($onlineInfo)) {
                        T(21126023);
                    }
                    $showUserOneMsg = L(21120064, ['user' => $onlineInfo['UserName'], 'ip' => $device['IP']]);
                    $msg = $showUserOneMsg;
                    break;
                case 'GuestDeviceCutOff':
                    $showUserOneMsg = L(21120069, ["ip" => $ip]);
                    $msg = L(21120069, ['ip' => $ip]);
                    break;
                default:
                    $showUserOneMsg = L(21120032, ['user' => $username, "ip" => $ip]);
                    $msg = L(21120031, ['username' => $username, 'ip' => $ip]);
            }
            $cutoffConfig = [
                'is_force_cutoff' => 1,
                'is_isolated_access' => 1,
                'is_send_cutoff_message' => 1,
                'show_user_one_message' => $showUserOneMsg,
                'source' => 4,
                'msg' => $msg,
                'remark' => $remark,
            ];
            NACServiceFacade::cutoff('WebCutoff', [['device_id' => $DeviceID]], $cutoffConfig);
            // 清除待审核级联
            GuestSelfApplyModel::updatePatch(['Status' => 0, 'DeviceID' => $DeviceID], ['Status' => -1, 'Reason' => '终端强制下线']);
        }

        $this->writeLog("被挤下线：" . $oriDeviceID);
        return L(21102005, ['DevName' => $device['DevName']]);
    }

    /**
     * 获取账户可以继续绑定设备的信息 原getUserBindDevNum
     * @param string $userId 用户id
     * @param string $bindNumLimit 用户可绑定数量
     * @return bool
     */
    public function canUserBindDev($userId, $bindNumLimit)
    {
        if ((int)$bindNumLimit === 0) {
            return true;
        }
        $cond = ['Type' => 1, 'AuthUserID' => $userId];
        $Num = DeviceBindUserModel::getCount($cond);
        $cond = ['Type' => 1, 'AuthUserID' => $userId, 'DeviceID' => $this->deviceId];
        $isExistNum = DeviceBindUserModel::getCount($cond);
        if ($Num < $bindNumLimit && $isExistNum < 1) {
            return true;
        }
        return false;
    }

    /**
     * 用户自动绑定设备
     * @param $UserID
     * @param int $AutoBindUserNum
     * @throws Exception
     */
    public function userBindDev($UserID, $AutoBindUserNum = 1)
    {
        // 检查用户是否是特权用户，如果是特权用户，则不绑定
        $userInfo = AuthUserModel::getOne($UserID, 'privilege');
        if (!empty($userInfo['IsPrivilege'])) {
            // 不绑定
            return;
        }
        cutil_php_log($userInfo, $this->logFileName);
        // 用户认证成功后，开启：自动绑定到设备，限制
        $params = ['IsBindDevice' => 1];
        //如果开启了全局设置，且用户没有设置，则使用全局的设置值
        if (empty($userInfo['UserBindDevNum'])) {
            $params['UserBindDevNum'] = $AutoBindUserNum;
        }
        AuthUserModel::update($UserID, $params);
        $bindParams = ['DeviceID' => $this->deviceId, 'AuthUserID' => $UserID, 'Type' => 1];
        $BindRecord = DeviceBindUserModel::getSingle($bindParams);
        if (empty($BindRecord)) {
            DeviceBindUserModel::insert($bindParams);
        }
    }

    /**
     * 同步设备信息
     *
     * @param $userInfo
     */
    public function copyDevice($userInfo)
    {
        $deviceParams = [];
        if (!empty($userInfo['TrueNames'])) {
            $deviceParams['UserName'] = $userInfo['TrueNames'];
        }
        if (!empty($userInfo['Tel'])) {
            $deviceParams['Tel'] = $userInfo['Tel'];
        }
        if (!empty($userInfo['DepartID'])) {
            $deviceParams['DepartID'] = $userInfo['DepartID'];
        }
        if (!empty($deviceParams)) {
            DeviceModel::update($this->deviceId, $deviceParams);
        }
    }

    /**
     * 记录认证设备到在线表
     *
     * @param $aResult
     * @param $TrueNames
     * @throws Exception
     */
    public function recordDeviceFromRole($aResult, $TrueNames)
    {
        if ($this->params['authType'] != AUTH_TYPE_GUEST && $this->params['authType'] != AUTH_TYPE_NOAUTH) {
            $aUserBindParama = DictModel::getAll('UserDevBind');
            $deviceUpadateParams = [];

            // 默认同步设备使用者
            if ($this->deviceInfo['UserName'] == '') {
                $upName = $TrueNames == '' ? $this->params['userName'] : $TrueNames;
                if ($upName != "") {
                    $deviceUpadateParams['UserName'] = $upName;
                }
            }
            if ($aUserBindParama['IsUpDevName'] == '1') {
                //0 =>将最后认证的用户姓名当做设备的使用者
                //1 =>将最后认证的用户账户当做设备的使用者
                $upName = $aUserBindParama['IsUpdateUserToDev'] == '1' ? $this->params['userName'] : $TrueNames;
                if ($upName != "" && $this->deviceId) {
                    $deviceUpadateParams['UserName'] = $upName;
                }
            }
            if (!empty($deviceUpadateParams)) {
                DeviceModel::update($this->deviceId, $deviceUpadateParams);
            }
        }
        $UserID = $aResult["ID"] ?? 0;
        // 来宾认证不走场景，场景ID为0
        $sceneId = $this->params['authType'] == 'Guest' ? 0 : ($aResult["SceneID"] ?? 0);
        $onlineParams = ['DeviceID' => $this->deviceId, 'AuthType' => $this->params['authType'], 'DevInfo' => $this->deviceInfo['DevInfo'],
            'UserName' => $this->params['userName'], 'RoleID' => $aResult["RoleID"], 'SceneID' => $sceneId, 'UserID' => $UserID,
            'InsertTime' => 'now()', 'AuthTime' => 'now()', 'LastHeartTime' => 'now()', 'Token' => $aResult['Token'], 'UpdateTime' => 'now()'];
        NacOnLineDeviceModel::repeatInsertToDb($onlineParams, 5);
        cutil_php_log($onlineParams, $this->logFileName);
        // 更新设备缓存信息
        $this->updateDeviceCacheAndLastTime();
        // 添加第三方联动 nielin
        if (hlp_common::isOpenThirdServer()) {
            NacPreOnLineDeviceModel::insert($onlineParams);
        }
        // 插入在线表之后通知MSEP
        NoticeServiceProvider::sendSyncStatus($this->deviceId,'deviceToUser');
    }

    /**
     * 补充公共数据信息
     *
     * @param $data
     * @param $userInfo
     * @param $params
     * @throws Exception
     */
    public function setCommonInfo(&$data, $userInfo, $params)
    {
        //通过auth_interface.php进入来自于8021x调用则双因子认证 自动失效
        // 802.1x使用AD域单点登录的情况下，双因子认证自动失效 bugid 12523
        if ($params['callfrom'] === '8021X' ||
            ($params['callfrom'] === AUTH_FROM_8021X
                && isset($params['servicePrefix'])
                && $params['servicePrefix'] === 'AdAuto')
        ) {
            $data['FactorAuth'] = false;
        } else {
            $data['FactorAuth'] = $params['factorAuth'] ?? false;
        }
        $data['IsAgainReg'] = (string)AuthServiceProvider::getIsAgainReg((int)$data['SceneID'], (int)$data['DeviceID']);
        if ($data['IsAgainReg'] === '1') {
            $devRes = NetManageRoleModel::getIPListIdAndCount((int)$data['DeviceID']); // bugID=8861，终端是快速入网则不受此控制
            $data['IsAgainReg'] = $devRes['count'] >= 1 ? '0' : '1';
        }
        //分析检查参数
        unset($data['CheckParam'], $data['auth_password'], $data['Password']);
        //获取部门信息
        $DepartID = $data["DepartID"] ?? 0;
        $departInfo = DepartModel::getOne($DepartID, 'one');
        $data["DepartName"] = $departInfo["DepartName"];
        $data["UpID"] = $departInfo["UpID"];
        $data["AllDepartID"] = $departInfo["AllDepartID"];
        $data['ZtpUser'] = $userInfo['ZtpUser'] ?? 0;
        $data['IsOpenSDCLinkage'] = strpos($this->deviceInfo['OSName'], 'Windows XP') === false && $userInfo['IsOpenSDCLinkage'] ? '1' : '0';
        $data["TrueNames"] = !empty($data["TrueNames"]) ? $data["TrueNames"] : $userInfo['TrueNames'];
        $data["nowTime"] = date("Y-m-d H:i:s");
        $aBridge = read_inifile(PATH_TBRIDGE_PRIVATE);
        $data['BRIDGE_TYPE'] = $aBridge['BRIDGE_TYPE']; // 认证安检成功读取当前服务器的准入类型
        //获取设备信息
        $data["DevName"] = $this->deviceInfo["DevName"] ?? '';
        $userInfo["DevInfo"] = $this->deviceInfo["DevInfo"] ?? '';
        $userInfo["Mac"] = $this->deviceInfo["Mac"] ?? '';
        $userInfo["IP"] = $this->deviceInfo["IP"] ?? '';
        $data['TokenTimestamp'] = LoginServiceProvider::getTimestamp();

        LoginServiceProvider::setSession($data, $userInfo);
        LoginServiceProvider::setLoginUserInfo($data['UserID'], $data['Token'], $this->userInfo);
        $needUnbind = empty($data['bindInfo']) ? true : false;
        $region = \DevIpRegionModel::getSingle(['DeviceID' => $data['DeviceID'], 'column' => 'net']);
        $qdata = ['deviceId' => $data['DeviceID'], 'Token' => $data['Token'], 'UserID' => $data['UserID'],
            'UserName' => $data['UserName'], 'is_client' => IS_CLIENT, 'needUnbind' => $needUnbind, 'Type' => $this->userType,
            'create_time' => (int)round(microtime(true) * 1000), 'AccessStatus' => 0, 'IPlistId' => 0,
        ];
        if (isset($region['IPListId'])) {
            $qdata['IPlistId'] = (int)$region['IPListId'];
            $qdata['AccessStatus'] = (int)$region['IPListId'] & 1;
        }
        lib_queue::addJob('AUTH_SUCCESS', $qdata);
        //保存上次设备信息 add by zhangkun
        ResultServiceProvider::saveLastDeviceInfo($params['deviceId'], $this->deviceInfo);
        $data["CiscoAccess"] = ServerServiceProvider::getPortalConfig('', true);
        $data['code'] = '200';
        $data['SubmitKey'] = ResultServiceProvider::generateSubmitKey($params['deviceId']);
    }

    /**
     * 补充双因子认证第二次DeviceTokenRedis数据
     * @param $data
     */
    public function setFactorCommonInfo($data)
    {
        \DeviceTokenRedis::setOne($this->deviceId, $data['Token']);
    }
}
