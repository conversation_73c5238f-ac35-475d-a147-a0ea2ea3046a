<?php
/**
 * Description: 零信任指定设备mac可访问策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;

use Services\Policy\Interfaces\PolicyServiceInterface;

class AllowMacAccessPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *@throws \Exception
     * @return bool
     */
    public function check(): bool
    {
        $device = $this->device;
        foreach ($this->params['Config'] as $mac){
            if (strtolower(str_replace([":",'-'],'',$mac)) == strtolower(str_replace([":",'-'],'',$device['Mac']))){
                return true;
            }
        }
        return false;
    }
}
