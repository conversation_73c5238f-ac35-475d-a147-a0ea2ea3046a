<?php

/**
 * Description: 用户日志
 * User: <EMAIL>
 * Date: 2022/05/06 23:32
 * Version: $Id$
 */

class UserLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUserLog';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'list' => 'ID,LogTypeID,Content,IP,InsertTime'
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['LogTypeID'])) {
            $where .= "AND LogTypeID = ".self::setData($cond['LogTypeID']);
        }

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }

        if (isset($cond['Keyword'])) {
            $Keyword = "%{$cond['Keyword']}%";
            $where .= "AND (Content like ".self::setData($Keyword) .
                      " OR IP like ".self::setData($Keyword) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
