<?php

/**
 * Description: ASC安检后转向页面表
 * User: <EMAIL>
 * Date: 2021/07/23 15:53
 * Version: $Id: AscLogUrlModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class AscLogUrlModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TAscLogUrl';
    public const PRIMARY_KEY = 'AscID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['AscID'])) {
            $where .= "AND AscID = ".self::setData($cond['AscID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
