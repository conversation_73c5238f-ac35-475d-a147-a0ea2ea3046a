<?php

/**
 * Description: 各模块的在线数据
 * User: <EMAIL>
 * Date: 2023/11/7 10:32
 * Version: $Id$
 */

class ModuleOnlineRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Online';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     *
     * @var string
     */
    protected static $localColumns = '';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = true;

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'       => '',
    ];

    /**
     * 删除
     *
     * @param string $key
     *
     * @return mixed
     */
    public static function deleteOne($key)
    {
        return self::del($key);
    }

    /**
     * 删除字段
     *
     * @param array $keys
     * @param string $token
     *
     * @return mixed
     */
    public static function deleteColumn($keys, $token)
    {
        return self::delColumn($token, $keys);
    }
}
