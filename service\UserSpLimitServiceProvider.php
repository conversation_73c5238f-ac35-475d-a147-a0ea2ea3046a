<?php
/**
 * Description: 隧道代理虚拟IP申请
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: VpnServiceProvider.php 153494 2021-08-19 01:25:07Z duanyc $
 */


class UserSpLimitServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'userSpLimit';

    /**
     * 生成所有用户限速配置
     * 1000条记录处理一次
     * @return true|false
     */
    public static function setAllUserSpLimit()
    {
        try {
//            self::log("==============0==============="."\n");
            $count = AuthUserModel::getCount(['ZtpUser'=>1]);
//            self::log("==============1===============".var_export($count,true)."\n");
            $num = ceil($count/1000);
            $num = (int)$num;
//            self::log("==============2===============".var_export($num,true)."\n");
            for ($i =0 ;$i< $num ;$i ++) {
                $start = $i*1000;
                $aUser = AuthUserModel::getList(['column'=>'base','ZtpUser'=>1], "TAuthUser.ID", $start);
//                self::log("==============3===============".var_export($aUser,true)."\n");
                self::set1000UserSpLimit($aUser);
            }
        } catch (Exception $e) {
            self::log("Err:".$e->getMessage()."\n");
            return false;
        }

        return true;
    }
    /**
     * 生成所有用户限速配置
     * @param $params
     * @return true|false
     */
    public static function set1000UserSpLimit($aUser)
    {
        $aAuthUser = [];
        foreach ($aUser as $item) {
            $aAuthUser[$item['ID']] = 0;
        }
//        self::log("==============00===============".var_export($aAuthUser, true)."\n");
        $aUser = "";
        $data = [];
        $aGwData = GateWayModel::getList();
        if (empty($aGwData)) {
            return false;
        }
        $aSplimit = SpeedLimitModel::getList();
        $aSp = [];
        if (!empty($aSplimit)) {
            foreach ($aSplimit as $item) {
                $aSp[$item['ID']] = $item;
            }
            $aSplimit = "";
            $aGwEx = GateWayExpendModel::getList(['Type'=>3]);
            $aSpGwE = [];
            if (!empty($aGwEx)) {
                foreach ($aGwEx as $item) {
                    $aSpGwE[$item['RID']][] = $item['GwID'];
                }
            }
            $aSpAss = SpLimitAssociationModel::getList(['Type'=>1]);
        }
        $aGwEx = "";

        $aSpGw = [];
        $aSpUser = [];
        $aGwUser = [];

        foreach ($aGwData as $item) {
            // 每个网关赋予默认值
            $aSpGw[$item['ID']]['priority'] =  1000;
            $aSpGw[$item['ID']]['upTrafficLimit'] =  0;
            $aSpGw[$item['ID']]['downTrafficLimit'] =  0;
        }
        if (!empty($aSpAss)) {
            foreach ($aSpAss as $item) {
                if (isset($aSpGwE[$item['SpID']])) {
                    // 在默认值的基础上,对每个网关进行计算
                    foreach ($aSpGwE[$item['SpID']] as $gwId) {
                        if ($item["RID"] == 0) {
                            // 权值数字越大,优先级越低
                            if (empty($aSpGw[$gwId]['priority']) || $aSpGw[$gwId]['priority'] >=  $aSp[$item['SpID']]['Priority']) {
                                $aSpGw[$gwId]['priority'] = $aSp[$item['SpID']]['Priority']?? 1000;
                                $aSpGw[$gwId]['upTrafficLimit'] = $aSp[$item['SpID']]['UpTraffic']?? 0;
                                $aSpGw[$gwId]['downTrafficLimit'] = $aSp[$item['SpID']]['DownTraffic']?? 0;
                            }
                        } else {
                            if (empty($aSpUser[$item['RID']][$gwId]['priority']) || $aSpUser[$item['RID']][$gwId]['priority'] >= $aSp[$item['SpID']]['Priority']) {
                                $aSpUser[$item['RID']][$gwId]['priority'] = $aSp[$item['SpID']]['Priority'] ?? 1000;
                                $aSpUser[$item['RID']][$gwId]['upTrafficLimit'] = $aSp[$item['SpID']]['UpTraffic'] ?? 0;
                                $aSpUser[$item['RID']][$gwId]['downTrafficLimit'] = $aSp[$item['SpID']]['DownTraffic'] ?? 0;
                            }
                            $aGwUser[$item['RID']][$gwId] = 0;
                        }
                    }
                }
            }
        }
//        self::log("==============11===============".var_export($aGwUser, true)."\n");
        $aSpAss = "";
        if ($aSpUser != []) {
            foreach ($aSpUser as $key => $item) {
                foreach ($item as $gwId => $val) {
                    UserSpLimitRedis::setOne($key, $gwId, $val);
                }
            }
        }
        if ($aSpGw != []) {
//            self::log("==============22===============".var_export($aSpGw, true)."\n");
            foreach ($aSpGw as $gwkey => $item) {
                GwSpeedRedis::setOne($gwkey, $item);
                if (is_array($aAuthUser)) {
                    foreach ($aAuthUser as $uid => $val) {
//                        self::log("==============33===============uid::".$uid."  gwid:".$gwkey."---".!isset($aGwUser[$uid][$gwkey])."\n");
                        if (!isset($aGwUser[$uid][$gwkey])) {
                            UserSpLimitRedis::setOne($uid, $gwkey, $item);
                        }
                    }
                }
            }
        }
        $aGwUser = "";
        $aSpGw = "";
        $aSpUser = "";
        $aAuthUser = "";
        return true;
    }
    /**
     * 下发单个用户限速配置
     * @param $userID
     * @param $gwId 网关ID
     * @param $data 限速数据
     * @return true|false
     */
    public static function pushOneUserSpLimit($userID, $gwId, $data)
    {
        // $params['Token'] 单个token
        if (intval($userID) <=0 || intval($gwId) <= 0) {
            return false;
        }
        try {
            $session = UserInfoRedis::getOne($userID);
            $aGw = GatewayRedis::getOne($gwId);
            $GwInfo = [$aGw['IpIn'] ?? ''];
            if (!empty($session['Sessions'])) {
                $aSession = json_decode($session['Sessions'], true);
                foreach ($aSession as $token => $val) {
//                    SessionRedis::setOne($token,$data);
                    $tokenKey = "UserSession:".$token;
                    SessionRedis::push($tokenKey, $data, $GwInfo);
                }
            }
        } catch (Exception $e) {
            self::log($userID."===".$gwId."====".var_export($data, true)."\n Err:".$e->getMessage());
            return false;
        }
        return true;
    }
    /**
     * 生成单个用户限速配置
     * 认证成功后通过该函数设置
     * @param $params
     * @return true|false
     */
    public static function setOneUserSpLimit($token)
    {
//        self::log("=============================".$token."\n");
        if (strlen($token) !== 32) {
            return false;
        }

        $aUsers = SessionRedis::getOne($token, 'gateway');
//        self::log("===============aUsers==============".var_export($aUsers, true)."\n");
        $userId = $aUsers['Uuid'] ??'';
        $GwId = $aUsers['IpListGate'] ?? '';
        $data = [];
//        self::log("===============GwId==============".var_export($GwId, true)."\n");
        if ($GwId != '') {
            $aGwId = json_decode($GwId, true);
//            self::log("=============================".var_export($aGwId, true)."\n");
            foreach ($aGwId as $gwid) {
                if ($gwid > 0) {
                    $data = UserSpLimitRedis::getOne($userId, $gwid);
//                    self::log("=====00====UserSpLimitRedis========userId::".$userId."===gwid::".$gwid."====".var_export($data, true)."\n");
                    if (!isset($data['upTrafficLimit'],$data['downTrafficLimit'])) {
                        $data = GwSpeedRedis::getOne($gwid);
                        if (!empty($data)) {
                            UserSpLimitRedis::setOne($userId, $gwid, $data);
                        }
                    }
                    if (!empty($data)) {
                        self::pushOneTokenSpLimit($token, $gwid, $data);
                    }
                }
            }
        }
        return true;
    }

    /**
     * 下发单个用户token限速配置
     * @param $token
     * @param $gwId 网关ID
     * @param $data 限速数据
     * @return true|false
     */
    public static function pushOneTokenSpLimit($token, $gwId, $data)
    {
        if (strlen($token) !== 32) {
            return false;
        }
        try {
            $aGw = GatewayRedis::getOne($gwId);
            $GwInfo = [$aGw['IpIn'] ??''];
//            self::log($token."===".$gwId."====".var_export($data, true)."\n gwinfo：:".var_export($GwInfo,true));
            $tokenKey = "UserSession:".$token;
            SessionRedis::push($tokenKey, $data, $GwInfo);
        } catch (Exception $e) {
            self::log($token."===".$gwId."====".var_export($data, true)."\n Err:".$e->getMessage());
            return false;
        }
        return true;
    }
}
