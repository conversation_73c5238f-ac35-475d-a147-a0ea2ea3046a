<?php
/**
 * Description: SSO调用封装
 * User: <EMAIL>
 * Date: 2022/02/24 10:52
 * Version: $Id$
 */

require_once(PATH_LIBRARY . '/otheruser/sso/Interfaces/ssoAuth.php');
require_once(PATH_LIBRARY . '/otheruser/sso/common/ssoCommon.php');
require_once(PATH_LIBRARY . '/otheruser/sso/Services/casClientService.php');
require_once(PATH_LIBRARY . '/otheruser/sso/Services/oauthClientService.php');

class sso implements ssoAuth
{

    /**
     * @var casClientService|oauthClientService
     */
    private $ssoClientService;

    public function __construct($params)
    {
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        if (isset($basicConfig['Mode']) && (int)$basicConfig['Mode'] === 1) {
            $this->ssoClientService = new casClientService($params);
        } else {
            $this->ssoClientService = new oauthClientService($params);
        }
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function logout()
    {
        return $this->ssoClientService->logout();
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function login($param = []): bool
    {
        return $this->ssoClientService->login($param);
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function checkLoginState()
    {
        return $this->ssoClientService->checkLoginState();
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function getUserInfo()
    {
        return $this->ssoClientService->getUserInfo();
    }

    /**
     * @inheritDoc
     * @return bool
     */
    public function isSSOLogin(): bool
    {
        return $this->ssoClientService->isSSOLogin();
    }
}