<?php



class UserVirtIpOnlineModel extends BaseModel
{
    public const TABLE_NAME = 'TUserVirtIpOnline';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'list'   => '*'
    ];

    /**
     * 获取新的资源信息,关联多个网关
     * **/

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        if (isset($cond['Token'])) {
            $where .= "AND Token = ".self::setData($cond['Token']);
        }

        if (isset($cond['GwID'])) {
            $where .= "AND GwID = ".self::setData($cond['GwID']);
        }
        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }
        if (isset($cond['State'])) {
            $where .= "AND State = ".self::setData($cond['State']);
        }
        if (isset($cond['VirtualIp'])) {
            $where .= "AND VirtualIp = ".self::setData($cond['VirtualIp']);
        }
        if (isset($cond['PoolID'])) {
            $where .= "AND PoolID in (".self::setArrayData($cond['PoolID']).")";
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
