<?php



class GwIpPoolModel extends BaseModel
{
    public const TABLE_NAME = 'TGwIpPool';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'list'   => 'ID,PoolNam,IpNum,IpUseNum,IpPool'
    ];

    /**
     * 获取新的资源信息,关联多个网关
     * **/

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
    /**
     * updated
     * a = a + 1
     * a = a - 1
     * **/
    public static function updated($ids, $type = 1) {
        self::$data = [];
        $pa = '+';
        $wh = '';
        if ($type == 0) {
            $pa = '-';
            $wh = ' and IpUseNum > 0';
        }
        $sql = 'update TGwIpPool set IpUseNum = IpUseNum '.$pa.' 1 where ID in ('.self::setArrayData($ids).')'.$wh;
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        lib_database::query($sql,$table['index'],false,self::$data);
    }
}
