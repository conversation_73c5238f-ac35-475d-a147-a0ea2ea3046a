<?php
/**
 * Description: 设备操作方法合集
 * User: renchen
 * Date: 2021/6/3 15:15
 * Version: $Id: DeviceServiceProvider.php 163160 2021-11-26 09:42:40Z huyf $
 */

use Common\Facades\NACServiceFacade;
use Services\Device\Services;
use Services\Common\Services\DeviceOperationService;

class DeviceServiceProvider extends BaseServiceProvider
{
    /**
     * 初始化设备服务
     * @param $params
     * @return bool|Object|Services\DeviceInfoControlService|Services\DeviceInfoCookieService|Services\DeviceInfoIpGetMacService
     * @throws Exception
     */
    public static function initDeviceInfoService($params = [])
    {
        $getType = '';
        if (!empty($params['getAccessType'])) {
            $getType = $params['getAccessType'];
        }

        $serviceClass = '\\Services\\Device\\Services\\' . 'DeviceInfo' . $getType . 'Service';
        return (new ReflectionClass($serviceClass))->newInstance($params);
    }

    /**
     * 初始化设备注册服务
     * @return Services\DeviceRegisterService
     * @throws Exception
     */
    public static function initDeviceRegisterService()
    {
        return new Services\DeviceRegisterService();
    }

    /**
     * 初始化设备状态服务
     * @return Services\DeviceStatusService
     */
    public static function initDeviceStatusService()
    {
        return new Services\DeviceStatusService();
    }

    /**
     * 初始化DASM设备服务
     * @return Services\DasmDevService
     */
    public static function initDasmDevService()
    {
        return new Services\DasmDevService();
    }

    /**
     * 初始化UAM设备服务（USB）
     * @return Services\UAMDeviceService
     */
    public static function initUAMDeviceService()
    {
        return new Services\UAMDeviceService();
    }

    /**
     * 初始化设备场景服务
     * @return Services\DeviceSceneService
     */
    public static function initDeviceSceneService()
    {
        return new Services\DeviceSceneService();
    }

    /**
     * 获取位置列表信息
     *
     * @return array
     */
    public static function getAllLocation()
    {
        $cond = ['column' => 'list'];
        return LocationModel::getList($cond, false, 0, 10000);
    }

    /**
     * 获取位置列表
     *
     * @param $cond
     * @param $start
     * @param $limit
     *
     * @return array
     */
    public static function getLocationList($cond, $start = 0, $limit = 100)
    {
        $cond['column'] = 'list';
        return LocationModel::getList($cond, false, $start, $limit);
    }

    /**
     * 获取位置数量
     *
     * @param $cond
     *
     * @return mixed
     */
    public static function getLocationCount($cond)
    {
        return LocationModel::getCount($cond);
    }

    /**
     * 获取位置列表信息
     *
     * @param        $locationId
     * @param string $splitStr
     *
     * @return array
     */
    public static function getDeviceLocation($locationId, $splitStr = '　└')
    {
        if ($locationId == '0') {
            $cond = ['UpID' => 0, 'column' => 'list'];
            return LocationModel::getList($cond, false, 0, 10000);
        } else {
            $cond = ['UpID' => $locationId, 'column' => 'list'];
            $list = LocationModel::getList($cond, false, 0, 10000);
            $fileter_arr = array();
            if (is_array($list)) {
                foreach ($list as $location) {
                    $location['LocationName'] = $splitStr . $location['Location'];
                    $fileter_arr[] = $location;
                    $result = self::getDeviceLocation($location['LocationID'], "　" . $splitStr);
                    if (is_array($result)) {
                        foreach ($result as $val) {
                            $fileter_arr[] = $val;
                        }
                    }
                }
            }
            return $fileter_arr;
        }
    }

    /**
     * 设备是否可注册 原deviceRegistrable方法
     * @param string|int|null $deviceID
     * @return bool
     * @throws Exception
     */
    public static function deviceRegistrable($deviceID): bool
    {
        if (!is_null($deviceID)) {
            $registered = ComputerModel::getOne($deviceID, 'info');
            if (is_array($registered) && (int)$registered['Registered'] === 1) {
                return true;
            }
        }

        return SystemServiceProvider::isRegistrable();
    }

    /**
     * 获取在线设备信息 原getDeviceOnlineData
     * @param int $deviceID
     * @param string $userName
     * @param string $type
     * @param int $userID
     * @return array
     * @throws Exception
     */
    public static function getDeviceOnlineList(int $deviceID, string $userName, string $type, $userID): array
    {
        $status = 0;
        $aUserBindParam = DictModel::getAll('UserDevBind');
        //优先使用用户ID查询
        if (!empty($userID)) {
            $userinfo = AuthUserModel::getOne($userID, 'num');
        } else {
            $userinfo = AuthUserModel::getOneByUserName('', $userName, 'num');
        }
        if (!empty($userinfo) && null !== $userinfo["AuthNums"]) {
            $aUserBindParam['IsUse'] = $userinfo["AuthNums"];
        }

        $cond = ['AuthType' => $type, 'UserName' => $userName, 'DeviceID' => $deviceID, 'UserID' => $userinfo['ID']];
        $order = 'GROUP BY A.DeviceID ORDER BY A.AuthTime';
        $aIPCut = NacOnLineDeviceModel::getListJoin($cond, $order, 0, 100);
        $num = count($aIPCut);
        $plannum = (int)$aUserBindParam['IsUse'];
        $data = $aIPCut;
        if ($plannum > 0 && $plannum <= $num) {
            $status = 1;
        }

        // 客户端DeviceID 需要int类型
        foreach ($data as $key => $item) {
            $data[$key]['DevName'] = $item['DevName'] ?: "-";
            $data[$key]['DeviceID'] = (int)$item['DeviceID'];
        }

        return ['num' => $num, 'plannum' => $plannum, 'data' => $data, 'devmax_status' => $status];
    }

    /**
     * 校验设备是否存在
     *
     * @param $deviceId
     *
     * @throws Exception
     */
    public static function checkDeviceExist($deviceId)
    {
        if (!empty($deviceId)) {
            $deviceInfo = DeviceModel::getOne($deviceId, 'info');
            if (empty($deviceInfo)) {
                T(21120003);
            }
        }
    }

    /**
     * 调用asmsvr接口查询设备ID
     *
     * @param $hard
     * @param $mac
     * @param $ip
     *
     * @return mixed
     * @throws Exception
     */
    public static function queryDeviceID($hard, $mac, $ip)
    {
        $deviceOperationService = new DeviceOperationService();
        $res = $deviceOperationService->getDeviceID($hard, $mac, $ip);
        if ($res['code'] !== 1) {
            throw new Exception($res['message']);
        }
        return $res['data']['DeviceID'];
    }

    /**
     * @Description:获取设备信息
     * @param $deviceId
     * @return mixed
     * @throws Exception
     */
    public static function getDevInfo($deviceId)
    {
        $device = DeviceModel::getJoinRelationComputer($deviceId, 'relattionInfo');
        if (empty($device)) {
            T(21103006);
        }
        // 优先用当前角色
        $resNacOnLineDevice = NacOnLineDeviceModel::getOne($deviceId, 'name');
        if (!empty($resNacOnLineDevice['SceneID'])) {
            $device['SceneID'] = (int)$resNacOnLineDevice['SceneID'];
        }
        if (empty($device['RoleID'])) {
            $aResult = DeviceModel::getDepartRole($deviceId);
            $device['RoleID'] = $aResult['RoleID'] ?? ROLE_ID_DEFAULT;  //缺省角色;
        }
        $device['SceneID'] = $device['SceneID'] ?? DEFAULT_SCENE_ID;
        $device['IsAgainReg'] = AuthServiceProvider::getIsAgainReg((int)$device['SceneID'], (int)$deviceId);
        // 已自动审核的设备需要重新审核则返回未注册的状态
        if ($device['IsAgainReg'] == 1 && $device['Registered'] == 1) {
            $device['Registered'] = -2;
        }
        if ($device['IsTrustDev'] != 1) {
            if ($device['CutOffStopTime'] !== DEFAULT_TIME && time() < strtotime($device['CutOffStopTime'])) {
                $device['IsCutOffDev'] = 1;
            } else {
                $device['IsCutOffDev'] = 0;
            }
        } else {
            if ($device['TrustStopTime'] != '9999-12-31 00:00:00' && time() > strtotime($device['TrustStopTime'])) {
                $device['IsTrustDev'] = 0;
            }
        }

        if (strtotime($device['AuditStopTime']) < time() && $device['AuditStopTime'] !== DEFAULT_TIME) {
            $device['AuditStop'] = 1;
        } else {
            $device['AuditStop'] = 0;
        }
        return $device;
    }

    /**
     * 发送ipv6报文 原方法AddIPv6AddrForDevice
     *
     * @param $ipv6data
     *
     * @return bool
     * @throws Exception
     */
    public static function addIPv6AddrForDevice($ipv6data)
    {
        $ipv6_enable = get_ini_info(PATH_ETC . 'asm/asc/etc/tbridge_private.ini', 'ipv6_bridge_enable');
        if ((int)$ipv6_enable === 1) {
            $xml = setIPv6Xml($ipv6data);
            cutil_php_debug($xml, 'selectipv6');
            cutil_exec_no_wait("asmsvr_internal_trade -c '$xml'");
        }
        return true;
    }

    /**
     * 获取设备的注册状态.
     *
     * @param $deviceID
     * @return false|mixed
     */
    public static function getDevRegistered($deviceID)
    {
        if (empty($deviceID)) {
            return false;
        }
        $result = ComputerModel::getOne($deviceID, 'info');

        return $result['Registered'] ?? false;
    }

    /**
     * 卸载客户端
     *
     * @param $deviceId
     */
    public static function uninstall($deviceId): void
    {
        DeviceModel::update($deviceId, ['InstallClient' => 0, 'ChangeFlags' => '']);
        // 更新缓存中的ChangeFlags状态
        cache_set_info('DeviceID_', $deviceId, ['TDevice.ChangeFlags' => null]);
        $cutoffConfig = [
            'is_force_cutoff' => 1,
            'is_isolated_access' => 1,
            'show_user_one_message' => L(21102002),
            'remark' => 'UninstallClient', // 终端准入状态服务使用
        ];
        NACServiceFacade::cutoff('WebCutoff', [['device_id' => $deviceId]], $cutoffConfig);
        cutil_php_debug('uninstall success：deviceId:' . $deviceId, 'uninstall_assistant');
    }

    /**
     * 修改设备信息
     *
     * @param $data
     *
     * @return array
     * @throws Exception
     */
    public static function updateDevInfo($data): array
    {
        $service = new DeviceOperationService();
        return $service->updateDevice($data);
    }

    /**
     * 解析敲门参数为设备ID
     *
     * @param $DeviceData
     *
     * @return string
     * @throws Exception
     */
    public static function parseFwknopData($DeviceData): string
    {
        $Data = hlp_common::parseFwknopData($DeviceData);
        $Ip = $Data[0] ?? '';
        $Mac = $Data[1] ?? '';
        $DiskId = $Data[2] ?? '';
        if (empty($Ip) || empty($Mac) || empty($DiskId)) {
            self::log("parseFwknopData: {$DeviceData}: " . json_encode($Data));
            T(21148021);
        }
        $DeviceId = $Data[3] ? $Data[3] : self::queryDeviceID($DiskId, $Mac, $Ip);
        $IsMobile = $Data[4] ?? 0;
        $deviceObj = DeviceModel::getOne($DeviceId, 'one');
        if (!empty($deviceObj) && $deviceObj['Mac'] === $Mac && $deviceObj['IP'] === $Ip) {
            return $DeviceId;
        }

        return self::getDeviceIDByInsert($Ip, $Mac, $DiskId, $IsMobile);
    }

    /**
     * 插入并获取设备ID
     *
     * @param $Ip
     * @param $Mac
     * @param $DiskId
     * @param $IsMobile
     *
     * @return string
     * @throws Exception
     */
    public static function getDeviceIDByInsert($Ip, $Mac, $DiskId, $IsMobile): string
    {
        $deviceOperationService = new DeviceOperationService();
        $data = ['IP' => $Ip, 'Mac' => $Mac, 'Hard' => $DiskId];
        $res = $deviceOperationService->insertDevice($data);
        if ($res['code'] !== 1) {
            throw new Exception($res['message']);
        }
        $DeviceID = $res['data']['DeviceID'];
        DeviceModel::update($DeviceID, ['Type' => $IsMobile ? DEVICE_TYPE_MOBILE : DEVICE_TYPE_PC]);
        return $DeviceID;
    }

    /**
     * 返回是否可信设备
     * 永久可信返回true
     * 期限可信在期限内返回true，否则false
     * @param $DeviceID
     * @return bool
     */
    public static function isTrustDev($DeviceID): bool
    {
        $isTrustInfo = RelationComputerModel::getOne($DeviceID, 'isTrust');
        if (empty($isTrustInfo)) {
            return false;
        }

        $isTrustDev = (int)$isTrustInfo['IsTrustDev'];
        $trustStopTime = $isTrustInfo['TrustStopTime'];
        if ($isTrustDev === 0) {
            return false;
        }
        if ($trustStopTime !== '9999-12-31 00:00:00' && time() > strtotime($trustStopTime)) {
            return false;
        }
        return true;
    }

    /**
     * 获取设备的token
     * @param int $deviceID
     * @return string
     */
    public static function getDeviceToken($deviceID): string
    {
        if (empty($deviceID)) {
            return '';
        }
        $token = DeviceTokenRedis::getOne($deviceID);
        if (empty($token)) {
            return '';
        }
        return $token;
    }

    /**
     * 获取单条设备信息
     * @param $deviceID
     * @param string $column
     * @return false|mixed
     */
    public static function getOneInfo($deviceID, $column = 'info')
    {
        return DeviceModel::getOne($deviceID, $column);
    }

    /**
     * 生成设备DiskID
     *
     * @param $ip
     *
     * @return mixed
     * @throws Exception
     */
    public static function genDeviceDiskID($ip)
    {
        $key = $ip . microtime(true) . random_int(100000, 999999);
        return sha1($key);
    }

    /**
     * 获取当前会话的diskId
     *
     * @return mixed
     */
    public static function getLastDeviceDiskID()
    {
        if (IS_INTERNAL) {
            return '';
        }
        $diskId = $_SERVER['HTTP_DEVICE_DISKID'] ?? '';
        return strlen($diskId) === 40 ? $diskId : '';
    }

    /**
     * 获取设备DiskID
     *
     * @return mixed
     * @throws Exception
     */
    public static function getDeviceDiskID()
    {
        if (IS_INTERNAL) {
            return '';
        }
        $DiskID = self::getLastDeviceDiskID();
        if (empty($DiskID)) {
            $ip = getRemoteAddress();
            $DiskID = self::genDeviceDiskID($ip);
        }
        return $DiskID;
    }

    /**
     * ip变动重新放开网络
     * @param int $deviceId
     * @param string $ip
     * @param string $source
     * @return int -1:参数错误 0:未放开网络 1:放开网络
     * @throws Exception
     */
    public static function ipRangeOpenNet(int $deviceId, string $ip, string $source): int
    {
        if ($deviceId <= 0 || empty($ip) || empty($source)) {
            return -1;
        }
        // 如果本身不是放开状态，则走流程后放开，这里只针对已经放开网络的设备，IP变换之后的放开
        $serviceStatus = self::initDeviceStatusService();
        $netStatus = $serviceStatus->getDeviceIpRegion($deviceId);
        if ($netStatus === 1) {
            // 通知放开网络
            $netParams = ['device_id' => $deviceId, 'ip' => $ip];
            NACServiceFacade::access("WebAccess:ipRangeOpenNet->" . $source, [$netParams], 'IpRangeOpenNet');

            // IP变动,执行命令 WebAccess
            return 1;
        }

        // IP变动,未放开网络不处理
        return 0;
    }


    /**
     * 处理病毒原始数据放入redis
     * @param $infoData
     * @return bool
     */
    public static function setDeviceAntiVirusResult($infoData): bool
    {
        if (empty($infoData)) {
            return false;
        }

        $deviceAntiArrTmp = [];
        $deviceIdArr = [];
        try {
            foreach ($infoData as $deviceId => $info) {
                // 最新的设备结果存储在hash ASM_Result:10，10为设备ID，AntiVirus：[{AntiVirusType：'Riskware',AntiVirusNum:''}]
                if (!empty($info['untreated'])) {
                    foreach ($info['untreated'] as $value) {
                        $deviceAntiArrTmp[] = ['AntiVirusType' => $value['type'], 'AntiVirusNum' => $value['num']];
                    }
                }
                $deviceAntiArr['AntiVirus'] = $deviceAntiArrTmp;
                ResultRedis::setOne($deviceId, $deviceAntiArr);
                $deviceAntiArrTmp=[];
                $deviceIdArr[] = $deviceId;
            }
            if (!empty($deviceIdArr)) {
                //批量修改匹配计算为需要计算
                $cond = ['InDeviceIDs' => $deviceIdArr];
                DynamicSafeResultModel::updatePatch($cond, ['IsNeedMatch' => 1]);
                // 此处需要异步触发下针对批量计算权限，获得立马计算的效果
                foreach ($deviceIdArr as $deviceID) {
                    //通过设备ID获取到对应的token
                    $token = DeviceTokenRedis::getOne($deviceID);
                    !empty($token) && TokenRedis::lPushData($token, 'setUserPower');
                }
                // 调用触发下实时计算权限
                \lib_queue::addJob('COMMON_RUN', ['run_func' => 'setUserPower']);
            }
        } catch (\Exception $exception) {
            cutil_php_log("setDeviceAntiVirusResult err deviceID:" . json_encode($deviceIdArr), 'setDeviceAntiVirusResult');
            cutil_php_log("setDeviceAntiVirusResult err:" . $exception->getTraceAsString(), 'setDeviceAntiVirusResult');
        }

        return true;
    }

}
