<?php
/**
 * Description: 免认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: NoAuthService.php 160319 2021-10-29 08:45:53Z duanyc $
 */

namespace Services\Auth\Services;

use AuthServiceProvider;
use Exception;
use Services\Auth\Interfaces\AuthServiceInterface;

class NoAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = AUTH_TYPE_NOAUTH;

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数校验
     * @return array|bool|void
     * @throws Exception
     */
    public function parseParams()
    {
        // 从场景中获取userType 处理当前端获取到来宾场景之后进行免认证登录 如果默认userType为空 则会获取到员工场景导致认证问题
        $userType =  AuthServiceProvider::getDeviceUserType($this->deviceId);
        $sceneInfo = AuthServiceProvider::getSceneInfoByDeviceID($this->deviceId, $userType);
        if (1 !== $sceneInfo['code']) {
            T($sceneInfo['code']);
        }
        if ($sceneInfo['data']['IsAuth'] && AuthServiceProvider::checkTrustDevResourceIsNeedAuth($this->deviceId)) {
            T(21120041);
        }
    }

    /**
     * 获取验证服务
     *
     * @return array
     */
    public function getAuthServer(): array
    {
        if ($this->params['subType'] === 'Wechat') {
            return ['Wechat'];
        }

        return [];
    }

    /**
     * 是否需要认证，需要认证，则不能进行免认证
     *
     * @param $isAuth
     * @param $AuthIP
     *
     * @return int
     */
    public function isNeedAuth($isAuth, $AuthIP): int
    {
        // 高级动态认证不需要校验
        if (!empty($this->params['notNeedAuth'])) {
            return 0;
        }
        // 只验证连接IP,使用连接IP而不是真实IP
        $ip = getRemoteAddress();
        if ($isAuth && $AuthIP == "") {
            return 1;
        }
        if (!$isAuth) {
            return 0;
        }
        // 走到这里说明开启了认证，只要在免认证IP范围则返回不需要认证
        if ($AuthIP != "") {
            $aAuthIP = explode(',', $AuthIP);
            $authCount = count($aAuthIP);
            for ($i = 0; $i < $authCount; $i++) {
                $aIP = explode('-', $aAuthIP[$i]);
                $startip = $aIP[0];
                $endip = $aIP[1];
                if (ipToNum($ip) >= ipToNum($startip) && ipToNum($ip) <= ipToNum($endip)) {
                    return 0;
                }
            }
        }
        return 1;
    }
}
