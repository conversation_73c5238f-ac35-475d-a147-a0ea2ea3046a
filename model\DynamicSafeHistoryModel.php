<?php

/**
 * Description: 动态策略主表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id
 */

class DynamicSafeHistoryModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDynamicSafeHistory';
    public const PRIMARY_KEY = 'HistoryID';
    protected static $columns = [
        'one' => 'ResultID,DeviceID,UserId,DPolicyID,Result,CalculateTime,IsNeedMatch,Status',
        '*'   => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['HistoryID'])) {
            $where .= "AND HistoryID = ".self::setData($cond['HistoryID']);
        }

        if (isset($cond['DPolicyID'])) {
            $where .= "AND DPolicyID = ".self::setData($cond['DPolicyID']);
        }

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['UserId'])) {
            $where .= "AND UserId = ".self::setData($cond['UserId']);
        }

        if (isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
