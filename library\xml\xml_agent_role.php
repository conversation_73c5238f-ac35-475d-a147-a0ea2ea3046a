<?php

/**
 * Description: 控件相关配置
 * User: <EMAIL>
 * Date: 2021/08/18 15:53
 * Version: $Id: xml_agent_role.php 153422 2021-08-18 08:30:56Z duanyc $
 */


/**
 * ***************************************** 解析控件相关配置信息 **********************************************
 */
class xml_agent_role extends cls_xml
{
    /**
     * 解析安检报文
     * @return array
     */
    public function parseXml()
    {
        if (is_array($this->res)) { //防止重复添加
            return $this->res;
        }
        $data = [];
        $data['OpenReCheckAction'] = $this->xml->ReCheckActions->OpenReCheckAction;
        $data['AfterReboot'] = $this->xml->ReCheckActions->AfterReboot;
        $data['ScreensaversRecovery'] = $this->xml->ReCheckActions->ScreensaversRecovery;
        $data['SleepRecovery'] = $this->xml->ReCheckActions->SleepRecovery;
        $data['UnLock'] = $this->xml->ReCheckActions->UnLock;
        $data['SwitchUser'] = $this->xml->ReCheckActions->SwitchUser;
        $data['Logout'] = $this->xml->ReCheckActions->Logout;
        $data['NoOperation'] = $this->xml->ReCheckActions->NoOperation;
        $data['NoOperation_Time'] = $this->xml->ReCheckActions->NoOperation_Time;
        $this->res['ReCheckActions'] = $data;
        $this->convertEncode();
        return $this->res;
    }
}
