<?php

/**
 * Description: 资源申请表
 * User: <EMAIL>
 * Date: 2023/10/26 23:32
 * Version: $Id: ResApplyModel.php $
 */

class ResApplyModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResourceApply';
    public const PRIMARY_KEY = 'ApplyID';
    protected static $columns = [
        '*' => '*',
        'log'=>'ApplyID,ResID,UserID,ResName,ApplyDays,Content,Status,InsertTime,UpdateTime',
        'apply' => 'UserID',
    ];
    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = " . self::setData($cond['UserID']);
        }

        if (isset($cond['ResID'])) {
            $where .= "AND ResID = " . self::setData($cond['ResID']);
        }

        if (isset($cond['Status'])) {
            $where .= "AND Status = " . self::setData($cond['Status']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
