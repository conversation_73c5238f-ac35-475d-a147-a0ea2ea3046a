<?php

/**
 * Description: redis hash通用redis
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: BaseRedis.php 175039 2022-05-05 07:33:41Z duanyc $
 */
class BaseHashRedis
{
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Dict';

    /**
     * 获取key
     *
     * @param array $keys
     *
     * @return mixed
     */
    public static function getKey($keys)
    {
        if (empty($keys)) {
            return static::TABLE_NAME;
        }

        return static::TABLE_NAME . ":" . implode('_', $keys);
    }

    /**
     * 单条
     *
     * @param array $columns array|string|null
     * @param array $keys
     *
     * @return array|string|null
     */
    protected static function get($columns = [], ...$keys)
    {
        $key = static::getKey($keys);
        $return = lib_redis::getHash(static::PREFIX, $key, $columns);
        if ($return) {
            foreach ($return as $column => $val) {
                if ($val === null) {
                    unset($return[$column]);
                }
            }
        }
        return $return;
    }

    /**
     * 获取数量
     * @param mixed ...$keys
     * @return int
     */
    protected static function count(...$keys)
    {
        $key = static::getKey($keys);
        $keys = lib_redis::keys(static::PREFIX . $key . "*");
        return count($keys);
    }

    /**
     * 修改
     *
     * @param array $key_values
     * @param array $keys
     *
     * @return boolean
     */
    protected static function set($key_values = [], ...$keys)
    {
        if (empty($key_values)) {
            return false;
        }
        $key = static::getKey($keys);
        $expire = $key_values['LifeTime'] ?? null;
        cutil_php_log(['set', $key, $key_values], "model_" . static::TABLE_NAME);
        // 删除LifeTime本身存入 hash key 中
        empty($expire) && $expire=$key_values['LifeTimeNotInKey'] ?? null;
        unset($key_values['LifeTimeNotInKey']);
        $redoCount = $key_values['redoCount'] ?? 3;
        for ($i = 0; $i < $redoCount; $i++) {
            $return = lib_redis::hMSetEx(static::PREFIX . $key, $key_values, $expire);
            if ($return) {
                break;
            }
            cutil_php_log(['setErr', $key, $return], "model_" . static::TABLE_NAME);
        }
        return $return;
    }

    /**
     * 删除
     *
     * @param array $keys
     *
     * @return boolean
     */
    public static function del(...$keys)
    {
        if (empty($keys)) {
            return false;
        }

        $key = static::getKey($keys);
        cutil_php_log(['del', $key], "model_" . static::TABLE_NAME);
        return lib_redis::mDel(static::PREFIX, [$key]);
    }


    /**
     * 获取hash类型的len长度
     * @param ...$keys
     * @return false|mixed|null
     */
    public static function hlen(...$keys)
    {
        if (empty($keys)) {
            return false;
        }
        $key = static::getKey($keys);
        return lib_redis::hLen(static::PREFIX, $key);
    }
    /**
     * 删除hash类型 中的某一个fields
     * @param $key
     * @param $fields
     * @return bool|null
     */
    public static function delColumn($key, $fields): ?bool
    {
        if (empty($key) || empty($fields)) {
            return false;
        }

        $keys = static::TABLE_NAME . ":" .$key;
        cutil_php_log(['delColumn', $key], "model_" . static::TABLE_NAME);
        return lib_redis::mDel(static::PREFIX, [$keys], $fields);
    }
}
