<?php
/**
 * Description: 零信任用户态登录的父类
 * User: <EMAIL>
 * Date: 2022/03/4 15:53
 * Version: $Id: ZtpController.php 175007 2022-05-05 02:51:17Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class ZtpController extends BaseController
{
    /**
     * 存储当前设备ID
     * @var array
     */
    public $deviceId = '';

    /**
     * 存储当前用户ID
     * @var array
     */
    public $userId = '';

    /**
     * 存储当前session的数据
     * @var array
     */
    public $session = [];

    /**
     * 存储ZTP
     * @var array
     */
    public $resInfo = [];

    /**
     * 不检查设备ID的方法 如：['auth' => true]
     * @var array
     */
    public $nocheck = [];

    /**
     * 不检查零信任用户的方法 如：['auth' => true]
     * @var array
     */
    public $nocheckZtpUser = [];

    /**
     * 通过检查资源cookie，来校验登录的方法 如：['auth' => true]
     * @var array
     */
    public $checkCookie = [];

    /**
     * 公共判断
     * @throws Exception
     */
    public function __construct()
    {
        parent::__construct();
        $this->deviceId = request('deviceid', 'request');
        $this->checkLogin();
    }

    /**
     * 检查登录
     * @throws Exception
     */
    private function checkLogin(): void
    {
        $ZTPCookie = request('ZTP_Cookie', 'cookie');

        if (!empty($this->checkCookie[$this->method])) {
            list($this->session, $this->resInfo) = LoginServiceProvider::checkCookieLogin($ZTPCookie);
        } else {
            $check = !empty($this->nocheck[$this->method]) ? false : true;
            $ztpNocheck = !empty($this->nocheckZtpUser[$this->method]);
            $this->session = LoginServiceProvider::checkLogin($this->deviceId, $check, $ztpNocheck);
        }

        $this->userId = $this->session['Uuid'] ?? 0;
        // 增加白名单的控制器方法不做这个检测
        if (!empty($this->nocheckZtpUser[$this->method])) {
            return;
        }

        // 非零信任用户没有权限
        $info = UserInfoRedis::getOne($this->userId, 'ZtpUser');
        $URI = $_SERVER['REQUEST_URI'];
        if (!$info['ZtpUser'] && (strpos($URI, 'ztpResource/ztpUserExceed') === false)) {
            T(21148053);
        }

    }

    /**
     * 检查资源
     *
     * @param $ResID
     *
     * @return mixed
     * @throws Exception
     */
    protected function checkResource($ResID)
    {
        $DepartIDs = $this->session['DepartIDs'];
        $RoleID = $this->session['RoleID'];
        $DeviceID = $this->session['DeviceID'];
        $Power = ResourceServiceProvider::getUserResourcePower($this->userId, $DeviceID, $DepartIDs, $RoleID, $ResID);
        if (empty($Power)) {
            T(21148001);
        }
        //解决自定义应用检查策略功能前移，其他类型的策略检测在网页跳转后也会再次检查，由于依赖应用需要检查策略此处去掉判断
        $checkRes = ResourceServiceProvider::checkResource($this->userId, $this->session['Token'], $ResID);
        //检查依赖应用
        if (!empty($checkRes['DependResIDs'])) {
            $this->checkDependResource($checkRes['DependResIDs']);
        }
        return $checkRes;
    }

    /**
     * 资源的策略检查
     *
     * @param $ResID
     *
     * @throws Exception
     */
    protected function checkResourcePolicy($ResID): void
    {
        try {
            $session = SessionRedis::getOne($this->session['Token'], 'policy');
            $PolicyID = PolicyServiceProvider::getPolicyID($ResID);
            if (!empty($PolicyID)) {
                $session['NoCheckRequireAuth'] = true;
                PolicyServiceProvider::checkPolicy($session, $PolicyID);
            } else {
                PolicyServiceProvider::checkNoPolicy($session, [$ResID]);
            }
        } catch (Exception $e) {
            $needClient = strpos($e->getMessage(), L(21148029)) !== false;
            $GLOBALS['CONFIG']['EXCEPTION_DATA']['needClient'] = $needClient;
            throw new Exception(str_replace('<br>', "", $e->getMessage()), $e->getCode());
        }
    }

    /**
     * 检查依赖资源
     *
     * @param $ResIDs
     *
     * @return mixed
     * @throws Exception
     */
    protected function checkDependResource($ResIDs)
    {
        if (empty($ResIDs)) {
            return false;
        }
        $DepartIDs = $this->session['DepartIDs'];
        $RoleID = $this->session['RoleID'];
        $DeviceID = $this->session['DeviceID'];
        $ResIDs = is_array($ResIDs) ? $ResIDs : explode(',', $ResIDs);
        $Power = ResourceServiceProvider::getUserResourcePower($this->userId, $DeviceID, $DepartIDs, $RoleID, $ResIDs);
        if (empty($Power)) {
            T(21148051);
        }
        //需要检查依赖应用对应的策略
        foreach ($ResIDs as $resID) {
            $this->checkResource($resID);
            $this->checkResourcePolicy($resID);
        }
        return true;
    }
}
