<?php

/**
 * Description: 管理worker
 * User: <EMAIL>
 * Date: 2021/08/24 15:53
 * Version: $Id: index.php 158081 2021-09-28 04:48:29Z duanyc $
 */
define('PATH_ROOT_CLI', strtr(__FILE__, ['\\' => '/', '/worker/index.php' => '', 'index.php' => '']));
define('IN_ACCESS', true);
include PATH_ROOT_CLI . '/init.php';

/* 永不超时 */
ini_set('max_execution_time', 0);

/* 执行worker */
try {
    worker_run_all();
} catch (\Exception $e) {
    cutil_php_log("error: " . $e->getMessage(), 'worker');
}

exit();

/**
 * worker_run_all函数
 * @throws Exception
 */
function worker_run_all()
{
    include_config('cronb');
    $time = microtime(true);
    try {
        $ipinfo = get_ini_info(PATH_ETC . 'deveth.ini.noback', ['hastatus']);
        if (in_array($ipinfo['hastatus'], [4, 5], false)) {
            cutil_php_log('backup worker stop! ', 'worker');
            exit();
        }
    } catch (Exception $e) {
        cutil_php_log('backup worker stop err: '.$e->getMessage(), 'worker');
    }

    /* 提取要执行的文件 */
    $exe_file = array();

    foreach ($GLOBALS['CROND_TIMER']['the_format'] as $format) {
        $key = date($format, ceil($time));

        if (isset($GLOBALS['CROND_TIMER']['the_time'][$key]) && is_array(@$GLOBALS['CROND_TIMER']['the_time'][$key])) {
            foreach ($GLOBALS['CROND_TIMER']['the_time'][$key] as $file => $devtypes) {
                if (hlp_common::isAccessRun($file, $devtypes)) {
                    $exe_file[] = $file;
                }
            }
        }
    }

    echo "\n" . date('Y-m-d H:i', time()), "\n\n";
    cutil_php_log('start worker: ', 'worker');

    /* 加载要执行的文件 */
    foreach ($exe_file as $file) {
        cutil_php_log("start ". $file, 'worker');
        echo '>> ', $file,"\n";
        //@include __THIS__ . '/' . $file;
        process_execute($file);
        echo "\n\n";
    }

    echo 'worker total: ', microtime(true) - $time . "\n";
    cutil_php_log("finish", 'worker');
}

/**
 * 多进程执行脚本
 * @param mixed $input
 */
function process_execute($input)
{
    //创建子进程
    $pid = pcntl_fork();

    //子进程
    if ($pid == 0) {
        $pid = posix_getpid();
        echo "** Sub process {$pid} was created for {$input}:\n\n";

        try {
            if (empty($GLOBALS['CONFIG']['queue'][$input])) {
                throw new Exception("ignore queue {$input}.");
            }

            lib_queue::runQueue($input);
            exit();
        } catch (Exception $e) {
            cutil_php_log("run error". $e->getMessage(), 'worker');
            echo "error:" . $e->getMessage() . PHP_EOL;
            exit();
        }
    }
    //创建失败
    elseif ($pid < 0) {
        echo "\n** Error to create sub process\n";
        exit;
    }
}
