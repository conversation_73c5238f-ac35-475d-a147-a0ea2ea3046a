<?php

/**
 * Description: 安检相关
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: CheckController.php 160704 2021-11-03 13:03:33Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class CheckController extends BaseController
{
    /**
     * 获取安检项，对应老交易 getcheckitem
     *
     * @return array
     * @throws Exception
     */
    public function item()
    {
        $itemid = request('itemid', 'request', 0, 'int');
        hlp_check::checkEmpty($itemid);
        $item = ResultServiceProvider::getCheckItem($itemid);

        if (empty($item)) {
            T(21104003);
        }

        $content = $item['Content'];
        // 由于小助手xml报文是自己写的解析器，不支持自闭合标签，这里使用字符串替换的土方法
        $end = '</CheckType>';
        $pos = strrpos($content, $end);
        if ($pos !== false) {
            $isRepairItem = $item['IsRepairItem'];
            $addIsRepairItem = "<IsRepairItem>$isRepairItem</IsRepairItem></CheckType>";
            $content = substr_replace($content, $addIsRepairItem, $pos, strlen($end));
        }

        $content = str_replace("\t", '', $content);
        if (API_VERSION < '1.0') {
            return $content;
        }

        return ['Content' => $content];
    }

    /**
     * 获取规范信息，对应老交易 getcheckpolicy
     *
     * @return array
     * @throws Exception
     */
    public function policy(): array
    {
        $params = [];
        $params['policyId'] = request('policy_id', 'request', 0, 'int');
        $params['ostype'] = OSTYPE_WINDOWS;
        $ostype = request('ostype', 'request', hlp_compatible::getOsType());
        if ($ostype != null && (in_array($ostype, SystemServiceProvider::$ostypes) || $ostype === 'old_android')) {
            $params['ostype'] = $ostype;
        }

        if (!$params['policyId']) {
            T(21104001);
        }

        $policyInfo = ResultServiceProvider::getPolicy($params['policyId']);
        if (!is_array($policyInfo)) {
            T(21104002);
        }

        $policyInfo['PolicyBody'] = ResultServiceProvider::getPolicyBody($params['ostype'], $policyInfo['PolicyBody']);
        return $policyInfo;
    }

    /**
     * PC安检上报，对应老交易 submit.php
     *
     * @return array
     * @throws Exception
     */
    public function submit(): array
    {
        $params = [];
        $params['deviceId'] = request('deviceid', 'request', 0, 'int');
        $params['ostypeMac'] = request('ostype_mac', 'request', 0, 'int');
        $params['policyId'] = request('policyid', 'request', 0, 'int');
        $params['itemsid'] = request('itemsid', 'request');
        $params['checkitem'] = request('checkitem', 'request');
        $params['checkres'] = request('checkres', 'request');
        $params['checkres'] = $params['ostypeMac'] == 1 ? $params['checkres'] : urldecode($params['checkres']);
        $params['roleId'] = request('roleid', 'request', 0, 'int');
        $params['sceneId'] = request('sceneId', 'request', 0, 'int');
        $params['auditStatus'] = request('AuditStatus', 'request');
        $params['processtime'] = request('processtime', 'request');
        $params['isSafecheck'] = request('is_safecheck', 'request', 0, 'int');
        $params['lastAuthID'] = request('LastAuthID', 'request');
        $params['repair'] = request('repair', 'request');
        $params['reCheck'] = request('reCheck', 'request');
        $params['authType'] = request('auth_type', 'request');
        $params['userName'] = request('user_name', 'request');
        $params['isActive'] = request('isActive', 'request', 0, 'int');
        $params['enforcement'] = request('enforcement', 'request');
        $params['bas'] = request('bas', 'request');
        $params['tbclientip4'] = request('tbclientip4', 'request');
        $params['res'] = request('Res', 'request');
        $params['controlPostion'] = request('ControlPostion', 'request');
        $params['from'] = request('from', 'request');
        $params['callPageFrom'] = ($params['from'] === 'assistant' ? 'assistant' : 'web');//小助手打开入网流程
        $params['agentOpenSubmit'] = (lib_request::$method === 'POST' && $params['from'] === 'assistant' ? 1 : 0);//小助手直接打开安检结果页面
        $params['from'] = request('from', 'request');
        $params['submitKey'] = request('SubmitKey', 'request');
        ResultServiceProvider::resultCheck($params['deviceId'], $params['submitKey']);

        if (empty($params['deviceId'])) {
            T(21104004);
        }

        $params['servicePrefix'] = 'Pc';
        $resultService = ResultServiceProvider::initResultService($params);
        $resultService->init();
        $return = $resultService->submit();
        $resultService->browserResultSuccess();
        return $return;
    }

    /**
     * 移动端安检上报，对应老交易 mobileresult
     *
     * @return array
     * @throws Exception
     */
    public function mobileResult()
    {
        $params = [];
        $params['deviceId'] = request('deviceid', 'request', 0, 'int');
        $params['policyId'] = request('policyid', 'request', 0, 'int');
        $params['itemsid'] = request('itemsid', 'request');
        // 客户端自动认证 自动认证入网功能增加判断，当为客户端后台自动认证时，判断其是否存在多个安检项ID 如果存在，对其加密进行解析处理
        if (strpos($params['itemsid'], '||') === false && !is_numeric($params['itemsid'])) {
            $params['itemsid'] = Base64DeExt($params['itemsid']);
        }
        $params['checkres'] = request('checkres', 'request');
        $params['roleId'] = request('roleid', 'request', 0, 'int');
        $params['sceneId'] = request('sceneId', 'request', 0, 'int');
        $params['processtime'] = request('processtime', 'request');
        $params['isSafecheck'] = request('is_safecheck', 'request');
        $params['lastAuthID'] = request('LastAuthID', 'request');
        $params['firsturl'] = request('firsturl', 'request');
        $params['enforcement'] = request('enforcement', 'request');
        $params['isPhoneClient'] = request('IsPhoneClient', 'request');
        $params['tbclientip4'] = request('tbclientip4', 'request');
        $params['ip'] = request('ip', 'request');
        $params['mac'] = request('mac', 'request');
        $ascid = request('ascid', 'request');
        $params['ascid'] = !empty(str_replace(":", "", $ascid)) && strlen($ascid) == 17 ? $ascid : "";

        if (empty($params['deviceId'])) {
            T(21104004);
        }

        $params['servicePrefix'] = 'Mobile';
        $resultService = ResultServiceProvider::initResultService($params);
        $resultService->init();
        $return = $resultService->submit();
        $resultService->isolationDevice($params['deviceId'], $return['CheckResult']['Res']);
        $resultService->clientResultSuccess();
        return $return;
    }

    /**
     * 安检完成后返回给客户端的数据
     *
     * @return mixed
     * @throws Exception
     */
    public function success()
    {
        $params = [];
        $params['roleId'] = request('roleid', 'request');
        $params['sceneId'] = request('sceneid', 'request');
        $params['deviceId'] = request('deviceid', 'request');
        $params['username'] = request('user_name', 'request');
        $params['policyId'] = request('policyid', 'request');
        $params['authType'] = request('auth_type', 'request');
        $params['enforcement'] = request('enforcement', 'request');
        $params['bas'] = request('bas', 'request');
        $params['isGuest'] = request('is_guest', 'request');
        $params['isAutoAuth'] = request('is_auto_auth', 'request');
        hlp_check::checkEmpty($params['roleId']);

        if (empty($params['deviceId']) || empty($params['sceneId'])) {
            T(21104004);
        }

        $params['servicePrefix'] = 'Pc';
        $resultService = ResultServiceProvider::initResultService($params);
        $resultService->init();

        //终端隔离列表 add by zhangkun
        $resultService->initPolicyInfo();
        $submitInfo = $resultService->getResultInfo();
        $resultService->isolationDevice($params['deviceId'], $submitInfo['CheckResult']);
        $resultService->clientResultSuccess();
        $resultService->publishIpRenew($params['deviceId']);
        return $submitInfo;
    }

    /**
     * 安全加固记录上报
     *
     * @return mixed
     * @throws Exception
     */
    public function securityFix()
    {
        $params = [];
        $params['policyId'] = request('policyid', 'request', 0, 'int');
        $params['InsideName'] = request('InsideName', 'request');
        $params['DeviceID'] = request('DeviceID', 'request', 0, 'int');
        if (!$params['policyId']) {
            T(21104001);
        }
        ResultServiceProvider::securityFix($params['DeviceID'], $params['policyId'], $params['InsideName']);
        return [];
    }

    /**
     * 移动端自动认证安检 原policycheckitem接口
     *
     * @return mixed
     * @throws Exception
     */
    public function auto()
    {
        $return = [];
        $params = [];
        $params['itemId'] = request('itemid', 'request');
        $params['policyId'] = request('policyid', 'request');
        $params['type'] = request('type', 'request');
        $params['roleId'] = request('roleid', 'request', 0, 'int');
        $params['user'] = request('user', 'request');
        $params['deviceId'] = request('deviceid', 'request', 0, 'int');
        $params['changeIp'] = request('changeIp', 'request');
        $params['servicePrefix'] = 'Auto';
        $resultService = ResultServiceProvider::initResultService($params);
        switch ($params['type']) {
            case 'getauthtype':
                $return = $resultService->getAuthType();
                break;
            case 'lifetime':
                $return = $resultService->getLifeTime();
                break;
            case 'getauthmes':
                $return = $resultService->getAuthMes();
                break;
            case 'xml':
                $return = $resultService->getXml();
                break;
            case 'getpolicy':
                $return = $resultService->getPolicyItem();
                break;
            default:
                T(21100002);
        }
        return $return;
    }
}
