<?php
/**
 * Description: THealthCheckLog健康日志
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: GateWayModel.php ***********-04-29 13:33:57Z duanyc $
 */

class HealthCheckLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'THealthCheckLog';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'      => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['ResID'])) {
            $where .= "AND ResID = ".self::setData($cond['ResID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}