<?php
/**
 * Description: 短信相关逻辑service
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: SmsServiceProvider.php 169479 2022-02-23 07:52:41Z lihao $
 */

class SmsServiceProvider extends BaseServiceProvider
{
    /**
     * 发送验证码
     *
     * @param $params
     * @param $isEmail
     *
     * @throws Exception
     */
    public static function sendCode($params, $isEmail): void
    {
        // 发送前处理
        $AuthTime = DictModel::getAll("AuthTime"); //获取动态口令超时时间
        $GetSmsInfo = DictModel::getAll("SMS"); //SMS信息
        $params['effTime'] = $AuthTime["EffTime"];
        self::sendAfterHandle($GetSmsInfo, $params);
        // 管理员ID
        $adminID = 0;
        if ($params['type'] === 'user') {
            self::checkUserFrequency($params);
        } elseif ($params['type'] === 'admin') {
            $adminID = self::checkAdminFrequency($params);
        }

        // 生成验证码
        $params['check_code'] = self::generateCode($params);

        // 验证是否传递了合法的userid
        if ((int)$params['userid'] <= 0) {
            $colunmnName = 'Tel';
            if ($isEmail) {
                $colunmnName = 'Email';
            }
            $userInfo = AuthUserModel::getSingle([$colunmnName => $params['phone'], 'IsDisabled' => 0, 'TypeNG' => 'Guest', 'column' => 'base']);
            $params['userid'] = $userInfo['ID'] ?? 0;
        }
        if ($isEmail) {
            // 发送邮件
            self::sendEmail($params);
        } else {
            // 发送短信
            self::sendSms($GetSmsInfo, $params, $adminID);
        }
    }

    /**
     * 解析敲门参数为发短信参数
     *
     * @param $CodeData
     *
     * @return array
     */
    public static function parseFwknopData($CodeData): array
    {
        $return = [];
        $Data = hlp_common::parseFwknopData($CodeData);
        $return['phone'] = $Data[0] ?? '';
        $return['isGuestAuth'] = $Data[1] ?? 0;
        return $return;
    }

    /**
     * 发送前处理
     *
     * @param $GetSmsInfo
     * @param $params
     *
     * @throws Exception
     */
    public static function sendAfterHandle($GetSmsInfo, $params)
    {
        //验证手机号是否为内网手机
        $verify = false;
        if ((int)$GetSmsInfo['ValidRegisteredPhone'] === 1 && !$params['isGuestAuth']) {
            $verify = true;
        }
        if ($verify) {
            if ($params['type'] === 'user') {
                $acond = ['IsDisabled' => 0, 'Tel' => $params['phone'], 'TypeNG' => AUTH_TYPE_GUEST, 'column' => 'base'];
                if (!empty($params['userid'])) {
                    $acond['ID'] = $params['userid'];
                }
                $existsUser = AuthUserModel::getSingle($acond);
                if (!$existsUser) { //发现设备表里不存在
                    $cond = ['TypeIn' => [DEVICE_TYPE_PC, DEVICE_TYPE_MOBILE],
                        'Tel' => $params['phone'], 'column' => 'one'];
                    $existsDev = DeviceModel::getSingle($cond);
                    if (!$existsDev) {
                        if (!empty($GetSmsInfo['ValidRegisteredPhoneTips'])) {
                            throw new Exception($GetSmsInfo['ValidRegisteredPhoneTips'], 21131003);
                        }
                        T(21131003);
                    }
                }
            }
        }
        //取消上次申请码
        $cond = ['Tel' => $params['phone'], 'Flag' => 0];
        SmsCodeModel::updatePatch($cond, ['Flag' => 2, 'UseTime' => 'now()']);
    }

    /**
     * 短信发送
     *
     * @param $params
     *
     * @return bool|string
     * @throws Exception
     */
    public static function sendInfogoSMS($params)
    {
        $message = isset($params['message']) ? $params['message'] : L(21131009) . "{$params['check_code']}，  {$params['effTime']} " . L(21131010) . $params['suffix'];
        $cmd = PATH_HTML . "/bin/SendSMS.sh \"{$params['phone']}\" \"{$message}\"";
        cutil_exec_wait($cmd, $TimeOut = 10, $ip = "127.0.0.1");
        sleep(3);
        $sms_status = file_get_contents("/tmp/logs/infogosms.log");
        if ($sms_status != 'success') {
            T(21131011);
        }
        return $sms_status;
    }

    /**
     * 其他短信发送方式
     *
     * @param $GetSmsInfo
     * @param $params
     *
     * @throws Exception
     */
    public static function sendOtherSMS($GetSmsInfo, $params)
    {
        $url = $GetSmsInfo['SendURL'];
        if ($url == "") {
            T(21131012);
        }
        //区分来宾短信模板
        $templateId = $params['isGuestAuth'] ? $GetSmsInfo['TemplateGuest'] : $GetSmsInfo['TemplateStaff'];
        $fields = array(
            'phone' => $params['phone'],
            'code' => $params['check_code'],
            'suffix' => $GetSmsInfo['SMSSuffix'],
            'templateId' => $templateId, //增加模板id
            'templateParam' => '"' . $params['check_code'] . '"，"' . $params['effTime'] . '"', //模板参数
            'effTime' => $params['effTime'],
            'message' => isset($params['message']) ? $params['message'] : L(21131013) . "{$params['check_code']}，{$params['effTime']}" .
                L(21131010) . $params['suffix']
        );
        $result = curl($url, 'POST', $fields, 10, 20);
        self::log("Send url:{$url} result:{$result['data']}} data:" . var_export($fields, true));
        return $result['data'];
    }

    /**
     * 生成验证码
     *
     * @param $params
     *
     * @return int
     * @throws Exception
     */
    public static function generateCode(&$params)
    {
        $check_code = @random_int(100000, 999999);
        if ($params['isGuestAuth']) {
            $aGuestAuth = DictModel::getAll("GUESTAUTH");

            if ($aGuestAuth['IsNeedAppoint'] == '1') { /* 手机号需要预约 */
                $cond = ['Tel' => $params['phone'], 'GuestStartTime' => time(), 'GuestEndTime' => time(), 'GustCodeType' => 'reserve', 'MaxNumber' => 1];
                $guestrelation = GuestRelationModel::getSingle($cond);
                if (is_array($guestrelation)) {
                    $guest = AuthUserModel::getOne($guestrelation['UserID'], '*');
                    if (!is_array($guest)) {
                        T(21131018);
                    }
                    $formatArr = ['user' => $params['phone'], 'pw' => $check_code, 'nowTime' => func_time_getNow()];
                    $remark = L(21131007, $formatArr);
                    $aparams = ['Type' => 'Guest', 'UserName' => '', 'Password' => $check_code, 'RoleID' => ROLE_ID_GUEST,
                        'DepartID' => 0, 'LifeTime' => $guest["LifeTime"], 'CreateUser' => $params['phone'],
                        'Remark' => $remark, 'TrueNames' => $guest['TrueNames'], 'Tel' => $params['phone'], 'Email' => '',
                        'GuestType' => 2
                    ];
                    $authId = AuthUserModel::insert($aparams);
                    AuthUserModel::update($authId, ['UserName' => 'Guest_' . $authId]);
                    // 更新来宾关系表
                    $gparams = ['AllowRegionIDs' => $guestrelation['AllowRegionIDs'], 'AllowTime' => $guestrelation['AllowTime'],
                        'IsNeedAudit' => $guestrelation['IsNeedAudit'], 'GustCodeType' => 'sms', 'TeamName' => $guestrelation['TeamName'], 'MaxNumber' => 0,
                        'GuestStartTime' => $guestrelation['GuestStartTime'], 'GuestEndTime' => $guestrelation['GuestEndTime'],
                        'Name' => $guestrelation['Name'], 'Unit' => $guestrelation['Unit'], 'Tel' => $guestrelation['Tel'],
                        'StaffName' => $guestrelation['StaffName'], 'StaffTel' => $guestrelation['StaffTel'],
                        'GreetType' => $guestrelation['GreetType'], 'GreetUserID' => $guestrelation['GreetUserID'], 'GreetUserName' => $guestrelation['GreetUserName'],
                        'GreetUserDesc' => $guestrelation['GreetUserDesc'], 'DeviceId' => $guestrelation['DeviceId']
                    ];

                    GuestRelationModel::updateByUserId($authId, $gparams);     // 更新来宾关系表
                    $params['userid'] = $authId;
                } else {
                    T(21131018);
                }
            } else {
                $formatArr = ['user' => $params['phone'], 'pw' => $check_code, 'nowTime' => func_time_getNow()];
                $remark = L(21131007, $formatArr);
                $aparams = ['Type' => 'Guest', 'UserName' => '', 'Password' => $check_code, 'RoleID' => ROLE_ID_GUEST,
                    'DepartID' => 0, 'LifeTime' => $aGuestAuth["NetCodeTime"], 'CreateUser' => $params['phone'],
                    'Remark' => $remark, 'TrueNames' => $params['phone'], 'Tel' => $params['phone'], 'Email' => '',
                    'GuestType' => 2
                ];
                $authId = AuthUserModel::insert($aparams);
                AuthUserModel::update($authId, ['UserName' => 'Guest_' . $authId]);
                $aGuestAuthBak = DictModel::getAll("GuestAuth");
                $aGuestAuth["smsStateTime"] = $aGuestAuth["smsStateTime"]??$aGuestAuthBak['smsStateTime'];
                $guestAuthList =  ['AllowTime' => $aGuestAuth["smsStateTime"]];
                if(!empty($params['phone'])) {
                    $guestAuthList['GustCodeType']  = 'sms';
                }
                \GuestRelationModel::updateByUserId($authId, $guestAuthList);
                $params['userid'] = $authId;
            }
        }
        return $check_code;
    }

    /**
     * 发送短信
     *
     * @param $GetSmsInfo
     * @param $params
     * @param $adminID
     *
     * @return array|bool|string
     * @throws Exception
     */
    public static function sendSms($GetSmsInfo, $params, $adminID)
    {
        if ($params['type'] === 'user') {
            $sparams = ['DeviceID' => $params['deviceid'], 'UserID' => $params['userid'],
                'Tel' => $params['phone'], 'CheckCode' => $params['check_code']];
            SmsCodeModel::insert($sparams);
        } elseif ($params['type'] === 'admin') {
            if (is_numeric($adminID) && $adminID > 0) {
                $sparams = ['DeviceID' => 0, 'UserID' => 0, 'AdminID' => $adminID,
                    'Tel' => $params['phone'], 'CheckCode' => $params['check_code']];
                SmsCodeModel::insert($sparams);
            }
        }
        $now_time = date('Y-m-d h:i:s', time());
        $formatArr = ['deviceid' => $params['deviceid'], 'userid' => $params['userid'],
            'phone' => $params['phone'], 'code' => $params['check_code'], 'time' => $now_time];
        $log_alert = L(21131008, $formatArr);
        check_log_forward('SmsCodeLog', $log_alert);
        $params['suffix'] = "";
        if (!empty($aSMSAlert['SMSSuffix'])) {
            $params['suffix'] = "[" . $aSMSAlert['SMSSuffix'] . "]";
        }
        $other_reuslt = [];
        if ($GetSmsInfo['SendType'] === 'InfogoSMS') {
            $other_reuslt = self::sendInfogoSMS($params);
        } elseif ($GetSmsInfo['SendType'] === 'OtherSMS') {
            $other_reuslt = self::sendOtherSMS($GetSmsInfo, $params);
        }
        self::log("SendType：{$GetSmsInfo['SendType']} param={phone:{$params['phone']},result:{$other_reuslt}}");
        return $other_reuslt;
    }

    /**
     * 发送邮件
     *
     * @param array $params phone传邮箱地址
     *
     * @return array|bool|string
     * @throws Exception
     */
    public static function sendEmail($params)
    {
        $sparams = ['DeviceID' => $params['deviceid'], 'UserID' => $params['userid'],
            'Tel' => $params['phone'], 'CheckCode' => $params['check_code']];
        SmsCodeModel::insert($sparams);
        $now_time = date('Y-m-d h:i:s', time());
        $formatArr = ['deviceid' => $params['deviceid'], 'userid' => $params['userid'],
            'phone' => $params['phone'], 'code' => $params['check_code'], 'time' => $now_time];
        $log_alert = L(21131008, $formatArr);
        check_log_forward('SmsCodeLog', $log_alert);
        $message = L(21131017) . "{$params['check_code']}，  {$params['effTime']} " . L(21131010) . $params['suffix'];
        $data = ['email' => $params['phone'], 'content' => $message];
        self::callRemoteSendEmail($params['phone'], $data);
        return true;
    }

    /**
     * 调用远程发送邮件功能
     * @param string $email 传邮箱地址
     * @param array $data 内容['email'=>'xxxx', 'content'=>'xxx', 'subject'=>'xxx'],其中subject是邮件主题，可以不传，默认为"报警信息"
     * @return bool
     */
    public static function callRemoteSendEmail($email, $data)
    {
        $result = lib_yar::clients('alarm', 'sendMail', $data, '127.0.0.1', 600000, 1);
        self::log("param={email:{$email}:" . var_export($result, true));
        return true;
    }

    /**
     * 检查用户频率
     *
     * @param $params
     *
     * @throws Exception
     */
    public static function checkUserFrequency($params)
    {
        /*检测发送频率*/
        $tmpArr = array();
        $tmpArr[$params['type']][$params['deviceid']][$params['phone']] = self::defaultSmsLimit();
        $sendSMS = self::recordSMSTimes($tmpArr, 1);
        if (!self::validateTimes($sendSMS, $params['deviceid'], $params['phone'], $params['type'])) {
            T(21131004);
        }
        //计入操作次数
        self::setTimesLimit($sendSMS, $params['deviceid'], $params['phone'], $params['type']);
    }

    /**
     * 检查管理员频率
     *
     * @param $params
     *
     * @return int|mixed
     * @throws Exception
     */
    public static function checkAdminFrequency($params)
    {
        /*检测发送频率*/
        $adminID = 0;
        $tmpArr = array();
        if (!empty($params['username'])) {
            $admin = OperatorModel::getSingle(['UserName' => $params['username'], 'column' => 'admin']);
            if (is_array($admin)) {
                $adminID = $admin['RID'];
                if (!empty($admin['Tel']) && $admin['Tel'] != $params['phone']) {
                    T(21131005);
                }
            }
        }

        $tmpArr[$params['type']][$adminID][$params['phone']] = self::defaultSmsLimit();
        $sendSMS = self::recordSMSTimes($tmpArr, 1);
        if (!self::validateTimes($sendSMS, $adminID, $params['phone'], $params['type'])) {
            T(21131006);
        }
        // 计入操作次数
        self::setTimesLimit($sendSMS, $adminID, $params['phone'], $params['type']);
        return $adminID;
    }

    /**
     * 默认短信发送限制
     *
     * @param int $used
     *
     * @return array
     * @throws Exception
     */
    public static function defaultSmsLimit($used = 0)
    {
        $stepTime = self::getSendTimes('stepTime');
        return array(
            'expired' => (string)(time() + $stepTime + 30),  //加上脚本重置误差时间
            'usedTimes' => $used
        );
    }

    /**
     * 获取发送频率设置
     *
     * @param string $key
     *
     * @return array
     * @throws Exception
     */
    public static function getSendTimes($key = '')
    {
        $path = PATH_HTML . '/phpdir/sms_dir/times/';
        $fileName = 'timesConf.ini';
        if (!file_exists($path)) {
            $filePerm = substr(sprintf("%o", fileperms($path)), -4);
            if ($filePerm == '0755' || $filePerm == '0644' || $filePerm == 0) {
                mkdir($path, 0777);
            }
        }
        $defaultSet = array(
            '#times' => '单位时间频率',
            '#stepTime' => '间隔时间',
            'times' => 3,
            'stepTime' => 60
        );
        if (!file_exists($path . $fileName)) {
            file_put_contents($path . $fileName, array_to_inistr($defaultSet));
        }
        $res = read_inifile($path . $fileName);
        if (!empty($key)) {
            if (array_key_exists($key, $res)) {
                return $res[$key];
            }
        }
        return $res;
    }

    /**
     * 记录发送文件频率
     *
     * @param array $data
     * @param int $type
     *
     * @return mixed
     */
    public static function recordSMSTimes($data = array(), $type = 0)
    {
        /*有数据时替换没有数据时候创建*/
        $path = PATH_HTML . '/phpdir/sms_dir/times/';
        $fileName = 'sendTimes.json';
        if (!file_exists($path)) {
            $filePerm = substr(sprintf("%o", fileperms($path)), -4);
            if ($filePerm == '0755' || $filePerm == '0644' || $filePerm == 0) {
                mkdir($path, 0777);
            }
        }
        if (!file_exists($path . $fileName)) {
            file_put_contents($path . $fileName, json_encode($data));
        }
        if ($type == 0) {
            $jsonStr = json_encode($data);
            file_put_contents($path . $fileName, $jsonStr);
        }
        if ($type) {
            $json = file_get_contents($path . $fileName);
            return json_decode($json, true);
        }
        return null;
    }

    /**
     * 短信发送频率检测
     * 限制发送间隔1min/日发送上限
     *
     * @param $sendSMS
     * @param $deviceId
     * @param $tell
     * @param string $type
     *
     * @return bool
     * @throws Exception
     */
    public static function validateTimes($sendSMS, $deviceId, $tell, $type = 'user')
    {
        if ($deviceId == '' || $tell == '') {
            return false;
        }
        $limitTimes = self::getSendTimes('times');
        /*初始化*/
        if (!isset($sendSMS[$type][$deviceId][$tell]['expired'])
            || !isset($sendSMS[$type][$deviceId][$tell]['usedTimes'])
            || $sendSMS[$type][$deviceId][$tell]['expired'] == null
            || $sendSMS[$type][$deviceId][$tell]['usedTimes'] == null) {
            $tmp = self::defaultSmsLimit();
            $sendSMS[$type][$deviceId][$tell] = $tmp;
            self::recordSMSTimes($sendSMS);
        }
        /*判断是否发送过频和超限*/
        if (time() < $sendSMS[$type][$deviceId][$tell]['expired']) {
            if ($limitTimes >= $sendSMS[$type][$deviceId][$tell]['usedTimes'] + 1) {
                return true;
            }
        }
        /* 超过时间次数还没用完*/
        if ($limitTimes >= $sendSMS[$type][$deviceId][$tell]['usedTimes'] + 1) {
            return true;
        }
        return false;
    }

    /***
     * 短信发送频率和次数限制设置
     *
     * @param $sendSMS
     * @param $deviceId
     * @param $tell
     * @param string $type
     * @throws Exception
     */
    public static function setTimesLimit($sendSMS, $deviceId, $tell, $type = 'user')
    {
        $lastUsedTimes = $sendSMS[$type][$deviceId][$tell]['usedTimes'] ?? 0;
        if (!isset($sendSMS[$type][$deviceId][$tell]['expired'])) {
            $tmp = self::defaultSmsLimit();
            $sendSMS[$type][$deviceId][$tell] = $tmp;
            self::recordSMSTimes($sendSMS);
        }
        if ($lastUsedTimes == 3 && $sendSMS[$type][$deviceId][$tell]['expired'] > time()) {
            $tmp = self::defaultSmsLimit();
            $sendSMS[$type][$deviceId][$tell]['expired'] = $tmp['expired'];
        }
        $sendSMS[$type][$deviceId][$tell]['usedTimes'] = $lastUsedTimes + 1;
        self::recordSMSTimes($sendSMS);
    }

    /**
     * 检查短信码是否存在
     * @param $phone
     * @param $checkCode
     * @return mixed
     */
    public static function getCheckCode($phone, $checkCode)
    {
        $AuthTime = DictModel::getAll("AuthTime"); //获取动态口令超时时间
        $SmsCodeEffTime = $AuthTime["EffTime"];
        return SmsCodeModel::getOneByInfo($phone, $checkCode, $SmsCodeEffTime);
    }

    /**
     * 使用短信码
     * @param $phone
     * @param $checkCode
     * @return bool
     */
    public static function useCheckCode($phone, $checkCode)
    {
        $cond = ['Tel' => $phone, 'CheckCode' => $checkCode];
        SmsCodeModel::updatePatch($cond, ['UseTime' => 'now()', 'Flag' => STRING_TRUE]);
        return true;
    }
}
