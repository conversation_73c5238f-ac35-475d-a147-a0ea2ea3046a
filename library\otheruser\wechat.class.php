<?php
/*
 * Description: 获取微信用户
 * User: <EMAIL>
 * Date: 2021/06/08 20:46
 * Version: $Id: wechat.class.php 175145 2022-05-06 07:53:42Z huyf $
 *
 *   微信返回用户信息参数：
 *   {
 *       "subscribe": 1,
 *       "openid": "oLVPpjqs2BhvzwPj5A-vTYAX4GLc",
 *       "nickname": "刺猬宝宝",
 *       "sex": 1,
 *       "language": "zh_CN",
 *       "city": "深圳",
 *       "province": "广东",
 *       "country": "中国",
 *       "headimgurl": "http://wx.qlogo.cn/mmopen/JcDicrZBlREhnNXZRudod9PmibRkIs5K2f1tUQ7lFjC63pYHaXGxNDgMzjGDEuvzYZbFOqtUXaxSdoZG6iane5ko9H30krIbzGv/0",
 *       "subscribe_time": 1386160805
 *   }
 */
include_once(PATH_LIBRARY . "/otheruser/wechat/emoji.php");

class wechat
{

    /**
     * IP地址
     * @var string
     */
    private $CODE;

    /**
     * IP地址
     * @var string
     */
    public $CLIENTIP;

    /**
     * IP地址
     * @var string
     */
    private $USER_INFO;

    /**
     * 初始化
     */
    public function __construct()
    {
        $this->CODE = request('code', 'request');
        $this->USER_INFO = Base64DeExt(request('info', 'request'));
    }

    /**
     * 打印日志
     *
     * @param $c
     * @param bool $s
     */
    public function log($c, $s = false)
    {
        cutil_php_log($c, "wechat");
        if ($s) {
            exit($c);
        }
    }

    /**
     * ip范围查找
     *
     * @param $iprange
     * @param string $split
     *
     * @return bool|int
     */
    private function ipRangeFind($iprange, $split = "|@@|")
    {
        $this->CLIENTIP = getRemoteAddress();
        if ($iprange != "") {
            $ipstr_arr1 = explode($split, $iprange);
            foreach ($ipstr_arr1 as $key => $str1) {
                $iparr = explode("-", $str1);
                $start = ipToNum($iparr[0]);
                $stop = ipToNum($iparr[1]);
                $nowip = ipToNum($this->CLIENTIP);
                if ($nowip >= $start && $nowip <= $stop) {
                    $this->log($this->CLIENTIP . ">>>>" . $str1);
                    return $key;
                }
            }
        }
        $this->log($this->CLIENTIP . ">>>>no iprange!");
        return false;
    }

    /**
     * 获取系统配置
     * @return mixed
     */
    private function getSysConfig()
    {
        return \DictModel::getAll("WeChatConfig");
    }

    /**
     * 获取微信用户信息
     *
     * @param $appid
     * @param $secret
     *
     * @return bool|mixed
     */
    private function getWechatUserInfo($appid, $secret)
    {
        //如果已经获取了微信用户信息
        if ($this->USER_INFO != "") {
            $this->log("url get user_info:" . $this->USER_INFO);
            if ($this->USER_INFO != "false") {
                $getUserInfoArr = json_decode($this->USER_INFO, true);
            } else {
                return false;
            }
        } else {
            //否则重新获取
            $weixin_baseurl = "https://api.weixin.qq.com";
            //使用code获取OpenID
            $requestUrl = $weixin_baseurl . "/sns/oauth2/access_token?appid=" . $appid . "&secret=" . $secret . "&code=" . $this->CODE . "&grant_type=authorization_code";
            $getAppidArr = json_decode(file_get_contents($requestUrl), true);
            if (isset($getAppidArr["errcode"])) {
                $this->log("get openid:" . var_export($getAppidArr, true));
                return false;
            }

            //获取全局Access Token
            $requestUrl = $weixin_baseurl . "/cgi-bin/token?grant_type=client_credential&appid=" . $appid . "&secret=" . $secret;
            $getAccessTokenArr = json_decode(file_get_contents($requestUrl), true);
            if (isset($getAccessTokenArr["errcode"])) {
                $this->log("get access token:" . var_export($getAccessTokenArr, true));
                return false;
            }
            //使用全局ACCESS_TOKEN获取OpenID的详细信息
            $requestUrl = $weixin_baseurl . "/cgi-bin/user/info?access_token=" . $getAccessTokenArr['access_token'] . "&openid=" . $getAppidArr['openid'] . "&lang=zh_CN";
            $getUserInfoArr = json_decode(file_get_contents($requestUrl), true);
            if (isset($getUserInfoArr["errcode"])) {
                $this->log("get user info:" . var_export($getUserInfoArr, true));
                return false;
            }
            //去掉昵称中的表情
            if (stristr($_SERVER['HTTP_USER_AGENT'], "iPhone ") || stristr($_SERVER['HTTP_USER_AGENT'], "iPad ") || stristr($_SERVER['HTTP_USER_AGENT'], "iOS ")) {
                $getUserInfoArr['nickname'] = strip_tags(emoji_unified_to_html($getUserInfoArr['nickname']));
            } else {
                $getUserInfoArr['nickname'] = trim(preg_replace('/\xEE[\x80-\xBF][\x80-\xBF]|\xEF[\x81-\x83][\x80-\xBF]/', '', $getUserInfoArr['nickname']));
            }
        }
        return $getUserInfoArr;
    }

    /**
     * 重定向
     *
     * @param string $url
     */
    private function redirectTO($url = '')
    {
        $url = strlen($url) > 0 ? $url : get_cururl(true, false, NOWASMIP) . "/mobile/ui/wel.html?1=1";
        $this->log("Location:" . $url . "&t=" . time());
        header("Location:" . $url . "&t=" . time());
        exit();
    }

    /**
     * 检查url是否通
     *
     * @param $url
     *
     * @return bool
     */
    public function checkUrl($url)
    {
        try {
            $data = curl($url);
            if (!empty($data['data']) && strpos($data['data'], '__res') !== false) {
                return true;
            }
            $this->log("checkUrl:" . $url . "， data: " . $data['data']);
        }
        catch (Exception $e) {
            $this->log("checkUrl:" . $url . "， errror: " . $e->getMessage());
        }

        return false;
    }

    /**
     * 入口函数
     * @throws Exception
     */
    public function main()
    {
        $config = $this->getSysConfig();
        //是否开启微信推广功能
        if ($config['openwechat'] == '1') {
            //是否在ip范围内
            $find_index = $this->ipRangeFind($config['clientips']);
            if ($find_index !== false) {
                $clientipArr = explode("|@@|", $config['serverip']);
                $unitnameArr = explode("|@@|", $config['unitname']);
                $serverip = $clientipArr[$find_index];
                $regUnit = $unitnameArr[$find_index];
                $this->log("serverip:" . $serverip);
                //开始获取微信用户信息
                $userinfo = $this->getWechatUserInfo(
                    $config['appid'],
                    $config['secret']
                );
                //写入数据库
                $authService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Wechat', 'deviceId' => '']);
                $insertID = $authService->actionDB($serverip, $regUnit, $userinfo);
                //下一步是否为当前asm,则进入页面
                $baseurl = get_cururl(true, false, $serverip) . "/a";
                if ($serverip == NOWASMIP) {
                    //是否关注微信
                    if ($userinfo['subscribe'] == '1') {
                        $url = $baseurl . "/mobile/wel.html?route_type=wechat&id=" . $insertID;
                        $this->redirectTO($url);
                    } else {
                        $this->redirectTO();
                    }
                } else {
                    //将用户信息已url的形式传递给下一跳asm
                    $url = $baseurl . "/getotheruser_int.php?info=" . Base64EnExt((is_array($userinfo) ? json_encode($userinfo) : 'false'));
                    if (!$this->checkUrl($url)) {
                        hlp_common::showMobileMessage(false , 21135001);
                    }
                    $this->redirectTO($url);
                }
            }
        }
        $this->redirectTO();
    }
}
