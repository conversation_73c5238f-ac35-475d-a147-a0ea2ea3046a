<?php

/**
 * Description: 用户消息TUserNotice表
 * User: <EMAIL>
 * Date: 2023/10/30 16:42
 * Version: $Id: UserNoticeModel.php $
 */

class UserNoticeModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUserNotice';
    public const PRIMARY_KEY = 'NoticeID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * @param $idArr
     * @param int $userId
     * @param array $key_values
     * @return bool|int|Object
     */
    public static function setNoticeRead($idArr, int $userId=0, $key_values = [])
    {
        if ((empty($idArr) && empty($userId)) || empty($key_values)) {
            //如果不是按id或用户修改则失败
            return false;
        }
        self::$data = [];
        if (empty($idArr)) {
            //按用户全部标记已读
            $where = "  UserID=" . self::setData($userId);
        } else {
            //按id标记已读
            $where = "  NoticeID=" . self::setArrayData($idArr);
        }
        if (empty($where)) {
            return false;
        }
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $key_values]), "model_{$table['name']}");
        return lib_database::update($key_values, $where, $table['name'], $table['index'], self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['UserID'])) {
            $where .= "AND UserID = " . self::setData($cond['UserID']);
        }
        if (isset($cond['Status'])) {
            $where .= "AND Status = " . self::setData($cond['Status']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
