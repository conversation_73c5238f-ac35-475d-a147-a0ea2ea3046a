<?php
/**
 * Description: 检查
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: hlp_check.php 170560 2022-03-08 04:47:23Z lihao $
 */

/**
 * 数据校验
 *
 * <AUTHOR>
 * @version $Id: hlp_check.php 170560 2022-03-08 04:47:23Z lihao $
 */
class hlp_check
{
    /**
     * 正则
     * @var array
     */
    private static $Regex = [
        'username' => "/^[A-Za-z\s\x{4e00}-\x{9fa5}-_\.@\d]{1,50}$/u",
        'date' => "/^\d{4}[\-|\/](0?[1-9]|1[012])[\-|\/](0?[1-9]|[12][0-9]|3[01])(\s+(0?[0-9]|1[0-9]|2[0-3])\:(0?[0-9]|[1-5][0-9])(\:(0?[0-9]|[1-5][0-9]))?)?$/",
        'remark' => "/^[^\\\\\/<>~^\'\"&+{}=]{0,1000}$/",
        'remark500' => "/^[^\\\\\/<>~^'\"&+]{0,500}$/",
        'mobile' => "/^[0-9]{2,18}$/",
    ];

    /**
     * 错误码
     * @var array
     */
    private static $RegexCode = [
        'username' => 21100011,
        'date' => 21100015,
        'remark' => 21100016,
        'remark500' => 21100017,
        'mobile' => 21100012,
    ];

    /**
     * 管理后台校验规则对应的正则校验
     * @var array
     */
    private static $rules = [
        "Default"      => ['code' => 21101004, 'pattern' => '/^[^\<\>\%\*\'\"\&]{1,50}$/u'],
        "Tel"          => ['code' => 21101005, 'pattern' => '/^[-()+*#（） \d]{0,25}$/'],
        "Email"        => ['code' => 21101006, 'pattern' => '/^\w+([-+.\']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/'],
        "IdCard"       => ['code' => 21101007, 'pattern' => '/^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/'],
        "HKMIdCard"    => ['code' => 21101008, 'pattern' => '/^([A-Z]\d{6,10}(\(\w{1}\))?)$/'],
        "TaiwanIdCard" => ['code' => 21101009, 'pattern' => '/^\d{8}|^[a-zA-Z0-9]{10}|^\d{18}$/'],
        "Number"       => ['code' => 21101010, 'pattern' => '/^[0-9]*$/'],
        "Letter"       => ['code' => 21101011, 'pattern' => '/^[A-Za-z]+$/'],
        "Chinese"      => ['code' => 21101012, 'pattern' => '/^[\x{4e00}-\x{9fa5}]{0,}$/u'],
    ];

    /**
     * url前缀的正则
     *
     * @var string
     */
    public static $RegexUrlPrefix = "/http(s?):\/\/([A-Za-z0-9-]+\.)+[A-Za-z0-9-]+(:\d+)?/";

    /**
     * 检查手机号
     *
     * @param $mobile
     *
     * @throws Exception
     */
    public static function checkMobile($mobile)
    {
        if (!preg_match('/^[-()+*#（） \d]{0,25}$/', $mobile)) {
            T(21100010);
        }
    }

    /**
     * 检查手机号宽松版
     *
     * @param $mobile
     * @param $columnName
     *
     * @throws Exception
     */
    public static function checkMobileNum($mobile, $columnName = '')
    {
        if (!preg_match("/^[-()+*#（） \d]{0,25}$/", $mobile)) {
            T(21100012, ['columnName' => $columnName]);
        }
    }

    /**
     * 检查单词
     *
     * @param $str
     *
     * @throws Exception
     */
    public static function checkWord($str)
    {
        $pattern = "/^[\w]+$/";
        if (!preg_match($pattern, $str)) {
            T(21100009);
        }
    }

    /**
     * 检查参数是否为空
     *
     * @param $str
     *
     * @throws Exception
     */
    public static function checkEmpty($str)
    {
        if (empty($str)) {
            hlp_common::setHeader(422, 'Param Error');
            T(21100002);
        }
    }

    /**
     * 检查用户名
     *
     * @param $str
     * @param $columnName
     *
     * @throws Exception
     */
    public static function checkUsername($str, $columnName = '')
    {
        if (!preg_match("/^[A-Za-z\s\x{4e00}-\x{9fa5}-_\.*()@\d]{1,50}$/u", $str)) {
            T(21100011, ['columnName' => $columnName]);
        }
    }

    /**
     * 检查参数是否为空
     *
     * @param $str
     * @param $columnName
     *
     * @throws Exception
     */
    public static function checkRemark($str, $columnName = '')
    {
        if (!preg_match("/^[^\\\\\/<>~^'\"&+]{0,500}$/", $str)) {
            T(21100013, ['columnName' => $columnName]);
        }
    }

    /**
     * 检查密码
     * @param $str
     * @throws Exception
     */
    public static function checkPasswd($str)
    {
        if (preg_match("/[\x7f-\xff]/", $str)) {
            T(21137014);
        }
    }

    /**
     * 检测ID数组是否符合要求
     *
     * @param $idArr
     * @param $allowZero
     *
     * @throws Exception
     */
    public static function checkIDArr($idArr = '', $allowZero = false)
    {
        if (is_string($idArr)) {
            $ids = explode(',', $idArr);

            foreach ($ids as $id) {
                if (!ctype_digit($id) || (int)$id < 0) {
                    return array("state" => 0, "msg" => L(21100014));
                }
                if (!$allowZero && (int)$id === 0) {
                    return array("state" => 0, "msg" => L(21100014));
                }
            }
        } else {
            return array("state" => 0, "msg" => L(21100014));
        }
        return array("state" => 1);
    }

    /**
     * 时间校验戳
     * @param $timestamp
     * @return array
     * @throws Exception
     */
    public static function checkTimestampValid($timestamp): array
    {
        if (strtotime(date('Y-m-d H:i:s', $timestamp)) === $timestamp) {
            return array("state" => 1);
        }
        return array("state" => 0, "msg" => L(21100015));
    }

    /**
     * 返回对应的正则表达式是否验证成功
     * example:CheckRegex("username","zhangsan")
     *
     * @param string $regex 需要验证的正则表达式
     * @param string $val 需要验证的值
     * @return array state 1:成功 0:失败 msg:错误信息
     */
    public static function checkRegex($regex, $val)
    {
        if ($regex == "username") {
            if (!IsUTF8($val)) {
                $val = gbkToUtf8($val);
            }
        }
        if (!isset(self::$Regex[$regex])) {
            return array("state" => 0, "msg" => L(21100002));
        }
        if (!preg_match(self::$Regex[$regex], $val)) {
            return array("state" => 0, "msg" => L(self::$RegexCode[$regex], ['columnName' => '']));
        }
        return array("state" => 1);
    }

    /**
     * 账户类型校验
     * @param string $userType 账户类型
     * @param bool $allowNoAuth 是否允许免认证
     * @return array
     * @throws Exception
     */
    public static function checkUserType(string $userType, bool $allowNoAuth = false): array
    {
        include_config('auth');
        $userTypes = $GLOBALS['AUTH_CONFIG']['AuthUserType'];
        $authUserTypeKey = array_keys($userTypes);
        if ($allowNoAuth) {
            $authUserTypeKey[] = AUTH_TYPE_NOAUTH;
        }
        if (in_array($userType, $authUserTypeKey, true)) {
            return array("state" => 1);
        }
        return array("state" => 0, "msg" => L(21100015));
    }

    /**
     * 检查是否必填
     * @param string $RequireExpand 配置
     * @return bool
     * @throws Exception
     */
    public static function checkRequire(string $RequireExpand)
    {
        $data = explode('|', $RequireExpand);
        $select = $data[0];
        if ($select == '1') {
            return true;
        }
        return false;
    }

    /**
     * 校验ip格式
     * @param $ip
     * @return bool
     * @throws Exception
     */
    public static function checkIP($ip)
    {
        if ($ip != '' && !filter_var($ip, FILTER_VALIDATE_IP)) {
            T(21100020);
        }
        return true;
    }

    /**
     * 校验mac格式
     * @param $mac
     * @return bool
     * @throws Exception
     */
    public static function checkMAC($mac)
    {
        $reg = "/^([A-Fa-f0-9]{2}:){5}[A-Fa-f0-9]{2}$/";
        if ($mac && !preg_match($reg, $mac)) {
            T(21100021);
        }
        return true;
    }

    /**
     * 检查规则
     *
     * @param $rule
     * @param $str
     * @param $column
     *
     * @throws Exception
     */
    public static function checkRule($rule, $str, $column = '')
    {
        if (!isset(self::$rules[$rule])) {
            return;
        }
        $curRule = self::$rules[$rule];
        if (!preg_match($curRule['pattern'], $str)) {
            $messagePrefix = !empty($column) ? L(21101014, ['column' => $column]) : '';
            throw new Exception($messagePrefix . L($curRule['code']), $curRule['code']);
        }
    }

    /**
     * 校验规则是否通过
     *
     * @param $rule
     * @param $str
     *
     * @return bool
     */
    public static function checkRuleResult($rule, $str)
    {
        if (!isset(self::$rules[$rule])) {
            return false;
        }
        $curRule = self::$rules[$rule];
        if (!preg_match($curRule['pattern'], $str)) {
            return false;
        }
        return true;
    }

    /**
     * 加锁
     *
     * @param $prefix
     * @param $key
     * @param int $expire
     *
     * @return bool|null
     */
    public static function addLock($prefix, $key, $expire = 10): ?bool
    {
        return \lib_redis::setnx('LOCK_', "{$prefix}_".md5($key), time(), $expire);
    }

    /**
     * 解锁
     *
     * @param $prefix
     * @param $key
     */
    public static function delLock($prefix, $key): void
    {
        \lib_redis::del('LOCK_', "{$prefix}_".md5($key));
    }

    /**
     * 检查来宾申请字段规则
     *
     * @param $require
     * @param $str
     * @param boolean $isExtend 是否扩展字段
     * @param bool $isEmpty  是否可为空
     */
    public static function checkGuestRequire($require, $str, $isExtend = false, $isEmpty = false)
    {
        $data = explode('|', $require);
        $select = $data[0];
        // 不是必选，也不是可选
        if ($select != '0' && $select != '1') {
            return;
        }
        // 不是必选，且为空，则不校验
        if (($select != '1' || $isEmpty) && empty($str)) {
            return;
        }
        // 是必填，且为空，则报错
        if ($select == '1' and !$isEmpty && empty($str)) {
            T(21101016, ['column' => $data[1]]);
        }
        $curRule = $isExtend ? $data[5] : $data[3];
        self::checkRule($curRule, $str, $data[1]);
    }
}
