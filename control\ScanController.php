<?php

/**
 * Description: 二维码扫一扫相关
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: ScanController.php 165094 2021-12-16 08:34:07Z duanyc $
 */

use Services\Common\Services\RoleService;

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class ScanController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['short' => true, 'index' => true, 'qrcode' => true];

    /**
     * 二维码方法
     * @var array
     */
    public static $requestType = [
        'img' => 'getQrcodeImg',
        'check' => 'getQrcodeStatus',
        'scan' => 'scanQrcodeAuth',
        'clear' => 'clearQrcode',
    ];

    /**
     * Nac扫一扫，对应老交易 get_user_imgcode
     *
     * @return array
     * @throws Exception
     */
    public function index()
    {
        $return = [];
        $params = [];
        $params['action'] = request('action', 'request');
        $deviceId = request('deviceId', 'request', 0, 'int'); // 兼容安卓
        $params['deviceId'] = request('deviceid', 'request', $deviceId, 'int');
        $params['qrCodeType'] = request('qrCodeType', 'request', 'image', 'string');
        if (!isset(self::$requestType[$params['action']])) {
            T(21100002);
        }
        $params['method'] = self::$requestType[$params['action']];
        // 不同动作的参数接收
        switch ($params['method']) {
            case 'getQrcodeImg': // 获取二维码图片
                hlp_check::checkEmpty($params['deviceId']);
                $return = QrcodeServiceProvider::getQrcodeImg($params['deviceId'], $params['qrCodeType']);
                break;
            case 'getQrcodeStatus': // 获取二维码状态
                hlp_check::checkEmpty($params['deviceId']);
                $return = QrcodeServiceProvider::getQrcodeStatus($params['deviceId']);
                break;
            case 'scanQrcodeAuth': // 手机扫码请求
                try {
                    $params['token'] = request('token', 'request');
                    cutil_php_log($params, 'net_auth');
                    hlp_check::checkEmpty($params['token']);
                    QrcodeServiceProvider::scanQrcodeAuth($params);
                    $this->printInfo(100);
                } catch (Exception $e) {
                    $this->printInfo($e->getCode());
                }
                break;
            case 'clearQrcode': // 清理二维码
                $return['state'] = true;
                break;
            default:
        }
        return $return;
    }

    /**
     * 展示
     *
     * @param int $code
     * @throws Exception
     */
    private function printInfo($code = 0)
    {
        if (isset(lib_request::$requests["deviceid"])) {
            $code = ($code == 100) ? 21138001 : $code;
            exit(L($code));
        } else {
            if ($code == 100) {
                hlp_common::showMobileMessage(true, 21138001);
            } else {
                hlp_common::showMobileMessage(false, $code);
            }
        }
    }

    /**
     * 来宾扫一扫，对应老交易 check_scaned
     *
     * @return array|string
     * @throws Exception
     */
    public function guest()
    {
        $action = request('action', 'request');
        $guestId = request('GuestId', 'request');
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
        if ($action === "getGuestId") {
            return $guestService->addUser();
        }
        return $guestService->getUser($guestId);
    }

    /**
     * 来宾扫一扫，获取来宾二维码信息
     *
     * @return array|string
     * @throws Exception
     */
    public function guestInfo()
    {
        $deviceId = request('deviceid', 'request', 0, 'int');
        hlp_check::checkEmpty($deviceId);
        DeviceServiceProvider::checkDeviceExist($deviceId);
        $return = [];
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
        $guestId = $guestService->addUser();
        $return['qrcodeUrl'] = $guestService->getScanShortUrl($deviceId, $guestId);
        $ServerPort = getHttpPort();
        $urlPrefix = 'http://' . getServerIp() . ($ServerPort == '80' ? '' : ":{$ServerPort}");
        $return['qrcodeHttpUrl'] = IS_INTERNAL ? $guestService->getScanShortUrl($deviceId, $guestId, $urlPrefix) : $return['qrcodeUrl'];
        $return['guestId'] = $guestId;
        return $return;
    }

    /**
     * 短链接跳转：极速入网
     * @throws Exception
     */
    public function short()
    {
        // 极速入网
        $code = request('c', 'request');
        $urlTime = request('_', 'request');
        if (!empty($code)) {
            $time = !empty($urlTime) ? $urlTime : time();
            mobileGotoUrl("/mobile/ui/wel.html?qrCode={$code}&isOpenUrl=1&_={$time}&route_type=QrCode");
        }
        // 来宾二维码
        $guestCode = request('g', 'request');
        if (!empty($guestCode)) {
            // 该设备ID已废弃，客户端没有传，3746到3746.R002的外网或者net环境都存在问题
            $deviceId = request('deviceid', 'request', 0, 'int');
            $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
            $url = $guestService->getScanCodeUrl($deviceId, $guestCode);
            cutil_php_log($url, 'ScanGuest');
            mobileGotoUrl($url);
        }
    }

    /**
     * 获取极速入网二维码相关信息
     * @return array
     * @throws Exception
     */
    public function info()
    {
        $params = [];
        $params['deviceId'] = request('DeviceID', 'request', 0, 'int');
        $params['roleID'] = request('roleID', 'request', 0, 'int');
        $params['username'] = request('username', 'request');
        $params['userType'] = request('userType', 'request');
        $params['clientType'] = request('clientType', 'request');
        $params['sceneID'] = request('sceneID', 'request', 0, 'int');

        $params['time'] = time();
        $return = [];
        if (!in_array($params['clientType'], ['A', 'W', 'O'])) {
            T(21100002);
        }
        // 极速入网不允许认证方式为NoAuth
        if ($params['userType'] === 'NoAuth') {
            T(21138006);
        }
        $userinfo = AuthServiceProvider::getUserByUserName($params['userType'], $params['username'], 'num');
        if (empty($userinfo)) {
            T(21138007);
        }
        // 检查设备信息
        // todo 后续统一走用户ID查询
        PermitAuthServiceProvider::checkBindDeviceInfo($params, (int)$userinfo['AuthNums'], $userinfo['ID']);
        // 加密数据
        $cryptData = PermitAuthServiceProvider::encryptCode($params);
        $return['qrcodeUrl'] = PermitAuthServiceProvider::getScanCodeUrl($cryptData, $params['time']);
        $return['code'] = Base64EnExt($cryptData);
        return $return;
    }

    /**
     * 二维码状态
     * @return array
     * @throws Exception
     */
    public function status()
    {
        $return = ['state' => false];
        $code = request('code', 'request');
        hlp_check::checkEmpty($code);
        $code = Base64DeExt($code);
        $data = cache_get_info('qrcode', dataEncrypt($code));
        if (empty($data)) {
            $return['type'] = "overtime";
        } elseif ($data['status'] == false || $data['time'] + PermitAuthServiceProvider::QRCODE_TIME < time()) {
            $return['type'] = "prescaned";
        } else {
            $return['state'] = true;
            $return['type'] = "scaned";
            $bindInfo = PermitAuthServiceProvider::decryptQrCode($code);
            $return['bindInfo'] = $bindInfo;
        }
        return $return;
    }

    /**
     * 二维码地址 原接口/asmtools/qrcode/qrcode.php
     */
    public function qrcode()
    {
        $code = request('code', 'request', 'qrcode demo');
        if (strpos($code, '?') === false) {
            $code .= "?";
        } else {
            $code .= "&";
        }
        $code .= "_=" . time() . '&route_type=QrCode';
        lib_qrcode::png($code, false, "L", 6, 1);
    }
}
