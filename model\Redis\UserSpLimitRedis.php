<?php

/**
 * Description: 用户信息
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: UserInfoRedis.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class UserSpLimitRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const PREFIX = 'ASM_';
    public const TABLE_NAME = 'UserSpLimit';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'Uuid,UserName,Priority,upTrafficLimit,downTrafficLimit,GwID';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = '';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['Uuid','UserName','upTrafficLimit','downTrafficLimit'],
        'ZtpUser' => ['ZtpUser']
    ];

    /**
     * 单条
     *
     * @param string $userID
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($userID, $gwId, $column = 'one')
    {
        return self::get($column, $userID, $gwId);
    }

    /**
     * 单条
     *
     * @param string $userID
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($userID, $gwId, $data)
    {
        $return = self::set($data, $userID, $gwId);
//        cutil_php_log("--------------------00----------------------","UserSpLimitRedis");
//        cutil_php_log(var_export($return, true), "UserSpLimitRedis");
        if ($return) {
            $cDatas = self::getChangeDatas();
//            cutil_php_log(var_export($cDatas, true), "UserSpLimitRedis");
//            cutil_php_log("upTrafficLimit::".$cDatas['upTrafficLimit'] ?? '', "UserSpLimitRedis");
//            cutil_php_log("downTrafficLimit::".$cDatas['downTrafficLimit']?? '', "UserSpLimitRedis");
            if (isset($cDatas['upTrafficLimit']) || isset($cDatas['downTrafficLimit'])) {
//                cutil_php_log("------------------01------------------------","UserSpLimitRedis");
                UserSpLimitServiceProvider::pushOneUserSpLimit($userID, $gwId, $data);
            }
        }
        return $return;
    }
}
