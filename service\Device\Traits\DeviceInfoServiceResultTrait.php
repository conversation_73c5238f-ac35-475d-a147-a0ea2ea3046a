<?php
/**
 * Description: 设备服务返回结果代码块 只能DeviceInfoService类使用
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id: DeviceInfoServiceResultTrait.php 171857 2022-03-24 09:16:07Z renchen $
 */

namespace Services\Device\Traits;

use Common\Facades\NACServiceFacade;
use Services\Common\Exceptions\AsmException;
use Services\Common\Services\DepartService;
use Services\Common\Services\DeviceOperationService;

trait DeviceInfoServiceResultTrait
{
    /**
     * 部门信息服务对象.
     *
     * @var DepartService
     */
    public $departService;

    /**
     * 设备id统一管理服务对象.
     *
     * @var DeviceOperationService
     */
    public $deviceOperationService;

    /**
     * 执行完所有流程后返回设备信息.
     *
     * @return array|mixed
     * @throws \Exception
     */
    public function returnDeviceInfo()
    {
        try {
            if ((1 === $this->params['newMobile'] || 1 === $this->params['is_mobile'])
                && 'control' !== $this->params['gettype']) {
                $this->updateMobileDevice();
            }

            // 微信认证加入
            if ($this->params['wechatid'] > 0) {
                $this->updateOtherUser($this->params['wechatid'], $this->deviceId);
            }
            // 2018-03-14 九州通-MAC客户端支持NAT&IP地址更新 yancheng add AscID
            $res = \DeviceModel::getJoinOne($this->deviceId);
            if (!\is_array($res) && $this->isWalkDevice === 0) {
                T(21103006);
            }

            if (in_array($res['ZtpDevice'], [\DeviceModel::ZTPDEVICE, \DeviceModel::UNKNOWZTPDEVICE]) && IS_INTERNAL) {
                \DeviceModel::update($this->deviceId, ['ZtpDevice' => 0]);
            }

            if ($res['ZtpDevice'] == \DeviceModel::UNKNOWZTPDEVICE && !IS_INTERNAL) {
                \DeviceModel::update($this->deviceId, ['ZtpDevice' => \DeviceModel::ZTPDEVICE]);
            }

            if ($this->isWalkDevice === 1) {
                $res['IP'] = $this->params['linkIP'];
            }

            // 设备信息变动的处理
            $this->changeDeviceInfo($res);

            $ip = $res['IP'];
            if ($res['GateIP']) {
                $ip = $res['GateIP'];
            }

            // 设备未审核且安检存在隐患情况下是否超过安全评估周期
            if (0 === (int)$res['Registered'] && 'fault' === $res['CheckResult']) {
                $res = $this->isOverCheckIntervalDay($res);
            }

            if (0 === (int)$res['LocationID']) {
                $companyName = cutil_dict_get('ADMINISTRATORINFO', 'CompanyName');
                $res['Position'] = $companyName;
                $res['Location'] = $companyName;
            }
            // 当发现部门没有获取到,则根据IP再取一次,为了弥补小助手注册的时候,不能根据IP来匹配部门的问题 zhangkb 2015-12-22
            if (0 === (int)$res['DepartID']) {
                $departId = $this->departService->getDepartByIp($ip);
                if ($departId > 0) {
                    \DeviceModel::update($res['DeviceID'], ['DepartID' => $departId]);
                    $allDepartName = \DepartModel::getOne($departId, 'name');

                    $res['AllDepartName'] = $allDepartName['AllDepartName'];
                    $res['DepartID'] = $departId;
                    $this->writeLog("departId:{$departId} ######");
                } else {
                    $companyName = cutil_dict_get('ADMINISTRATORINFO', 'CompanyName');
                    $res['AllDepartName'] = $companyName;
                }
            }
            if (!$res['CutOffStopTime'] && !$res['AuditStopTime']) {
                $total = \RelationComputerModel::getCount(['DeviceID' => $this->deviceId]);
                if ($total <= 0) {
                    \RelationComputerModel::repeatInsertToDb(['DeviceID' => $this->deviceId]);
                    $res['CutOffStopTime'] = DEFAULT_TIME;
                    $res['AuditStopTime'] = '9999-12-31 00:00:00';
                    $res['ForbidGuest'] = '0';
                    $res['IsTrustDev'] = '0';
                }
            }
            // 是否是漫游设备
            $res['isWalkDevice'] = $this->isWalkDevice;
            $res['isBrowserFingerprintDevice'] = $this->isBrowserFingerprintDevice;
            // 漫游设备的管理器ID取空，否则取当前服务器的管理器id
            $res['CoreID'] = $this->isWalkDevice === 1 ? '' : $this->params['licenseInfo']['ManageCode'] ?? '';
            // 非漫游场景才需要判断
            $res['IsPermit'] = 0;
            if (!$this->isWalkDevice) {
                $res['ipMacBindIllegal'] = $this->hasIpMacBindIllegal($res['Mac']);
                $res['fingerIllegal'] = $this->hasFingerIllegal($res['DeviceID']);
                // 未违规
                if ($res['ipMacBindIllegal'] === 0 && $res['fingerIllegal'] === 0) {
                    $res['IsPermit'] = $this->hasFastAccess($res['DeviceID']);
                }
            }
            // 是否绑定用户
            $res['isBindUser'] = $this->isBindUser($res['DeviceID']);
            //是否检查安装哨兵(AJC)断网
            $res['isAjcCut'] = $this->isAjcCut((int)$res['IsTrustDev'], $res['IP'], $res['DepartID'], $res['DeviceID']);
            //伪造hard 防止只能获取到ip且ip发生改变重复插入设备问题
            $res['fakeHard'] = $res['Hard'];

            $res['Location_parentID'] = $this->getParentLocation((int)$res['LocationID']);
            $res['IsnewDevice'] = $this->isNewDevice;

            if (isset($this->params['data_type']) && $this->params['data_type']) {
                $this->return['data'] = $res;
                $returnData = $this->return;
            } else {
                $returnData = $res;
            }
            $this->writeLog('返回最终结果：' . var_export($returnData, true));
            $this->setPortalServer();
            return $returnData;
        } catch (\Exception $e) {
            $this->writeLog('returnDeviceInfo 设备ID为：' . $this->deviceId . '出错了，错误信息为：' . $e->getMessage() . ",Code:" . $e->getCode() .
                PHP_EOL . $e->getFile() . ' 错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 设备信息变动处理
     *
     * @param $device
     */
    protected function changeDeviceInfo(&$device)
    {
        if (empty($this->deviceInfo)) {
            return;
        }

        // 设备类型从非用户设备变成用户设备，需要修复数据
        if (!in_array($this->deviceInfo['Type'], [DEVICE_TYPE_PC, DEVICE_TYPE_MOBILE])) {
            $this->repairComputerInfo($device);
        }
    }

    /**
     * 修复TComputer表数据
     * @param $device
     */
    protected function repairComputerInfo(&$device)
    {
        $computerInfo = \ComputerModel::getOne($this->deviceId, 'info');
        if (!empty($computerInfo)) {
            return;
        }
        $this->writeLog('转交换机后，computer表数据被删除，转回用户设备后修复数据：' . $this->deviceId);
        $data = [
            'DeviceID' => $this->deviceId,
            'GateIP' => $this->params['linkIP'],
            'Registered' => $this->params['Registered'],
            'OSName' => $this->params['OS'],
            'OSInstallTime' => $this->params['OSInstallTime'],
            'IEVersion' => $this->params['browser']
        ];
        foreach ($data as $key => $val) {
            $device[$key] = $val;
        }
        \ComputerModel::insert($data);
    }

    /**
     * 移动终端更新信息.
     */
    protected function updateMobileDevice(): bool
    {
        try {
            $updateData = array();
            $phoneInfo = $this->params['phoneinfo'];
            //每次更新ip/mac
            if ('ipgetmac' === $this->params['gettype'] || 'ipfake' === $this->params['gettype']) {
                if (6 !== $this->params['ipVer'] && $this->params['linkIP']) {
                    $updateData['IP'] = $this->params['linkIP'];
                    $updateData['TComputer']['GateIP'] = $this->params['linkIP'];
                }
                if ($this->mac) {
                    $updateData['Mac'] = $this->mac;
                }
            }
            if ('' !== $phoneInfo['ComputerName']) {
                $updateData['TDevice']['ComputerName'] = $phoneInfo['ComputerName'];
            }
            $updateData['DeviceID'] = $this->deviceId;

            // yanzj 如果是管理员修改的设备类型则不更新
            $result = \DeviceModel::getDevHand($this->deviceId);
            if (1 !== (int)$result['IsHandDevName']) {
                $updateData['TDevice']['DevName'] = $phoneInfo['DevName'];
            }
            if (1 !== (int)$result['IsHandType']) {
                if ('1' === $result['IsUserDef']) {
                    $updateData['TDevice']['Type'] = $result['Type'];
                } else {
                    $updateData['TDevice']['Type'] = $this->params['deviceType'];
                }
                $updateData['TDevice']['SubType'] = $this->params['deviceSubType'];
            }
            if ((int)$result['Type'] === -1) {
                $updateData['TDevice']['Type'] = $this->params['deviceType'];
                $updateData['TDevice']['SubType'] = $this->params['deviceSubType'];
            }
            if ('' !== trim($phoneInfo['OSName'])) {
                $updateData['TComputer']['OSName'] = $phoneInfo['OSName'];
            }
            $isNeedUpdatePolicyVersion = false;
            if (isset($this->params['ascid']) && $this->params['ascid'] !== '') {
                $updateData['TDevice']['AscID'] = $this->params['ascid'];
                // ascid变化情况下需要通知版本号变化
                if ($result['AscID'] !== $updateData['TDevice']['AscID']) {
                    $isNeedUpdatePolicyVersion = true;
                }
            }
            $updateRes = $this->deviceOperationService->updateDevice($updateData);
            $this->writeLog('更新手机信息：' . var_export($updateRes, true));
            if ($isNeedUpdatePolicyVersion) {
                $this->updatePolicyVersion($this->deviceId, 'mobile:AscID');
            }

            if ($updateRes['code'] !== 1) {
                throw new AsmException('更新移动终端信息失败：' . $updateRes['message']);
            }

            return true;
        } catch (\Exception $e) {
            $this->writeLog('updateMobileDevice 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 更新微信用户表.
     *
     * @param $wechatId
     * @param int $deviceId
     */
    protected function updateOtherUser($wechatId, int $deviceId): void
    {
        try {
            \OtherUserModel::update($wechatId, ['DeviceID' => $deviceId]);

            // 获取微信推广配置
            $wechatConfig = \DictModel::getAll('WeChatConfig');
            if (1 === (int)$wechatConfig['openwechat']) {
                \DeviceModel::update($deviceId, ['DepartId' => $wechatConfig['departid']]);
            }

            $this->writeLog('[更新微信用户表wechat]');
        } catch (\Exception $e) {
            $this->writeLog('updateOtherUser 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }
    }

    /**
     * 判断安检存在隐患情况下是否超过安全评估周期.
     *
     * @param array $res
     *
     * @return mixed
     */
    protected function isOverCheckIntervalDay(array $res)
    {
        if (!empty($res['SceneID'])) {
            $aRoleDict = \SceneDictModel::getAll($res['SceneID']);
            $res['IsSafeCheck'] = $aRoleDict['IsSafeCheck'];
            $res['CheckIntervalDay'] = $aRoleDict['CheckIntervalDay']; //安检周期
            $outCheckTime = ((time() - strtotime($res['CheckTime'])) / 86400) > $res['CheckIntervalDay'] ? 1 : 0;
            if ($res['IsSafeCheck'] && $outCheckTime && $res['CheckIntervalDay'] > 0) {
                $res['CheckResult'] = 'out';
            }
        } else {
            //有保活时间作为限制，超过保活时间后不生效
            $res['CheckResult'] = '';
        }

        return $res;
    }

    /**
     * 在设备可信状态下（ipmac违规高于可信）
     * 判断ipmac绑定是否违规，如果违规则提示用户且不能入网.
     *
     * @param string $mac
     *
     * @return int 1:违规  0:无违规
     */
    public function hasIpMacBindIllegal(string $mac): int
    {
        $res = cutil_dict_get('IpMacSet', 'IpMacBind');
        //判断是否开启ipmac绑定
        if (1 === (int)$res) {
            //30分钟内是否存在违规
            return \IPMacErrorOnLineModel::getCount(['MAC' => $mac]) > 0 ? 1 : 0;
        }

        return 0;
    }

    /**
     * 在设备可信状态下（指纹违规高于可信）
     * 判断指纹是否违规，如果违规则提示用户且不能入网.
     *
     * @param int $deviceId
     *
     * @return int 1/0 违规/无违规
     */
    public function hasFingerIllegal(int $deviceId): int
    {
        return \FingerViolationModel::getCount(['DeviceID' => $deviceId]) > 0 ? 1 : 0;
    }

    /**
     * 判断是否快速入网
     *
     * @param int $deviceId
     *
     * @return int 1/0 是/否
     * @throws \Exception
     */
    public function hasFastAccess(int $deviceId): int
    {
        // 是否被动访问
        if ((!isset($this->params['forbid']) || $this->params['forbid'] !== 1)
            && isset($this->params['firstUrl']) && $this->params['firstUrl'] !== '') {
            // 是否存在启用的快速入网规则
            $cond = ['IsPermit' => 1, 'State' => 0];
            $enableAndIsPermitCount = \NetManageRoleModel::getCount($cond);
            if ($enableAndIsPermitCount > 0) {
                // 设备是否为快速入网设备
                $devRes = \NetManageRoleModel::getIPListIdAndCount($deviceId);
                $devCount = (int)$devRes['count'];
                // 如果不存在 重新再算一遍
                if ($devCount === 0) {
                    // 异步重新更新设备角色
                    \lib_yar::clients('net', 'UpdateDeviceRole', ['deviceId' => $deviceId]);
                    $devRes = \NetManageRoleModel::getIPListIdAndCount($deviceId);
                    $devCount = (int)$devRes['count'];
                }
                if ($devCount > 0) {
                    // 通知放开网络
                    try {
                        $netParams = ['device_id' => $deviceId, 'iplistid' => $devRes['IPListId']];
                        NACServiceFacade::access("WebAccess:hasFastAccess", [$netParams], 'Access');
                        return 1;
                    }
                    catch (\Exception $e) {
                        $this->writeLog('WebAccess:hasFastAccess 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
                    }
                }
            }
        }
        return 0;
    }

    /**
     * 开启asw后,如果没有匹配到对应的asw,就不允许入网.
     * 1: 未安装asw, 0:已安装asw.
     *
     * @param int $isTrust
     * @param string $DevIP
     * @param $depId
     * @param $DevID
     *
     * @return int
     */
    protected function isAjcCut(int $isTrust, string $DevIP, $depId, $DevID): int
    {
        try {
            if (1 === $isTrust) {
                return 0;
            }
            $res = read_inifile(PATH_ETC . 'asm/asc/etc/tbridge_private.ini');
            // 开启在策略路由模式下，未安装控制器的终端将直接给予断网处理。
            if (isset($res['DepCutNet']) && 1 === (int)$res['DepCutNet'] && 'rbridge' === $res['BRIDGE_TYPE']) {
                $aDevIp = \AswDepIpRegionModel::getAll();
                if (\is_array($aDevIp)) {
                    $ipin = bindec(decbin(ip2long($DevIP)));
                    foreach ($aDevIp as $item) {
                        if ($ipin >= $item['IPStartInt'] && $ipin <= $item['IPEndInt']) {
                            return 0;
                        }
                    }
                }

                $aAswDev = \DevAswInfoModel::getJoinDepart($depId);
                if (!\is_array($aAswDev) ||
                    (!$aAswDev['flag'] && 1 === $aAswDev['IsManage'] && '' !== $aAswDev['IPSegment'])) {
                    return 1;
                }

                $aCutDep = \DepCutNetModel::getSingle(['DeviceID' => $DevID, 'column' => 'one']);
                if (\is_array($aCutDep) && '' !== $aCutDep['IP']) {
                    return 1;
                }
            }
        } catch (\Exception $e) {
            $this->writeLog('isAjcCut 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }

        return 0;
    }

    /**
     * 判断设备是否绑定了账号
     * @param $DevID
     * @return int
     */
    protected function isBindUser($DevID): int
    {
        try {
            $compuer = \RelationComputerModel::getBindUser($DevID);
            if ($compuer['IsBindUser'] == '1') {
                $userList = \AuthUserModel::getBindUserList($DevID);
                if (count($userList) > 0) {
                    return 1;
                }
            }
        } catch (\Exception $e) {
            $this->writeLog('isBindUser 出错了，错误信息为：' . $e->getMessage() . PHP_EOL . '错误行数：' . $e->getLine(), 'ERROR');
        }
        return 0;
    }

    /**
     * 返回首级设备位置.
     *
     * @param int $locationId
     *
     * @return int
     */
    public function getParentLocation(int $locationId): int
    {
        if (!$locationId || $locationId <= 0) {
            return 0;
        }

        $res = \LocationModel::getOne($locationId, 'one');
        if (0 === (int)$res['UpID']) {
            return $locationId;
        }

        return $this->getParentLocation($res['UpID']);
    }
}
