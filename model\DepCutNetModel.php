<?php

/**
 * Description: TDepCutNet
 * User: <EMAIL>
 * Date: 2021/05/27 10:32
 * Version: $Id
 */

class DepCutNetModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDepCutNet';
    public const PRIMARY_KEY = 'IP';
    protected static $columns = [
        'one'  => 'IP',
        '*'    => '*',
    ];

    /**
     * 不分页获取所有数据，如果需要分页请用getList
     *
     * @param array $cond
     * @param string $Column
     *
     * @return mixed
     */
    public static function getAll(array $cond = [], $Column = 'one')
    {
        self::$data = [];
        $where = !empty($cond) ? self::getWhere($cond) : '';

        $column = self::$columns[$Column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";

        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if(isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if(isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
