<?php
/**
 * Description: 自动认证相关服务
 * User: <EMAIL>
 * Date: 2021/08/27 15:53
 * Version: $Id: AutoResultService.php 168702 2022-02-15 06:50:30Z huyf $
 */

namespace Services\Result\Services;

use Services\Common\Services\RoleService;
use Services\Device\Services\DeviceSceneService;

class AutoResultService extends BaseResultService
{

    /**
     * 初始化
     *
     * @param $params
     * @throws \Exception
     */
    public function __construct($params)
    {
        $this->logFileName = 'policycheckitem';
        parent::__construct($params);
    }

    /**
     * 获取认证关键状态类型
     * @return string
     */
    public function getAuthType()
    {
        $deviceObj = \DeviceModel::getOne($this->deviceId, 'info');
        // 获取认证方式 user：账号密码认证 NoAuth：自动认证 Stop：禁止认证
        $mobileConfig = \DictModel::getAll("Mobile");
        $checkResult = $this->checkFrequency($mobileConfig, $deviceObj['Type']);
        if (!empty($checkResult)) {
            return $checkResult;
        }
        $auth = $mobileConfig['IsAuth'];
        $authtype = $mobileConfig['AuthType'];
        $stopauth = $mobileConfig['onmobileclientauth'];
        date_default_timezone_set('Asia/Shanghai');
        // 获取当前服务器时间
        $time = date('Y-m-d H:i:s', time());
        cutil_php_log("dev_live=" . $deviceObj['DeviceID'], $this->logFileName);
        // 获取当前设备状态,当客户端调取接口时进行传值
        if (empty($deviceObj)) {
            return "NotLive"; // 设备被删除
        }
        $relcomputerObj = \RelationComputerModel::getOne($this->deviceId, 'auth');
        $cos_time = $relcomputerObj['CutOffStopTime'];
        cutil_php_log("CutOffStopTime=" . $cos_time . ",Systime=" . $time, $this->logFileName);
        if ($cos_time >= $time) {
            return "Isolation"; // 设备被隔离
        }
        $ClientCheck = \DictModel::getAll('CLIENTCHECK');
        $computerObj = \ComputerModel::getOne($this->deviceId, 'info');
        //如果设备处于未认证状态，且开启了设备需要注册。再判断需要重新注册的角色是否存在
        $rcond = ['Groups' => 'DevReg', 'ItemName' => 'IsNeedReg', 'ItemValue' => '1', 'column' => 'one'];
        if (IS_MOBILE){
            $rcond['ItemName'] = 'Mobile_IsNeedReg';
        }
        $sceneDict = \SceneDictModel::getSingle($rcond);
        $sceneList = \SceneRelationModel::getList(['ConfigID' => $sceneDict['ConfigID'], 'column' => 'one']);

        if ($computerObj['Registered'] == "-1" && $ClientCheck['DeviceAutoReg'] == "1") {
            foreach ($sceneList as $ro) {
                if ($ro['SceneID'] == $this->params['sceneId']) {
                    return "Registered";//设备需要重新注册
                }
            }
            //当用户在开启设备需要注册而没有选择当前设备所属角色时，进行自动注册
            return "AutoRegistered";//设备需要重新注册
            //判断用户是否为自动注册
        } else if ($computerObj['Registered'] == "-1" && $ClientCheck['DeviceAutoReg'] == "0") {
            foreach ($sceneList as $ro) {
                if ($ro['SceneID'] == $this->params['sceneId']) {
                    return "AutoRegistered";//设备需要重新注册
                }
            }
        }
        if (strpos(strtolower($authtype), 'user') !== false && $auth == 1 && $stopauth != 1) {
            //获取设备的IP段 处理设备IP在免认证IP端内的判断问题
            $authIpList = explode(",", $mobileConfig['AuthIP']);
            cutil_php_log("当前设备IP=" . $deviceObj['IP'], $this->logFileName);
            cutil_php_log("所有免认证IP段=" . var_export($authIpList, true), $this->logFileName);
            foreach ($authIpList as $ips) {
                $ips = str_replace("-", ",", $ips);
                $ip_arr = explode(",", $ips);
                cutil_php_log("免认证IP段=" . var_export($ip_arr, true), $this->logFileName);
                $live = FindInIP($deviceObj['IP'], $ip_arr[0], $ip_arr[1]);
                //当设备的IP段处于配置的免认证的IP段内，则返回NoAuth
                if ($live == 1) {
                    return "NoAuth";
                }
            }
            return "User"; // 账号密码认证
        }
        if ($auth == 0 && $stopauth != 1) {
            return "NoAuth"; // 免认证
        }
        if ($stopauth == 1 || ($auth == 1 && strpos(strtolower($authtype), 'user') === false)) {
            return "Stop"; // 禁止认证
        }
        return "";
    }

    /**
     * 获取判断用户账户是否超过使用期限
     * @return string
     */
    public function getLifeTime()
    {
        $user = Base64DeExt($this->params['user']);
        $userinfo = \AuthUserModel::getOneByUserName('', $user);
        if (empty($userinfo)) {
            return "NoExist";
        }
        cutil_php_log("角色时间信息=" . var_export($userinfo, true), $this->logFileName);
        $time = strtotime("now") - (int)$userinfo["LifeTime"] * 3600;
        if ($userinfo["InsertTime"] < date("Y-m-d H:i:s", $time)) {
            return "OverDue";
        }
        return "";
    }

    /**
     * 获取认证结果
     * @return string
     * @throws \Exception
     */
    public function getAuthMes(): string
    {
        \hlp_check::checkEmpty($this->deviceId);
        // 获取当前设备最后认证的角色id
        $cond = ['DeviceID' => $this->deviceId, 'column' => 'one'];
        $onlineDevice = \NacOnLineDeviceModel::getSingle($cond, 'InsertTime desc');
        $sceneId = $onlineDevice['SceneID'] ?? 0;
        if ($sceneId === 0) {
            return "Error";
        }
        $sceneService = new DeviceSceneService();
        $roleInfo = $sceneService->getSceneInfoById($sceneId);
        $mes = $roleInfo['IsSafeCheck'] . "#infogo#" . $roleInfo['PolicyID'] . "#infogo#" . $sceneId;
        cutil_php_log("角色规范信息=" . $mes, $this->logFileName);
        if ($mes !== '' && $roleInfo['PolicyID'] !== '') {
            return $mes;
        }

        return "Error";
    }

    /**
     * 根据PolicyID获取安检项对应的ItemID值
     * @return string
     * @throws \Exception
     */
    public function getXml()
    {
        \hlp_check::checkEmpty($this->params['policyId']);
        //根据PolicyID获取安检项对应的ItemID值
        $policy = \NacPolicyListModel::getOne($this->params['policyId'], 'info');
        $policyBody = $this->getPolicyBody(OSTYPE_ANDROID, $policy['PolicyBody']);
        $xml = new \xml_safe_check_policy($policyBody); //解析规范
        $policyData = $xml->parseXml();
        $policyitems = array();
        cutil_php_log("policyData=" . var_export($policyData, true), $this->logFileName);
        foreach ($policyData['ItemData'] as $row) {
            array_push($policyitems, "{$row['ItemId']}:{$row['Key']}");
        }
        cutil_php_log("policyitems=" . var_export($policyitems, true), $this->logFileName);
        return implode("#infogo#", $policyitems);
    }

    /**
     * 根据PolicyID获取安检项对应的ItemID值
     * @return string
     * @throws \Exception
     */
    public function getPolicyItem()
    {
        //根据ItemID获取对应的安检项的XML报文
        \hlp_check::checkEmpty($this->params['itemId']);
        $checkItem = \NacCheckItemModel::getOne($this->params['itemId'], 'info');
        $content = $checkItem['Content'] ?? '';
        $xml = str_replace("\r\n", "", $content);
        return $xml;
    }

    /**
     * 检查认证频率
     *
     * @param $mobileConfig
     * @param $Type
     *
     * @return string
     */
    private function checkFrequency($mobileConfig, $Type)
    {
        // 是否IP变动触发自动认证，或者不是安卓还是原逻辑
        if ($Type != DEVICE_TYPE_MOBILE || !empty($this->params['changeIp'])) {
            return false;
        }

        $cond = ['DeviceID' => $this->deviceId, 'IsSuccess' => '1'];
        $authLog = \NacAuthLogModel::getSingle($cond, 'RID DESC');
        $useTime = time() - strtotime($authLog['InsertTime']);
        // 3分钟之内属于重复认证
        $maxTime = 180;
        cutil_php_log("useTime=" . $useTime . ",maxTime:{$maxTime},AutoAuthCycle:" .
            $mobileConfig['AutoAuthCycle'], $this->logFileName);

        // 网络超时的时间
        if (!empty($authLog) && $useTime < $maxTime) {
            return "RepeatAuth";//设备重复认证
        }

        return false;
    }
}
