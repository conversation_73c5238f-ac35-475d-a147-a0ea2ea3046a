<?php
/**
 * Description: 分布式相关接口
 * User: <EMAIL>
 * Date: 2021/08/16 09:46
 * Version: $Id
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class DistributedController extends BaseController
{
    /**
     * 从DASC上报信息ASM，ASM查询设备信息后返回， DASC再插入本地数据库，对应老交易 get_dasm_switch_info，必须为protected
     * ps: 查阅相关代码之后，未发现有调用的地方，暂时不重构 2021-08-16
     * @return mixed
     * @throws Exception
     */
    protected function switchInfo()
    {
        $SwitchIP = request('SwitchIP','request','','string');
        $DevLastTime = request('LastTime','request','','string');
        $manageIP = request('manageIP','request','','string') ?? get_dasm_ip();
        $rpcParams = [
            'SwitchIP' => $SwitchIP,
        ];

        if (!filter_var($SwitchIP, FILTER_VALIDATE_IP)) {
            return ['status' => 'n','data' => L(21100002)];
        }

        $switchInfo = \lib_yar::clients('dasm', 'switchInfo', $rpcParams, $manageIP, 10 * 1000,2);
        cutil_php_log("RPC获取DASM上的网络设备信息，返回结果：" . json_encode($switchInfo, JSON_UNESCAPED_UNICODE), 'get_dasm_dev_info');
        if (is_array($switchInfo) && $switchInfo['code'] === 1) {
            if ($DevLastTime > 0 && is_array($switchInfo['data'])
                && !empty($switchInfo['data']['TDevice']) && !empty($switchInfo['data']['TDevice']['LastTime'])) {
                $Online = (int)$switchInfo['data']['TDevice']['Online'];
                $lastTime = $switchInfo['data']['TDevice']['LastTime'];
                if ($Online === 1 && (time() - strtotime($lastTime) < $DevLastTime)) {
                    cutil_php_log("自动发现网络设备，asm上有此设备，但是该设备被其他asc管理了，不插入本地条目", 'get_dasm_dev_info');
                    return array('status' => 's', 'data' => $switchInfo['data']['TDevice']['OrigID']);
                }
            }
            return \DeviceModel::insertIntoTable($switchInfo['data'],'switch');
        }

        return ['status' => 'n','data' => 'Usb Not Exists'];
    }

    /**
     * 从dasm获取数据漫游到本地.
     * 该方法是直接对外提供的接口：先通过rpc接口获取数据 dasmDevService再判断数据是否需要漫游到本地
     * 对应老交易：get_dasm_dev_info
     * asmsvr会直接调用这个接口，web判断是否漫游是通过rpc接口向dasm询问：DasmController/devInfo
     * @return array
     * @throws Exception
     */
    public function devInfo(): array
    {
        $params = lib_request::$requests;
        $dasmDevService = DeviceServiceProvider::initDasmDevService();
        if(!isset($params['dev_xml'],$params['manageIP']) || empty($params['dev_xml'])) {
            T(21100002);
        }

        $xml = new \xml_dev_info($params['dev_xml']);
        $xmlInfo = $xml->parseXml();
        $devInfo = [
            'manageIP' => $params['manageIP'],
            'Ip' => $xmlInfo['Ip'],
            'Mac' => $xmlInfo['Mac'],
            'DiskId' => $xmlInfo['DiskId'],
            'DevLastTime' => $params['DevLastTime'] ?? 0,
        ];

        return $dasmDevService->roamDevInsertToLocal($devInfo);
    }

    /**
     * 从DASC上报信息ASM，ASM查询设备信息后返回， DASC再插入本地数据库，对应老交易 get_dasm_usb_info
     *
     * @return mixed
     * @throws Exception
     */
    public function usbInfo()
    {
        $UsbID = request('UsbID','request','','string');
        $DeviceID = request('DeviceID','request','','string');
        if (!$UsbID || !$DeviceID) {
            return ["status" => "n", "data" => 'UsbID and DeviceID can\'t be empty!'];
        }

        $rpcParams = [
            'UsbID' => $UsbID,
            'DeviceID' => $DeviceID
        ];

        $manageIP = get_dasm_ip();
        $usbInfo = \lib_yar::clients('dasm', 'usbInfo', $rpcParams, $manageIP, 10 * 1000,2);
        cutil_php_log("RPC获取DASM上的USB信息，返回结果：" . json_encode($usbInfo, JSON_UNESCAPED_UNICODE), 'get_dasm_usb_info');
        if (is_array($usbInfo) && $usbInfo['code'] === 1) {
            $UAMDeviceService = DeviceServiceProvider::initUAMDeviceService();
            $insertUsbRes = $UAMDeviceService->UsbRoamInsertLocal($DeviceID, $usbInfo['data']);
            if ($insertUsbRes['code'] === 1) {
                return ['status' => 'y','data' => $insertUsbRes['data']];
            }
        } else {
            return ['status' => 'n','data' => 'Usb Not Exists'];
        }
        return ['status' => 'n','data' => 'operation failure'];
    }

    /**
     * 插入设备接口(Dasm上导入设备，下发给Dasc执行)
     * ps:原接口 import_dasm_dev_info
     * @throws Exception
     */
    public function importDevFromDasm():array
    {
        $insertDeviceData = array (
            'IP' => request('IP'),
            'Mac' => request('Mac'),
            'Hard' => request('Hard'),
            'TDevice' => request('TDevice','post',[],'array'),
            'TComputer' => request('TComputer','post',[],'array'),
            'TRelationComputer' => request('TRelationComputer','post',[],'array'),
            'TDeviceExpand' => request('TDeviceExpand','post',[],'array'),
        );

        cutil_php_log('import Dev: ' . var_export($insertDeviceData, true), 'import_dasm_dev_info');

        $dasmDevService = DeviceServiceProvider::initDasmDevService();

        return $dasmDevService->importDevFromDasm($insertDeviceData);
    }
}
