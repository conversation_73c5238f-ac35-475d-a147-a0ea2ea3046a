<?php
/**
 * Description: 设备操作接口类
 * User: renchen
 * Date: 2020/7/15 11:08
 * Version: $Id: DeviceOperationInterface.php 147146 2021-06-17 02:04:51Z duanyc $.
 */

namespace Services\Common\Interfaces;

interface DeviceOperationInterface
{
    /**
     * 根据Hard,Mac,IP获取设备id.
     *
     * @param string $hard
     * @param string $mac
     * @param string $ip
     *
     * @return array
     */
    public function getDeviceID(string $hard, string $mac, string $ip):array;

    /**
     * 更新设备信息（IP,Mac,Hard）.
     *
     * @param array $data
     * IP,Mac,Hard 通过统一接口更新，其他信息在service服务中更新
     * TDevice表示更新该表的信息，TComputer表示更新TComputer的信息
     * $data = array(
     *  'DeviceID' => 1,
     *  'IP' => '************',
     *  'Mac' => 'xxxx',
     *  'TDevice' => array(
     *      'Type' => 101,
     *  ),
     * 'TComputer' => array(
     *      'Registered' => 1
     * )
     * );
     *
     * @return array
     */
    public function updateDevice(array $data):array;

    /**
     * 插入设备.
     *
     * @param array $data
     *
     * @return array
     */
    public function insertDevice(array $data):array;

    /**
     * 更新设备缓存信息.
     *
     * @param array $data
     *
     * @return bool
     */
    public function updateDeviceCacheAndLastTime(array $data):bool;
}