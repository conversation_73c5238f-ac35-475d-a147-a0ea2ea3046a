<?php

/**
 * Description: 网管组表
 * User: <EMAIL>
 * Date: 2022/04/29 23:32
 * Version: $Id: GateWayGroupModel.php 174952 2022-04-29 13:33:57Z duanyc $
 */

class GateWayGroupModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGateWayGroup';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'one'     => 'NetworkIP,NetworkPort,IP,HttpPort',
        'group'     => 'ID,IP'
    ];

    /**
     * 获取ID与IP映射
     *
     * @return mixed
     */
    public static function getAllIp()
    {
        self::$data = [];
        $column = self::$columns['group'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} ";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $allIps = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $allIps[$row['ID']] = $row['IP'];
            }
        }
        return $allIps;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
