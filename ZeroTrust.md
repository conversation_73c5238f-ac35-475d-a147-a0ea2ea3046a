## 零信任
### 一、零信任代码分布结构
    > control
        > ZtpController 零信任资源等在控制中心业务的父类(ASM)
        > ZtpGatewayController 提供前端调用的网关相关的交易(ASG)
        > ZtpPolicyController 零信任资源的策略相关交易(ASM)
        > ZtpResourceController 零信任资源的相关交易(ASM)
        > ZtpUserController 零信任用户的相关交易(ASM)
    > service
        > AgentServiceProvider 零信任资源代理业务(ASM)
        > AppServiceProvider 零信任网关应用业务(ASG)
        > GatewayGroupServiceProvider 零信任网管组业务(ASM下发负载均衡配置)
        > GatewayServiceProvider 零信任网关业务(ASM下发配置)
        > GuacamoleServiceProvider 零信任远程终端业务(ASM的RDP在线记录维护)
        > LoginServiceProvider 零信任登录态session维护(ASM)
        > NoticeServiceProvider 零信任通知计算业务(ASM)
        > PolicyServiceProvider 零信任策略检查业务(ASM)
        > RemoteServiceProvider 零信任远程应用业务(ASM)
        > ResourceServiceProvider 零信任资源基础业务(ASM)
        > VpnServiceProvider 零信任vpn业务(ASM)
        > WatermarkServiceProvider 零信任水印业务(ASM)
        > Policy
            > Services
                > AllowIPAccessPolicyService 零信任指定IP地址策略项
                > AllowLocationPolicyService 零信任指定位置策略项
                > AllowMacAccessPolicyService 零信任指定设备mac策略项
                > AllowOSPolicyService 零信任允许操作系统策略项
                > AllowProcessPolicyService 零信任允许运行软件策略项
                > AllowIPAccessPolicyService 
    > model
    > webroot
