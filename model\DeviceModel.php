<?php

/**
 * Description: 设备表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: DeviceModel.php 172482 2022-03-31 10:33:29Z renchen $
 */

class DeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDevice';
    public const PRIMARY_KEY = 'DeviceID';
    public const ZTPDEVICE = 1;
    public const UZTPDEVICE = 0;
    public const UNKNOWZTPDEVICE = 2;

    protected static $columns = [
        'ascID' => 'AscID',
        'hand' => 'IsHandType,IsHandDevName,IsUserDef,Type,SubType,AscID',
        'name' => 'ComputerName',
        'one'  => 'Mac,IP,DeviceID,DevName',
        'dasc' => 'IP,DeviceID,DAscID, OrigID',
        'relattionInfo' => 'TRelationComputer.SceneID,TRelationComputer.IsTrustDev, TRelationComputer.CutOffStopTime, TRelationComputer.TrustStopTime,' .
                           'TRelationComputer.AuditStopTime, TComputer.Registered, TDevice.Tel, TDevice.RoleID',
        'info' => 'DevName, ComputerName, IP, Mac, SwitchIP, SwitchPort, UserName, DepartId, Type, SubType, DeviceID, IsGuestDevice, IsHandType, IsUserDef,ZtpDevice,RegistIsInternal',
        'role' => 'RoleID, DepartID, IP, Tel, EMail',
        'online' => 'TDevice.DepartID,TNacOnLineDevice.UserID,TNacOnLineDevice.UserName,TNacOnLineDevice.RoleID,'.
                    ' TDevice.IP, TRelationComputer.CodeRight, TDevice.DeviceID,TNacOnLineDevice.AuthType, TRelationComputer.IsTrustDev',
        'relation' => 'TDevice.DeviceID, TRelationComputer.IsTrustDev, DevName, ComputerName, TComputer.OSName, ' .
              'TComputer.SPNumber,TComputer.Registered, IP, MAC, CheckResult, LastCheckTID, LastFaultTime, CheckTime , CodeRight',
        'computer' => 'TDevice.ComputerName,TDevice.IsGuestDevice,TDevice.IsUserDef,TComputer.Registered,TComputer.OSName,TComputer.IEVersion',
        'computerInfo' => 'TDevice.IP,TDevice.DeviceID,TDevice.Mac',
        'gateInfo' => 'IP, MAC, TComputer.GateIP',
        'register' => 'TDevice.LocationId,TComputer.Registered,TComputer.AllMac,TComputer.Hard,TDevice.IP,TDevice.Mac,TDevice.IsHandType',
        'registerInfo' => 'A.DeviceID, A.UserName, C.Registered, A.DevName, A.IP, A.DepartID, A.MAC, A.UserName, C.OSName, ' .
                          'B.DepartName, B.AllDepartName, A.Remark, A.LocationId, A.Tel, A.EMail, A.Type',
        'networkStatus' => 'TDevice.IP, TDevice.Mac, TRelationComputer.CheckTime, TRelationComputer.UserID as RUserID,TNacOnLineDevice.UserName, TNacOnLineDevice.UserID,
                            TNacOnLineDevice.SceneID,TNacOnLineDevice.AuthType, TNacOnLineDevice.RoleID,TNacOnLineDevice.AuthTime,TNacOnLineDevice.Token',
        'isOnline' => 'online',
        'asc' => 'AscID',
        'mac' => 'DeviceID,Mac,LastTime,InstallClient',
        'deviceIds' => 'GROUP_CONCAT(DeviceID) as DevID',
        'installClient' => 'InstallClient,IP',
        'policy' => 'IP,Mac,RoleID,SubType,InstallClient,DevName,UserName,IEVersion,OSName,TComputer.GateIP,TDevice.DepartID',
        'clientInfo' => 'TDevice.DevName,TComputer.OSName',
        'relationScene' => 'TDevice.RoleID, TDevice.Type, TDevice.SubType, TDevice.DepartID, TDevice.IP, TDevice.Tel, TDevice.EMail, TRelationComputer.SceneID, TComputer.GateIP, TRelationComputer.LastUserType, TDevice.AgentVersion, TDevice.InstallClient',
        '*'   => '*',
        'ip' => 'IP',
        'msep' => 'IP,DeviceID'
    ];

    /**
     * 单条加条件Type
     *
     * @param string $DeviceID
     * @param string $Type
     *
     * @return mixed
     */
    public static function getOneByType($DeviceID, $Type)
    {
        if (empty($DeviceID) || empty($Type)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns['one'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE DeviceID = ".self::setData($DeviceID)." AND Type = ".self::setData($Type);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 从Dasc获取对应Dasm的DeviceID
     *
     * @param $deviceID
     *
     * @return mixed
     * @throws Exception
     */
    public static function getDevIDFromDasc($deviceID)
    {
        if (empty($deviceID)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "select OrigID from TDevice where DeviceID = ".self::setData($deviceID);
        $devResult = lib_database::getOne($sql, $table['index'], false, 1, self::$data);
        return (int)$devResult['OrigID'] !== 0 ? $devResult['OrigID'] : $deviceID;
    }

    /**
     * 根据mac获取最后条
     *
     * @param string $Mac
     * @param string $Column
     *
     * @return mixed
     */
    public static function getOneByMac(string $Mac, $Column = 'one')
    {
        if (empty($Mac)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns[$Column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE Mac = ".self::setData($Mac)." ORDER BY LastTime DESC";
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据ip和mac获取最后条
     *
     * @param string $Ip
     * @param string $Mac
     *
     * @return mixed
     */
    public static function getOneByIpMac(string $Ip, string $Mac)
    {
        if (empty($Ip) || empty($Mac)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns['one'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE IP = ".self::setData($Ip)." AND Mac = ".self::setData($Mac);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取连表查询数据
     *
     * @param $DeviceID
     *
     * @return array|bool
     */
    public static function getJoinOne($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $sql = 'SELECT A.DeviceID, A.ZtpDevice,A.RegistIsInternal,A.UserName,A.IP,A.Mac, A.DepartId as DepartID,F.RoleID, A.Tel,A.EMail,A.Type,';
        $sql .= ' A.DevName,A.ComputerName,A.LocationId as LocationID,A.AscID, B.Registered, B.RoamFlag, B.ReRegReason,';
        $sql .= ' B.OSName,B.Hard,B.OSName,B.GateIP,C.CutOffStopTime,C.AuditStopTime,C.ForbidGuest,';
        $sql .= ' C.IsTrustDev,A.Remark,TDepart.AllDepartName,D.Location  as Position,D.Location,D.LocationID,';
        $sql .= ' E.Expand_1,E.Expand_2,E.Expand_3,E.Expand_4,E.Expand_5,E.Expand_6,E.Expand_7,E.Expand_8,';
        $sql .= ' E.Expand_9,E.Expand_10,C.CheckResult,C.CheckTime,F.SceneID ';
        $sql .= ' FROM TDevice A LEFT JOIN TComputer B ON A.DeviceID=B.DeviceID';
        $sql .= ' LEFT JOIN TRelationComputer C ON A.DeviceID=C.DeviceID';
        $sql .= ' LEFT JOIN TLocation D ON A.LocationId=D.LocationID';
        $sql .= ' LEFT JOIN TDeviceExpand E ON A.DeviceID=E.DeviceID';
        $sql .= ' LEFT JOIN TNacOnLineDevice F ON A.DeviceID = F.DeviceID';
        $sql .= ' LEFT JOIN TDepart ON A.DepartId = TDepart.DepartID';
        $sql .= " WHERE A.DeviceID = ".self::setData($DeviceID);
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取准入关系信息
     *
     * @param $DeviceID
     * @param string $Column
     *
     * @return array|bool
     */
    public static function getJoinRelationComputer($DeviceID, $Column = 'one')
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns[$Column];
        $sql = "SELECT {$column} FROM TDevice LEFT JOIN TRelationComputer on TDevice.DeviceID = TRelationComputer.DeviceID " .
               " LEFT JOIN TComputer on TDevice.DeviceID=TComputer.DeviceID WHERE TDevice.DeviceID = ".self::setData($DeviceID);
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取在线设备数据
     *
     * @param $cond
     *
     * @return array|bool
     */
    public static function getJoinOnlineDevice($cond)
    {
        if (empty($cond['DeviceID']) && empty($cond['Mac']) && empty($cond['IP'])) {
            return false;
        }
        self::$data = [];
        $columnName = isset($cond['column']) ? $cond['column'] : 'online';
        $column = self::$columns[$columnName];
        $sql = "SELECT {$column} FROM TRelationComputer,TDevice LEFT JOIN TNacOnLineDevice ON " .
               "TNacOnLineDevice.DeviceID=TDevice.DeviceID WHERE TRelationComputer.DeviceID=TDevice.DeviceID";
        if (isset($cond['DeviceID'])) {
            $sql .= " AND TDevice.DeviceID = ".self::setData($cond['DeviceID']);
        }
        if (isset($cond['Mac'])) {
            $sql .= " AND TDevice.Mac = ".self::setData($cond['Mac']);
        }
        if (isset($cond['IP'])) {
            $sql .= " AND TDevice.IP = ".self::setData($cond['IP']);
        }
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取连表查询数据: 如果是可信设备 查出是否有在线信息,有用在线角色，没有用设备角色
     *
     * @param $DeviceID
     *
     * @return array|bool
     */
    public static function getJoinOnline($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $where = "where a.DeviceID = ".self::setData($DeviceID)." and a.DeviceID = b.DeviceID";
        $sql = "select a.UserName as UserAccount,a.AuthType,a.RoleID from TNacOnLineDevice a,TDevice b {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取连表查询数据: 部门
     *
     * @param $DeviceID
     *
     * @return array|bool
     */
    public static function getJoinDepart($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $where = "where TDepart.DepartId = TDevice.DepartId  AND TDevice.DeviceID=".self::setData($DeviceID);
        $sql = "select DepartName, AllDepartName from TDepart,TDevice {$where} ";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取连表查询数据: 部门的角色
     *
     * @param $DeviceID
     *
     * @return array|bool
     */
    public static function getDepartRole($DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $where = "LEFT JOIN TDepart B ON A.DepartID = B.DepartID LEFT JOIN TRelationComputer R on A.DeviceID = R.DeviceID ";
        $where .= " WHERE A.DeviceID = ".self::setData($DeviceID);
        $sql = "SELECT B.RoleID, A.DepartID, A.UserName ,A.IP,A.Tel,A.EMail,R.SceneID FROM TDevice A {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * @param int $DeviceID
     * @return array|false|null
     */
    public static function getDevHand(int $DeviceID)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $column = self::$columns['hand'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $where = "WHERE DeviceID = ".self::setData($DeviceID);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取管理TComputer表关联的数据.
     *
     * @param int $DeviceID
     * @param string $columnName
     * @param bool $isOne
     *
     * @return array|false|null
     */
    public static function getJoinComputer(int $DeviceID, $columnName = 'computer', $isOne = true)
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$columnName];
        $where = "LEFT JOIN TComputer ON TDevice.DeviceID = TComputer.DeviceID WHERE TDevice.DeviceID = ".self::setData($DeviceID);
        $sql = "SELECT {$column} FROM TDevice {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);

        if (!empty($isOne)) {
            return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
        }

        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }


    /**
     * 根据授权组获取对应的设备列表.
     *
     * @param $authorizationID
     * @return array|false|null
     */
    public static function getListByAuthorizationGroup($authorizationID)
    {
        if (empty($authorizationGroupID)) {
            return [];
        }
        self::$data = [];
        $column = static::$columns['one'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE FIND_IN_SET(" . self::setData($authorizationGroupID) . ",AuthorizationGroup) > 0 ";
        $sql = "SELECT $column FROM {$table['name']} {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取管理TComputer表关联的数据.
     *
     * @param array $cond
     * @param string $columnName
     *
     * @return array|false|null
     */
    public static function getJoinComputerByCond(array $cond, $columnName = 'computerInfo')
    {
        if (empty($cond)) {
            return false;
        }

        self::$data = [];
        $where = "WHERE ";

        if (isset($cond['BrowserFingerprint'])) {
            $where .= " TComputer.BrowserFingerprint = ".self::setData($cond['BrowserFingerprint']);
        }

        $column = static::$columns[$columnName];
        $where = "LEFT JOIN TComputer ON TDevice.DeviceID = TComputer.DeviceID {$where} ";
        $sql = "SELECT {$column} FROM TDevice {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取管理TComputer和TDepart表关联的数据
     *
     * @param int $DeviceID
     * @param string $columnName
     *
     * @return array|bool|mixed
     */
    public static function getJoinComputerAndDepart(int $DeviceID, $columnName = 'registerInfo')
    {
        if (empty($DeviceID)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$columnName];
        $where = " LEFT JOIN TDepart B ON A.DepartID = B.DepartID LEFT JOIN TComputer C on " .
                 "A.DeviceID = C.DeviceID WHERE A.DeviceID = ".self::setData($DeviceID);
        $sql = "SELECT {$column} FROM TDevice A {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取已使用点数
     * @return int
     */
    public static function getUseCount()
    {
        self::$data = [];
        $rwhere = "WHERE A.ZtpDevice in (0,2) AND B.Registered > 0 and B.RoamFlag = 0 and A.Type > ".self::setData(99);
        $where = " LEFT JOIN TComputer B ON A.DeviceID = B.DeviceID {$rwhere} ";
        $sql = "SELECT count(DISTINCT Mac) as count FROM TDevice A {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $result = lib_database::getOne($sql, $table['index'], false, 1, self::$data);

        if ($result) {
            return $result['count'];
        }

        return false;
    }

    /**
     * 设备由DASC漫游至ASM上，清除ASM上的DAscID，切断关联关系
     * @param string $device_ids
     * @return bool
     */
    public static function DAscRoamAsmDevice(string $device_ids): bool
    {
        //删除设备涉及到的表
        $tables = array(
            'TNacAuthList',
            'TNacRepairLog',
            'TDevice',
            'TComputer',
            'TRelationComputer',
            'TDeviceExpand',
            'TSwitch',
            'TSwitchConfig',
            'TRouter',
            'TNetWorkTopLink',
            'TArp',
            'TSwitchMac',
            'TNacCheckTimes',
            'TNacAuthLog',
            'TNacOnLineDevice',
            'TDeviceBindUser',
            'TSoftRelation',
            'TSoftChangeLog',
            'THardRelation',
            'THardChangeLog',
            'TDevIPLog',
            'TTaskToDevice',
            'TSoftLicenseInfo',
            'TFingerViolation',
        );
        $dateTime = date('Y-m-d H:i:s', time()+8);
        $updateData = [
            'DAscID' => '',
            'OrigID' => 0,
            'InsertTime' => $dateTime,
            'UpdateTime' => null
        ];
        $device_ids = explode(',', $device_ids);
        foreach ($tables as $table) {
            self::$data = [];
            $_table = hlp_common::getSplitTable(null, $table);
            if ($table === 'TNetWorkTopLink') {
                $where = " LeftDeviceID IN (".self::setArrayData($device_ids).") OR RightDeviceID IN (".self::setArrayData($device_ids).")";
            } elseif (in_array($table, ['TSwitchMac','TArp'])) {
                $where = " SwitchID IN (".self::setArrayData($device_ids).")";
            } elseif ($table === 'TRouter') {
                $where = " switchid IN (".self::setArrayData($device_ids).")";
            } else {
                $where = " DeviceID IN (".self::setArrayData($device_ids).")";
                if ($table === 'TComputer') {
                    $updateData['RoamFlag'] = 0;
                    cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $updateData]), "model_{$table}");
                    lib_database::update($updateData, $where, $_table['name'], $_table['index'], self::$data);
                    unset($updateData['RoamFlag']);
                    continue;
                }
            }
            cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $updateData]), "model_{$table}");
            lib_database::update($updateData, $where, $_table['name'], $_table['index'], self::$data);
        }
        return true;
    }

    /**
     * 查询asm上的设备信息.
     *
     * @param $DeviceID
     * @param string $devType
     * @return array|false
     */
    public static function getDeviceAllInfo($DeviceID, string $devType='device')
    {
        if ($DeviceID <= 0) {
            return false;
        }

        $hasDevice = self::getOne($DeviceID, 'dasc');
        if ($hasDevice) {
            $data = [];
            $data['DAscID'] = $hasDevice['DAscID'];
            $tables = self::getTables('tables', $devType);
            $multiRowTables = self::getTables('multiRowTables', $devType);
            $replaceTablesKey = self::getTables('replaceTablesKey', $devType);
            foreach ($tables as $table => $key) {
                $data[$table] = [];
                cutil_php_log("table:" . $table.' key:'.$key, 'get_dasm_dev_info');
                $_table = hlp_common::getSplitTable(null, $table);
                if ($key && in_array($table, $multiRowTables, true)) {
                    self::$data = [];
                    $sql = 'SELECT * FROM ' . $_table['name'] . ' WHERE DeviceID=' . self::setData($DeviceID) . ' ORDER BY InsertTime DESC';
                    if (array_key_exists($table, $replaceTablesKey)) {
                        $repStr = 'WHERE ' . $replaceTablesKey[$table] . '=';
                        $sql = str_replace('WHERE DeviceID=', $repStr, $sql);
                    }
                    $records = \lib_database::getAll($sql, $_table['index'], false, self::$data);

                    if (is_array($records)) {
                        foreach ($records as $record) {
                            $tmpKey = $record[$key];
                            $record['DAscID'] = '11:11:11:11:11:11';
                            $record['OrigID'] = $tmpKey;
                            unset($record[$key], $record['UpdateTime']);
                            $data[$table][$tmpKey] = $record;
                        }
                    }
                } else {
                    self::$data = [];
                    $sql = 'SELECT * FROM ' . $_table['name'] . ' WHERE DeviceID=' . self::setData($DeviceID) . ' ORDER BY InsertTime DESC';
                    if (array_key_exists($table, $replaceTablesKey)) {
                        $repStr = 'WHERE ' . $replaceTablesKey[$table] . '=';
                        $sql = str_replace('WHERE DeviceID=', $repStr, $sql);
                    }
                    $record = \lib_database::getOne($sql, $_table['index'], false, 1, self::$data);
                    if (is_array($record)) {
                        if ($table === 'TDevice') { /* 设备漫游至dasc上，默认为受dasc管控设备 */
                            $record['AscID'] = '11:11:11:11:11:11';
                        }
                        if ($table === 'TComputer') {
                            $record['Registered'] = (int)$record['Registered'] === 0 ? -2 : (int)$record['Registered'];
                            $record['RoamFlag'] = (int)$record['Registered'] === 1 ? 1 : 0;
                        }
                        if ($key) {
                            $record['DAscID'] = '11:11:11:11:11:11';
                            $record['OrigID'] = $record[$key];
                            if (empty($replaceTablesKey[$table])) {
                                unset($record[$key]);
                            }
                        }
                        unset($record['UpdateTime']);
                        $data[$table] = $record;
                    }
                }
            }
            return $data;
        }
        return false;
    }

    /**
     * 插入多条数据条目（分布式数据漫游场景）
     * @param array $data
     * @param string $devType 设备类型，取值为：device,switch
     * @return bool|int
     */
    public static function insertIntoTable(array $data, $devType='device')
    {
        try {
            if (is_array($data)) {
                /* 从asm上获取到了数据 */
                $NewDeviceID = 0;
                $aTRelationComputer = array('LastCheckTID' => 0, 'LastAuthID' => 0);
                $multiRowTables = self::getTables('multiRowTables', $devType);
                $tables = self::getTables('tables', $devType);
                foreach ($data as $key => $vals) {
                    if (in_array($key, $multiRowTables, true)) {
                        foreach ($vals as $val) {
                            self::insertRow($key, $val, $NewDeviceID, $aTRelationComputer, $tables);
                        }
                    } else {
                        $val = $vals;
                        self::insertRow($key, $val, $NewDeviceID, $aTRelationComputer, $tables);
                    }
                }
                if ($NewDeviceID > 0) {
                    return $NewDeviceID;
                }
            }
        } catch (\PDOException $e) {
            cutil_php_log("error:" . $e->getMessage(), 'get_dasm_dev_info');
        }
        return false;
    }

    /**
     * 获取设备ID（该方法只允许在DASM上调用）.
     *
     * @param array $params
     * @return array|false|null
     */
    public static function getDeviceID(array $params)
    {
        try {
            $deviceType = get_ini_info(PATH_ETC . 'devinfo.ini', 'devtype');
            // todo 由于DASM没有开启设备id统一管理服务，这里还是用原来的调用方式 这里只允许DASM设备上调用
            // 其他设备类型走设备ID统一管理的方法
            if ($deviceType !== 'dasm') {
                T(21100002);
            }
            $hard = $params['DiskId'] ?? '';
            $mac = $params['Mac'] ?? '';
            $ip = $params['Ip'] ?? '';
            if ($ip && !filter_var($ip, FILTER_VALIDATE_IP)) {
                T(21103002);
            }
            if (!$mac || !IsStrHaveMac($mac)) {
                T(21103003);
            }

            self::$data = [];
            $sql = "SELECT func_isRegistered(".self::setData($hard).", ".self::setData($mac).", ".self::setData($ip).")  as DeviceID";
            $table = hlp_common::getSplitTable(null, self::TABLE_NAME);

            return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
        } catch (\Exception $e) {
            cutil_php_log($e->getMessage(), 'get_dasm_dev_info');
        }

        return false;
    }

    /**
     * 获取正确的设备ID
     * @param $deviceIDs
     * @return array
     */
    public static function getCorrectDeviceID($deviceIDs)
    {
        self::$data = [];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "select func_getCorrectDeviceID(".self::setArrayData($deviceIDs).") as DeviceID";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 分布式场景下数据漫游涉及到的相关表.
     * @param string $type
     * @param string $device
     * @return array
     */
    protected static function getTables(string $type = 'tables', string $device='device'): array
    {
        $tables = [
            'device' => [
                'TDevice' => 'DeviceID', // TDevice 需要保持在数组第一个，方便处理后面的关联表
                'TSwitch' => 'ID',
                'TSwitchConfig' => 'ID',
                'TNacAuthList' => 'ID',
                'TComputer' => 'ID',
                'TDeviceExpand' => 'ID',
                'TDevIpRegion' => '',
                'TNacCheckTimes' => 'ID',
                'TNacAuthLog' => 'RID',
                'TNacOnLineDevice' => 'RID',
                'TNacCutNetDevice' => 'RID',
                // 'TTaskToDevice' => 'ID',   // 胡宋说要去掉该表下发，具体问题见： bugId 11279
                'TRelationComputer' => 'ID', // 此表关联了TDevice， TNacCheckTimes， TNacAuthLog，延后插入
                'TFingerprintSave' => 'DevID',
                'TFingerViolation' => 'DeviceID',
                'TDeviceBindUser' => 'ID'
            ],
            'switch' => [
                'TDevice' => 'DeviceID', /* TDevice 需要保持在数组第一个，方便处理后面的关联表 */
                'TSwitch' => 'ID'
            ],
        ];

        // 需要将查询条件DeviceID替换的表及替换值
        $replaceTablesKey = [
            'device' => [
                'TFingerprintSave' => 'DevID',
            ],
            'switch' => [],
        ];

        // 需要获取多行数据的表 $tables主键不能为空
        $multiRowTables = [
            'device' => [
                'TSwitchConfig',
                'TTaskToDevice',
                'TDeviceBindUser'
            ],
            'switch' => [],
        ];
        // 重构之后这部分数据暂时没有使用到，原因是这里不更新，后续被getdeviceinfo交易给更新回去了
        // 实际上，产品最终的想法是保留设备的使用人和部门，这个会根据用户的认证角色来改变 --add by renchen 2021-09-06
        $noUpdateTables = [
            'device' => [
                'TRelationComputer' => [
                    'LastCheckTID',
                    'LastAuthID',
                    'CheckResult',
                    'CheckTime',
                    'LastFaultTime',
                    'AuditStopTime',
                    'AuditCutoffNet',
                ],
                'TDevice' => [
                    'IP',
                    'Mac',
                    'SwitchIp',
                    'SwitchPort',
                    'LastTime',
                    'BootTime',
                    'Online',
                    'RunTime'
                ],
                'TComputer' => [
                    'AllIP',
                    'Hard',
                    'GateIP',
                    'GateWay'
                ],
            ],
            'switch' => [],
        ];

        return ${$type}[$device] ?? [];
    }

    /**
     * 插入一条数据（分布式数据漫游场景）
     * @param $table
     * @param $data
     * @param $NewDeviceID
     * @param $aTRelationComputer
     * @param $tables
     * @return bool
     */
    protected static function insertRow($table, $data, &$NewDeviceID, &$aTRelationComputer, $tables = []): bool
    {
        if (empty($tables)) {
            $tables = self::getTables('tables');
        }
        $_table = hlp_common::getSplitTable(null, $table);
        if (is_array($data)) {
            if (empty($data)) {
                cutil_php_log($table." 数据为空，不处理：", 'get_dasm_dev_info');
                return false;
            }
            foreach ($data as $key => $value) {
                if ($key === 'DeviceID' || ($table === 'TFingerprintSave' && $key === 'DevID')) {
                    $data[$key] = $NewDeviceID;
                }
                if ($table === 'TDevIpRegion' && $key === 'MacID') {
                    $macID = explode('_', $value);
                    $macID[2] = $NewDeviceID;
                    $data[$key] = implode('_', $macID);
                }
                if ($table === 'TRelationComputer' && in_array($key, array('LastCheckTID', 'LastAuthID'))) {
                    $data[$key] = $aTRelationComputer[$key];
                }
                // 该表主键id是设备ID 没有自增，在DASM上获取数据时已去除主键，这里需要重新补上
                if ($table === 'TFingerViolation' && !isset($data['DeviceID'])) {
                    $data['DeviceID'] = $NewDeviceID;
                }
            }

            $result = lib_database::replace($data, $_table['name'], $_table['index']);
            $lastId = 0;
            if ($result) {
                $lastId = lib_database::insertId();
            }
            cutil_php_log('table:'.$_table['name']." lastId：" . $lastId, 'get_dasm_dev_info');

            /* 保留插入的id值，用于计算关联关系 */
            if ($table === 'TDevice') {
                $NewDeviceID = $lastId;
            }
            if ($table === 'TNacCheckTimes') {
                $aTRelationComputer['LastCheckTID'] = $lastId;
            }
            if ($table === 'TNacAuthLog') {
                $aTRelationComputer['LastAuthID'] = $lastId;
            }
            if (!empty($tables[$table])) {
                /* 将更新时间后延，让dasc强制上报一次更新，更新asm上的DASCID */
                self::$data = [];
                $sql = 'UPDATE ' . $table . ' SET UpdateTime=DATE_ADD(NOW(), interval 8 second) WHERE ' . $tables[$table] . '=' . self::setData($lastId);
                lib_database::query($sql, $_table['index'], false, self::$data);
            }
        } else {
            cutil_php_log("传递非数组类型数据，疑似出错：" . var_export($data, true), 'get_dasm_dev_info');
        }

        return true;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['DeviceIds'])) {
            $where .= "AND DeviceID in (" . self::setArrayData($cond['DeviceIds']) . ")";
        }

        if (isset($cond['SubType'])) {
            $where .= "AND SubType = ".self::setData($cond['SubType']);
        }

        if (isset($cond['Mac'])) {
            $where .= "AND Mac = ".self::setData($cond['Mac']);
        }

        if (isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        if (isset($cond['TypeIn'])) {
            $where .= "AND Type IN (".self::setArrayData($cond['TypeIn']).") ";
        }

        if (isset($cond['Tel'])) {
            $where .= "AND Tel = ".self::setData($cond['Tel']);
        }

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        if (isset($cond['LastTimeGE'])) {
            $where .= "AND LastTime >= ".self::setData($cond['LastTimeGE']);
        }

        if (isset($cond['DAscID'])) {
            $where .= "AND DAscID = ".self::setData($cond['DAscID']);
        }

        if (isset($cond['OrigID'])) {
            $where .= "AND OrigID = ".self::setData($cond['OrigID']);
        }

        if (isset($cond['InstallClient'])) {
            $where .= "AND InstallClient = ".self::setData($cond['InstallClient']);
        }

        if (isset($cond['RoleIds'])) {
            $where .= "AND RoleID IN (" . self::setArrayData($cond['RoleIds']) . ")";
        }

        if (isset($cond['IsGuestDevice'])) {
            $where .= "AND IsGuestDevice = ".self::setData($cond['IsGuestDevice']);
        }

        if (!empty($cond['UserNameOR']) || !empty($cond['TelOR'])) {
            $orWhere = [];
            if (!empty($cond['UserNameOR'])) {
                $orWhere[] = " UserName = ".self::setData($cond['UserNameOR']);
            }
            if (!empty($cond['TelOR'])) {
                $orWhere[] = " Tel = ".self::setData($cond['TelOR']);
            }
            $where .= "AND ( ".implode('OR', $orWhere)." ) ";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
