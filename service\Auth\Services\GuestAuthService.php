<?php
/**
 * Description: 来宾认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: GuestAuthService.php 175145 2022-05-06 07:53:42Z huyf $
 */

namespace Services\Auth\Services;

use AuthServiceProvider;
use AuthUserModel;
use Common\Facades\NACServiceFacade;
use ComputerModel;
use DeviceModel;
use DeviceServiceProvider;
use DictModel;
use Exception;
use GuestOnLineDeviceModel;
use GuestRelationModel;
use GuestSelfApplyModel;
use NacOnLineDeviceModel;
use NetRoleModel;
use NetServiceProvider;
use PrivateSetModel;
use RelationComputerModel;
use RoleRelationModel;
use Services\Common\Services\DESService;
use Services\Auth\Interfaces\AuthServiceInterface;
use Services\Device\Services\DeviceStatusService;
use SmsCodeModel;
use SmsServiceProvider;
use SystemServiceProvider;
use SceneDictModel;
use SceneModel;

class GuestAuthService extends BaseAuthService implements AuthServiceInterface
{
    public const GUEST_TYPE_DEFAULT = 0;  // 未启用来宾码
    public const GUEST_TYPE_NETCODE = 1;  // 来宾码
    public const GUEST_TYPE_QRCODE = 2;   // 来宾二维码开启时
    public const GUEST_TYPE_SMS = 3;      // 来宾短信

    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'Guest';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['action'] = request('action', 'request');
        $this->params['usefor'] = request('usefor', 'request');
        $this->params['guestId'] = request('GuestId', 'request');
        $this->params['netCode'] = request('net_code', 'request');
        $this->params['guestType'] = request('guestType', 'request');
        $this->params['guestselfId'] = request('guestselfid', 'request', 0, 'int');
        $this->params['deviceID'] = $this->deviceId;
        $this->params['GustCodeType'] = '';
        return $this->params;
    }

    /**
     * 获取验证服务
     *
     * @return array
     */
    public function getAuthServer(): array
    {
        if ($this->params['action'] === 'guestself') {
            return ["SelfGuest"];
        }

        return [];
    }

    /**
     * 添加用户
     *
     * @throws Exception
     */
    public function addUser()
    {
        $config = DictModel::getAll('GUESTAUTH');
        $authId = AuthUserModel::insert(['Type' => $this->userType, 'LifeTime' => $config["NetCodeTime"]]);
        $guestId = "Guest_{$authId}";
        AuthUserModel::update($authId, ['UserName' => $guestId]);
        return $guestId;
    }

    /**
     * 获取来宾扫码地址
     *
     * @param $deviceId
     * @param $guestId
     * @param $urlPrefix
     *
     * @return string
     */
    public function getScanShortUrl($deviceId, $guestId, $urlPrefix = '')
    {
        $cryptData = DESService::desEcbEncrypt("{$deviceId}|{$guestId}|0");
        if (empty($urlPrefix)) {
            $urlPrefix = URL_BASE;
        }
        $code = "{$urlPrefix}/access/qr?g={$cryptData}&isOpenUrl=1";
        $url = "/access/qrcode?code=" . urlencode($code);
        return $urlPrefix . $url;
    }

    /**
     * 获取真实地址
     *
     * @param int $deviceId
     * @param        $g
     *
     * @return string
     * @throws Exception
     */
    public function getScanCodeUrl($deviceId, $g)
    {
        $deQrCode = DESService::desEcbDecrypt(stringDecode($g));
        cutil_php_log($deQrCode, 'ScanGuest');
        $arr = explode('|', $deQrCode);
        if (count($arr) < 2) {
            T(21100002);
        }
        $guestmacordevid = $arr[0];
        $guestId = $arr[1];
        $userId = str_replace('Guest_', '', $guestId);   //去掉Guest_ 获取来宾帐号的用户ID
        $roleId = ROLE_ID_GUEST;
        $lang = $GLOBALS['CONFIG']['LANG_MAP'][LANG];
        $data = "GuestId={$guestId}&UserId={$userId}&local_lguage_set={$lang}&guestmacordevid={$guestmacordevid}&deviceid={$deviceId}";
        return "/access/guest/imgcode?user_name=Guest&roleid={$roleId}&type=Guest&" . $data;
    }

    /**
     * 根据guestId获取用户
     *
     * @param $guestId
     *
     * @return array|mixed|null
     */
    public function getUser($guestId)
    {
        $userinfo = AuthUserModel::getOneByUserName($this->userType, $guestId, 'user');
        if (empty($userinfo['RoleID']) || isDefaultTime($userinfo['AuthTime'])) {
            return false;
        }
        return $userinfo;
    }

    /**
     * 获取可分配角色
     *
     * @param $roleId
     *
     * @return bool|mixed
     */
    public function getAssignedRoleList($roleId)
    {
        $roleList = NetRoleModel::getGuestAssignedRoleList($roleId);
        if (empty($roleList)) {
            //默认获取来宾
            $roleList = [['ID' => ROLE_ID_GUEST, 'RoleName' => L(21126007), 'DefRoleID' => ROLE_ID_GUEST]];
        }
        return $roleList;
    }

    /**
     * 获取可分配安全域
     *
     * @param $roleId
     *
     * @return bool|mixed
     */
    public function getAssignedRegionList($roleId)
    {
        return NetRoleModel::getGuestAssignedRegionList($roleId);
    }

    /**
     * 校验权限
     *
     * @param $deviceId
     * @param $regionIds
     * @param array $checkswitch 校验后台开关
     * @throws Exception
     */
    public function checkPower($deviceId, $regionIds, $checkswitch = [])
    {
        $onlineDevice = NacOnLineDeviceModel::getOne($deviceId, 'role');

        if (empty($onlineDevice['RoleID'])) {
            T(21126023);
        }
        // 增加安检结果判断 未安检或安检不通过的终端没有来宾权限
        $checkResult = RelationComputerModel::getOne($deviceId, 'safecheck');
        if (empty($checkResult['CheckResult']) || $checkResult['CheckResult'] == 'false') {
            T(21126015);
        }

        if (is_array($checkswitch)) {
            foreach ($checkswitch as $key => $val) {
                if ($this->isGuestAuthority($deviceId, $key) === false) {
                    T($val);
                }
            }
        }

        // 根据场景判断是否有接入权限安全域
        if (!$this->isGusetAllocation($deviceId, $regionIds)) {
            T(21126040);
        }
    }

    /**
     * 生成来宾码
     *
     * @param $roleId
     * @param $params
     *
     * @return array
     * @throws Exception
     */
    public function generateNetcode($roleId, $params)
    {
        // 生成上网码
        cutil_php_log($params, 'GuestNetcode');
        $password = (string)@random_int(100000, 999999);
        // 获取设备所在部门
        $device = DeviceModel::getOne($params['deviceId'], 'info');
        if (!strlen($device['DepartId'])) {
            T(21126009);
        }

        $formatArr = ['user' => $params['userName'], 'ip' => $device['IP'], 'pw' => $password,
            'nowTime' => func_time_getNow()];
        $remark = L(21126010, $formatArr);

        $aGuestAuth = DictModel::getAll('GUESTAUTH');
        // 所有设备使用来宾上网码权限,1为开启,0为关闭
        if ($aGuestAuth['INetRight'] == 0) {
            T(21126011);
        }

        // 根据guestId来判断是常规来宾码插入还是二维码插入
        $aparams = [];
        $return = [];
        $aparams['Password'] = $password;
        $aparams['RoleID'] = $roleId;
        $aparams['DepartID'] = $device['DepartId'];
        $aparams['LifeTime'] = $aGuestAuth['NetCodeTime'];
        $aparams['CreateUser'] = $params['userName'];
        $aparams['Remark'] = $remark;
        $cond = ['Type' => $this->userType, 'UserName' => $params['GuestId']];
        AuthUserModel::updatePatch($cond, $aparams);
        $return['GuestId'] = $params['GuestId'];
        $return['Code'] = $password;
        return $return;
    }

    /**
     * 有可接待权限用户生成来宾码
     *
     * @param $params
     * @param $isThird
     *
     * @return array
     * @throws Exception
     */
    public function userGenerateNetcode($params, $isThird = false)
    {
        // 生成上网码
        cutil_php_log($params, 'GuestNetcode');
        $password = (string)@random_int(100000, 999999);
        if (!$isThird) {
            // 获取设备所在部门
            $device = DeviceModel::getOne($params['deviceId'], 'info');
            if (!strlen($device['DepartId'])) {
                T(21126009);
            }
            $formatArr = ['user' => $params['userName'], 'ip' => $device['IP'], 'pw' => $password, 'nowTime' => func_time_getNow()];
            $remark = L(21126010, $formatArr);
        } else {
            $params['deviceId'] = $params['deviceId'] ?? 0;
            $remark = L(21126039, ['user' => $params['userName'], 'pw' => $password]);
        }
        $aGuestAuth = DictModel::getAll('GUESTAUTH');
        // 所有设备使用来宾上网码权限,1为开启,0为关闭
        if ($aGuestAuth['INetRight'] == 0) {
            T(21126011);
        }
        $aparams = [];
        $return = [];
        $aparams['Password'] = $password;
        $aparams['RoleID'] = 2;
        $aparams['DepartID'] = $device['DepartId'] ?? 0;
        $aparams['LifeTime'] = $aGuestAuth['NetCodeTime'];
        $aparams['CreateUser'] = $params['userName'];
        $aparams['Remark'] = $remark;
        $aparams['Type'] = $this->userType;
        $aparams['UserName'] = '';
        $aparams['TrueNames'] = isset($params['TrueNames']) ? $params['TrueNames'] : '';
        $aparams['Tel'] = isset($params['Tel']) ? $params['Tel'] : $params['guestMobile'];
        $aparams['Email'] = isset($params['Email']) ? $params['Email'] : '';
        $userInfo = AuthUserModel::getOne($params["UserID"], 'all');
        $authId = AuthUserModel::insert($aparams);
        $params['GuestId'] = 'Guest_' . $authId;
        AuthUserModel::update($authId, ['UserName' => $params['GuestId']]);

        //如果开启了短信码发送通知 并且勾选了手机号发送
        $GetSmsInfo = DictModel::getAll("SMS"); //SMS信息
        if ($aGuestAuth['IsSendCode'] == 1 && $params['isSendSMS'] == 1 && $aparams['Tel'] != "") {
            $message = str_replace('${code}', $password, $aGuestAuth['NetCodeTpl']);
            $data = [
                'type' => 'other',
                'phone' => $aparams['Tel'],
                'deviceid' => $params['deviceId'],
                'check_code' => $password,
                'userid' => $authId,
                'message' => $message
            ];
            cutil_php_log($data, 'GuestNetcode');
            SmsServiceProvider::sendSms($GetSmsInfo, $data, 0);
        }
        // 更新来宾关系表
        $gparams = ['AllowRegionIDs' => $params['AllowRegionIDs'], 'DeviceId' => $params['deviceId'], 'AllowTime' => $params['AllowTime'], 'IsNeedAudit' => $params['IsNeedAudit'],
            'GuestStartTime' => $params['GuestStartTime'], 'GuestEndTime' => $params['GuestEndTime'],
            'Name' => $params['guestName'], 'Unit' => $params['guestCompany'], 'Tel' => $params['guestMobile'],
            'Remark' => $params['content'], 'StaffName' => $userInfo['TrueNames'], 'StaffTel' => $userInfo['Tel'],
            'GreetType' => (int)$params['UserID'] !== 0 ? 'user' : 'device', 'GreetUserID' => $params['UserID'], 'GreetUserName' => $userInfo['UserName'],'GreetAllowRegionIDs' => $params['AllowRegionIDs'], 'GreetAllowTime' => $params['AllowTime']];
        if ((int)$gparams['GreetUserID'] === 0) {
            $gparams['GreetUserDesc'] = $device['DevName'] ?? '';
            $gparams['GreetUserName'] = $device['Mac'] ?? '';
        } else {
            $gparams['GreetUserDesc'] = $userInfo['TrueNames'];
        }

        for ($i = 1; $i <= 5; $i++) {
            $gparams["GuestExpand_{$i}"] = $params["GuestExpand_{$i}"] ?? '';
        }
        GuestRelationModel::updateByUserId($authId, $gparams);     // 更新来宾关系表
        $return['GuestId'] = $params['GuestId'];
        $return['Code'] = $password;
        return $return;
    }

    /**
     * 有可接待权限用户生成团队来宾码
     *
     * @param $params
     *
     * @return array
     * @throws Exception
     */
    public function generateBatchNetcode($params)
    {
        // 生成上网码
        cutil_php_log($params, 'GuestNetcode');
        $password = (string)@random_int(100000, 999999);
        $password = "M" . $password;
        // 获取设备所在部门
        $device = DeviceModel::getOne($params['deviceId'], 'info');
        if (!strlen($device['DepartId'])) {
            T(21126009);
        }
        $userInfo = AuthUserModel::getOne($params["UserID"], 'all');

        $formatArr = ['user' => $userInfo['UserName'], 'ip' => $device['IP'], 'pw' => $password, 'nowTime' => func_time_getNow()];
        $remark = L(21126027, $formatArr);
        $aGuestAuth = DictModel::getAll('GUESTAUTH');
        // 所有设备使用来宾上网码权限,1为开启,0为关闭
        if ($aGuestAuth['INetRight'] == 0) {
            T(21126011);
        }

        // 大开关不需要了
        //        if ($aGuestAuth['AllowGuestTeam'] != '1') {
        //            T(21126035);
        //        }
        $aparams = [];
        $return = [];
        $aparams['Password'] = $password;
        $aparams['RoleID'] = 2;
        $aparams['DepartID'] = $device['DepartId'];
        $aparams['LifeTime'] = $aGuestAuth['NetCodeTime'];
        $aparams['CreateUser'] = $userInfo['UserName'];
        $aparams['Remark'] = $remark;
        $aparams['Type'] = $this->userType;
        $aparams['UserName'] = '';
        $authId = AuthUserModel::insert($aparams);
        $params['GuestId'] = 'Guest_' . $authId;
        AuthUserModel::update($authId, ['UserName' => $params['GuestId']]);
        // 更新来宾关系表
        $gparams = ['AllowRegionIDs' => $params['AllowRegionIDs'], 'DeviceId' => $params['deviceId'], 'AllowTime' => $params['AllowTime'],
            'IsNeedAudit' => 0, 'GustCodeType' => 'batch', 'TeamName' => $params['TeamName'], 'MaxNumber' => $params['MaxNumber'], 'ReceptionMaxNumber' => $params['MaxNumber'],
            'GuestStartTime' => $params['GuestStartTime'], 'GuestEndTime' => $params['GuestEndTime'],
            'StaffName' => $userInfo['TrueNames'], 'StaffTel' => $userInfo['Tel'],
            'GreetType' => (int)$params['UserID'] !== 0 ? 'user' : 'device', 'GreetUserID' => $params['UserID'], 'GreetUserName' => $userInfo['UserName'], 'GreetAllowRegionIDs' => $params['AllowRegionIDs'], 'GreetAllowTime' => $params['AllowTime']];
        if ((int)$gparams['GreetUserID'] == 0) {
            $gparams['GreetUserDesc'] = $device['DevName'] ?? '';
            $gparams['GreetUserName'] = $device['Mac'] ?? '';
        } else {
            $gparams['GreetUserDesc'] = $userInfo['TrueNames'];
        }

        GuestRelationModel::updateByUserId($authId, $gparams);     // 更新来宾关系表
        $return['GuestId'] = $params['GuestId'];
        $return['Code'] = $password;
        $return['qrcodeUrl'] = $this->getGuestTeamUrl($password, $authId);
        return $return;
    }

    /**
     * 来宾二维码处理
     *
     * @param $params
     *
     * @throws Exception
     */
    public function qrcodeAuth(&$params)
    {
        // 兼容oppo
        $keyCount = PrivateSetModel::getCount(['Type' => 'Dom', 'ProjectMod' => 'oppo_qrcodekey']);
        $params['keyCount'] = $keyCount;
        // 查询最后在线的设备信息
        $ip = getRemoteAddress();
        cutil_php_log("connect IP" . $ip, 'GuestImgcode');
        $device = DeviceModel::getSingle(['IP' => $ip], 'LastTime desc');
        $params['mac'] = $device['Mac'] ?? '';
        $aGuestAuth = DictModel::getAll("GUESTAUTH");
        if (!strlen($params['userName'])) {
            $params['userName'] = L(21126008);
        }
        if (empty($aGuestAuth['QrCode'])) {
            T(21126018);
        }
        // 判断是否已存在
        $userinfo = AuthUserModel::getOneByUserName($this->userType, $params['guestId'], 'create');
        if (!empty($userinfo['CreateUser'])) {
            T(21126012);
        }
        // 判断key
        if ($keyCount > 0) {
            $params['mac'] = request('mac', 'request');
            if (empty($params['key']) || $params['key'] != $aGuestAuth['QrcodeKey']) {
                T(21126013);
            }
        }
        // 默认获取来宾
        if ($params['roleId'] <= 0 || $params['deviceId'] > 100000000) {
            T(21126014);
        }
        $params['roleId'] = $params['roleId'] ? $params['roleId'] : ROLE_ID_GUEST;
        // 判断扫描设备、用户是否存在 传设备ID的逻辑已废弃，客户端并没有传扫码设备的设备ID
        if (!empty($params['deviceId'])) {
            cutil_php_log("client scan " . $params['deviceId'], 'GuestImgcode');
            // 客户端未获取到deviceId时容灾处理
            if ($params['deviceId'] === "N/A") {
                T(21126009);
            }
            // 客户端扫描
            $deviceInfo = DeviceModel::getJoinOnlineDevice(['DeviceID' => $params['deviceId']]);
        } else {
            cutil_php_log("wechat qq scan" . $params['mac'], 'GuestImgcode');
            // 微信、QQ等扫描
            $deviceInfo = DeviceModel::getJoinOnlineDevice(['Mac' => $params['mac']]);
            $params['deviceId'] = $deviceInfo['DeviceID'] ?? 0;
        }
        cutil_php_log(var_export($deviceInfo, true), 'GuestImgcode');
        if (empty($deviceInfo)) {
            T(21126009);
        }
        // 被扫码设备是否放开网络 处于安检放开域
        $deviceStatusService = new DeviceStatusService();
        $deviceIpRegion = $deviceStatusService->getDeviceIpRegion($params['deviceId']);
        if (empty($deviceInfo['RoleID']) || $deviceIpRegion !== 1) {
            T(21126015);
        }
        // 判断角色是否允许生成来宾码   使用终端在线表对应的认证帐号的帐号角色判断
        $greetUserInfo = AuthUserModel::getOne($deviceInfo['UserID'], 'one');

        $roleCount = $this->isGuestAuthority($params['deviceId'], 'QrCode');
        cutil_php_log(var_export($roleCount, true), 'GuestImgcode');
        if (!$roleCount) {
            T(21126016);
        }
        $deviceInfo['greetUserRoleID'] = $greetUserInfo['RoleID'];
        //需入网设备的deviceId
        if (!empty($params['newMobile'])) {
            //手机端传入设备ID
            $params['guestdeviceId'] = $params['guestMacOrDevId'];
        } else {
            //电脑端传入MAC
            $guestDevice = DeviceModel::getOneByMac($params['guestMacOrDevId'], 'one');
            if (!empty($guestDevice)) {
                $params['guestdeviceId'] = $guestDevice['DeviceID'];
            } else {
                if (!empty($params['guestMacOrDevId']) && is_numeric($params['guestMacOrDevId'])) {
                    $params['guestdeviceId'] = $params['guestMacOrDevId'];
                } else {
                    $params['guestdeviceId'] = $deviceInfo['DeviceID'];
                }
            }
        }
        $scanDevice = DeviceModel::getOne($params['guestdeviceId'], 'one');
        if (empty($scanDevice)) {
            T(21126025);
        }
        return $deviceInfo;
    }

    /**
     * 来宾二维码处理完通知
     *
     * @param $params
     *
     * @throws Exception
     */
    public function qrcodeNotice($params)
    {
        $cond = ['Type' => $this->userType, 'UserName' => $params['guestId']];
        AuthUserModel::updatePatch($cond, ['AuthTime' => 'now()']);
        $data = ['guestId' => $params['guestId']];
        cache_set_info('guestQrcodeResult', $params['guestdeviceId'], $data, 60);
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);

        $registered = DeviceServiceProvider::getDevRegistered($this->deviceId);
        if ((int)$registered !== 1) {    //来宾放开网络将未注册的设备设置为已注册
            // 检查授权点数
            if (!SystemServiceProvider::isRegistrable()) {
                T(21139006);
            }
            $computerParams = [
                'Registered' => '1',
                'RegTime' => 'now()'
            ];
            ComputerModel::update($this->deviceId, $computerParams);
            $data['Registered'] = '1';
        }
        $data['PolicyID'] = "0";  //来宾认证返回PolicyID和IsSafeCheck要设置为0
        $data['IsSafeCheck'] = "0";

        $ExpirateTime = date("Y-m-d H:i:s", (int)(strtotime("now") + (float)$data['guestRelation']['AllowTime'] * 3600));
        /**
         * 修改默认安全域或者来宾无需认证入网为场景配置默认安全域
         */
        if ($data['guestRelation']['AllowRegionIDs'] == "-1" || $data['UserID'] == "1") {
            $regionID['ItemValue'] = 1;
            $regionID = SceneModel::getScenePassIpRegion($data['SceneID']);
            $data['guestRelation']['AllowRegionIDs'] = $regionID['ItemValue'];
        }
        $ipListID = NetServiceProvider::getOtherRegionID($data['guestRelation']['AllowRegionIDs']);

        // 更新场景安全域信息
        $guestSelfApply = GuestSelfApplyModel::getSingle(["DeviceID" => $this->deviceId, "UserID" => $data['UserID']], "ID desc");
        $guestData = ['DeviceID' => $this->deviceId, 'UserID' => $data['UserID'], 'IplistID' => $ipListID,
            'ExpirateTime' => $ExpirateTime, 'RegionIDs' => $data['guestRelation']['AllowRegionIDs'], 'AccessType' => $data['guestRelation']['GustCodeType'], "GuestSelfApplyID" => $guestSelfApply['ID']];
        if ($data['guestRelation']['GreetType'] === 'user') {
            $guestData['GreetUserID'] = $data['guestRelation']['GreetUserID'];
        }
        if (!empty($data['guestRelation']['GuestStartTime'])) {
            $guestData['StartTime'] = $data['guestRelation']['GuestStartTime'];
        }
        if (!empty($data['guestRelation']['GuestEndTime'])) {
            $guestData['EndTime'] = $data['guestRelation']['GuestEndTime'];
        }
        $guestOnlineID = GuestOnLineDeviceModel::insert($guestData);
        GuestSelfApplyModel::update($guestSelfApply['ID'], ['GuestOnLineID' => $guestOnlineID]);

        switch ($data['CheckType']) {
            case self::GUEST_TYPE_NETCODE:
            case self::GUEST_TYPE_QRCODE:
                // 来宾上网码只能使用一次
                $GustCodeType = $data['GustCodeType'] ?? $this->params['GustCodeType'];
                if ($GustCodeType !== 'batch') {  //团队来宾码需要一直使用,不能去掉
                    AuthUserModel::update($userInfo['ID'], ['Password' => '']);
                }
                break;
            case self::GUEST_TYPE_SMS:
                $cond = ['Tel' => $this->params['mobilePhone'], 'CheckCode' => $this->params['checkCode']];
                SmsCodeModel::updatePatch($cond, ['UseTime' => 'now()', 'Flag' => STRING_TRUE]);
                //来宾认证：手机短信接入申请时，会创建来宾账户，这里用完验证码需将对应的来宾账户的验证码置空
                $cond = ['Type' => $this->userType, 'Password' => $this->params['checkCode']];
                AuthUserModel::updatePatch($cond, ['Password' => '']);
                break;
        }
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'net_code', 'mobile_phone', 'check_code'];
    }

    /**
     * 获取单个接待在线来宾管理信息
     *
     * @param $params
     *
     * @return bool|mixed
     */
    public function getSingleInfoList($params)
    {
        $SingleInfoList = GuestOnLineDeviceModel::getSingleInfoList($params);
        foreach ($SingleInfoList as &$item) {
            $lifeTime = ceil((strtotime($item['ExpirateTime']) - strtotime("now")) / 60);
            $lifeTime = $lifeTime > 0 ? $lifeTime : 0;
            $hours = intval(floor($lifeTime / 60));
            $hours = $hours < 10 ? '0' . $hours : $hours;
            $minutes = $lifeTime % 60;
            $minutes = $minutes < 10 ? '0' . $minutes : $minutes;
            $lefttime = $hours . ':' . $minutes . ':00';
            $item['lifeTime'] = $lefttime;
        }
        return $SingleInfoList;
    }

    /**
     * 获取单个接待在线来宾管理信息
     *
     * @param $params
     *
     * @return bool|mixed
     */
    public function getBatchInfoList($params)
    {
        $batchInfoList = GuestRelationModel::getList($params);
        foreach ($batchInfoList as $key=> &$item) {
            //如果最大接待数和剩余接待数相等的话，在团队接待中不展示
            if ($item['MaxNumber'] > 0 && $item['ReceptionMaxNumber'] > 0 && (int)$item['MaxNumber'] === (int)$item['ReceptionMaxNumber']) {
                unset($batchInfoList[$key]);
            } else {
                $userInfo = AuthUserModel::getOne($item['UserID'], 'one');
                $item['Code'] = $userInfo['Password'];
                $item['OnlineNumber'] = GuestOnLineDeviceModel::getCount(['UserID' => $item['UserID']]);
            }
        }
        sort($batchInfoList);
        return $batchInfoList;
    }

    /**
     * 获取团队接入二维码地址
     *
     * @param $code
     * @param $userId
     *
     * @return string
     */
    public function getGuestTeamUrl($code, $userId)
    {
        $urlPrefix = URL_BASE;
        $code = "{$urlPrefix}/mobile/ui/wel.html#/access/guestReg?code={$code}&isOpenUrl=1&userId={$userId}";
        $url = "/access/qrcode?code=" . urlencode($code);
        return $urlPrefix . $url;
    }

    /**
     * todo 测试没问题就把这个接口移除掉
     * 来宾访客入网提交,调用放开网络相关的逻辑
     * @param $params
     */
    public function guestSubmit($params): void
    {
        //        $sceneID=$params['sceneInfo']['SceneID'];
        //        $relationData = ['SceneID' => $sceneID, 'LastCheckTID' => $params['LastCheckTID'], 'CheckResult' => $params['CheckResult'],'LastFaultTime' => 'now()', 'CheckTime' => 'now()'];
        //        RelationComputerModel::update($params['DeviceId'], $relationData);


        //        NACServiceFacade::access("WebAccess:GuestAuth guestSubmit", [$nac_params], 'Access');
    }

    /*
   * 查询设备id是否为免认证
   * */
    public function isnoAuth($deviceID)
    {
        $noGuestAuth = false;
        // 兼容来宾无需认证方式
        $deviceInfo = DeviceModel::getJoinRelationComputer($deviceID, 'relationScene');
        if (isset($deviceInfo['SceneID'])) {
            //          // 查看来宾是否有接待权限
            $sceneInfo = SceneModel::getOne($deviceInfo['SceneID']);
            if ((int)$sceneInfo['UserType'] === 2) {
                // 员工才会有接入接待场景权限
                $sceneconfig = SceneDictModel::getValue($deviceInfo['SceneID'], 'AccessType', 'AccessType');
                $noGuestAuth = stripos($sceneconfig, 'NoAuth') !== false ? true : false;
            }
        }
        cutil_php_log("noGuestAuth:{$noGuestAuth}", $this->logFileName);
        return $noGuestAuth;
    }

    /*
    * 查询设备id是否为免注册
    * */
    public function isnoRegistered($deviceID)
    {
        $snoRegistered = false;
        // 兼容来宾无需认证方式
        $deviceInfo = \DeviceModel::getJoinRelationComputer($deviceID, 'relationScene');
        if (isset($deviceInfo['SceneID'])) {
            // 查看来宾场景是否免注册
            $sceneInfo = \SceneModel::getOne($deviceInfo['SceneID']);
            if ((int)$sceneInfo['UserType'] === 2) {
                $sceneconfig = \SceneDictModel::getOneConfig($deviceInfo['SceneID'], 'IsNeedReg', 'DevReg');
                $snoRegistered = (isset($sceneconfig['Config']) && (int)$sceneconfig['Config'] === 0) ? true : false;
            }
        }
        cutil_php_log("isnoRegistered:{$snoRegistered}", $this->logFileName);
        return $snoRegistered;
    }


    /**
     * 指定团队访客列表下线
     * @param $params
     * @throws Exception
     */
    public function guestTeamCutoff($params): void
    {
        $guestRelation = GuestRelationModel::getOne($params['UserID'], 'all');
        if ((int)$guestRelation['GreetUserID'] !== (int)$params['GreetUserID']  || (int)$guestRelation['DeviceId'] !== (int)$params['DeviceId']) {
            T(21126029);
        }
        $guestDevices = GuestOnLineDeviceModel::getList(['UserID' => $params['UserID'], 'column' => 'device']);
        if (count($guestDevices) > 0) {
            $cutoffConfig = [
                'is_force_cutoff' => 1,
                'is_isolated_access' => 1,
                'remark' => 'GuestDeviceCutOff', // 终端准入状态服务使用
            ];
            NACServiceFacade::cutoff('WebCutoff', $guestDevices, $cutoffConfig);
        }
        GuestSelfApplyModel::updatePatch(['UserID' => $params['UserID']], ['OffLineTime' => 'now()']); // 将离线时间更新为当前时间
        GuestSelfApplyModel::updatePatch(['Status' => 0, 'UserID' => $params['UserID']], ['Status' => -1, 'Reason' => '终端强制下线']);

        $gparams = ['MaxNumber' => 0, 'IsCancel' => 1];  //重置接入数量
        GuestRelationModel::updateByUserId($params['UserID'], $gparams);
    }

    /**
     * 接待者扫来宾二维码码入网提交
     * @param $params
     * @throws Exception
     */
    public function guestScanSubmit($params): void
    {
        $device = DeviceModel::getOne($params['DeviceId'], 'one');
        $UserInfo = AuthUserModel::getOne($params['UserID'], 'all');
        $greetUserInfo = AuthUserModel::getOne($params['GreetUserID'], 'all');
        $greetType = 'user';
        if (empty($device)) {
            T(21100002);
        }
        if (!$UserInfo) {
            T(21126030);
        }
        if (!$greetUserInfo) {
            $greetType = 'device';
            $greetUserInfo['TrueNames'] = $device['DevName'] ?? '';
            $greetUserInfo['UserName'] = $device['Mac'] ?? '';
            $greetUserInfo['Type'] = 'device';
        }
        $params['guestId'] = "Guest_" . $params['UserID'];

        // 更新来宾关系表
        $gparams = ['GustCodeType' => 'code_state', 'AllowRegionIDs' => $params['AllowRegionIDs'],
            'Name' => $params['guestName'], 'Unit' => $params['guestCompany'], 'Tel' => $params['guestMobile'],
            'Remark' => $params['content'],'StaffName' => $params['StaffName'], 'StaffTel' => $params['StaffTel'],
            'AllowTime' => $params['AllowTime'], 'IsNeedAudit' => 0, 'GreetType' => $greetType, 'GreetUserID' => $params['GreetUserID'],
            'GreetUserName' => $greetUserInfo['UserName'] ?? '' , 'GreetUserDesc' => $greetUserInfo['TrueNames'], 'DeviceId' => $params['DeviceId'],'GreetAllowRegionIDs' => $params['AllowRegionIDs'], 'GreetAllowTime' => $params['AllowTime']];
        for ($i = 1; $i <= 5; $i++) {
            $gparams["GuestExpand_{$i}"] = $params["GuestExpand_{$i}"];
        }
        GuestRelationModel::updateByUserId($params['UserID'], $gparams);

        // 插入申请表
        $gparams = ['UserID' => $params['UserID'], 'DeviceID' => $params['DeviceId'],
            'IP' => $device['IP'], 'Mac' => $device['Mac'], "DevName" => $device['DevName'],
            'Name' => $params['guestName'], 'Unit' => $params['guestCompany'], 'Tel' => $params['guestMobile'],
            'ApproveName' => $params['StaffName'], 'ApproveType' => $greetUserInfo['Type'] ?? '', 'ApproveUserName' => $params['StaffName'],
            'ApproveTime' => 'now()',
            'Remark' => $params['content'],
            'FromType' => L(21130012),
            'InsertTime' => 'now()',
            'ValidTime' => 'now()',
            'GreetType' => 'user',
            'GreetUserID' => $params['GreetUserID'],
            'GreetUserName' => $greetUserInfo['UserName'] ?? '',
            'AccessType' => 'code_state'];
        $aGuestAuth = DictModel::getAll('GuestAuth');
        $gparams['Status'] = 0;
        for ($i = 1; $i <= 5; $i++) {
            $gparams["GuestExpand_{$i}"] = $params["GuestExpand_{$i}"];
        }

        $reamrk = L(21130037);
        $cancelParams = ['FromType' => L(21130012), 'Status' => -1, 'Reason' => $reamrk];

        $params['DeviceId'] && GuestSelfApplyModel::updatePatch(['DeviceID'=>$params['DeviceId'],'Status'=>0], $cancelParams);

        GuestSelfApplyModel::insert($gparams);
        // 生成来宾码
        $deviceInfo = DeviceModel::getJoinOnlineDevice(['DeviceID' => $params['DeviceId']]);
        $gparams = ['userName' => $deviceInfo['UserName'], 'deviceId' => $params['DeviceId'], 'GuestId' => $params['guestId']];
        $guestInfo = $this->generateNetcode(ROLE_ID_GUEST, $gparams);
        if (empty($guestInfo['Code'])) {
            T(21126022);
        }
        $guestParams = [];
        $guestParams['deviceId'] = $params['guestdeviceId'];
        $guestParams['callfrom'] = '';
        $guestParams['netCode'] = $guestInfo['Code'];
        // 通过模拟校验code完成扫码校验
        $guestParams['servicePrefix'] = 'Guest';
        AuthServiceProvider::callbackRpcAuthServer($guestParams);
        //通知扫码完成校验
        $this->qrcodeNotice($params);
    }

    /**
     * 有可接待权限用户生成来宾码
     *
     * @param $params
     *
     * @throws Exception
     */
    public function userReserve($params)
    {
        // 生成上网码
        cutil_php_log($params, 'GuestNetcode');
        // 获取设备所在部门
        $device = DeviceModel::getOne($params['deviceId'], 'info');
        if (!strlen($device['DepartId'])) {
            T(21126009);
        }

        $formatArr = ['user' => $params['userName'], 'ip' => $device['IP'], 'pw' => '', 'nowTime' => func_time_getNow()];
        $remark = L(21126010, $formatArr);
        $aGuestAuth = DictModel::getAll('GUESTAUTH');
        // 所有设备使用来宾上网码权限,1为开启,0为关闭
        if ($aGuestAuth['INetRight'] == 0) {
            T(21126011);
        }
        $aparams = [];
        $return = [];
        $aparams['Password'] = '';
        $aparams['RoleID'] = 2;
        $aparams['DepartID'] = $device['DepartId'];
        $aparams['LifeTime'] = $aGuestAuth['NetCodeTime'];
        $aparams['CreateUser'] = $params['userName'];
        $aparams['Remark'] = $remark;
        $aparams['Type'] = $this->userType;
        $aparams['GuestType'] = 2;
        $aparams['UserName'] = '';
        $aparams['Tel'] = isset($params['guestMobile']) ? $params['guestMobile'] : '';
        $userInfo = AuthUserModel::getOne($params["UserID"], 'all');
        $authId = AuthUserModel::insert($aparams);
        $params['GuestId'] = 'Guest_' . $authId;
        AuthUserModel::update($authId, ['UserName' => $params['GuestId']]);

        // 更新来宾关系表
        $gparams = ['AllowRegionIDs' => $params['AllowRegionIDs'], 'AllowTime' => $params['AllowTime'], 'IsNeedAudit' => $params['IsNeedAudit'],
            'GuestStartTime' => $params['GuestStartTime'], 'GuestEndTime' => $params['GuestEndTime'], 'GustCodeType' => 'reserve',
            'Name' => $params['guestName'], 'Unit' => $params['guestCompany'], 'Tel' => $params['guestMobile'], 'MaxNumber' => '999',
            'Remark' => $params['content'], 'StaffName' => $userInfo['TrueNames'], 'StaffTel' => $userInfo['Tel'],
            'GreetType' => 'user', 'GreetUserID' => $params['UserID'], 'GreetUserName' => $params['userName'],'DeviceId' => $params['deviceId'],'GreetAllowRegionIDs' => $params['AllowRegionIDs'], 'GreetAllowTime' => $params['AllowTime']];

        if ((int)$gparams['GreetUserID'] === 0) {
            $gparams['GreetType'] = 'device';
            $gparams['GreetUserDesc'] = $device['DevName'] ?? '';
            $gparams['GreetUserName'] = $device['Mac'] ?? '';
        } else {
            $gparams['GreetUserDesc'] = $userInfo['TrueNames'];
        }
        GuestRelationModel::updateByUserId($authId, $gparams);     // 更新来宾关系表
        $return['GuestId'] = $params['GuestId'];

        //如果开启了短信码发送通知 并且勾选了手机号发送
        $GetSmsInfo = DictModel::getAll("SMS"); //SMS信息
        if ($aGuestAuth['AllowSMS'] == 1 && $params['isSendSMS'] == 1) {
            $message = str_replace('${mobile}', $aparams['Tel'], $aGuestAuth['ReserveCodeTpl']);
            $data = [
                'type' => 'other',
                'phone' => $aparams['Tel'],
                'deviceid' => $params['deviceId'],
                'check_code' => '',
                'userid' => $authId,
                'message' => $message
            ];
            cutil_php_log($data, 'GuestNetcode');
            SmsServiceProvider::sendSms($GetSmsInfo, $data, 0);
        }
        return $return;
    }

    /**
     * 获取有效期内的预约码
     *
     * @param $params
     *
     * @return bool|mixed
     */
    public function getUnusedList($params)
    {
        $unusedInfoList = GuestRelationModel::getList($params);
        foreach ($unusedInfoList as $key=> &$item) {
            //如果最大接待数和剩余接待数不相等的话，在团队待接入中不展示
            if ((int)$item['MaxNumber']>0 && (int)$item['ReceptionMaxNumber']>0  && (int)$item['MaxNumber']!==(int)$item['ReceptionMaxNumber'] && $item['GustCodeType']==='batch'){
                unset($unusedInfoList[$key]);
            }else{
                /* 如果是团队申请，需要把团队名给Name字段*/
                if ($item['GustCodeType'] == 'batch') {
                    $item['Name'] = $item['TeamName'];
                }
                if ($item['GustCodeType'] == 'reserve') {
                    $item['Code'] = '';
                    $item['GustCodeType'] = 'reserve';//预约
                } else {
                    $userInfo = AuthUserModel::getOne($item['UserID'], 'one');
                    $item['Code'] = $userInfo['Password'];
                    $item['GustCodeType'] = 'code';//访客码
                }
                unset($item['UserID'], $item['TeamName']);
            }
        }
        sort($unusedInfoList);
        return $unusedInfoList;
    }

    /**
     * 取消有效期内的预约码
     *
     * @param $params
     *
     * @throws Exception
     */
    public function guestUndo($params)
    {
        $unusedInfo = GuestRelationModel::getSingle($params);
        cutil_php_log('取消预约' . var_export($unusedInfo, true), 'GuestNetcode');
        if (is_array($unusedInfo) && $unusedInfo['ID'] > 0) {
            if (strtotime($unusedInfo['GuestEndTime']) < time()) {
                T(21126032);
            }
            if ($unusedInfo['MaxNumber'] > 0) {
                GuestRelationModel::updateByUserId($unusedInfo['UserID'], ['MaxNumber' => 0, 'IsCancel' => 1]);
            } else {
                T(21126034);
            }
        } else {
            T(21126033);
        }
    }

    /**
     * 获取认证的字段
     * @return array
     */
    public function getAuthColumns()
    {
        return ['authType', 'guestId', 'action', 'guestType', 'newMobile', 'callfrom', 'isGuestAuth', 'deviceID',
            'mobilePhone', 'checkCode', 'guestselfId', 'netCode'];
    }

    /**
     *  判断是否可以接待
     * $DeviceID 设备id
     * $code 接待方式
     * return bool true/false
     */
    public function isGuestAuthority($deviceId, $code): bool
    {
        $devInfo = RelationComputerModel::getOne($deviceId);
        $allowArray = array('AllowGuestTeam', 'GuestApplySelf', 'IsNeedAppoint', 'NetCode', 'QrCode');
        if (!in_array($code, $allowArray)) {
            return true;
        }
        // 根据场景获取接入权限
        if (!empty($devInfo['SceneID'])) {
            $sceneInfo = SceneModel::getOne($devInfo['SceneID']);
            cutil_php_log(var_export($sceneInfo, true), 'GuestImgcode');

            if ((int)$sceneInfo['UserType'] === 1) {
                // 员工才会有接入接待场景权限
                $sceneConfig = SceneDictModel::getValue($devInfo['SceneID'], 'receiveAccess', 'ReceiveConfig');
                cutil_php_log(var_export($sceneConfig, true), 'GuestImgcode');
                return stripos($sceneConfig, $code) !== false;
            }
        }
        return false;
    }

    /**
     *  判断是否有接待的安全域
     * $DeviceID 设备id
     * $allocation 安全域
     * return bool true/false
     */
    public function isGusetAllocation($deviceId, $allocation): bool
    {
        $devInfo = RelationComputerModel::getOne($deviceId);
        if (!$allocation) {
            return false;
        }
        $allocationArr = explode(',', $allocation);
        // 根据场景获取接入权限
        if (!empty($devInfo['SceneID'])) {
            $sceneInfo = SceneModel::getOne($devInfo['SceneID']);
            if ((int)$sceneInfo['UserType'] === 1) {
                // 员工接待安全域
                $allocationRegion = SceneDictModel::getValue($devInfo['SceneID'], 'allocationRegion', 'ReceiveConfig');
                foreach ($allocationArr as $k => $v) {
                    if (stripos($allocationRegion, $v) === false) {
                        return false;
                    }
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 查询设备id场景
     * */
    public function getScene($deviceID)
    {
        $sceneInfo = array();
        // 兼容来宾无需认证方式
        $deviceInfo = DeviceModel::getJoinRelationComputer($deviceID, 'relationScene');
        $sceneInfo = SceneModel::getOne($deviceInfo['SceneID']);
        $sceneInfo['AccessType'] = SceneDictModel::getValue((int)$deviceInfo['SceneID'], 'AccessType', 'AccessType');
        $sceneInfo['PassIpRegion'] = SceneDictModel::getValue((int)$deviceInfo['SceneID'], 'PassIpRegion', 'RegionList');
        $isAuditConfig = SceneDictModel::getOneConfig((int)$deviceInfo['SceneID'], 'IsNeedAuto', 'DevAudit');
        $isAudit = $isAuditConfig['Config'] ?? 0;
        $sceneInfo['AuditAccessType'] = $isAudit == 1 ? SceneDictModel::getValue((int)$deviceInfo['SceneID'], 'AuditAccessType', 'GuestAuditConfig') : "";
        return $sceneInfo;
    }
}
