<?php

/**
 * Description: 来宾自助申请提交
 * User: <EMAIL>
 * Date: 2021/05/18 09:46
 * Version: $Id: GuestController.php 173649 2022-04-18 13:47:28Z renchen $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class GuestController extends BaseController
{
    /**
     * 使用get方式的白名单方法 如：['auth' => true]
     * @var array
     */
    public $whiteList = ['imgcode' => true];

    /**
     * 认证类型
     * @var array
     */
    public static $requestType = [
        'Insert' => 'applyGuest',
        'State' => 'getStatus',
        'Notice' => 'approvalGuest',
        'Query' => 'getInfo',
        'Identity' => 'setRole',
        'Submit' => 'submitGuestInfo',
        'Recept' => 'receptGuest',
        'Cancel' => 'cancelApply',
        'GetAudit' => 'getAudit',
        'Audit' => 'emailAudit'
    ];

    /**
     * 参数校验.
     * @return mixed
     * @throws \Exception
     */
    public function selfParams()
    {
        $params = [];
        $params['action'] = request('action', 'request');
        $params['deviceId'] = request('device_id', 'request', 0, 'int');
        $params['deviceId'] = request('deviceid', 'request', $params['deviceId'], 'int');
        $params['guestselfId'] = request('guestselfid', 'request', 0, 'int');
        $params['state'] = request('state', 'request', 0, 'int');
        $params['roleId'] = request('roleid', 'request', 0, 'int');
        $params['status'] = request('Status', 'request', 0, 'int');
        $params['approveName'] = request('ApproveName', 'request');
        $params['UserID'] = request('UserID', 'request', 0, 'int');
        $params['token'] = request('token', 'request', '', 'string');
        $params['AccessTypeCode'] = request('AccessTypeCode', 'request', '', 'string');
        $params['from'] = 'self';

        if (!isset(self::$requestType[$params['action']])) {
            T(21100002);
        }
        $params['method'] = self::$requestType[$params['action']];
        return $params;
    }

    /**
     * 申请参数解析
     *
     * @param $params
     * @param bool $isEmpty
     * @throws Exception
     */
    public function applyParams(&$params, $isEmpty = false)
    {
        $CLIENTCHECK = ConfigServiceProvider::getDictAll('CLIENTCHECK');

        $params['guestName'] = request('guestname', 'request', '', 'origin');
        $params['username'] = request('username', 'request', '', 'origin');
        $params['guestCompany'] = request('guestcompany', 'request', '', 'origin');
        $params['userMobile'] = request('usermobile', 'request', '', 'origin');
        $params['guestMobile'] = request('guestmobile', 'request', '', 'origin');
        $params['isSendSMS'] = request('issendsms', 'request', '0', 'origin');
        $mobileContent = request('mobile_content', 'request', '', 'origin');
        $params['content'] = str_replace('¥', '￥', request('content', 'request', $mobileContent, 'origin'));

        // 参数检查
        if (LANG == 'en_US') {
            $suffix = '_en';
        } else {
            $suffix = '';
        }
        // 兼容来宾无需认证方式
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest']);
        $snoRegistered = $guestService->isnoRegistered($params['deviceId']);

        //免注册不用判断
        if ($snoRegistered == true) {
            return;
        }
        // 是否关闭注册
        if ($params['from'] !== 'scanSubmit' && $snoRegistered == true) {
            hlp_check::checkEmpty($params['deviceId']);
            hlp_check::checkGuestRequire($CLIENTCHECK['AuditRequireUser' . $suffix], $params['username'], false, $isEmpty);
            hlp_check::checkGuestRequire($CLIENTCHECK['AuditRequireTel' . $suffix], $params['userMobile'], false, $isEmpty);
            return;
        }
        // 扫码员工对于必填字段不用必填 可由开启注册时候来宾进行必填
        if ($params['from'] == 'scanSubmit') {
            for ($i = 1; $i <= 5; $i++) {
                $expand = request("GuestRequireExpand_{$i}", 'request', '', 'array');
                $params["GuestExpand_{$i}"] = !is_array($expand) ? stripslashes($expand) : implode(',', $expand);
            }
            return;
        }
        hlp_check::checkGuestRequire($CLIENTCHECK['GuestRequireUser' . $suffix], $params['guestName'], false, $isEmpty);
        hlp_check::checkGuestRequire($CLIENTCHECK['GuestRequireUnit' . $suffix], $params['guestCompany'], false, $isEmpty);
        hlp_check::checkGuestRequire($CLIENTCHECK['GuestRequireTel' . $suffix], $params['guestMobile'], false, $isEmpty);
        hlp_check::checkGuestRequire($CLIENTCHECK['GuestRequireReason' . $suffix], $params['content'], false, $isEmpty);
        if ($params['from'] !== 'submitGuestInfo' && $params['from'] !== 'scanSubmit') {
            hlp_check::checkEmpty($params['deviceId']);
            hlp_check::checkGuestRequire($CLIENTCHECK['AuditRequireUser' . $suffix], $params['username'], false, $isEmpty);
            hlp_check::checkGuestRequire($CLIENTCHECK['AuditRequireTel' . $suffix], $params['userMobile'], false, $isEmpty);
        }

        for ($i = 1; $i <= 5; $i++) {
            $expand = request("GuestRequireExpand_{$i}", 'request', '', 'array');
            $params["GuestExpand_{$i}"] = !is_array($expand) ? stripslashes($expand) : implode(',', $expand);
            hlp_check::checkGuestRequire($CLIENTCHECK["GuestRequireExpand_{$i}{$suffix}"], $params["GuestExpand_{$i}"], true, $isEmpty);
        }
    }

    /**
     * 来宾自助申请提交，对应老交易 selfguestsubmit
     *
     * @return array
     * @throws Exception
     */
    public function self()
    {
        // 公共参数接收
        $params = $this->selfParams();
        $return = [];
        try {
            if ($params['method'] !== 'getStatus') {
                cutil_php_log(lib_request::$requests, 'SelfGuestSubmit');
            }
            $params['servicePrefix'] = 'SelfGuest';
            // 不同动作的参数接收
            switch ($params['method']) {
                case 'applyGuest': // 来宾自助申请提交
                    $this->applyParams($params);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->applyGuest();
                    break;
                case 'getStatus': // 查询来宾自助申请是否被批准
                    hlp_check::checkEmpty($params['guestselfId']);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->getStatus();
                    break;
                case 'cancelApply': // 访客取消来宾自助申请
                    hlp_check::checkEmpty($params['guestselfId']);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->cancelApply();
                    break;
                case 'approvalGuest': // 员工批准或拒绝来宾
                    hlp_check::checkEmpty($params['guestselfId']);
                    hlp_check::checkEmpty($params['deviceId']);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->approvalGuest();
                    break;
                case 'setRole': // 设置来宾被批准后能拥有的角色
                    hlp_check::checkEmpty($params['guestselfId']);
                    hlp_check::checkEmpty($params['roleId']);
                    hlp_check::checkUsername($params['approveName']);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->setRole();
                    break;
                case 'getInfo': // 查询来宾被审批后能设置的角色和来宾的电话
                    hlp_check::checkEmpty($params['guestselfId']);
                    hlp_check::checkEmpty($params['deviceId']);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->getInfo();
                    break;
                case 'submitGuestInfo': // 来宾接入必填信息提交
                    $params['from'] = "submitGuestInfo";
                    $this->applyParams($params);
                    $params['guestSelfID'] = request('guestSelfID', 'request', 0, 'int');
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->submitGuestInfo();
                    break;
                case 'receptGuest': // 员工接待来宾 批准/拒绝
                    hlp_check::checkEmpty($params['guestselfId']);
                    hlp_check::checkEmpty($params['deviceId']);
                    $accessTypeCode = $params['AccessTypeCode'] ?? '';
                    $code = 'GuestApplySelf'; // 该值表示是否有审核权限
                    $auestauthority = $this->isGuestAuthority($params['deviceId'], $code);
                    if (!$auestauthority) {
                        T(21126016);
                    }
                    $params['AllowRegionIDs'] = request('AllowRegionIDs', 'request');
                    $params['AllowTime'] = request('AllowTime', 'request');
                    $params['Reason'] = request('Reason', 'request');
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->receptGuest();
                    break;
                case 'getAudit': // 获取审批信息
                    hlp_check::checkEmpty($params['token']);
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->getAudit();
                    break;
                case 'emailAudit': // 邮件审批
                    hlp_check::checkEmpty($params['guestselfId']);
                    hlp_check::checkEmpty($params['deviceId']);
                    $code = 'GuestApplySelf'; // 该值表示是否有审核权限
                    $auestauthority = $this->isGuestAuthority($params['deviceId'], $code);
                    if (!$auestauthority) {
                        T(21126016);
                    }
                    $params['AllowRegionIDs'] = request('AllowRegionIDs', 'request');
                    $params['AllowTime'] = request('AllowTime', 'request');
                    $params['state'] = 1;
                    $selfService = AuthServiceProvider::initAuthService($params);
                    $return = $selfService->audit();
                    break;
                default:
            }
        } catch (Exception $e) {
            if (API_VERSION < '1.0') {
                $return = ["state" => "error", "msg" => $e->getMessage()];
            } else {
                throw $e;
            }
        }
        return $return;
    }

    /**
     * 生成来宾上网码，对应老交易 get_guest_netcode
     *
     * @return array
     * @throws Exception
     */
    public function netcode()
    {
        cutil_php_log(lib_request::$requests, 'GuestNetcode');
        $getType = request('getType', 'request');
        $roleId = request('roleid', 'request');  //认证帐号的角色
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
        if (empty($roleId)) {
            T(21126006);
        }
        switch ($getType) {
            case 'getrole':
                $return = $guestService->getAssignedRoleList($roleId);
                break;
            case 'getregion':
                $return = $guestService->getAssignedRegionList($roleId);
                break;
            default:
                $params = ['from' => 'netcode'];
                $params['userName'] = request('user_name', 'request');
                if (!strlen($params['userName'])) {
                    $params['userName'] = L(21126008);
                }
                $params['UserID'] = request('UserID', 'request', 0, 'int');
                $params['deviceId'] = request('deviceId', 'request', 0, 'int');
                $params['AllowRegionIDs'] = request('AllowRegionIDs', 'request');
                $params['AllowTime'] = request('AllowTime', 'request', 8, 'int');
                $params['IsNeedAudit'] = request('IsNeedAudit', 'request', 0, 'int');
                $params['GuestStartTime'] = request('GuestStartTime', 'request');
                $params['GuestEndTime'] = request('GuestEndTime', 'request');
                $this->applyParams($params, true);
                $guestService->checkPower($params['deviceId'], $params['AllowRegionIDs'], ['NetCode' => '21126024']);
                $return = $guestService->userGenerateNetcode($params);
                cutil_php_log($return, 'GuestNetcode');
        }
        return $return;
    }

    /**
     * 获取团队来宾上网码和二维码
     *
     * @return array
     * @throws Exception
     */
    public function batchNetcode()
    {
        cutil_php_log(lib_request::$requests, 'GuestNetcode');
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
        $params = [];
        $params['UserID'] = request('UserID', 'request', 0, 'int');
        $params['deviceId'] = request('deviceId', 'request', 0, 'int');
        $params['AllowRegionIDs'] = request('AllowRegionIDs', 'request');
        $params['TeamName'] = request('TeamName', 'request');
        $params['AllowTime'] = request('AllowTime', 'request', 8, 'int');
        $params['MaxNumber'] = request('MaxNumber', 'request', 1, 'int');
        $params['GuestStartTime'] = request('GuestStartTime', 'request');
        $params['GuestEndTime'] = request('GuestEndTime', 'request');
        $guestService->checkPower($params['deviceId'], $params['AllowRegionIDs'], ['AllowGuestTeam' => '21126024']);
        $return = $guestService->generateBatchNetcode($params);
        cutil_php_log($return, 'GuestNetcode');
        return $return;
    }

    /**
     * 来宾扫一扫，对应老交易 get_guest_imgcode
     *
     * @return array
     * @throws Exception
     */
    public function imgcode()
    {
        try {
            $return = [];
            $params = [];
            $params['userName'] = request('user_name', 'request');
            $params['newMobile'] = request('NewMobile', 'request', 0, 'int');
            $params['roleId'] = request('roleid', 'request', 0, 'int');
            $params['guestMacOrDevId'] = request('guestmacordevid', 'request', '', 'char');
            $params['deviceId'] = request('deviceid', 'request', 0, 'int');
            $params['host'] = request('host', 'request');
            $params['getType'] = request('getType', 'request');
            $params['guestId'] = request('GuestId', 'request');
            $params['key'] = request('key', 'request');
            $params['callfrom'] = request('callfrom', 'request');
            $params['UserId'] = request('UserId', 'request');
            cutil_php_log(var_export($params, true), 'GuestImgcode');
            $guestParams = ['servicePrefix' => 'Guest', 'deviceId' => $params['deviceId']];
            $guestService = AuthServiceProvider::initAuthService($guestParams);
            // 二维码扫码处理
            $deviceInfo = $guestService->qrcodeAuth($params);
            cutil_php_log("来宾设备ID: {$params['guestdeviceId']}", 'GuestImgcode');
            $isIphone = request('isIphone', 'request');
            $prefix = !empty($isIphone) ? "?isIphone={$isIphone}" : '';
            $url = "/mobile/ui/wel.html{$prefix}#/access/scanReception?guestdeviceId={$params['guestdeviceId']}&greetUserRoleID={$deviceInfo['greetUserRoleID']}&userId={$params['UserId']}&GreetUserID={$deviceInfo['UserID']}&isOpenUrl=1";
            mobileGotoUrl($url);  //有接待权限的话接待人需要填写访客入网必填信息
        } catch (Exception $e) {
            $errConfig = [21126013 => 'no', 21126015 => 'fault', 21126022 => 'error'];
            $code = $e->getCode();
            $this->printInfo($params, $code, false);
            if (isset($errConfig[$code])) {
                $return['status'] = $errConfig[$code];
                $return['msg'] = $e->getMessage();
                return $return;
            } elseif (API_VERSION < '1.0') {
                exit($e->getMessage());
            }
            T($code);
        }
        return $return;
    }

    /**
     * 展示
     *
     * @param array $params
     * @param int $code
     * @param bool $isSuc
     * @throws Exception
     */
    private function printInfo($params, $code, $isSuc = true)
    {
        cutil_php_log("printInfo " . $code, 'GuestImgcode');
        // 判断key
        if ($params['keyCount'] == 0) {
            // 只有内部小助手才存在ostype，微信等其他的则不存在
            $ostype = hlp_compatible::getExtendData('ostype');
            if (!empty($ostype)) {
                exit(L($code));
            } else {
                hlp_common::showMobileMessage($isSuc, $code);
            }
        }
    }

    /**
     * 获取批准来宾账户信息列表
     * @return array|bool
     */
    public function infoList()
    {
        $params = [
            'approveName' => request('approveName', 'request'),
            'approveType' => request('approveType', 'request'),
        ];
        $params['servicePrefix'] = 'SelfGuest';
        try {
            $selfService = AuthServiceProvider::initAuthService($params);
            $return = $selfService->getInfoList();
        } catch (Exception $e) {
            $return = ["state" => "error", "msg" => $e->getMessage()];
        }
        return $return;
    }

    /**
     * 获取来宾申请待批准列表
     * @return array|bool
     */
    public function approveInfoList()
    {
        $params = [
            'GreetType' => 'user',
            'GreetUserID' => request('GreetUserID', 'request', 0, 'int')
        ];
        $params['servicePrefix'] = 'SelfGuest';
        try {
            $GreetDeviceID = request('GreetDeviceID', 'request', 0);
            $selfService = AuthServiceProvider::initAuthService($params);
            $return = $selfService->getApproveInfoList($GreetDeviceID);
        } catch (Exception $e) {
            $return = ["state" => "error", "msg" => $e->getMessage()];
        }
        return $return;
    }

    /**
     * 获取单个接待在线来宾管理列表
     * @return array|bool
     */
    public function singleInfoList()
    {
        $params['servicePrefix'] = 'Guest';
        $singleParams = [
            'AccessType' => ['single', 'sms', 'self_apply', 'code_state'],
            'GreetUserID' => request('GreetUserID', 'request', 0, 'int'),
            'DeviceId' => request('DeviceId', 'request', 0, 'int')
        ];
        try {
            $selfService = AuthServiceProvider::initAuthService($params);
            $return = $selfService->getSingleInfoList($singleParams);
        } catch (Exception $e) {
            $return = ["state" => "error", "msg" => $e->getMessage()];
        }
        return $return;
    }

    /**
     * 获取团队/会议接待在线来宾管理列表
     * @return array|bool
     */
    public function batchInfoList()
    {
        $params['servicePrefix'] = 'Guest';
        $singleParams = [
            'GustCodeType' => 'batch',
            'GreetUserID' => request('GreetUserID', 'request', 0, 'int'),
            'GuestStartTime' => 'now()',
            'GuestEndTime' => 'now()',
            'IsCancel' => 0,
            'column' => 'batch'
        ];
        $deviceId = request('DeviceId', 'request', 0, 'int');
        if ((int)$singleParams['GreetUserID'] === 0) {
            $singleParams['DeviceId'] = $deviceId;
        }
        try {
            $selfService = AuthServiceProvider::initAuthService($params);
            $return = $selfService->getBatchInfoList($singleParams);
        } catch (Exception $e) {
            $return = ["state" => "error", "msg" => $e->getMessage()];
        }
        return $return;
    }

    /**
     * 来宾访客入网提交,调用放开网络
     *
     * @return array
     * @throws Exception
     */
    public function submit()
    {
        $params = [];
        $params['DeviceId'] = request('DeviceId', 'request', 0, 'int');
        $params['UserID'] = request('UserID', 'request', 0, 'int');
        $params['CheckResult'] = request('checkResult', 'request', 'success', 'string');
        $params['LastCheckTID'] = request('lastCheckId', 'request', '0', 'string');
        if (empty($params['DeviceId'])) {
            T(21104004);
        }
        $deviceSceneService = DeviceServiceProvider::initDeviceSceneService();
        $sceneInfo = $deviceSceneService->getDevSceneInfo($params['DeviceId']);
        $params['sceneInfo'] = $sceneInfo['data'];
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => $params['DeviceId']]);
        $guestService->guestSubmit($params);
        return ["Result" => "success"];
    }

    /**
     * 接待者扫来宾二维码码入网提交
     *
     * @return array
     * @throws Exception
     */
    public function scanSubmit(): array
    {
        $params = [];
        $params['DeviceId'] = request('DeviceId', 'request', 0, 'int');
        $params['guestdeviceId'] = request('guestdeviceId', 'request', $params['DeviceId'], 'int');
        $params['UserID'] = request('UserID', 'request', 0, 'int');
        $params['GreetUserID'] = request('GreetUserID', 'request', 0, 'int');
        $params['AllowTime'] = request('AllowTime', 'request', 8, 'int');
        $params['AllowRegionIDs'] = request('AllowRegionIDs', 'request');
        $params['from'] = "scanSubmit";
        if (empty($params['DeviceId'])) {
            T(21104004);
        }
        $auestauthority = $this->isGuestAuthority($params['DeviceId'], 'QrCode');

        if (!$auestauthority) {
            T(21126016);
        }
        $this->applyParams($params);
        $guestParams = ['servicePrefix' => 'Guest', 'deviceId' => $params['DeviceId']];
        $guestService = AuthServiceProvider::initAuthService($guestParams);
        $guestService->guestScanSubmit($params);
        return ["Result" => "success"];
    }

    /**
     * 指定团队下线接口
     *
     * @return array
     * @throws Exception
     */
    public function teamCutoff()
    {
        $params = [];
        $params['GreetUserID'] = request('GreetUserID', 'request', 0, 'int');
        $params['UserID'] = request('UserID', 'request', 0, 'int');
        $params['DeviceId'] = request('DeviceId', 'request', 0, 'int');
        if ((empty($params['GreetUserID']) && empty($params['DeviceId'])) || empty($params['UserID'])) {
            T(21100002);
        }
        $guestParams = ['servicePrefix' => 'Guest', 'deviceId' => ''];
        $guestService = AuthServiceProvider::initAuthService($guestParams);
        $guestService->guestTeamCutoff($params);
        return ["Result" => "success"];
    }

    /**
     * 预约提交
     * @throws Exception
     */
    public function reserve()
    {
        cutil_php_log(lib_request::$requests, 'GuestNetcode');
        $roleId = request('roleid', 'request');  //认证帐号的角色
        $guestService = AuthServiceProvider::initAuthService(['servicePrefix' => 'Guest', 'deviceId' => '']);
        $params = [];
        $params['userName'] = request('user_name', 'request');
        // 前台传参用户名用Base64EnExt加密，在此解密
        if (!is_numeric($params['userName'])) {
            if ($params['userName'] != Base64DeExt($params['userName'])) {
                $params['userName'] = Base64DeExt($params['userName']);
            }
        }
        if (!strlen($params['userName'])) {
            $params['userName'] = L(21126008);
        }
        $params['UserID'] = request('UserID', 'request', 0, 'int');
        $params['deviceId'] = request('deviceId', 'request', 0, 'int');
        $params['guestName'] = request('guestname', 'request', '', 'origin');
        $params['guestCompany'] = request('guestcompany', 'request', '', 'origin');
        $params['guestMobile'] = request('guestmobile', 'request', '', 'origin');
        $params['isSendSMS'] = request('issendsms', 'request', '0', 'origin');
        $params['AllowRegionIDs'] = request('AllowRegionIDs', 'request');
        $params['AllowTime'] = request('AllowTime', 'request', 8, 'int');
        $params['IsNeedAudit'] = request('IsNeedAudit', 'request', 0, 'int');
        $params['GuestStartTime'] = request('GuestStartTime', 'request');
        $params['GuestEndTime'] = request('GuestEndTime', 'request');
        $guestService->checkPower($params['deviceId'], $params['AllowRegionIDs'], ['SmsState' => '21126038', 'IsNeedAppoint' => '21126038']);
        $return = $guestService->userReserve($params);
        cutil_php_log($return, 'GuestNetcode');
        return $return;
    }

    /**
     * 获取待接入的访客预约列表，包括来宾码，手机号预约
     * @throws Exception
     */
    public function unusedList()
    {
        $params['GreetUserID'] = request('GreetUserID', 'request', 0, 'int');
        $params['servicePrefix'] = 'Guest';
        $singleParams = [
            'GreetUserID' => $params['GreetUserID'],
            'GuestEndTime' => 'now()',
            'MaxNumber' => '1',
            'column' => 'unused'
        ];
        $deviceId = request('DeviceId', 'request', 0, 'int');
        if ((int)$singleParams['GreetUserID'] === 0) {
            $singleParams['DeviceId'] = $deviceId;
        }
        try {
            $selfService = AuthServiceProvider::initAuthService($params);
            $return = $selfService->getUnusedList($singleParams);
        } catch (Exception $e) {
            $return = ["state" => "error", "msg" => $e->getMessage()];
        }
        return $return;
    }

    /**
     * 取消未使用的访客预约
     * @throws Exception
     */
    public function undo()
    {
        $params['RelationID'] = request('RelationID', 'request', 0, 'int');
        $params['GreetUserID'] = request('GreetUserID', 'request', 0, 'int');
        $params['DeviceId'] = request('DeviceId', 'request', 0, 'int');
        if (((empty($params['GreetUserID']) || $params['GreetUserID'] <= 0) && !$params['DeviceId']) || empty($params['RelationID']) || $params['RelationID'] <= 0) {
            T(21100002);
        }
        $singleParams = [
            'ID' => $params['RelationID'],
            'GreetUserID' => $params['GreetUserID'],
            'column' => 'all'
        ];
        if ((int)$singleParams['GreetUserID'] === 0) {
            $singleParams['DeviceId'] = $params['DeviceId'] ;
        }
        $guestParams = ['servicePrefix' => 'Guest', 'deviceId' => ''];
        $guestService = AuthServiceProvider::initAuthService($guestParams);
        $guestService->guestUndo($singleParams);
        return ["Result" => "success"];
    }

    /*
    * 判断是否可以接待
    * $DeviceID 设备id
    * $code 接待方式
    * return bool true/false
    * */
    private function isGuestAuthority($deviceId, $code): bool
    {
        $sign = false;
        $guestParams = ['servicePrefix' => 'Guest', 'deviceId' => $deviceId];
        $guestService = AuthServiceProvider::initAuthService($guestParams);
        $sign = $guestService->isGuestAuthority($deviceId, $code);
        return $sign;
    }
}
