## 服务层代码
> 服务层也就是业务逻辑层，这里独立出来专门处理具体的业务逻辑，相同的业务都是调用相应的服务处理。

> 根目录下放服务提供者类，如：AuthServiceProvider::getDictValue，一些逻辑简单的可直接写在服务提供者类中即可，比如ConfigServiceProvider；对于业务复杂的，由服务提供者类，返回对应的服务对象类，对应的服务对象类实现具体的逻辑，比如认证业务，AuthServiceProvider::initAuthService()返回对应的认证方式的服务类对象，再调用auth方法进行认证等。

> service外不可以直接调用service子目录下的类的方法，只能通过ServiceProvider返回服务对象类，再调用对应方法。

### Common目录 公共服务（1698版本重构的）
##### AD域和LDAP服务
##### 部门服务

### Device目录 设备相关服务

### Auth目录 认证相关服务

### Result目录 安检结果相关服务

### Patch目录 补丁相关服务

### Distributed目录 分布式场景下以及数据漫游相关业务