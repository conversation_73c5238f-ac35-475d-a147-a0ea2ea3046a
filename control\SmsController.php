<?php

/**
 * Description: 短信相关
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: SmsController.php 158320 2021-09-29 12:41:30Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class SmsController extends BaseController
{
    /**
     * 发送短信，对应老交易 send_sms
     *
     * @return array|string
     * @throws Exception
     */
    public function send()
    {
        $params = [];
        $params['callfrom'] = request('callfrom', 'request');
        $params['deviceid'] = request('deviceid', 'request', 0, 'int');
        $session = LoginServiceProvider::isLogin($params['deviceid']);
        $phone = request('mobile_phone', 'request');
        $params['phone'] = $phone;
        $params['userid'] = request('userid', 'request', $session['Uuid']);
        $params['username'] = request('username', 'request');
        // admin为管理员登录，user为入网认证,未传递为user
        $params['type'] = request('type', 'request', 'user');
        $params['codeType'] = request('codeType', 'request', 'mobile');
        $params['isGuestAuth'] = request('isGuestAuth', 'request', 0, 'int');
        $isEmail = hlp_check::checkRuleResult('Email', $phone);
        $isMobile = hlp_check::checkRuleResult('Tel', $phone);

        // 验证手机号格式和邮箱格式
        if ($params['type'] === 'user' && !$isEmail && !$isMobile) {
            $code = ($params['codeType'] === 'email') ? 21101006 : 21131002;
            T($code);
        }

        SmsServiceProvider::sendCode($params, $isEmail);
        $this->errmsg = $isEmail ? L(21131015) : L(21131014);

        if (API_VERSION < '1.0') {
            return $this->errmsg;
        }

        return ['info' => $this->errmsg, 'code' => '200'];
    }

    /**
     * 敲门发短信
     *
     * @return mixed
     * @throws Exception
     */
    public function fwknop()
    {
        $CodeData = request('CodeData', 'request');
        $params = SmsServiceProvider::parseFwknopData($CodeData);
        $params['userid'] = 0;
        $params['deviceid'] = time();
        $params['type'] = 'user';
        $isEmail = hlp_check::checkRuleResult('Email', $params['phone']);
        $isMobile = hlp_check::checkRuleResult('Tel', $params['phone']);
        if (!$isEmail && !$isMobile) {
            T(21131002);
        }
        SmsServiceProvider::sendCode($params, $isEmail);
        return [];
    }

    /**
     * 向指定账户 手机/邮箱 发送验证码
     * @return array
     * @throws Exception
     */
    public function sendUserCode()
    {
        // 接受用户参数
        $deviceid = request('deviceid', 'request', 0, 'int');
        $username = request('user', 'request', '', 'string');
        $type = request('codeType', 'request', '');
        hlp_check::checkUsername($username);
        hlp_check::checkEmpty($type);
        hlp_check::checkEmpty($deviceid);

        // 先查询 本地用户 是否存在，不存在则查询 用户是否第三方用户，给出对应提示
        $userinfo = AuthServiceProvider::getUserByUserName('User', $username);
        if (empty($userinfo)) {
            $userinfo = AuthServiceProvider::getUserByUserName('', $username);
            if (empty($userinfo)) {
                T(21137021);
            } else {
                T(21137022);
            }
        }

        // 校验手机号和邮箱的正确性
        if ($type == 'email') {
            if ($userinfo['EMail'] == '') {
                T(21137024);
            }
            $ret = hlp_check::checkRuleResult('Email', $userinfo['EMail']);
            if (!$ret) {
                T(21137020);
            }
            $isEmail = true;
            $params['phone'] = $userinfo['EMail'];
            list($emailUsername, $emailDomain) = explode('@', $params['phone']);
            // 邮箱用户名大于1位才进行脱敏
            $emailUsername = strlen($emailUsername) > 1 ? substr($emailUsername, 0, 1) . '*******' . substr($emailUsername, -1) : $emailUsername;
            $codeMessage = $emailDomain ? $emailUsername . '@' . $emailDomain : $emailUsername;
        } else {
            if ($userinfo['Tel'] =='') {
                T(21137023);
            }
            $ret = hlp_check::checkRuleResult('Tel', $userinfo['Tel']);
            if (!$ret) {
                T(21137019);
            }
            $isEmail = false;
            $params['phone'] = $userinfo['Tel'];
            // 手机号大于7位才进行脱敏
            $codeMessage = strlen($params['phone']) > 7 ? substr($params['phone'], 0, 3) . '****' . substr($params['phone'], -4) : $params['phone'];
        }
        // 封装参数，发送验证码
        $params['type'] = 'user';
        $params['deviceid'] = $deviceid;
        $params['userid'] = $userinfo['ID'];
        $params['username'] = $userinfo['UserName'];
        SmsServiceProvider::sendCode($params, $isEmail);
        $this->errmsg = $isEmail ? L(21131015) : L(21131014);
        //返回脱敏性手机号/邮箱
        return ["code_message" => $codeMessage];
    }
}
