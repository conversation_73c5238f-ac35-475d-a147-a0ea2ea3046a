<?php

/**
 * Description: 用户
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: AuthUserModel.php 166879 2022-01-11 04:50:15Z duanyc $
 */

class AuthUserModel extends BaseModel
{
    /**
     * 统一认证的查询字段
     */
    public const COLUMN_AUTH = 'ID,IsDisabled,UserName,Password,RoleID,Type,LifeTime,InsertTime,Tel,TrueNames,IsBindDevice,' .
    'Email as EMail,DepartID,ID as UserID,GuestType,Remark,RelationConfig,UserBindDevNum,AuthTime,AuthNums,ZtpUser,PassUpdateTime,MustChange,AuthorizationGroup,openid,IsOpenSDCLinkage,LastWrongNums,LastWrongTimes';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TAuthUser';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'all' => '*',
        'one' => self::COLUMN_AUTH,
        'user' => self::COLUMN_AUTH,
        'auth' => self::COLUMN_AUTH,
        'qrcode' => self::COLUMN_AUTH,
        'base' => 'ID,UserName,RoleID',
        'open' => 'ID,RoleID,UserName,DepartID',
        'create' => 'CreateUser',
        'max' => 'max(ID) as MaxID',
        'userinfo' => 'Tel,Email',
        'update' => 'RoleID,UserName,TrueNames,DepartID',
        'ztpuser' => 'UserName,TrueNames,ZtpUser',
        'status' => 'ZtpUser',
        'privilege' => 'IsPrivilege, UserBindDevNum',
        'num' => 'ID,AuthNums',
        'sdclink' => 'IsOpenSDCLinkage',
        'otp' => 'UserName,CurrentSecretKey,ActivationSecretKey',
        'sdc'=>'UserName,Password,Password2,Type',
        '*' => '*',
    ];

    /**
     * 根据账号获取用户数据
     * 来宾账号   查TAuthUser
     * 非来宾账号 先查Account，再通过关联的userID查TAuthUser
     * 不支持使用用户名查用户信息
     * @param $Type
     * @param $UserName
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByUserName($Type, $UserName, $Column = 'one')
    {
        if (empty($UserName)) {
            return false;
        }
        if ($Type === AUTH_TYPE_GUEST) {
            return self::getOneUser($Type, $UserName, $Column);
        }
        if ($Type === AUTH_TYPE_MOBILE) {
            return self::getOneUserByTel($UserName, $Column);
        }
        $accountInfo = \AccountModel::getOneByUserName($Type, $UserName, 'all');
        if (empty($accountInfo)) {
            return false;
        }
        $userinfo = self::getOne($accountInfo['user_id'], $Column);
        if (empty($userinfo)) {
            return false;
        }
        return array_merge($userinfo, $accountInfo);
    }

    /**
     * 零信任用户信息
     * @param $userId
     * @return array|mixed
     */
    public static function getZtpUser($userId)
    {
        $user = ['ZtpUser' => '0'];
        if (empty($userId)) {
            return $user;
        }
        $cache = UserInfoRedis::getOne($userId, 'status');
        if (!empty($cache)) {
            return $cache;
        }
        return self::getOne($userId, 'status');
    }

    /**
     * 根据类型获取用户数据
     * @param $Type
     * @param $UserName
     * @param $Column
     * @return array
     */
    public static function getOneUser($Type, $UserName, $Column)
    {
        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Type = " . self::setData($Type) . " AND UserName = " . self::setData($UserName);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据手机号获取用户数据
     * @param $Tel
     * @param $Column
     * @return array
     */
    public static function getOneUserByTel($Tel, $Column)
    {
        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Tel = " . self::setData($Tel);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据ActivationSecretKey获取用户数据
     * @param $activationSecretKey
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByActivationSecretKey($activationSecretKey, $Column = 'one')
    {
        if (empty($activationSecretKey)) {
            return false;
        }
        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE ActivationSecretKey = " . self::setData($activationSecretKey);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据CurrentSecretKey获取用户数据
     * @param $currentSecretKey
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByCurrentSecretKey($currentSecretKey, $Column = 'one')
    {
        if (empty($currentSecretKey)) {
            return false;
        }
        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE CurrentSecretKey = " . self::setData($currentSecretKey);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据密码参数获取用户数据
     * 目前只有来宾用到
     * @param $Type
     * @param $Password
     * @param string $Column
     * @return array|bool
     */
    public static function getOneByPassword($Type, $Password, $Column = 'one')
    {
        if (empty($Password)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);

        if (!empty($Type)) {
            $where = "WHERE Type = " . self::setData($Type) . " AND Password = " . self::setData($Password);
        } else {
            $where = "WHERE Password = " . self::setData($Password);
        }

        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 获取设备绑定的用户
     *
     * @param $deviceId
     *
     * @return bool|mixed
     */
    public static function getBindUserList($deviceId)
    {
        if (empty($deviceId)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE A.AuthUserID = B.ID AND A.Type = 0 AND A.DeviceID = " . self::setData($deviceId);
        $sql = "SELECT B.ID, B.Type, B.UserName FROM TDeviceBindUser A, {$table['name']} B {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取用户绑定的设备
     *
     * @param $userID
     *
     * @return bool|mixed
     */
    public static function getBindDeviceList($userID)
    {
        if (empty($userID)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE A.AuthUserID = B.ID AND A.Type = 1 AND B.ID = " . self::setData($userID);
        $sql = "SELECT A.DeviceID FROM TDeviceBindUser A, {$table['name']} B {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 根据授权组ID获取用户列表
     *
     * @param $authorizationGroupID
     * @return bool|mixed
     */
    public static function getListByAuthorizationGroup($authorizationGroupID)
    {
        if (empty($authorizationGroupID)) {
            return [];
        }
        self::$data = [];
        $column = static::$columns['base'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE ZtpUser = 1 AND FIND_IN_SET(" . self::setData($authorizationGroupID) . ",AuthorizationGroup) > 0 ";
        $sql = "SELECT $column FROM {$table['name']} {$where}";
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 根据用户ID和设备ID获取对应的授权组ID
     *
     * @param $userId
     * @param  $deviceId
     * @return bool|mixed
     */
    public static function getAuthorizationGroup($userId, $deviceId=0)
    {
        if (empty($userId) && empty($deviceId)) {
            return "";
        }
        $userInfoMap = self::getOne($userId);
        $authorizationIDs = $userInfoMap['AuthorizationGroup'] ?? '';

        $deviceInfoMap = DeviceModel::getOne($deviceId);
        $dAuthorizationIDs = $deviceInfoMap['AuthorizationGroup'] ?? '';
        if (!empty($dAuthorizationIDs)) {
            $authorizationIDs = !empty($authorizationIDs) ? $authorizationIDs . ',' . $dAuthorizationIDs : $dAuthorizationIDs;
        }
        return $authorizationIDs;
    }


    /**
     * 插入用户
     * 同时需要插入账号
     * @param array $key_values
     * @return int
     * @throws Exception
     */
    public static function insert($key_values = [])
    {
        if (empty($key_values) || empty($key_values['Type'])) {
            return false;
        }
        self::ztpUser($key_values);
        //认证账号关联的认证源服务器ID
        $serverID = isset($key_values['ServerID']) ? $key_values['ServerID'] : 0;
        unset($key_values['ServerID']);
        $key_values['RelationConfig'] = '{"' . $key_values['Type'] . '":1}';
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $dasmIp = get_pub_dasm_ip();

        if (!empty($dasmIp)) {
            try {
                $result = lib_yar::clients('duser', 'getUserId', [], $dasmIp);
                if (!empty($result['state'])) {
                    $key_values['ID'] = $result['data']['userId'];
                    if ($key_values['Type'] == AUTH_TYPE_GUEST && empty($key_values['UserName'])) {
                        $key_values['UserName'] = 'Guest_' . $key_values['ID'];
                    }
                    single_table_pub(static::TABLE_NAME, $key_values, 'insert');
                }
            } catch (Exception $e) {
                cutil_php_log(json_encode(['model_insert', $e->getMessage()]), "model_{$table['name']}");
            }
        }

        $result = lib_database::duplicate($key_values, $table['name'], $table['index']);
        cutil_php_log(json_encode(['model_insert', $table, $key_values]), "model_{$table['name']}");

        if ($result) {
            $userId = lib_database::insertId();
            if (!$userId) {
                // 金仓数据库特殊处理
                $userId = $key_values['ID']??0;
            }
            if ($key_values['Type'] !== AUTH_TYPE_GUEST) {
                $aparams = ['user_id' => $userId, 'auth_type' => $key_values['Type'], 'auth_name' => $key_values['UserName'], 'ServerID' => $serverID];
                if (isset($key_values['Password'])) {
                    $aparams['auth_password'] = $key_values['Password'];
                }
                if (isset($key_values['Password2'])) {
                    $aparams['auth_credential'] = $key_values['Password2'];
                }
                if (isset($key_values['openid'])) {
                    $aparams['open_id'] = $key_values['openid'];
                }
                if (isset($key_values['unionid'])) {
                    $aparams['union_id'] = $key_values['unionid'];
                }
                \AccountModel::insert($aparams);

                $cmd = 'php ' . PATH_ADMIN . '/artisan search:add-index user ' . $userId;
                cutil_exec_no_wait($cmd); //插入非来宾帐号需要立即建索引

                unset($aparams);
            } else {
                $params = ['UserID' => $userId, 'AllowRegionIDs' => '-1'];
                \GuestRelationModel::insert($params);
                unset($params);
            }
            //插入用户之后通知MSEP
            NoticeServiceProvider::sendSyncStatus($userId,'user');
            return $userId;
        }

        return $result;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['Type'])) {
            $where .= "AND Type = " . self::setData($cond['Type']);
        }

        if (isset($cond['ZtpUser'])) {
            $where .= " AND ZtpUser = " . self::setData($cond['ZtpUser']);
        }

        if (isset($cond['Tel'])) {
            $where .= " AND Tel = " . self::setData($cond['Tel']);
        }

        if (isset($cond['Email'])) {
            $where .= "AND Email = " . self::setData($cond['Email']);
        }

        if (isset($cond['IsDisabled'])) {
            $where .= "AND IsDisabled = " . self::setData($cond['IsDisabled']);
        }

        if (isset($cond['TypeNG'])) {
            $where .= "AND Type <> " . self::setData($cond['TypeNG']);
        }

        if (isset($cond['TrueNames'])) {
            $where .= " AND TrueNames = " . self::setData($cond['TrueNames']);
        }

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = " . self::setData($cond['UserName']);
        }
        if (isset($cond['Password'])) {
            $where .= "AND Password = " . self::setData($cond['Password']);
        }
        if (isset($cond['ID'])) {
            $where .= "AND ID = " . self::setData($cond['ID']);
        }

        if (isset($cond['DepartIds'])) {
            $where .= "AND DepartID IN (" . self::setArrayData($cond['DepartIds']) . ")";
        }

        if (isset($cond['RoleIds'])) {
            $where .= "AND RoleID IN (" . self::setArrayData($cond['RoleIds']) . ")";
        }

        if (isset($cond['UserIds'])) {
            $where .= "AND ID IN (" . self::setArrayData($cond['UserIds']) . ")";
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * @throws Exception
     */
    public static function ztpUser(&$key_values): void
    {
        if (isset($key_values['DepartID']) && getmoduleregist(11)) { //有授权
            $authNumber = getmoduleregist('11 1');
            $ztpDepart = DepartModel::getCount(['ZtpDepart' => 1, 'DepartID' => $key_values['DepartID']]);
            $ztpUser = self::getCount(['ZtpUser' => 1]);
            if ($ztpDepart && $authNumber > $ztpUser) {
                $key_values['ZtpUser'] = 1;
            }
        }
    }

    /**
     * 修改
     * @param string $ID
     * @param array $key_values
     * @param array $origin_values
     * @return bool|void
     * @throws Exception
     */
    public static function update($ID, $key_values = [], $origin_values = [])
    {
        self::ztpUser($key_values);
        parent::update($ID, $key_values, $origin_values);
    }
}
