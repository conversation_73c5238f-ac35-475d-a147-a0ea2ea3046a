parameters:
	ignoreErrors:
		-
			message: "#^Parameter \\#1 \\$item_value of static method hlp_common\\:\\:getSplitTable\\(\\) expects int, null given\\.$#"
			count: 2
			path: bin/install.php

		-
			message: "#^Variable \\$checkItems in empty\\(\\) always exists and is not falsy\\.$#"
			count: 2
			path: bin/install.php

		-
			message: "#^Variable \\$params might not be defined\\.$#"
			count: 1
			path: control/AuthController.php

		-
			message: "#^If condition is always true\\.$#"
			count: 1
			path: control/DepartController.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: control/DepartController.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: control/DepartController.php

		-
			message: "#^Cannot access property \\$deviceId on bool\\|object\\.$#"
			count: 2
			path: control/DeviceController.php

		-
			message: "#^Cannot access property \\$deviceOperationService on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot access property \\$params on bool\\|object\\.$#"
			count: 14
			path: control/DeviceController.php

		-
			message: "#^Cannot call method checkRepeatDevice\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method getDeviceSubTypeId\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method getDeviceTypeId\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method hasIpMacBindIllegal\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method hasfingerIllegal\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method isDeviceExist\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method returnDeviceInfo\\(\\) on bool\\|object\\.$#"
			count: 2
			path: control/DeviceController.php

		-
			message: "#^Cannot call method setDeviceInfo\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method setPortalServer\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method updateDeviceComputerNameAndOsName\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method updateDeviceOnline\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Cannot call method writeLog\\(\\) on bool\\|object\\.$#"
			count: 5
			path: control/DeviceController.php

		-
			message: "#^Offset 'PClient' does not exist on array\\{phoneinfo\\: array, deviceType\\: mixed, deviceSubType\\: mixed\\}\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Offset 'basIP' on array\\{phoneinfo\\: array, deviceType\\: mixed, deviceSubType\\: mixed\\} on left side of \\?\\? does not exist\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Offset 'dev_xml' does not exist on array\\{phoneinfo\\: array\\}\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Offset 'deviceid' does not exist on array\\{phoneinfo\\: array, deviceType\\: mixed, deviceSubType\\: mixed\\}\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Parameter \\#1 \\$mac of method Services\\\\Device\\\\Services\\\\DeviceStatusService\\:\\:getByMac\\(\\) expects string, bool given\\.$#"
			count: 1
			path: control/DeviceController.php

		-
			message: "#^Offset 'guestdeviceId' does not exist on array\\{userName\\: mixed, newMobile\\: mixed, roleId\\: mixed, guestMacOrDevId\\: mixed, deviceId\\: mixed, host\\: mixed, getType\\: mixed, guestId\\: mixed, \\.\\.\\.\\}\\.$#"
			count: 2
			path: control/GuestController.php

		-
			message: "#^Result of && is always true\\.$#"
			count: 1
			path: control/NetController.php

		-
			message: "#^Variable \\$deviceIds in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: control/NetController.php

		-
			message: "#^Cannot call method deviceRepairPatch\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/PatchController.php

		-
			message: "#^Cannot call method getBelongTimes\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/PatchController.php

		-
			message: "#^Cannot call method getPatchServerInfo\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/PatchController.php

		-
			message: "#^Cannot call method getPathDetail\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/PatchController.php

		-
			message: "#^Cannot call method insertInstallLog\\(\\) on bool\\|object\\.$#"
			count: 1
			path: control/PatchController.php

		-
			message: "#^Variable \\$params might not be defined\\.$#"
			count: 1
			path: control/ResourceController.php

		-
			message: "#^Parameter \\#1 \\$key of function L expects string, int\\<min, 99\\>\\|int\\<101, max\\> given\\.$#"
			count: 1
			path: control/ScanController.php

		-
			message: "#^Property ZtpController\\:\\:\\$deviceId \\(array\\) does not accept default value of type string\\.$#"
			count: 1
			path: control/ZtpController.php

		-
			message: "#^Property ZtpController\\:\\:\\$userId \\(array\\) does not accept default value of type string\\.$#"
			count: 1
			path: control/ZtpController.php

		-
			message: "#^PHPDoc tag @return with type mixed is not subtype of native type array\\.$#"
			count: 1
			path: control/ZtpGatewayController.php

		-
			message: "#^Variable \\$Resource might not be defined\\.$#"
			count: 1
			path: control/ZtpPolicyController.php

		-
			message: "#^Parameter \\#1 \\$get_as_float of function microtime expects bool, string given\\.$#"
			count: 1
			path: helper/hlp_common.php

		-
			message: "#^Parameter \\#2 \\$httpType of function get_cururl expects bool, string given\\.$#"
			count: 1
			path: helper/hlp_common.php

		-
			message: "#^Variable \\$arr in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: helper/hlp_common.php

		-
			message: "#^Call to method verifyCsrf\\(\\) on an unknown class csrfControll\\.$#"
			count: 1
			path: helper/hlp_compatible.php

		-
			message: "#^Call to method write_error_log\\(\\) on an unknown class csrfControll\\.$#"
			count: 1
			path: helper/hlp_compatible.php

		-
			message: "#^Instantiated class csrfControll not found\\.$#"
			count: 1
			path: helper/hlp_compatible.php

		-
			message: "#^Comparison operation \"\\<\\=\" between numeric\\-string and \\(array\\<int, string\\>\\|string\\) results in an error\\.$#"
			count: 1
			path: helper/hlp_match.php

		-
			message: "#^Comparison operation \"\\>\\=\" between numeric\\-string and \\(array\\<int, string\\>\\|string\\) results in an error\\.$#"
			count: 1
			path: helper/hlp_match.php

		-
			message: "#^Property XdbSearcher\\:\\:\\$header is never read, only written\\.$#"
			count: 1
			path: library/Ip2region/XdbSearcher.php

		-
			message: "#^Strict comparison using \\=\\=\\= between array\\|string and null will always evaluate to false\\.$#"
			count: 1
			path: library/function/cache.func.php

		-
			message: "#^Call to function is_string\\(\\) with bool will always evaluate to false\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Default value of the parameter \\#1 \\$der \\(false\\) of function extractSignature\\(\\) is incompatible with type string\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Function getSignatureAlgorithmOid\\(\\) should return string but returns false\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Function getSignatureHash\\(\\) should return string but returns false\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Function pemToDer\\(\\) should return bool but returns string\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$caCert$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$cert$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Parameter \\#1 \\$certPem of function RSACertSinger expects null, mixed given\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Parameter \\#1 \\$certPem of function SMCertSinger expects null, mixed given\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Parameter \\#2 \\$caCertPem of function RSACertSinger expects null, mixed given\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Parameter \\#2 \\$caCertPem of function SMCertSinger expects null, mixed given\\.$#"
			count: 1
			path: library/function/cer.func.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 3
			path: library/function/cer.func.php

		-
			message: "#^Function dataEncrypt\\(\\) should return string but returns false\\.$#"
			count: 1
			path: library/function/crypt.func.php

		-
			message: "#^Else branch is unreachable because ternary operator condition is always true\\.$#"
			count: 9
			path: library/function/device.func.php

		-
			message: "#^Strict comparison using \\!\\=\\= between false and false will always evaluate to false\\.$#"
			count: 3
			path: library/function/device.func.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 1
			path: library/function/exec.func.php

		-
			message: "#^Function writeFile\\(\\) never returns bool so it can be removed from the return type\\.$#"
			count: 1
			path: library/function/ini.func.php

		-
			message: "#^Function writeFile\\(\\) never returns int so it can be removed from the return type\\.$#"
			count: 1
			path: library/function/ini.func.php

		-
			message: "#^Parameter \\#1 \\$number of function round expects float, string given\\.$#"
			count: 1
			path: library/function/log.func.php

		-
			message: "#^Binary operation \"\\-\" between int\\<1, max\\> and string results in an error\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function IsStrHaveMac\\(\\) should return bool but returns string\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function PortNameIsSame\\(\\) should return true but returns false\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function eregi_replace not found\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function get_client_ip\\(\\) never returns null so it can be removed from the return type\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function group_port_name\\(\\) should return array but returns string\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function ipToNumber\\(\\) never returns bool so it can be removed from the return type\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function isHavIP\\(\\) should return 1 but returns 0\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Function isHavIP\\(\\) should return 1 but returns string\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Parameter \\#2 \\$right_operand of function bcmul expects string, int given\\.$#"
			count: 1
			path: library/function/net.func.php

		-
			message: "#^Default value of the parameter \\#6 \\$port \\(int\\) of function AsmDbAgent\\(\\) is incompatible with type string\\.$#"
			count: 1
			path: library/function/socket.func.php

		-
			message: "#^Function client_exchange_tcp\\(\\) should return string but returns false\\.$#"
			count: 3
			path: library/function/socket.func.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 7
			path: library/function/socket.func.php

		-
			message: "#^Parameter \\#2 \\$port of function UdpSend expects string, int given\\.$#"
			count: 1
			path: library/function/socket.func.php

		-
			message: "#^Parameter \\#2 \\$port of function UdpSendAndRecv expects string, int given\\.$#"
			count: 1
			path: library/function/socket.func.php

		-
			message: "#^Parameter \\#4 \\$timeOutSec of function UdpSendAndRecv expects string, int given\\.$#"
			count: 1
			path: library/function/socket.func.php

		-
			message: "#^Function GetNormalDownStr\\(\\) should return Exception but returns string\\.$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Function GetSubStr\\(\\) should return 'bb' but returns ''\\.$#"
			count: 2
			path: library/function/string.func.php

		-
			message: "#^Function GetSubStr\\(\\) should return 'bb' but returns \\(string\\|false\\)\\.$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Parameter \\#2 \\$callback of function preg_replace_callback expects callable\\(array\\<int\\|string, string\\>\\)\\: string, 'parse_source_code' given\\.$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: applet$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: base$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: bgsound$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: blink$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: body$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: embed$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: expression$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: form$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: frame$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: frameset$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: fscommand$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: head$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: iframe$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ilayer$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: input$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: javascript$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: layer$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: link$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: meta$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: object$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onabort$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onactivate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onafterprint$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onafterupdate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforeactivate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforecopy$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforecut$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforedeactivate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforeeditfocus$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforepaste$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforeprint$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforeunload$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbeforeupdate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbegin$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onblur$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onbounce$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: oncellchange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onchange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onclick$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: oncontextmenu$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: oncontrolselect$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: oncopy$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: oncut$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondataavailable$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondatasetchanged$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondatasetcomplete$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondblclick$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondeactivate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondrag$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondragdrop$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondragend$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondragenter$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondragleave$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondragover$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondragstart$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ondrop$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onend$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onerror$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onerrorupdate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onfilterchange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onfinish$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onfocus$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onfocusin$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onfocusout$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onhashchange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onhelp$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: oninput$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onkeydown$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onkeypress$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onkeyup$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onlayoutcomplete$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onload$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onlosecapture$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmediacomplete$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmediaerror$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmessage$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmousedown$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmouseenter$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmouseleave$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmousemove$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmouseout$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmouseover$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmouseup$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmousewheel$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmove$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmoveend$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onmovestart$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onoffline$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onoutofsync$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onpaste$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onpause$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onpopstate$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onprogress$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onpropertychange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onreadystatechange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onredo$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrepeat$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onreset$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onresize$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onresizeend$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onresizestart$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onresume$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onreverse$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowdelete$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowenter$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowexit$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowinserted$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowsdelete$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowsenter$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onrowsinserted$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onscroll$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onseek$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onselect$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onselectionchange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onselectstart$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onstart$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onstop$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onstorage$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onsubmit$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onsuspend$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onsyncrestored$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ontimeerror$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ontouchstart$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: ontrackchange$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onundo$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onunload$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: onurlflip$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: plaintext$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: script$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: seeksegmenttime$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: style$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: svg$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: title$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: vbscript$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: video$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: vmlframe$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Regex pattern is invalid\\: Delimiter must not be alphanumeric or backslash in pattern\\: xml$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Variable \\$f_str_1 in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: library/function/string.func.php

		-
			message: "#^Function func_time_getTime\\(\\) should return string but returns int\\.$#"
			count: 1
			path: library/function/time.func.php

		-
			message: "#^Cannot call method closeCursor\\(\\) on int\\<min, \\-1\\>\\|int\\<1, max\\>\\|object\\|true\\.$#"
			count: 2
			path: library/lib_database.php

		-
			message: "#^Cannot call method fetch\\(\\) on int\\<min, \\-1\\>\\|int\\<1, max\\>\\|object\\|true\\.$#"
			count: 1
			path: library/lib_database.php

		-
			message: "#^Cannot call method fetchAll\\(\\) on int\\<min, \\-1\\>\\|int\\<1, max\\>\\|object\\|true\\.$#"
			count: 1
			path: library/lib_database.php

		-
			message: "#^Method lib_database\\:\\:getOne\\(\\) should return array but returns null\\.$#"
			count: 1
			path: library/lib_database.php

		-
			message: "#^Method lib_database\\:\\:insertId\\(\\) should return int but returns string\\|false\\.$#"
			count: 1
			path: library/lib_database.php

		-
			message: "#^Static property lib_database\\:\\:\\$current_pdo \\(PDO\\) does not accept null\\.$#"
			count: 2
			path: library/lib_database.php

		-
			message: "#^Variable \\$dbconn in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: library/lib_database.php

		-
			message: "#^Parameter \\#1 \\$str of function strtolower expects string, true given\\.$#"
			count: 1
			path: library/lib_function.php

		-
			message: "#^Dead catch \\- Exception is already caught above\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^Method lib_mosquitto\\:\\:init\\(\\) should return Mosquitto\\\\Client but returns null\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^Method lib_mosquitto\\:\\:publish\\(\\) should return null but returns int\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^Parameter \\#1 \\$id of class Mosquitto\\\\Client constructor expects string\\|null, int given\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^Static property lib_mosquitto\\:\\:\\$mqtt \\(Mosquitto\\\\Client\\) in empty\\(\\) is not falsy\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^While loop condition is always true\\.$#"
			count: 1
			path: library/lib_mosquitto.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 2
			path: library/lib_phpmail.php

		-
			message: "#^Method lib_qrcode\\:\\:png\\(\\) should return bool\\|string but empty return statement found\\.$#"
			count: 1
			path: library/lib_qrcode.php

		-
			message: "#^Parameter \\#2 \\$handle of function pcntl_signal expects \\(callable\\(\\)\\: mixed\\)\\|int, array\\{'self', 'sigHandler'\\} given\\.$#"
			count: 5
			path: library/lib_queue.php

		-
			message: "#^Variable \\$input in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: library/lib_queue.php

		-
			message: "#^If condition is always true\\.$#"
			count: 37
			path: library/lib_redis.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:del\\(\\) should return int but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:exists\\(\\) should return bool but returns int\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:getHash\\(\\) should return array\\|string but returns null\\.$#"
			count: 2
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:hMSetEx\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:hgetAll\\(\\) should return null but returns array\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:init\\(\\) should return Redis but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:keys\\(\\) should return float\\|null but returns array\\<int, string\\>\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:lget\\(\\) should return array\\|bool\\|string but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:mDel\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:rpush\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_redis\\:\\:set\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_redis.php

		-
			message: "#^Method lib_yar\\:\\:client\\(\\) should return array\\|bool but returns null\\.$#"
			count: 3
			path: library/lib_yar.php

		-
			message: "#^Method lib_yar\\:\\:concurrentClient\\(\\) should return bool but returns int\\.$#"
			count: 1
			path: library/lib_yar.php

		-
			message: "#^Method lib_yar\\:\\:concurrentClient\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_yar.php

		-
			message: "#^Method lib_yar\\:\\:concurrentLoop\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_yar.php

		-
			message: "#^Method lib_yar\\:\\:server\\(\\) should return bool but returns null\\.$#"
			count: 1
			path: library/lib_yar.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$rpc_call_back$#"
			count: 1
			path: library/lib_yar.php

		-
			message: "#^Variable \\$rpc_url might not be defined\\.$#"
			count: 3
			path: library/lib_yar.php

		-
			message: "#^Method cls_google_authenticator\\:\\:setCodeLength\\(\\) has invalid return type PHPGangsta_GoogleAuthenticator\\.$#"
			count: 1
			path: library/object/cls_google_authenticator.php

		-
			message: "#^Method cls_google_authenticator\\:\\:setCodeLength\\(\\) should return PHPGangsta_GoogleAuthenticator but returns \\$this\\(cls_google_authenticator\\)\\.$#"
			count: 1
			path: library/object/cls_google_authenticator.php

		-
			message: "#^Parameter \\#1 \\$input of function str_pad expects string, int\\<0, max\\> given\\.$#"
			count: 1
			path: library/object/cls_google_authenticator.php

		-
			message: "#^Parameter \\#5 \\$y of function imagettftext expects int, float given\\.$#"
			count: 1
			path: library/object/cls_validate_code.php

		-
			message: "#^Variable \\$charset might not be defined\\.$#"
			count: 1
			path: library/object/cls_validate_code.php

		-
			message: "#^Variable \\$f_str_1 in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: library/object/cls_xml.php

		-
			message: "#^Call to function is_object\\(\\) with string will always evaluate to false\\.$#"
			count: 1
			path: library/otheruser/dingtalk.class.php

		-
			message: "#^Property dingtalk\\:\\:\\$dingtalkDepartment \\(dingMessage\\) does not accept dingDepartment\\.$#"
			count: 1
			path: library/otheruser/dingtalk.class.php

		-
			message: "#^Property dingtalk\\:\\:\\$http \\(weHttp\\) does not accept Http\\.$#"
			count: 1
			path: library/otheruser/dingtalk.class.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: library/otheruser/dingtalk.class.php

		-
			message: "#^Method Http\\:\\:get\\(\\) should return string but returns false\\.$#"
			count: 2
			path: library/otheruser/dingtalk/http.php

		-
			message: "#^Method Http\\:\\:post\\(\\) should return string but returns false\\.$#"
			count: 2
			path: library/otheruser/dingtalk/http.php

		-
			message: "#^Method dingUser\\:\\:getUserInfo\\(\\) invoked with 1 parameter, 2 required\\.$#"
			count: 1
			path: library/otheruser/feishu.class.php

		-
			message: "#^Method weworkDepartment\\:\\:listDept\\(\\) invoked with 4 parameters, 2 required\\.$#"
			count: 1
			path: library/otheruser/feishu.class.php

		-
			message: "#^Property feishu\\:\\:\\$fsDepartment \\(weworkDepartment\\) does not accept fsDepartment\\.$#"
			count: 1
			path: library/otheruser/feishu.class.php

		-
			message: "#^Property feishu\\:\\:\\$fsMessage \\(dingMessage\\) does not accept fsMessage\\.$#"
			count: 1
			path: library/otheruser/feishu.class.php

		-
			message: "#^Property feishu\\:\\:\\$fsUser \\(dingUser\\) does not accept fsUser\\.$#"
			count: 1
			path: library/otheruser/feishu.class.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: library/otheruser/feishu.class.php

		-
			message: "#^Class fsHttp referenced with incorrect case\\: fshttp\\.$#"
			count: 2
			path: library/otheruser/feishu/fsDepartment.php

		-
			message: "#^Method fsHttp\\:\\:get\\(\\) should return string but returns false\\.$#"
			count: 2
			path: library/otheruser/feishu/fsHttp.php

		-
			message: "#^Method fsHttp\\:\\:post\\(\\) should return string but returns false\\.$#"
			count: 2
			path: library/otheruser/feishu/fsHttp.php

		-
			message: "#^Class fsHttp referenced with incorrect case\\: fshttp\\.$#"
			count: 2
			path: library/otheruser/feishu/fsMessage.php

		-
			message: "#^Offset 'UserName' on non\\-empty\\-array\\<int\\|string, mixed\\> in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: library/otheruser/sso/Services/oauthClientService.php

		-
			message: "#^Parameter \\#2 \\$field of static method ssoCommon\\:\\:cacheGet\\(\\) expects null, string given\\.$#"
			count: 3
			path: library/otheruser/sso/Services/oauthClientService.php

		-
			message: "#^Method Firebase\\\\JWT\\\\JWT\\:\\:sign\\(\\) should return string but return statement is missing\\.$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^PHPDoc tag @throws with type Firebase\\\\JWT\\\\BeforeValidException\\|Firebase\\\\JWT\\\\ExpiredException\\|Firebase\\\\JWT\\\\SignatureInvalidException\\|InvalidArgumentException\\|UnexpectedValueException is not subtype of Throwable$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Parameter \\#1 \\$signature of function sodium_crypto_sign_verify_detached expects non\\-empty\\-string, string given\\.$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Parameter \\#2 \\$secretkey of function sodium_crypto_sign_detached expects non\\-empty\\-string, string given\\.$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Parameter \\#3 \\$publickey of function sodium_crypto_sign_verify_detached expects non\\-empty\\-string, string given\\.$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Strict comparison using \\=\\=\\= between false and string will always evaluate to false\\.$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Strict comparison using \\=\\=\\= between null and object will always evaluate to false\\.$#"
			count: 2
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Unsafe call to private method Firebase\\\\JWT\\\\JWT\\:\\:handleJsonError\\(\\) through static\\:\\:\\.$#"
			count: 2
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Unsafe call to private method Firebase\\\\JWT\\\\JWT\\:\\:safeStrlen\\(\\) through static\\:\\:\\.$#"
			count: 4
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^Unsafe call to private method Firebase\\\\JWT\\\\JWT\\:\\:verify\\(\\) through static\\:\\:\\.$#"
			count: 1
			path: library/otheruser/sso/common/JWT.php

		-
			message: "#^PHPDoc tag @param for parameter \\$isHttps with type bool\\|null is not subtype of native type bool\\.$#"
			count: 1
			path: library/otheruser/sso/common/ssoCommon.php

		-
			message: "#^Parameter \\#2 \\$field of static method ssoCommon\\:\\:cacheGet\\(\\) expects null, string given\\.$#"
			count: 1
			path: library/otheruser/sso/common/ssoCommon.php

		-
			message: "#^Access to an undefined property object\\:\\:\\$user_ticket\\.$#"
			count: 1
			path: library/otheruser/wework.class.php

		-
			message: "#^Method weHttp\\:\\:get\\(\\) should return string but returns false\\.$#"
			count: 2
			path: library/otheruser/wework/wehttp.php

		-
			message: "#^Method weHttp\\:\\:post\\(\\) should return string but returns false\\.$#"
			count: 2
			path: library/otheruser/wework/wehttp.php

		-
			message: "#^Call to static method Conversion\\(\\) on an unknown class ArrayIconvJson\\.$#"
			count: 1
			path: library/otheruser/wework/weworkMessage.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 30
			path: library/xml/xml_dev_info.php

		-
			message: "#^Comparison operation \"\\>\" between int\\<1, max\\> and 0 is always true\\.$#"
			count: 1
			path: service/AgentServiceProvider.php

		-
			message: "#^Parameter \\#2 \\$value of function setcookie expects string, null given\\.$#"
			count: 1
			path: service/AgentServiceProvider.php

		-
			message: "#^Result of \\|\\| is always true\\.$#"
			count: 1
			path: service/AgentServiceProvider.php

		-
			message: "#^Offset mixed does not exist on array\\{\\}\\.$#"
			count: 2
			path: service/AppServiceProvider.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\AdAutoAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/AdAutoAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\AdDomainAuthService\\:\\:auth\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:auth\\(\\)$#"
			count: 1
			path: service/Auth/Services/AdDomainAuthService.php

		-
			message: "#^Comparison operation \"\\<\\=\" between int\\<1, max\\> and 0 is always false\\.$#"
			count: 1
			path: service/Auth/Services/BaseAuthService.php

		-
			message: "#^Comparison operation \"\\>\" between int\\<1, max\\> and 0 is always true\\.$#"
			count: 1
			path: service/Auth/Services/BaseAuthService.php

		-
			message: "#^Parameter \\#1 \\$string of function strlen expects string, int given\\.$#"
			count: 1
			path: service/Auth/Services/BaseAuthService.php

		-
			message: "#^Parameter \\#2 \\$timestamp of function date expects int, float given\\.$#"
			count: 1
			path: service/Auth/Services/BaseAuthService.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: service/Auth/Services/BaseAuthService.php

		-
			message: "#^Variable \\$lastLogid might not be defined\\.$#"
			count: 2
			path: service/Auth/Services/BaseAuthService.php

		-
			message: "#^Method Services\\\\Auth\\\\Services\\\\DingTalkAuthService\\:\\:addDepart\\(\\) should return string but returns int\\.$#"
			count: 1
			path: service/Auth/Services/DingTalkAuthService.php

		-
			message: "#^Parameter \\#2 \\$data of function hash_hmac expects string, float given\\.$#"
			count: 1
			path: service/Auth/Services/DingTalkAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\DingTalkAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/DingTalkAuthService.php

		-
			message: "#^Method Services\\\\Auth\\\\Services\\\\FeiShuAuthService\\:\\:addDepart\\(\\) should return string but returns int\\.$#"
			count: 1
			path: service/Auth/Services/FeiShuAuthService.php

		-
			message: "#^Offset 'user' does not exist on string\\.$#"
			count: 1
			path: service/Auth/Services/FeiShuAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\FeiShuAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/FeiShuAuthService.php

		-
			message: "#^Call to method WServiceVerify\\(\\) on an unknown class Services\\\\Auth\\\\Services\\\\SoapClient\\.$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^Instantiated class Services\\\\Auth\\\\Services\\\\SoapClient not found\\.$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$flag$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$headerArr$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$ip$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$isProxy$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$is_header$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$is_redirect$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$port$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^PHPDoc tag @throws has invalid value \\(/Exception\\)\\: Unexpected token \"/Exception\", expected type at offset 69$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\FingerAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^Variable \\$res might not be defined\\.$#"
			count: 1
			path: service/Auth/Services/FingerAuthService.php

		-
			message: "#^Parameter \\#2 \\$timestamp of function date expects int, float given\\.$#"
			count: 1
			path: service/Auth/Services/GuestAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\GuestAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/GuestAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\MobileAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/MobileAuthService.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 1
			path: service/Auth/Services/NoAuthService.php

		-
			message: "#^PHPDoc tag @throws with type Services\\\\Auth\\\\Services\\\\Exception is not subtype of Throwable$#"
			count: 2
			path: service/Auth/Services/OTPAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\OTPAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/OTPAuthService.php

		-
			message: "#^Return type \\(array\\<bool\\>\\) of method Services\\\\Auth\\\\Services\\\\OTPAuthService\\:\\:auth\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:auth\\(\\)$#"
			count: 1
			path: service/Auth/Services/OTPAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\QrCodeAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/QrCodeAuthService.php

		-
			message: "#^Parameter \\#3 \\$addr of function radius_put_addr expects string, float\\|int given\\.$#"
			count: 1
			path: service/Auth/Services/RadiusAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\RadiusAuthService\\:\\:auth\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:auth\\(\\)$#"
			count: 1
			path: service/Auth/Services/RadiusAuthService.php

		-
			message: "#^Parameter \\#4 \\$rpc_ip of static method lib_yar\\:\\:clients\\(\\) expects string, true given\\.$#"
			count: 1
			path: service/Auth/Services/SelfGuestAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\SmsAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/SmsAuthService.php

		-
			message: "#^Parameter \\#2 \\$field of static method ssoCommon\\:\\:cacheGet\\(\\) expects null, string given\\.$#"
			count: 1
			path: service/Auth/Services/SsoAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\SsoAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/SsoAuthService.php

		-
			message: "#^Return type \\(array\\<bool\\>\\) of method Services\\\\Auth\\\\Services\\\\SsoAuthService\\:\\:auth\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:auth\\(\\)$#"
			count: 1
			path: service/Auth/Services/SsoAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\UKeyAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/UKeyAuthService.php

		-
			message: "#^Result of && is always true\\.$#"
			count: 1
			path: service/Auth/Services/UserAuthService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Auth\\\\Services\\\\UserAuthService\\:\\:parseParams\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\AuthServiceInterface\\:\\:parseParams\\(\\)$#"
			count: 1
			path: service/Auth/Services/UserAuthService.php

		-
			message: "#^Variable \\$errPasswordArr in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: service/Auth/Services/UserAuthService.php

		-
			message: "#^Cannot access offset 'id' on true\\.$#"
			count: 2
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Cannot access offset 'name' on true\\.$#"
			count: 2
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Cannot access offset 'parentid' on true\\.$#"
			count: 1
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Comparison operation \"\\<\" between 0\\|1 and 2 is always true\\.$#"
			count: 1
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Method Services\\\\Auth\\\\Services\\\\WeWorkAuthService\\:\\:addDepart\\(\\) should return string but returns int\\.$#"
			count: 1
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Offset 'id' does not exist on array\\{parentid\\: int\\<min, 75336\\>\\|int\\<75338, max\\>, order\\: int\\}\\.$#"
			count: 1
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Offset 'order' does not exist on array\\{parentid\\: int\\}\\.$#"
			count: 1
			path: service/Auth/Services/WeWorkAuthService.php

		-
			message: "#^Method Services\\\\Auth\\\\Services\\\\WebAuthService\\:\\:getUrl\\(\\) never returns bool so it can be removed from the return type\\.$#"
			count: 1
			path: service/Auth/Services/WebAuthService.php

		-
			message: "#^Parameter \\#3 \\$postdata of function curl expects string, array given\\.$#"
			count: 1
			path: service/Auth/Services/WebAuthService.php

		-
			message: "#^Parameter \\#2 \\$Type of method Services\\\\Device\\\\Services\\\\DeviceSceneService\\:\\:getSceneInfoById\\(\\) expects null, int given\\.$#"
			count: 1
			path: service/AuthServiceProvider.php

		-
			message: "#^If condition is always false\\.$#"
			count: 2
			path: service/Common/AdLdap/AdLdap.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 5
			path: service/Common/AdLdap/AdLdap.php

		-
			message: "#^Parameter \\#2 \\$context of static method Services\\\\Common\\\\AdLdap\\\\AdLdapError\\:\\:debugLog\\(\\) expects null, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: service/Common/AdLdap/AdLdap.php

		-
			message: "#^Parameter \\#3 \\$context of class Services\\\\Common\\\\AdLdap\\\\AdLdapException constructor expects string\\|null, array\\<string, int\\|string\\> given\\.$#"
			count: 2
			path: service/Common/AdLdap/AdLdap.php

		-
			message: "#^Parameter \\#3 \\$context of class Services\\\\Common\\\\AdLdap\\\\AdLdapException constructor expects string\\|null, array\\<string, mixed\\> given\\.$#"
			count: 2
			path: service/Common/AdLdap/AdLdap.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 5
			path: service/Common/AdLdap/AdLdap.php

		-
			message: "#^Unsafe access to private property Services\\\\Common\\\\AdLdap\\\\AdLdapConfig\\:\\:\\$allAdDomains through static\\:\\:\\.$#"
			count: 3
			path: service/Common/AdLdap/AdLdapConfig.php

		-
			message: "#^Unsafe access to private property Services\\\\Common\\\\AdLdap\\\\AdLdapConfig\\:\\:\\$allLdaps through static\\:\\:\\.$#"
			count: 3
			path: service/Common/AdLdap/AdLdapConfig.php

		-
			message: "#^Variable \\$default might not be defined\\.$#"
			count: 1
			path: service/Common/AdLdap/AdLdapConfig.php

		-
			message: "#^Static property Services\\\\Common\\\\AdLdap\\\\AdLdapError\\:\\:\\$curCode \\(int\\) does not accept string\\.$#"
			count: 1
			path: service/Common/AdLdap/AdLdapError.php

		-
			message: "#^Parameter \\#1 \\$key of function L expects string, int\\<min, \\-1\\>\\|int\\<1, max\\> given\\.$#"
			count: 1
			path: service/Common/AdLdap/AdLdapException.php

		-
			message: "#^If condition is always false\\.$#"
			count: 15
			path: service/Common/AdLdap/LdapBase.php

		-
			message: "#^Method Services\\\\Common\\\\AdLdap\\\\LdapBase\\:\\:getError\\(\\) should return string but returns null\\.$#"
			count: 1
			path: service/Common/AdLdap/LdapBase.php

		-
			message: "#^Method Services\\\\Common\\\\AdLdap\\\\LdapBase\\:\\:getErrorNo\\(\\) should return int but returns null\\.$#"
			count: 1
			path: service/Common/AdLdap/LdapBase.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 1
			path: service/Common/AdLdap/LdapBase.php

		-
			message: "#^Property Services\\\\Common\\\\AdLdap\\\\LdapBase\\:\\:\\$connection \\(null\\) does not accept resource\\|false\\.$#"
			count: 1
			path: service/Common/AdLdap/LdapBase.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\AbstractDepartAdapter\\:\\:\\$companyName has unknown class Services\\\\Common\\\\Adapter\\\\公司名称 as its type\\.$#"
			count: 1
			path: service/Common/Adapter/AbstractDepartAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\AbstractDepartAdapter\\:\\:\\$ldapResults has unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据 as its type\\.$#"
			count: 1
			path: service/Common/Adapter/AbstractDepartAdapter.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(int 字段类型，部门或MemeberOf\\)\\: Unexpected token \"字段类型，部门或MemeberOf\", expected variable at offset 93$#"
			count: 1
			path: service/Common/Adapter/AbstractUserAdapter.php

		-
			message: "#^PHPDoc tag @param references unknown parameter\\: \\$strict$#"
			count: 1
			path: service/Common/Adapter/AbstractUserAdapter.php

		-
			message: "#^Parameter \\#2 \\$context of static method Services\\\\Common\\\\AdLdap\\\\AdLdapError\\:\\:debugLog\\(\\) expects null, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: service/Common/Adapter/AbstractUserAdapter.php

		-
			message: "#^Variable \\$depat_all might not be defined\\.$#"
			count: 1
			path: service/Common/Adapter/AbstractUserAdapter.php

		-
			message: "#^Variable \\$depat_all_ might not be defined\\.$#"
			count: 1
			path: service/Common/Adapter/AbstractUserAdapter.php

		-
			message: "#^Access to offset string on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 1
			path: service/Common/Adapter/AdDomainUserAdapter.php

		-
			message: "#^Cannot call method getItem\\(\\) on null\\.$#"
			count: 1
			path: service/Common/Adapter/AdDomainUserAdapter.php

		-
			message: "#^PHPDoc type Services\\\\Common\\\\Adapter\\\\Ldap原始数据 of property Services\\\\Common\\\\Adapter\\\\AdDomainUserAdapter\\:\\:\\$ldapResults is not covariant with PHPDoc type array of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$ldapResults\\.$#"
			count: 1
			path: service/Common/Adapter/AdDomainUserAdapter.php

		-
			message: "#^PHPDoc type null of property Services\\\\Common\\\\Adapter\\\\AdDomainUserAdapter\\:\\:\\$domainConfig is not covariant with PHPDoc type Services\\\\Common\\\\AdLdap\\\\AdLdapConfig of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$domainConfig\\.$#"
			count: 1
			path: service/Common/Adapter/AdDomainUserAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\AdDomainUserAdapter\\:\\:\\$ldapResults has unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据 as its type\\.$#"
			count: 1
			path: service/Common/Adapter/AdDomainUserAdapter.php

		-
			message: "#^Access to offset 'count' on an unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据\\.$#"
			count: 1
			path: service/Common/Adapter/CaiFuDepartAdapter.php

		-
			message: "#^Comparison operation \"\\>\" between int\\<1, max\\> and 0 is always true\\.$#"
			count: 1
			path: service/Common/Adapter/CaiFuDepartAdapter.php

		-
			message: "#^Iterating over an object of an unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据\\.$#"
			count: 1
			path: service/Common/Adapter/CaiFuDepartAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\AbstractDepartAdapter\\:\\:\\$ldapResults \\(Services\\\\Common\\\\Adapter\\\\原始ldap数据\\) does not accept array\\<int\\|string, mixed\\>\\.$#"
			count: 1
			path: service/Common/Adapter/CaiFuDepartAdapter.php

		-
			message: "#^Result of && is always true\\.$#"
			count: 1
			path: service/Common/Adapter/CaiFuDepartAdapter.php

		-
			message: "#^Variable \\$aDep might not be defined\\.$#"
			count: 2
			path: service/Common/Adapter/CaiFuDepartAdapter.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 3
			path: service/Common/Adapter/CaiFuUserAdapter.php

		-
			message: "#^Undefined variable\\: \\$aLdapAllDn$#"
			count: 2
			path: service/Common/Adapter/CaiFuUserAdapter.php

		-
			message: "#^Variable \\$aLdapAllDn in isset\\(\\) is never defined\\.$#"
			count: 2
			path: service/Common/Adapter/CaiFuUserAdapter.php

		-
			message: "#^Access to offset 'count' on an unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/Depart/DepartAdapter.php

		-
			message: "#^Access to offset int\\<0, max\\> on an unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/Depart/DepartAdapter.php

		-
			message: "#^Access to offset string on an unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/AdDomainUserAdapter.php

		-
			message: "#^PHPDoc type Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap of property Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\AdDomainUserAdapter\\:\\:\\$ldapResults is not covariant with PHPDoc type array of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$ldapResults\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/AdDomainUserAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\AdDomainUserAdapter\\:\\:\\$ldapResults has unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap as its type\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/AdDomainUserAdapter.php

		-
			message: "#^Access to offset 'cn' on an unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Access to offset 'displayname' on an unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Access to offset 'dn' on an unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据\\.$#"
			count: 2
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Access to offset 'name' on an unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Access to offset string on an unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据\\.$#"
			count: 4
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Cannot call method getAll\\(\\) on null\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^PHPDoc type Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据 of property Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\LdapUserAdapter\\:\\:\\$ldapResults is not covariant with PHPDoc type array of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$ldapResults\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^PHPDoc type null of property Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\LdapUserAdapter\\:\\:\\$domainConfig is not covariant with PHPDoc type Services\\\\Common\\\\AdLdap\\\\AdLdapConfig of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$domainConfig\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\LdapUserAdapter\\:\\:\\$ldapResults has unknown class Services\\\\Common\\\\Adapter\\\\Custom\\\\User\\\\Ldap原始数据 as its type\\.$#"
			count: 1
			path: service/Common/Adapter/Custom/User/LdapUserAdapter.php

		-
			message: "#^Access to offset 'count' on an unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据\\.$#"
			count: 1
			path: service/Common/Adapter/DepartAdapter.php

		-
			message: "#^Access to offset int\\<0, max\\> on an unknown class Services\\\\Common\\\\Adapter\\\\原始ldap数据\\.$#"
			count: 1
			path: service/Common/Adapter/DepartAdapter.php

		-
			message: "#^Cannot call method getAll\\(\\) on null\\.$#"
			count: 1
			path: service/Common/Adapter/LdapUserAdapter.php

		-
			message: "#^PHPDoc type null of property Services\\\\Common\\\\Adapter\\\\LdapUserAdapter\\:\\:\\$domainConfig is not covariant with PHPDoc type Services\\\\Common\\\\AdLdap\\\\AdLdapConfig of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$domainConfig\\.$#"
			count: 1
			path: service/Common/Adapter/LdapUserAdapter.php

		-
			message: "#^Access to offset 'cn' on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 2
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Access to offset 'displayname' on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 3
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Access to offset 'distinguishedname' on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 2
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Access to offset 'name' on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 2
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Access to offset 'userprincipalname' on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 3
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Access to offset mixed on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 2
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Cannot call method getAll\\(\\) on null\\.$#"
			count: 1
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^PHPDoc type Services\\\\Common\\\\Adapter\\\\Ldap原始数据 of property Services\\\\Common\\\\Adapter\\\\SyncADUserAdapter\\:\\:\\$ldapResults is not covariant with PHPDoc type array of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$ldapResults\\.$#"
			count: 1
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^PHPDoc type null of property Services\\\\Common\\\\Adapter\\\\SyncADUserAdapter\\:\\:\\$domainConfig is not covariant with PHPDoc type Services\\\\Common\\\\AdLdap\\\\AdLdapConfig of overridden property Services\\\\Common\\\\Adapter\\\\AbstractUserAdapter\\:\\:\\$domainConfig\\.$#"
			count: 1
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\SyncADUserAdapter\\:\\:\\$ldapResults has unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据 as its type\\.$#"
			count: 1
			path: service/Common/Adapter/SyncADUserAdapter.php

		-
			message: "#^Access to offset 'count' on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 1
			path: service/Common/Adapter/TuoWeiDepartAdapter.php

		-
			message: "#^Access to offset int\\<0, max\\> on an unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据\\.$#"
			count: 11
			path: service/Common/Adapter/TuoWeiDepartAdapter.php

		-
			message: "#^Property Services\\\\Common\\\\Adapter\\\\TuoWeiDepartAdapter\\:\\:\\$ldapResults has unknown class Services\\\\Common\\\\Adapter\\\\Ldap原始数据 as its type\\.$#"
			count: 1
			path: service/Common/Adapter/TuoWeiDepartAdapter.php

		-
			message: "#^Variable \\$aDisName might not be defined\\.$#"
			count: 1
			path: service/Common/Adapter/TuoWeiDepartAdapter.php

		-
			message: "#^Variable \\$dataarray might not be defined\\.$#"
			count: 1
			path: service/Common/Adapter/TuoWeiDepartAdapter.php

		-
			message: "#^Cannot call method getItem\\(\\) on null\\.$#"
			count: 4
			path: service/Common/Adapter/TuoWeiUserAdapter.php

		-
			message: "#^Variable \\$aDisName might not be defined\\.$#"
			count: 2
			path: service/Common/Adapter/TuoWeiUserAdapter.php

		-
			message: "#^Variable \\$is_hav might not be defined\\.$#"
			count: 1
			path: service/Common/Adapter/TuoWeiUserAdapter.php

		-
			message: "#^Variable \\$str2 might not be defined\\.$#"
			count: 1
			path: service/Common/Adapter/TuoWeiUserAdapter.php

		-
			message: "#^Variable \\$upids might not be defined\\.$#"
			count: 3
			path: service/Common/Adapter/TuoWeiUserAdapter.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 1
			path: service/Common/Entity/DepartTreeEntity.php

		-
			message: "#^Property Services\\\\Common\\\\Entity\\\\DepartTreeEntity\\:\\:\\$adapter \\(array\\) does not accept Services\\\\Common\\\\Adapter\\\\AdapterInterface\\.$#"
			count: 1
			path: service/Common/Entity/DepartTreeEntity.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 1
			path: service/Common/Entity/DepartTreeEntity.php

		-
			message: "#^Property Services\\\\Common\\\\Entity\\\\UserEntity\\:\\:\\$adapter \\(null\\) does not accept Services\\\\Common\\\\Adapter\\\\AdapterInterface\\.$#"
			count: 1
			path: service/Common/Entity/UserEntity.php

		-
			message: "#^Method Services\\\\Common\\\\Interfaces\\\\AdDepartServiceInterface\\:\\:getDomainConfig\\(\\) has invalid return type Services\\\\Common\\\\Interfaces\\\\AdLdapConfig\\.$#"
			count: 1
			path: service/Common/Interfaces/AdDepartServiceInterface.php

		-
			message: "#^Method Services\\\\Common\\\\Interfaces\\\\AdDepartServiceInterface\\:\\:getDomainConfig\\(\\) has invalid return type Services\\\\Common\\\\Interfaces\\\\AdLdapConfig对象\\.$#"
			count: 1
			path: service/Common/Interfaces/AdDepartServiceInterface.php

		-
			message: "#^PHPDoc tag @throws with type Services\\\\Common\\\\Interfaces\\\\AdLdapException is not subtype of Throwable$#"
			count: 1
			path: service/Common/Interfaces/AdDepartServiceInterface.php

		-
			message: "#^Method Services\\\\Common\\\\Interfaces\\\\AdUserServiceInterface\\:\\:getDomainConfig\\(\\) has invalid return type Services\\\\Common\\\\Interfaces\\\\AdLdapConfig对象\\.$#"
			count: 1
			path: service/Common/Interfaces/AdUserServiceInterface.php

		-
			message: "#^PHPDoc tag @throws with type Services\\\\Common\\\\Interfaces\\\\AdLdapException is not subtype of Throwable$#"
			count: 3
			path: service/Common/Interfaces/AdUserServiceInterface.php

		-
			message: "#^Method Services\\\\Common\\\\Services\\\\AdDepartService\\:\\:getAllAdDomainConfigs\\(\\) should return array\\|string but returns false\\.$#"
			count: 1
			path: service/Common/Services/AdDepartService.php

		-
			message: "#^Method Services\\\\Common\\\\Services\\\\AdDepartService\\:\\:getAllLdapConfigs\\(\\) should return array\\|string but returns false\\.$#"
			count: 1
			path: service/Common/Services/AdDepartService.php

		-
			message: "#^Parameter \\#2 \\$context of static method Services\\\\Common\\\\AdLdap\\\\AdLdapError\\:\\:errorLog\\(\\) expects null, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: service/Common/Services/AdDepartService.php

		-
			message: "#^Property Services\\\\Common\\\\Services\\\\AdDepartService\\:\\:\\$adldap \\(Services\\\\Common\\\\AdLdap\\\\AdLdap\\) does not accept null\\.$#"
			count: 1
			path: service/Common/Services/AdDepartService.php

		-
			message: "#^Variable \\$ldapResults in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: service/Common/Services/AdDepartService.php

		-
			message: "#^Else branch is unreachable because previous condition is always true\\.$#"
			count: 2
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Method Services\\\\Common\\\\Services\\\\AdUserService\\:\\:authLdapUser\\(\\) never returns array so it can be removed from the return type\\.$#"
			count: 1
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Method Services\\\\Common\\\\Services\\\\AdUserService\\:\\:getAllAdDomainConfigs\\(\\) should return array\\|string but returns false\\.$#"
			count: 1
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Method Services\\\\Common\\\\Services\\\\AdUserService\\:\\:getAllLdapConfigs\\(\\) should return array\\|string but returns false\\.$#"
			count: 1
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Negated boolean expression is always false\\.$#"
			count: 4
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Parameter \\#2 \\$context of static method Services\\\\Common\\\\AdLdap\\\\AdLdapError\\:\\:errorLog\\(\\) expects null, array\\<string, mixed\\> given\\.$#"
			count: 4
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Parameter \\#2 \\$context of static method Services\\\\Common\\\\AdLdap\\\\AdLdapError\\:\\:errorLog\\(\\) expects null, array\\<string, string\\> given\\.$#"
			count: 1
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Property Services\\\\Common\\\\Services\\\\AdUserService\\:\\:\\$adldap \\(Services\\\\Common\\\\AdLdap\\\\AdLdap\\) does not accept null\\.$#"
			count: 1
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Variable \\$adaptRes in isset\\(\\) always exists and is not nullable\\.$#"
			count: 1
			path: service/Common/Services/AdUserService.php

		-
			message: "#^Variable \\$bindResult might not be defined\\.$#"
			count: 3
			path: service/Common/Services/AdUserService.php

		-
			message: "#^PHPDoc tag @param for parameter \\$content with type mixed is not subtype of native type string\\.$#"
			count: 1
			path: service/Common/Services/CommonService.php

		-
			message: "#^Offset 'DeviceID' does not exist on bool\\|string\\.$#"
			count: 1
			path: service/Common/Services/DeviceOperationService.php

		-
			message: "#^Method ConfigServiceProvider\\:\\:saveDict\\(\\) should return bool but returns int\\.$#"
			count: 1
			path: service/ConfigServiceProvider.php

		-
			message: "#^Call to function is_numeric\\(\\) with array\\|bool will always evaluate to false\\.$#"
			count: 2
			path: service/Device/Services/DasmDevService.php

		-
			message: "#^Parameter \\#1 \\$DeviceID of static method DeviceModel\\:\\:getOneByType\\(\\) expects string, int\\<min, \\-1\\>\\|int\\<1, max\\> given\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoControlService.php

		-
			message: "#^Call to function is_numeric\\(\\) with array\\|bool will always evaluate to false\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Cannot access offset 'Type' on int\\<min, \\-1\\>\\|int\\<1, max\\>\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Parameter \\#1 \\$ID of static method BaseModel\\:\\:getOne\\(\\) expects string, int\\<1, max\\> given\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Parameter \\#1 \\$ID of static method BaseModel\\:\\:getOne\\(\\) expects string, int\\<min, \\-1\\>\\|int\\<1, max\\> given\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Parameter \\#2 \\$port of function UdpSend expects string, int given\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Parameter \\#2 \\$port of function UdpSendAndRecv expects string, int given\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Parameter \\#4 \\$timeOutSec of function UdpSendAndRecv expects string, int given\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Variable \\$basIP in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: service/Device/Services/DeviceInfoService.php

		-
			message: "#^Parameter \\#1 \\$ID of static method BaseModel\\:\\:getOne\\(\\) expects string, int\\<min, \\-1\\>\\|int\\<1, max\\> given\\.$#"
			count: 1
			path: service/Device/Services/DeviceSceneService.php

		-
			message: "#^Parameter \\#1 \\$deviceId of static method DeviceTokenRedis\\:\\:getOne\\(\\) expects string, int\\<min, \\-1\\>\\|int\\<1, max\\> given\\.$#"
			count: 1
			path: service/DeviceServiceProvider.php

		-
			message: "#^Variable \\$DiskId in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: service/DeviceServiceProvider.php

		-
			message: "#^Variable \\$cmds in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: service/GatewayServiceProvider.php

		-
			message: "#^Parameter \\#1 \\$json of function json_decode expects string, null given\\.$#"
			count: 1
			path: service/LoginServiceProvider.php

		-
			message: "#^Unreachable statement \\- code above always terminates\\.$#"
			count: 6
			path: service/LoginServiceProvider.php

		-
			message: "#^Negated boolean expression is always true\\.$#"
			count: 1
			path: service/NetServiceProvider.php

		-
			message: "#^Constructor of class Services\\\\Patch\\\\Services\\\\PatchService has an unused parameter \\$params\\.$#"
			count: 1
			path: service/Patch/Services/PatchService.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: service/Policy/Services/SafeCheckPolicyService.php

		-
			message: "#^Strict comparison using \\=\\=\\= between 'fault' and 'false' will always evaluate to false\\.$#"
			count: 1
			path: service/Policy/Services/SafeCheckPolicyService.php

		-
			message: "#^Method Services\\\\Result\\\\Services\\\\AutoResultService\\:\\:checkFrequency\\(\\) should return string but returns false\\.$#"
			count: 2
			path: service/Result/Services/AutoResultService.php

		-
			message: "#^Method Services\\\\Result\\\\Services\\\\BaseResultService\\:\\:getAscLogUrl\\(\\) should return string but returns int\\.$#"
			count: 2
			path: service/Result/Services/BaseResultService.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\: \\$ascid 控制器ID\\)\\: Unexpected token \"\\:\", expected type at offset 70$#"
			count: 1
			path: service/Result/Services/BaseResultService.php

		-
			message: "#^PHPDoc tag @throws has invalid value \\(/Exception\\)\\: Unexpected token \"/Exception\", expected type at offset 107$#"
			count: 1
			path: service/Result/Services/BaseResultService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Result\\\\Services\\\\MobileResultService\\:\\:submit\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\ResultServiceInterface\\:\\:submit\\(\\)$#"
			count: 1
			path: service/Result/Services/MobileResultService.php

		-
			message: "#^Parameter \\#1 \\$ID of static method BaseModel\\:\\:getOne\\(\\) expects string, int\\<1, max\\> given\\.$#"
			count: 1
			path: service/Result/Services/PcResultService.php

		-
			message: "#^Return type \\(array\\) of method Services\\\\Result\\\\Services\\\\PcResultService\\:\\:submit\\(\\) should be compatible with return type \\(bool\\) of method Services\\\\Auth\\\\Interfaces\\\\ResultServiceInterface\\:\\:submit\\(\\)$#"
			count: 1
			path: service/Result/Services/PcResultService.php

		-
			message: "#^Parameter \\#3 \\$subject of function str_replace expects array\\|string, float given\\.$#"
			count: 1
			path: service/ResultServiceProvider.php

		-
			message: "#^Call to function is_string\\(\\) with array will always evaluate to false\\.$#"
			count: 2
			path: service/ServerServiceProvider.php

		-
			message: "#^Empty array passed to foreach\\.$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Left side of && is always true\\.$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Method ServerServiceProvider\\:\\:getIpException\\(\\) should return int but returns string\\.$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Method ServerServiceProvider\\:\\:getPortalConfig\\(\\) should return array\\|bool but returns string\\.$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 2
			path: service/ServerServiceProvider.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Undefined variable\\: \\$gProductInfo$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Variable \\$gProductInfo in isset\\(\\) is never defined\\.$#"
			count: 1
			path: service/ServerServiceProvider.php

		-
			message: "#^Binary operation \"\\+\" between int\\<1, max\\> and array results in an error\\.$#"
			count: 1
			path: service/SmsServiceProvider.php

		-
			message: "#^Comparison operation \"\\>\\=\" between array and \\(float\\|int\\) results in an error\\.$#"
			count: 2
			path: service/SmsServiceProvider.php

		-
			message: "#^Parameter \\#3 \\$postdata of function curl expects string, array\\<string, mixed\\> given\\.$#"
			count: 1
			path: service/SmsServiceProvider.php

		-
			message: "#^Undefined variable\\: \\$aSMSAlert$#"
			count: 1
			path: service/SmsServiceProvider.php

		-
			message: "#^Variable \\$aSMSAlert in empty\\(\\) is never defined\\.$#"
			count: 1
			path: service/SmsServiceProvider.php

		-
			message: "#^Binary operation \"\\-\" between string\\|null and int results in an error\\.$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^Expression on left side of \\?\\? is not nullable\\.$#"
			count: 3
			path: service/SystemServiceProvider.php

		-
			message: "#^Method SystemServiceProvider\\:\\:getModuleCount\\(\\) should return string\\|null but returns int\\.$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^Method SystemServiceProvider\\:\\:isUploadDir\\(\\) should return bool but returns string\\.$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\: \\$firsturl\\)\\: Unexpected token \"\\:\", expected type at offset 95$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^PHPDoc tag @param has invalid value \\(\\: \\$logInfo\\)\\: Unexpected token \"\\:\", expected type at offset 70$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^Parameter \\#1 \\$string of function substr expects string, int\\<min, \\-1\\>\\|int\\<1, max\\> given\\.$#"
			count: 3
			path: service/SystemServiceProvider.php

		-
			message: "#^Variable \\$client on left side of \\?\\? always exists and is always null\\.$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^Variable \\$osType on left side of \\?\\? always exists and is always null\\.$#"
			count: 1
			path: service/SystemServiceProvider.php

		-
			message: "#^Binary operation \"\\*\" between string and 60 results in an error\\.$#"
			count: 1
			path: service/UserServiceProvider.php

		-
			message: "#^Method UserServiceProvider\\:\\:registerUser\\(\\) should return bool but returns int\\.$#"
			count: 1
			path: service/UserServiceProvider.php

		-
			message: "#^Right side of && is always true\\.$#"
			count: 1
			path: service/UserServiceProvider.php

		-
			message: "#^Variable \\$userinfo in empty\\(\\) always exists and is not falsy\\.$#"
			count: 1
			path: service/UserServiceProvider.php
