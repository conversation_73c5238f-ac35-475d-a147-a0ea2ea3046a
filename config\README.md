## 配置文件
### 放配置文件
#### 1、基本配置
##### 1）inc_config.php：基础配置，如redis服务器，队列配置，RPC接口配置等。
##### 2）inc_constants.php：常量定义，如系统常量主要为路径（如PATH_LOG、PATH_HTML），还有业务常量（如DEVICE_TYPE_PC、OSTYPE_WINDOWS、ROLE_ID_GUEST、DEVTYPE_DASM）。
##### 3）inc_database.php：数据库相关配置
##### 4）在初始化文件init.php中，加载inc_constants.php；inc_config.php；inc_database.php，这三个文件为必须加载的文件。任何请求或脚本都必须require初始化文件init.php。
#### 2、按需加载的配置
##### 1）其他配置文件为需要时再加载的文件，可使用方法include_config在需要时，引入对应的配置文件,如include_config('auth')，引入配置文件inc_auth.php。
##### 2）inc_auth.php：认证相关配置
##### 3）inc_cronb.php：定时器相关配置
##### 4）inc_dict.php：server/info接口中，TDict表数据配置需要返回的字段，在数据库中添加了一行TDict数据，想要在该接口返回，则还需要在这里配置下才行。-- 为了安全，防止返回不需要字段，如密码。
##### 5）inc_lang.php：多语言配置编号所在的文件，配置提示路由。错误码共8位，前5位可路由到对应的配置文件。（如错误码：21120044，前5位为21120，对应Auth，则对应文件data/lang/$langDir/Auth.php，$langDir为zh_CN或en_US）
##### 6）inc_route.php：url路由配置，循环$GLOBALS['ROUTE']['routes']第一个匹配的路由为准。$GLOBALS['ROUTE']['map']为兼容老的调用方式的映射。

