<?php

/**
 * Description: 操作员表
 * User: <EMAIL>
 * Date: 2021/04/11 23:32
 * Version: $Id: OperatorModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class OperatorModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TOperator';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        '*'    => '*',
        'username' => 'UserName',
        'admin' => 'RID,Tel',
    ];

    /**
     * 所有条目
     *
     * @param $Column
     * @return mixed
     */
    public static function getAll($Column = '*')
    {
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']}";
        return lib_database::getAll($sql, $table['index']);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
