<?php

/**
 * Description: 存储TDict缓存
 * User: <EMAIL>
 * Date: 2022/05/16 10:32
 * Version: $Id$
 */

class DictHashRedis extends BaseHashRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Dict';

    /**
     * 单条
     *
     * @param string $type
     * @param array $columns
     *
     * @return mixed
     */
    public static function getOne($type, $columns = [])
    {
        if (empty($type)) {
            return false;
        }
        return self::get($columns, $type);
    }

    /**
     * 数量
     *
     * @return mixed
     */
    public static function getCount()
    {
        return self::count();
    }

    /**
     * 单条
     *
     * @param string $type
     * @param array $data
     *
     * @return mixed
     */
    public static function setOne($type, $data)
    {
        return self::set($data, $type);
    }

    /**
     * 删除
     *
     * @param string $type
     *
     * @return mixed
     */
    public static function deleteOne($type)
    {
        return self::del($type);
    }
}
