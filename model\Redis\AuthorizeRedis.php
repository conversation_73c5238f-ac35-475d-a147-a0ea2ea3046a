<?php

/**
 * Description: Websocket
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: SessionRedis.php 175039 2022-05-05 07:33:41Z duanyc $
 */

class AuthorizeRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'Authorize';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'Regist,SumNum,StopTime';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one'    => ['Regist','SumNum'],
    ];

    /**
     * 单条
     *
     * @param string $modId 授权ID
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne(string $modId, string $column = 'one')
    {
        return self::get($column, $modId);
    }

    /**
     * 单条
     *
     * @param string $modId 授权ID
     * @param array $data
     *
     * @return bool
     */
    public static function setOne(string $modId, array $data): bool
    {
        return self::set($data, $modId);
    }

    /**
     * 删除
     *
     * @param string $modId 授权ID
     *
     * @return bool
     */
    public static function deleteOne(string $modId): bool
    {
        return self::del($modId);
    }

}
