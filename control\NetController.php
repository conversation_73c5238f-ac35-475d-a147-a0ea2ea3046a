<?php
/**
 * Description: 网络相关接口
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: NetController.php 172267 2022-03-29 10:26:37Z huyf $
 */
!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class NetController extends BaseController
{

    /**
     * 索引映射
     * @var array
     */
    private $indexConfig = [
        21104005 => 233, 21102004 => 888
    ];

    /**
     * 强制下线
     * @return array
     * @throws Exception
     */
    public function cutoff()
    {
        $params = [];
        $isSendCutOffMsg = request('isSendCutOffMsg', 'request', 0, 'int');
        $currDeviceId = request('currDeviceId', 'request', 0, 'int');
        $params['device_id'] = request('device_id', 'request', request('deviceid', 'request'));
        $params['msg'] = Base64DeExt(request('msg', 'request', '','string'), true);
        $params['cuttype'] = request('cuttype', 'request');
        $params['fromweb'] = request('fromweb', 'request');
        $params['login'] = request('login', 'request');
        $params['pass'] = request('pass', 'pass');
        $params['passwd'] = request('passwd', 'request', $params['pass']);
        $params['ip'] = request('ip', 'request');
        $params['mac'] = request('mac', 'request');
        $params['isolation'] = request('isolation', 'request');
        $params['remark'] = request('remark', 'request', 'LogOut');
        $params['isSendCutOffMsg'] = !empty($isSendCutOffMsg) ? $isSendCutOffMsg : 1;
        //加个并发锁，俩秒内禁止重复请求，入网状态会重复
        $this->isLocked(md5(json_encode($params)), 2);
        if (!empty($isSendCutOffMsg) && !empty($currDeviceId)) {
            $aparams = ['servicePrefix' => 'User', 'deviceId' => $currDeviceId];
            $userAuthService = AuthServiceProvider::initAuthService($aparams);
            $userAuthService->setDeviceInfo();
            $errmsg = $userAuthService->onlineOutDevice($params['device_id'], $params['remark']);
        } else {
            //下线自己的时候才删除session信息
            LoginServiceProvider::logout();
            // 这里先保持现状，在html工程中，将断网的替换才新的grpc接口调用 update by renchen 2022/09/28
            //该位置默认3秒超时重试，也有较大概率会出现发布多次断网请求,入网状态会重复，改为30秒超时 xlj 20230215 TODO 后续优化掉RPC旧调用方式
            $result = lib_yar::clients('net', 'cutoffNet', $params, '127.0.0.1', 30000);
            if (empty($result['state'])) {
                throw new Exception($result['message']);
            }
            $errmsg = $result['data']['info'];
        }
        if (!empty($params['device_id'])) {
            $deviceIds = explode(',', $params['device_id']);
            if (is_array($deviceIds) && !empty($deviceIds)) {
                ssoCommon::cacheDelete($deviceIds);
            }
        }

        $this->errmsg = $errmsg;
        return [];
    }

    /**
     * windows CE系统放开网络，对应老交易 wince_net
     *
     * @return mixed
     * @throws Exception
     */
    public function winceAccess()
    {
        $deviceId = request('deviceId', 'request');
        $isActive = request('isActive', 'request', '', 'string');
        $tbclientip4 = request('tbclientip4', 'request');
        cutil_php_log('wince_net:  get deviceid :' . $deviceId, 'wince_net');
        hlp_check::checkEmpty($deviceId);
        try {
            NetServiceProvider::winceAllowNet($deviceId, $isActive, $tbclientip4);
            if (API_VERSION < '1.0') {
                return 200;
            }
            $this->errmsg = L(21102003);
            return [];
        } catch (Exception $e) {
            $code = $e->getCode();
            if (API_VERSION < '1.0') {
                return $this->indexConfig[$code] ?? 0;
            }
            throw $e;
        }
    }

    /**
     * 运维模式客户端推送继续访问放开指定设备
     * @return mixed
     * @throws Exception
     */
    public function goToVisit()
    {
        $deviceId = request('deviceId', 'request');
        hlp_check::checkEmpty($deviceId);
        return NetServiceProvider::makeEngineRegion($deviceId);
    }
}
