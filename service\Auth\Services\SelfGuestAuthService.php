<?php
/**
 * Description: 来宾自助认证服务
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: SelfGuestAuthService.php 175145 2022-05-06 07:53:42Z huyf $
 */

namespace Services\Auth\Services;

use AuthUserModel;
use DeviceModel;
use DictModel;
use Exception;
use GuestOnLineDeviceModel;
use GuestRelationModel;
use GuestSelfApplyModel;
use lib_alarm;
use lib_yar;
use NacOnLineDeviceModel;
use NetRoleModel;
use NetServiceProvider;
use RoleRelationModel;
use Services\Auth\Interfaces\AuthServiceInterface;
use SmsServiceProvider;
use SceneModel;

class SelfGuestAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'Guest';

    private $defaultTimeOut = 1800; //邮件审批默认30分钟超时

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 获取关联的角色ID 不需要使用了
     * @return array
     */
    //    private function getRelationRoleIds()
    //    {
    //        //查出所有拥有来宾角色分配权限的角色
    //        $resultRoleRelation = RoleRelationModel::getList(['ConfigID' => 4]);
    //        $roleRelation = [];
    //        if (!empty($resultRoleRelation)) {
    //            foreach ($resultRoleRelation as $Item) {
    //                $roleRelation[] = $Item["RoleID"];
    //            }
    //        }
    //        return $roleRelation;
    //    }

    /**
     * 获取接待权限的用户列表
     * @param $receptType 'name|mobile'
     * @return array
     */
    private function getRelationUserList($receptType)
    {
        $userList = [];
        $ucond = [];
        //根据手机号用户名查找对应的账号
        if (!empty($this->params['username']) && stripos($receptType, 'name') !== false) {
            $ucond['TrueNames'] = $this->params['username'];
        }
        if (!empty($this->params['userMobile']) && stripos($receptType, 'mobile') !== false) {
            $ucond['Tel'] = $this->params['userMobile'];
        }
        if (!empty($ucond)) {
            $ucond['TypeNG'] = 'Guest';
            $ucond['column'] = '*';
            $userList = AuthUserModel::getList($ucond);
        }
        return $userList;
    }

    /**
     * 获取根据注册信息接待权限的用户列表
     * @param $receptType 'name|mobile'
     * @return array
     */
    private function getRelationRegisterList($receptType)
    {
        $deviceList = [];
        $ucond = [];
        //根据手机号用户名查找对应的账号
        if (!empty($this->params['username']) && stripos($receptType, 'name') !== false) {
            $ucond['UserName'] = $this->params['username'];
        }
        if (!empty($this->params['userMobile']) && stripos($receptType, 'mobile') !== false) {
            $ucond['Tel'] = $this->params['userMobile'];
        }
        // 查询设备信息
        if (!empty($ucond)) {
            $ucond['column'] = '*';
            $deviceList = DeviceModel::getList($ucond);
        }
        return $deviceList;
    }

    /**
     * 插入来宾自助申请记录
     * @return array
     * @throws Exception
     */
    public function applyGuest()
    {
        $password = (string)@rand(100000, 999999);
        $aGuestAuth = DictModel::getAll("GUESTAUTH");
        if ($aGuestAuth['GuestApplySelf'] != '1') {
            T(21130030);
        }
        $deviceInfo = DeviceModel::getOne($this->deviceId, 'info');
        if (empty($deviceInfo)) {
            T(21103006);
        }
        $formatArr = ['user' => $this->params['guestName'], 'ip' => $deviceInfo['IP'],
            'pw' => $password, 'nowTime' => func_time_getNow()];
        $remark = L(21130002, $formatArr);
        // 插入用户表
        $aparams = ['Type' => $this->userType, 'UserName' => '', 'Password' => $password, 'RoleID' => ROLE_ID_GUEST,
            'DepartID' => $deviceInfo['DepartId'], 'LifeTime' => $aGuestAuth['NetCodeTime'], 'CreateUser' => $this->params['guestName'],
            'Remark' => $remark, 'TrueNames' => $this->params['guestName'], 'Tel' => $this->params['guestMobile'], 'Email' => ''];
        $userId = AuthUserModel::insert($aparams);
        AuthUserModel::update($userId, ['UserName' => 'Guest_' . $userId]);
        // 更新来宾关系表
        $reparams = ['IsNeedAudit' => '1', 'GustCodeType' => 'self_apply', 'Remark' => $this->params['content'],
            'Name' => $this->params['guestName'], 'Unit' => $this->params['guestCompany'], 'Tel' => $this->params['guestMobile'],
            'StaffName' => $this->params['username'], 'StaffTel' => $this->params['userMobile']];
        for ($i = 1; $i <= 5; $i++) {
            $reparams["GuestExpand_{$i}"] = $this->params["GuestExpand_{$i}"];
        }
        GuestRelationModel::updateByUserId($userId, $reparams);     // 更新来宾关系表
        // 插入申请表
        $gparams = ['UserID' => $userId, 'DeviceID' => $this->deviceId, 'Name' => $this->params['guestName'],
            'Unit' => $this->params['guestCompany'], 'Tel' => $this->params['guestMobile'], 'StaffName' => $this->params['username'],
            'StaffTel' => $this->params['userMobile'], 'Remark' => $this->params['content'],
            'Status' => 0, 'InsertTime' => 'now()', 'ValidTime' => 'now()', 'IP' => $deviceInfo['IP'], 'Mac' => $deviceInfo['Mac'], "DevName" => $deviceInfo['DevName']];
        for ($i = 1; $i <= 5; $i++) {
            $gparams["GuestExpand_{$i}"] = $this->params["GuestExpand_{$i}"];
        }
        $reamrk = L(21130037);
        $cancelParams = ['FromType' => L(21130012), 'Status' => -1, 'Reason' => $reamrk];
        GuestSelfApplyModel::updatePatch(['DeviceID'=>$this->deviceId,'Status'=>0], $cancelParams);

        $guestselfid = GuestSelfApplyModel::insert($gparams);
        $guestselfinfo = GuestSelfApplyModel::getOne($guestselfid, '*');
        $AccessType = $guestselfinfo['AccessType'];
        cutil_php_log("aGuestAuth:" . var_export($aGuestAuth, true), 'SelfGuestSubmit');

        // 获取来宾接待审核人匹配方式
        $matching = $aGuestAuth['matching'] ?? '';
        if ($matching === '') {
            $aguestAuth = DictModel::getAll("GuestAuth");
            // 获取来宾接待审核人匹配方式
            $matching = $aguestAuth['matching'] ?? '';
        }
        $userList =[] ;
        $registerList = [] ;
        // 获取发送审核人匹配方式
        if (stripos($matching, 'matchingRegister') !== false) {
            // 终端注册匹配
            $registerList = $this->getRelationRegisterList($aGuestAuth['ReceptType']);
            cutil_php_log("registerList:" . var_export($registerList, true), 'SelfGuestSubmit');
        }
        if (stripos($matching, 'matchingAccount') !== false) {
            // 用户信息匹配
            $userList = $this->getRelationUserList($aGuestAuth['ReceptType']);
            cutil_php_log("userList:" . var_export($userList, true), 'SelfGuestSubmit');
        }
        $this->sendMessage($guestselfid, $userList, $registerList, $guestselfinfo, $aGuestAuth);
        return ["state" => "OK", "guestselfid" => $guestselfid];
    }

    /**
     * 更新校验时间
     * @param $isDasm
     */
    public function updateValidTime($isDasm = false)
    {
        if (empty($this->params['guestselfId'])) {
            return false;
        }

        if (!empty($isDasm)) {
            $this->params['guestselfId'] = GuestSelfApplyModel::getPrimaryId($this->params['AscID'], $this->params['guestselfId']);
        }

        return GuestSelfApplyModel::update($this->params['guestselfId'], ['ValidTime' => 'now()']);
    }

    /**
     * 查询来宾自助申请是否被批准
     * @return array
     * @throws Exception
     */
    public function getStatus()
    {
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], 'info');
        if ($result['Status'] == '0') {
            $this->updateValidTime();
            $dasmIp = get_pub_dasm_ip();
            if (!empty($dasmIp)) {
                $cfg = read_inifile(PATH_ETC . 'asc.ini');
                $params = ['guestselfId' => $this->params['guestselfId'], 'AscID' => $cfg['AscID']];
                $result = lib_yar::clients('dasm', 'updateValidTime', $params, $dasmIp);
            }
        }
        if ($result && $result["Status"] == "0") {
            return ["state" => "WAIT"];
        } elseif ($result && $result["Status"] == "1") {
            return ["state" => "OK"];
        } elseif ($result && $result["Status"] == "-1") {
            $result["Reason"] = $result["Reason"] ?: "--";
            return ["state" => "refuse", "ApproveName" => $result["ApproveName"], "Reason" => $result["Reason"]];
        } elseif (empty($result)) {
            return ["state" => "refuse", "Reason" => L(21130017)];
        }
        return ["state" => "WAIT"];
    }

    /**
     * 访客取消来宾自助申请
     * @return array
     * @throws Exception
     */
    public function cancelApply(): array
    {
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], 'info');
        if (!empty($result['UserID'])) {
            if ($result['Status'] == "0" || $result['Status'] == "1") {
                $reamrk = L(21130020, ['date' => date('Y-m-d H:i:s', time())]);
                $gparams = ['FromType' => L(21130012), 'Status' => -1, 'Reason' => $reamrk];
                GuestSelfApplyModel::update($this->params['guestselfId'], $gparams);
                AuthUserModel::update($result['UserID'], ['Remark' => $reamrk]);
                \RelationComputerModel::update($result['DeviceID'], ['LastUserType'=>'']); //放弃接入情况lastuserType
                NacOnLineDeviceModel::delete(['DeviceID' => $result['DeviceID']]);
                cutil_php_debug("cancelGuest success:," . $reamrk, 'SelfGuestSubmit');
            }
        }
        return ["state" => "cancel"];
    }

    /**
     * 员工批准或拒绝来宾
     * 1:表示同意，-1：表示拒绝
     * @return array
     * @throws Exception
     */
    public function approvalGuest()
    {
        if (!in_array($this->params['state'], [1, -1, 0])) {
            T(21100002);
        }
        // 判断安检是否通过
        if (!$this->messageCheck($this->deviceId)) {
            T(21130034);
        }
        $resultOnLine = NacOnLineDeviceModel::getOne($this->deviceId, 'name');
        if (empty($resultOnLine)) {
            T(21130010);
        }
        $userName = $approveUserName = $resultOnLine['UserName'];
        //todo 这里需要核实用户名
        $userinfo = AuthUserModel::getOneByUserName($resultOnLine['AuthType'], $userName, 'user');
        if (!empty($userinfo['TrueNames'])) {
            $userName = $userinfo['TrueNames'];
        }
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], 'info');
        if (!empty($result['UserID'])) {
            if ($result['Status'] == "0") {
                $gparams = [
                    'FromType' => L(21130012), 'ApproveName' => $userName, 'ApproveUserName' => $approveUserName,
                    'ApproveTime' => 'now()', 'ApproveType' => $resultOnLine['AuthType']
                ];
                $reamrk = "";
                if ($this->params['state'] == -1) {
                    $reamrk = L(21130013, ['userName' => $userName, 'date' => date('Y-m-d H:i:s', time())]);
                    $gparams['Status'] = -1;
                }
                if ($this->params['state'] == 1) {
                    $reamrk = L(21130014, ['userName' => $userName, 'date' => date('Y-m-d H:i:s', time())]);
                }
                GuestSelfApplyModel::update($this->params['guestselfId'], $gparams);
                AuthUserModel::update($result['UserID'], ['Remark' => $reamrk]);
                cutil_php_debug("approvalGuest success:," . $reamrk, 'SelfGuestSubmit');
            } else {
                cutil_php_debug("approvalGuest fail, had approval by " . $result['ApproveName'] . "", 'SelfGuestSubmit');
                T(21130031);
            }
        }
        return ["state" => "OK", "ApproveName" => $userName, "Status" => $this->params['state']];
    }

    /**
     * 有接待权限的用户接待来宾  批准/拒绝
     * 1:表示同意，-1：表示拒绝
     * @return array
     * @throws Exception
     */
    public function receptGuest(): array
    {
        if (!in_array($this->params['state'], [1, -1, 0])) {
            T(21100002);
        }

        $resultOnLine = NacOnLineDeviceModel::getOne($this->deviceId, 'name');
        if (empty($resultOnLine)) {
            T(21130010);
        }
        $userName = $approveUserName = $resultOnLine['UserName'];
        /* 检查安全域是否合法 */
        if ($this->params['state'] == 1) {
            /* 检查安全域是否合法 */
            $regionList = (new GuestAuthService($this->params))->isGusetAllocation($this->deviceId, $this->params['AllowRegionIDs']);
            if (!$regionList) {
                T(21130029);
            }
        }
        //todo 这里需要核实用户名
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], 'info');
        // 检查当前接入方式是否允许
        $aGuestAuth = DictModel::getAll("GUESTAUTH");
        $AccessTypeArr = ['code' => ['switch' => 'NetCode', 'message' => L(21130021)],
            'self_apply' => ['switch' => 'GuestApplySelf', 'message' => L(21130022)],
            'code_state' => ['switch' => 'QrCode', 'message' => L(21130023)],
            'sms' => ['switch' => 'SmsState', 'message' => L(21130024)],
            'no_auth' => ['switch' => 'NoAuth', 'message' => L(21130035)]
        ];
        $accesstype = $AccessTypeArr[$result['AccessType']] ?? [];
        if (isset($aGuestAuth[$accesstype['switch']]) && $aGuestAuth[$accesstype['switch']] != '1') {
            T(21130032, ['accesstype' => $accesstype['message']]);
        }

        if (!empty($result['UserID'])) {
            if ($result['Status'] == "0") {
                $gparams = [
                    'FromType' => L(21130012),
                    'ApproveName' => $userName, 'ApproveUserName' => $approveUserName,
                    'ApproveTime' => 'now()', 'ApproveType' => $resultOnLine['AuthType']
                ];
                $guestMessage = $this->getGuestMessage($this->deviceId, $this->params['UserID']);
                $gparams = array_merge($gparams, $guestMessage);
                $reamrk = "";
                if ($this->params['state'] == -1) {
                    $reamrk = L(21130013, ['userName' => $userName, 'date' => date('Y-m-d H:i:s', time())]);
                    $gparams['Status'] = -1;
                    $gparams['Reason'] = $this->params['Reason'];
                    // 清除在线记录与上次认证方式
                    NacOnLineDeviceModel::delete(['DeviceID' => $result['DeviceID']]);
                    \RelationComputerModel::update($result['DeviceID'], ['LastUserType'=>'']); //放弃接入情况lastuserType
                }
                if ($this->params['state'] == 1) {
                    $reamrk = L(21130014, ['userName' => $userName, 'date' => date('Y-m-d H:i:s', time())]);
                    $gparams['Status'] = 1;

                    $GuestRelationParams = ['AllowRegionIDs' => $this->params['AllowRegionIDs'], 'AllowTime' => $this->params['AllowTime'], 'MaxNumber' => 0];
                    GuestRelationModel::updateByUserId($result['UserID'], $GuestRelationParams);     // 更新来宾关系表
                    // 审核强制修改安全域
                    $ipListID = NetServiceProvider::getOtherRegionID($this->params['AllowRegionIDs']);
                    $guestOnlineParams=[
                        'IplistID'=>$ipListID,
                        'RegionIDs'=>$this->params['AllowRegionIDs']
                    ];
                    if (!empty($this->params['AllowTime'])) {
                        $guestOnlineParams['ExpirateTime'] = date("Y-m-d H:i:s", (int)(strtotime("now") + (float)$this->params['AllowTime'] * 3600));
                    }
                    GuestOnLineDeviceModel::updateById($result['UserID'], $result['DeviceID'], $guestOnlineParams);
                }

                GuestSelfApplyModel::update($this->params['guestselfId'], $gparams);
                AuthUserModel::update($result['UserID'], ['Remark' => $reamrk]);

                cutil_php_debug("approvalGuest success:," . $reamrk, 'SelfGuestSubmit');
            } else {
                cutil_php_debug("approvalGuest fail, had approval by " . $result['ApproveName'] . "", 'SelfGuestSubmit');
                T(21130031);
            }
        }
        return ["state" => "OK", "Status" => $this->params['state']];
    }

    /**
     * 设置来宾被批准后能拥有的角色
     * @return array
     * @throws Exception
     */
    public function setRole()
    {
        if (!in_array($this->params['status'], [1, -1, 0])) {
            T(21100002);
        }
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], 'info');

        if (!empty($result) && $result['Status'] == 0 && $result['ApproveName'] == $this->params['approveName']) {
            GuestSelfApplyModel::update($this->params['guestselfId'], ['Status' => 1]);
            AuthUserModel::update($result['UserID'], ['RoleID' => $this->params['roleId']]);
        }

        return ["state" => "OK"];
    }

    /**
     * 查询来宾被审批后能设置的角色和来宾的电话
     * @return array
     * @throws Exception
     */
    public function getInfo()
    {
        $resultOnLine = NacOnLineDeviceModel::getOne($this->deviceId, 'name');
        if (empty($resultOnLine)) {
            T(21130010);
        }
        $roleList = NetRoleModel::getGuestCodeRoleList($resultOnLine['RoleID']);
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], 'data');
        return ["state" => "OK", "Name" => $result["Name"], "Tel" => $result["Tel"], "Role" => $roleList];
    }

    /**
     * 获取批准来宾账户信息列表
     */
    public function getInfoList()
    {
        return GuestSelfApplyModel::getInfoList([
            'approveName' => $this->params['approveName'],
            'approveType' => $this->params['approveType']
        ]);
    }

    /**
     * 获取批准来宾账户信息列表
     */
    public function getApproveInfoList($GreetDeviceID)
    {
        // 判断安检是否通过
        if (!$this->messageCheck($GreetDeviceID)) {
            return [];
        }
        $return = GuestSelfApplyModel::getApproveInfoList([
            'GreetType' => $this->params['GreetType'],
            'GreetUserID' => $this->params['GreetUserID'],
            'GreetDeviceID' => $GreetDeviceID
        ]);
        $AccessTypeArr = ['code' => L(21130021), 'self_apply' => L(21130022), 'code_state' => L(21130023), 'sms' => L(21130024), 'no_auth' => L(21130035)];
        foreach ($return as &$item) {
            $item['AccessTypeCode'] = $item['AccessType'];
            $item['AccessType'] = $AccessTypeArr[$item['AccessType']];
        }
        return $return;
    }

    /**
     * 提交来宾必填信息插入自助申请记录
     * @return array
     * @throws Exception
     */
    public function submitGuestInfo()
    {
        if ((int)$this->params['UserID'] === 1) {
            $guestRelation = ["IsNeedAudit" => "0", "StaffName" => "", "StaffTel" => "", "GreetType" => "",
                "GreetUserID" => "0", "GreetUserName" => ''];
        } else {
            $guestRelation = GuestRelationModel::getOne($this->params['UserID'], 'all');
        }

        $deviceInfo = DeviceModel::getOne($this->deviceId, 'one');
        $guestSelfApplyInfo = GuestSelfApplyModel::getOne($this->params['guestSelfID'], '*');
        if (empty($deviceInfo)) {
            T(21103006);
        }
        if (empty($guestRelation)) {
            T(21130018);
        }
        if (empty($guestSelfApplyInfo)) {
            T(21130019);
        }
        // 已拒绝的设备不能提交信息
        if ($guestSelfApplyInfo['Status'] == -1) {
            T(21130036);
        }
        $devInfo = \RelationComputerModel::getOne($this->deviceId);
        cutil_php_log("devinfo:" . var_export($devInfo, true), 'SelfGuestSubmit');
        $auditAccessType = \SceneDictModel::getValue($devInfo['SceneID'], 'AuditAccessType', 'GuestAuditConfig');
        $isAuditConfig = \SceneDictModel::getOneConfig($devInfo['SceneID'], 'IsNeedAuto', 'DevAudit');
        cutil_php_log("IsNeedAuto:" . var_export($isAuditConfig, true), 'SelfGuestSubmit');
        $isAudit = $isAuditConfig['Config'] ?? 0;
        $guestSelfApplyInfo['AccessType'] = fieldMapping($guestSelfApplyInfo['AccessType']);
        cutil_php_log("AccessType:" . $guestSelfApplyInfo['AccessType'], 'SelfGuestSubmit');
        cutil_php_log("auditAccessType:" .$auditAccessType, 'SelfGuestSubmit');
        // 查询场景确定是否需要审核
        $isNeedAudit = ($isAudit == 1 && stripos($auditAccessType, $guestSelfApplyInfo['AccessType']) !== false) ? 1 : 0;
        cutil_php_log("isNeedAudit:" .$isNeedAudit, 'SelfGuestSubmit');
        $guestSelfApplyInfo['Status'] = $isNeedAudit == 0 ? 1 : 0; //无需审核自动变成已审核
        // 更新申请表
        $staffName = $this->params['username'] ? $this->params['username']: $guestRelation['StaffName'];
        $staffTel = $this->params['userMobile'] ? $this->params['userMobile'] : $guestRelation['StaffTel'];
        $gparams = ['UserID' => $this->params['UserID'], 'DeviceID' => $this->deviceId, 'Name' => $this->params['guestName'],
            'Unit' => $this->params['guestCompany'], 'Tel' => $this->params['guestMobile'], 'StaffName' => $staffName,
            'StaffTel' => $staffTel, 'Remark' => $this->params['content'], 'Reason' => '',
            'GreetType' => $guestRelation['GreetType'], 'GreetUserID' => $guestRelation['GreetUserID'], 'GreetUserName' => $guestRelation['GreetUserName'],
            'IP' => $deviceInfo['IP'], "Mac" => $deviceInfo['Mac'], "DevName" => $deviceInfo['DevName'], 'Status' => $guestSelfApplyInfo['Status'],
            'ValidTime' => 'now()'];
        for ($i = 1; $i <= 5; $i++) {
            $gparams["GuestExpand_{$i}"] = $this->params["GuestExpand_{$i}"];
        }

        GuestSelfApplyModel::update($this->params['guestSelfID'], $gparams);

        // 根据提交时获取的场景修改安全域
        $regionID = SceneModel::getScenePassIpRegion($devInfo['SceneID']);
        cutil_php_log("new SceneID:" . $devInfo['SceneID'], 'SelfGuestSubmit');
        $allowRegionIDs = $regionID['ItemValue'] ?? '';
        //认证方式为免认证 自助申请 无预约的短信认证
        if (!empty($allowRegionIDs) && (in_array($guestRelation['GustCodeType'], ['no_auth', 'self_apply']) || ($guestRelation['AllowRegionIDs'] =='-1' &&  $guestRelation['GustCodeType'] == 'sms'))) {
            $ipListID = NetServiceProvider::getOtherRegionID($allowRegionIDs);
            cutil_php_log("new ipListID:" . $ipListID, 'SelfGuestSubmit');
            cutil_php_log("new allowRegionIDs:" . $allowRegionIDs, 'SelfGuestSubmit');
            GuestRelationModel::updateByUserId($this->params['UserID'], ['AllowRegionIDs' => $allowRegionIDs]);     // 根据场景更新AllowRegionIDs
            GuestOnLineDeviceModel::updateById($this->params['UserID'], $this->deviceId, ['IplistID' => $ipListID, 'RegionIDs' => $allowRegionIDs]);
        }
        $guestselfinfo = GuestSelfApplyModel::getOne($this->params['guestSelfID'], '*');
        $this->messageAudit($devInfo['SceneID'], $guestselfinfo);
        return ["state" => "OK", "guestselfid" => $this->params['guestSelfID'], "IsNeedAudit" => $isNeedAudit];
    }

    /**
     * 审批通知对应的人
     * @param $id $guestselfinfo
     */
    public function messageAudit($sceneId, $guestselfinfo)
    {
        $aGuestAuth = [];
        // 获取场景的匹配方式
        $matching = \SceneDictModel::getValue($sceneId, 'NotifyType', 'GuestAuditConfig');
        // 获取场景的匹配信息
        $ReceptType = \SceneDictModel::getValue($sceneId, 'ReceptType', 'GuestAuditConfig');
        // 获取场景是否需要发送邮件
        $aGuestAuth['isSendEmail'] =  \SceneDictModel::getValue($sceneId, 'IsSendEmail', 'GuestAuditConfig');
        // 获取场景邮件主题
        $aGuestAuth['emailTitleTpl'] =  \SceneDictModel::getValue($sceneId, 'EmailTitleTpl', 'GuestAuditConfig');
        // 获取场景邮件内容模板
        $aGuestAuth['emailBodyTpl'] =  \SceneDictModel::getValue($sceneId, 'EmailBodyTpl', 'GuestAuditConfig');
        $guestselfid = $guestselfinfo['ID'];
        $userList =[] ;
        $registerList = [] ;

        // 获取发送审核人匹配方式
        if (stripos($matching, 'matchingRegister') !== false) {
            // 终端注册匹配
            $registerList = $this->getRelationRegisterList($ReceptType);
            cutil_php_log("registerList:" . var_export($registerList, true), 'SelfGuestSubmit');
        }
        if (stripos($matching, 'matchingAccount') !== false) {
            // 用户信息匹配
            $userList = $this->getRelationUserList($ReceptType);
            cutil_php_log("userList:" . var_export($userList, true), 'SelfGuestSubmit');
        }
        $this->sendMessage($guestselfid, $userList, $registerList, $guestselfinfo, $aGuestAuth);
        return ["state" => "OK", "guestselfid" => $guestselfid];
    }

    /**
     *发送信息
     * @guestselfid selfID
     * @userList 用户集合 registerList 设备集合 guestselfinfo 来宾信息 aGuestAuth 短信相关模版内容
     * @return boolean
     */
    public function sendMessage($guestselfid, $userList, $registerList, $guestselfinfo, $aGuestAuth)
    {
        $receiveUserList = [];  //有接待来宾权限的账号合集
        $receiveDeviceList = [];  //有接待来宾权限的设备合集
        $hasBeenSendDeviceList = [];  //已发送通知的设备列表
        $managerIp = getManagerIp();
        if (!empty($userList) || !empty($registerList)) {
            foreach ($userList as $Item) {
                //如果用户配了邮箱，并开启了邮箱审批，则发送邮箱
                if (!empty($Item['Email']) && $aGuestAuth['isSendEmail'] == 1) {
                    $this->sendUserEmail($Item["ID"], $Item['Email'], $aGuestAuth['emailTitleTpl'], $aGuestAuth['emailBodyTpl'], $guestselfinfo, $managerIp);
                }
                $receiveUserList[] = $Item["ID"];
                $dcond = ['UserID' => $Item["ID"], 'column' => 'name'];
                $resultOnLine = NacOnLineDeviceModel::getList($dcond);
                cutil_php_log("nacOnLineDeviceModel user info:," . var_export($resultOnLine, true), 'SelfGuestSubmit');
                if (empty($resultOnLine)) {
                    continue;
                }

                foreach ($resultOnLine as $ItemOnLine) {
                    if (in_array($ItemOnLine['DeviceID'], $hasBeenSendDeviceList)) {
                        continue;
                    }
                    if (!$this->messageCheck($ItemOnLine['DeviceID'])) {
                        continue;
                    }
                    $url = get_cururl(true, 'http', $managerIp) . "/client/ui/#/selfGuestNotice?isWsIPC=1";
                    $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>OpenNewUrl</WhatToDo><Title>" .
                        L(21130011) . "</Title><Url>" . base64_encode($url) . "</Url><Width>378</Width><Height>249</Height><Position>RightDown</Position></ASM>";
                    $msgSenderInfo = ['DeviceId' => $ItemOnLine['DeviceID'], 'Content' => $content, 'IsNull' => true];
                    lib_alarm::createMsgSender($msgSenderInfo);
                    $hasBeenSendDeviceList[] =  $ItemOnLine['DeviceID'];
                }
            }

            foreach ($registerList as $rItem) {
                //如果设备配了邮箱，并开启了邮箱审批，则发送邮箱
                if (!empty($rItem['EMail']) && $aGuestAuth['isSendEmail'] == 1) {
                    $this->sendUserEmail(0, $rItem['EMail'], $aGuestAuth['emailTitleTpl'], $aGuestAuth['emailBodyTpl'], $guestselfinfo, $managerIp);
                }
                $receiveDeviceList[] = $rItem["DeviceID"];
                if (in_array($rItem['DeviceID'], $hasBeenSendDeviceList)) {
                    continue;
                }
                $dcond = ['DeviceID' =>  $rItem["DeviceID"], 'column' => 'name'];
                $resultOnLine = NacOnLineDeviceModel::getList($dcond);
                cutil_php_log("nacOnLineDeviceModel Device info:," . var_export($resultOnLine, true), 'SelfGuestSubmit');
                if (empty($resultOnLine)) {
                    continue;
                }
                if (!$this->messageCheck($rItem['DeviceID'])) {
                    continue;
                }
                $url = get_cururl(true, 'http', $managerIp) . "/client/ui/#/selfGuestNotice?isWsIPC=1";
                $content = "<ASM><TradeCode>SvrMsgToAss</TradeCode><WhatToDo>OpenNewUrl</WhatToDo><Title>" .
                    L(21130011) . "</Title><Url>" . base64_encode($url) . "</Url><Width>378</Width><Height>249</Height><Position>RightDown</Position></ASM>";
                $msgSenderInfo = ['DeviceId' => $rItem['DeviceID'], 'Content' => $content, 'IsNull' => true];
                lib_alarm::createMsgSender($msgSenderInfo);
                $hasBeenSendDeviceList[] = $rItem['DeviceID'];

            }
            $SelfApplyGreetDeviceIDs = "," . implode(',', $receiveDeviceList) . ",";  //前后都加一个逗号 方便来宾待审批列表的查询
            $selfApplyGreetUserIDs = "," . implode(',', $receiveUserList) . ",";  //前后都加一个逗号 方便来宾待审批列表的查询
            GuestSelfApplyModel::update($guestselfid, ['SelfApplyGreetUserIDs' => $selfApplyGreetUserIDs,'SelfApplyGreetDeviceIDs' => $SelfApplyGreetDeviceIDs]);
        }
        return true;
    }

    /**
     * 给接待人发送邮件审批
     * @param int $greeuserid 接待人ID
     * @param $email
     * @param $title
     * @param $body
     * @param $guestinfo
     * @param $managerip
     */
    public function sendUserEmail($greeuserid, $email, $title, $body, $guestinfo, $managerip)
    {
        $token = json_encode([
            'ID' => $guestinfo['ID'],
            'UserID' => $guestinfo['UserID'],
            'GreetUserID' => $greeuserid,
            'timestamp' => time(),
        ]);
        $url = get_cururl(true, 'http', $managerip) . '/access/ui/index.html?token=' . aesEncrypt($token) . '#/email/audit';
        $body = str_replace('${company}', $guestinfo['Unit'], $body);
        $body = str_replace('${visitorname}', $guestinfo['Name'], $body);
        $body = str_replace('${mobile}', $guestinfo['Tel'], $body);
        $body .= "<br/>审批链接如下:<br/><a href='" . $url . "'>" . $url . "</a>";
        $data = [
            'email' => $email,
            'content' => $body,
            'subject' => $title,
            'async' => true
        ];
        cutil_php_log("email content:" . var_export($data, true), 'SelfGuestSubmit');
        // 发送邮件耗时较久，改为异步发送
        SmsServiceProvider::callRemoteSendEmail($email, $data);
        return true;
    }


    /**
     * 获取等待邮件审批的条目
     * @throws Exception
     */
    public function getAudit()
    {
        $data = json_decode(aesDecrypt($this->params['token']), true);
        if (!is_array($data)) {
            T(21100002);
        }
        /* 校验时间戳是否在30分钟内 */
        if (((int)$data['timestamp'] + $this->defaultTimeOut) < time()) {
            T(21130025);
        }
        $userinfo = AuthUserModel::getOne($data['GreetUserID'], 'all');
        $auditRecord = GuestSelfApplyModel::getSingle(['ID' => $data['ID'], 'column' => 'auditrecord']);
        if (is_array($auditRecord)) {
            if ($auditRecord['Status'] != '0') {
                T(21130026);
            }
        } else {
            T(21130025);
        }
        $auditRecord['GreetUserID'] = $data['GreetUserID'];
        $auditRecord['GreetUserRoleID'] = $userinfo['RoleID'] ?? 1;
        return $auditRecord;
    }

    /**
     * 邮件审批接口
     * @throws Exception
     */
    public function audit()
    {
        $aGuestAuth = DictModel::getAll("GUESTAUTH");
        $userinfo = AuthUserModel::getOne($this->params['UserID'], 'all');


        $resultOnLine = NacOnLineDeviceModel::getOne($this->params['deviceId'], 'name');
        if (empty($resultOnLine)) {
            T(21130010);
        }
        if (!$this->messageCheck($this->params['deviceId'])) {
            T(21130034);
        }

        /* 检查安全域是否合法 */
        $regionList = (new GuestAuthService($this->params))->isGusetAllocation($this->params['deviceId'], $this->params['AllowRegionIDs']);
        if (!$regionList) {
            T(21126040);
        }


        $userName = $userinfo['TrueNames'] ?? '';
        $approveUserName = $userinfo['UserName'] ?? '';
        $UserType = $userinfo['Type'] ?? '';
        $result = GuestSelfApplyModel::getOne($this->params['guestselfId'], '*');

        if (!empty($result['UserID']) || strtotime($result['InsertTime']) + $this->defaultTimeOut < time()) {
            if ($result['Status'] == "0") {
                $gparams = [
                    'FromType' => L(21130012),
                    'ApproveName' => $userName, 'ApproveUserName' => $approveUserName,
                    'ApproveTime' => 'now()', 'ApproveType' => $UserType
                ];
                $guestMessage = $this->getGuestMessage($this->params['deviceId'], $this->params['UserID']);
                $gparams = array_merge($gparams, $guestMessage);
                $reamrk = "";
                if ($this->params['state'] == 1) {
                    $reamrk = L(21130014, ['userName' => $userName, 'date' => date('Y-m-d H:i:s', time())]);
                    $gparams['Status'] = 1;
                    $GuestRelationParams = ['AllowRegionIDs' => $this->params['AllowRegionIDs'], 'AllowTime' => $this->params['AllowTime'], 'MaxNumber' => 0];
                    GuestRelationModel::updateByUserId($result['UserID'], $GuestRelationParams);     // 更新来宾关系表

                    $ipListID = NetServiceProvider::getOtherRegionID($this->params['AllowRegionIDs']);
                    $guestOnlineParams=[
                        'IplistID'=>$ipListID,
                        'RegionIDs'=>$this->params['AllowRegionIDs']
                    ];
                    if (!empty($this->params['AllowTime'])) {
                        $guestOnlineParams['ExpirateTime'] = date("Y-m-d H:i:s", (int)(strtotime("now") + (float)$this->params['AllowTime'] * 3600));
                    }
                    GuestOnLineDeviceModel::updateById($result['UserID'], $result['DeviceID'], $guestOnlineParams);
                }
                GuestSelfApplyModel::update($this->params['guestselfId'], $gparams);
                AuthUserModel::update($result['UserID'], ['Remark' => $reamrk]);
                cutil_php_debug("approvalGuest success:," . $reamrk, 'SelfGuestSubmit');
            } else {
                cutil_php_debug("approvalGuest fail, had approval by " . $result['ApproveName'] . "", 'SelfGuestSubmit');
                T(21130026);
            }
        } else {
            T(21130025);
        }
        return ["state" => "OK", "Status" => $this->params['state']];
    }

    /*
     * 获取审核人或者设备信息
     * param deviceID $userId
     * return array
    */
    public function getGuestMessage($deviceId, $userId)
    {
        $gparams = [];
        if(empty($deviceId)) {
            return $gparams;
        }
        if (!empty($userId)) {
            $messageInfo = AuthUserModel::getOne($userId, 'ztpuser');
            $gparams['AuditType'] = 'user';
            $gparams['AuditUserID'] = $userId;
            $gparams['AuditUserName'] =$messageInfo['UserName'];
            $gparams['AuditUserDesc'] =$messageInfo['TrueNames'];
        } else {
            $messageInfo = DeviceModel::getOne($deviceId, 'info');
            $gparams['AuditType'] = 'device';
            $gparams['AuditUserID'] = $deviceId;
            $gparams['AuditUserName'] =$messageInfo['Mac'] ?? '';
            $gparams['AuditUserDesc'] =$messageInfo['DevName'] ?? '';
        }
        return $gparams;
    }

    // 获取设备安检情况
    public function messageCheck($deviceId)
    {
        $isCheck = true;
        $computer = \RelationComputerModel::getOne($deviceId, 'auth');
        if (!in_array($computer['CheckResult'], ['success', 'fault'])) {
            $isCheck = false;
        }
        return $isCheck;
    }
}
