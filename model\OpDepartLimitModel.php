<?php

/**
 * Description: 操作TOpDepartLimit表
 * User: <EMAIL>
 * Date: 2021/04/11 23:07
 * Version: $Id: OpDepartLimitModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class OpDepartLimitModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TOpDepartLimit';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'depart' => 'DepartID'
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        if (isset($cond['DepartID'])) {
            $where .= "AND DepartID = ".self::setData($cond['DepartID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
