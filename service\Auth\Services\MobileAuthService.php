<?php
/**
 * Description: 验证码方式：包括手机验证码、邮箱验证码
 * User: <EMAIL>
 * Date: 2021/2/18
 * Version: $Id: MobileAuthService.php 161644 2021-11-12 13:26:30Z duanyc $
 */

namespace Services\Auth\Services;

use DeviceModel;
use DictModel;
use Exception;
use hlp_check;
use Services\Auth\Interfaces\AuthServiceInterface;
use SmsCodeModel;

class MobileAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = AUTH_TYPE_MOBILE;

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['userName'] = $this->params['mobilePhone'];
        $isEmail = hlp_check::checkRuleResult('Email', $this->params['mobilePhone']);
        if ($isEmail) {
            $this->userType = AUTH_TYPE_EMAIL;
            $this->params['email'] = $this->params['mobilePhone'];
            $this->params['authType'] = 'Mailbox';
        }
        $this->params['currUserID'] = $this->params['currUserID'] ?? '';
        return $this->params;
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);
        $GetSmsInfo = DictModel::getAll("SMS"); //SMS信息
        if ($GetSmsInfo['ValidRegisteredPhoneToDev'] == 1) {
            //将手机号同步到设备上
            DeviceModel::update($this->deviceId, ['Tel' => $this->params['mobilePhone']]);
        }
        $cond = ['Tel' => $this->params['mobilePhone'], 'CheckCode' => $this->params['checkCode']];
        SmsCodeModel::updatePatch($cond, ['UseTime' => 'now()', 'Flag' => STRING_TRUE]);
    }

    /**
     * 附加认证后处理
     */
    public function addAuthAfter(): void
    {
        parent::addAuthAfter();
        $cond = ['Tel' => $this->params['mobilePhone'], 'CheckCode' => $this->params['checkCode']];
        SmsCodeModel::updatePatch($cond, ['UseTime' => 'now()', 'Flag' => STRING_TRUE]);
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'mobile_phone', 'check_code'];
    }

    /**
     * 获取认证的字段
     * @return array
     */
    public function getAuthColumns()
    {
        return ['authType', 'mobilePhone', 'email', 'checkCode', 'currUserID'];
    }
}
