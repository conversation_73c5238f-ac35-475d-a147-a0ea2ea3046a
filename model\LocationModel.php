<?php

/**
 * Description: 位置TLocation表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: LocationModel.php 162689 2021-11-24 03:13:59Z duanyc $
 */

class LocationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TLocation';
    public const PRIMARY_KEY = 'LocationID';
    protected static $columns = [
        'one' => 'UpID',
        'list' => 'LocationID,UpID,Location',
        '*'   => '*',
    ];

    /**
     * 单条
     *
     * @param string $Location
     * @param string $Column
     *
     * @return mixed
     */
    public static function getOneByLocation($Location, $Column = '*')
    {
        if (empty($Location) || empty($Column)) {
            return false;
        }

        self::$data = [];
        $column = static::$columns[$Column];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE Location = ".self::setData($Location);
        $sql = "SELECT {$column} FROM {$table['name']} {$where} ";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }

    /**
     * 根据LocationID获取LocationName
     * @param int $locationID
     * @return mixed|string
     */
    public static function getLocationNameByLocationID(int $locationID)
    {
        if ($locationID) {
            $res = self::getSingle(['LocationID' => $locationID, 'column' => 'list']);
            $locationName = $res['Location'] ?? '';
        } else {
            $res = \DictModel::getOneItem('ADMINISTRATORINFO','CompanyName');
            $locationName = $res['ItemValue'] ?? '';
        }

        return $locationName;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['LocationID'])) {
            $where .= "AND LocationID = ".self::setData($cond['LocationID']);
        }

        if (isset($cond['UpID'])) {
            $where .= "AND UpID = ".self::setData($cond['UpID']);
        }

        if (!empty($cond['Keyword'])) {
            $DepartName = "%{$cond['Keyword']}%";
            $where .= "AND Location LIKE ".self::setData($DepartName);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
