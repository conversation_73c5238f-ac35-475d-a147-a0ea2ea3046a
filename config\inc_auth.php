<?php
/**
 * Description: 认证配置
 * User: <EMAIL>
 * Date: 2021/9/23 15:54
 * Version: $Id$
 */

/* 认证配置 配置文件 */
$GLOBALS['AUTH_CONFIG'] = [
    // key: authType, value: servicePrefix 根据传的type值得到服务前缀，没有配置则为传的type值
    'TypeService'      =>  [
        'NoAuth'      => 'No',
        'SMS'         => 'Sms',
        'ADAutoLogin' => 'AdAuto',
        'Qrcode'      => 'QrCode',
    ],
    // 映射
    'AuthType'  => [
        'ADAutoLogin' => 'AdDomain'
    ],
    // key: 第三方方式, value: servicePrefix
    'OtherService'      => [
        'wework'    => 'WeWork',
        'dingtalk'  => 'DingTalk',
        'feishu'    => 'FeiShu'
    ],
    'AuthUserType' => [
        'User'     => L(21120014),
        'AdDomain' => L(21120015),
        'LDAP'     => L(21120016),
        'Radius'   => L(21120017),
        'UKey'     => L(21120018),
        'UMac'     => L(21120019),
        'Email'    => L(21120020),
        'WebAuth'  => L(21120022),
        'Guest'    => L(21120021),
        'Finger'   => L(21120023),
        'DingTalk' => L(21120025),
        'WeWork'   => L(21120026),
        'FeiShu'   => L(21120042),
        'Mobile'   => L(21120027),
    ],
    // 认证接口必须要的字段
    'RequireColumns'      =>  [
        'ID',
        'UserID',
        'DeviceID',
        'UserName',
        'RoleID',
        'Token',
        'TokenTimestamp',
        'Tel',
        'User',
        'EMail',
        'AuthType',
        'ZtpUser',
        'DepartID',
        'DepartName',
        'AuthTime',
        'FactorAuth',
        'BindAuth',
        'BeforeAuthType',
        'AuthInterval',
        'IsAgainReg',
        'LastAuthID',
        'SubmitKey',
        'Registered',
        'ReRegReason',
        'webKeepalive',
        'code'
    ],
    // 认证接口可选的字段
    'OptionalColumns'      =>  [
        'pwdCheckResCode',
        'pwdCheckResMsg',
        'guestRelation',    // 来宾使用
        'guestSelfID',      // 来宾使用
        'IsRoleChange',     // windows等客户端还在用
        'CiscoAccess',      // windows等客户端还在用
        'IsOpenSDCLinkage',      // 客户端SDC配置
        'guestAccessType', //来宾使用
        'IsNeedAudit'
    ]
];
