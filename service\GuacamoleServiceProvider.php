<?php
/**
 * Description: Guacamole相关逻辑service
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: GuacamoleServiceProvider.php 153494 2021-08-19 01:25:07Z duanyc $
 */

class GuacamoleServiceProvider extends BaseServiceProvider
{
    /**
     * 服务层日志
     * @var string
     */
    public static $logFile = 'guacamole';

    /**
     * 保存session
     *
     * @param $data
     * @param $cond
     *
     * @return bool|int
     */
    public static function saveSession($data, $cond = [])
    {
        if (!empty($cond)) {
            return GuacamoleSessionModel::updatePatch($cond, $data);
        }
        $data['UpdateTime'] = 'now()';
        return GuacamoleSessionModel::insert($data);
    }

    /**
     * 更新心跳数据
     *
     * @param $TunnelIds
     *
     * @return bool|array
     */
    public static function updateHeart($TunnelIds)
    {
        if (empty($TunnelIds)) {
            return false;
        }
        $cond = ['InTunnelID' => $TunnelIds, 'column' => 'list'];
        $list = GuacamoleSessionModel::getList($cond, false, 0, 10000);
        if (empty($list)) {
            return false;
        }
        foreach ($list as $key => $row){
            $session = SessionRedis::getOne($row['Token'], 'policy');
            $updateData = [];
            $list[$key]['FinishReason'] = '';
            self::log("resIds:{$row['ResID']};{$row['Token']}", 'DEBUG');
            if (empty($session)) {
                self::setFinished($list[$key], $updateData, L(21148023));
            } else {
                $AuthAppIDs = isset($session['AuthAppID']) ? json_decode($session['AuthAppID'], true) : [];
                if (!in_array($row['ResID'], $AuthAppIDs, false)) {
                    self::setFinished($list[$key], $updateData, L(21148001));
                } else {
                    $Reason = self::checkResStatus($session, $row);
                    if (!empty($Reason)) {
                        self::setFinished($list[$key], $updateData, $Reason);
                    }
                }
            }
            $updateData['UpdateTime'] = 'now()';
            if (!empty($updateData)) {
                self::log('GuacamoleSession update:' . var_export($updateData, true));
                GuacamoleSessionModel::update($row['GuacSessID'], $updateData);
            }
        }
        return $list;
    }

    /**
     * 检查资源信息变更状态和策略状态
     *
     * @param $session
     * @param $guacamoleSession
     *
     * @return bool|string
     */
    private static function checkResStatus($session, $guacamoleSession)
    {
        try {
            if ($guacamoleSession['ResTag'] === 'rdp-app') {
                $resInfo = ResRemoteModel::getRelationOne($guacamoleSession['ResID'], 'guacamole');
            } else {
                $resInfo = ResConfigListModel::getOne($guacamoleSession['ResID'], 'guacamole');
            }
            if (empty($resInfo)) {
                T(21148024);
            }
            $resInfo['RealProtocol'] = hlp_net::getProxyProtocol($resInfo['AccessTypeID']);
            $columns = ['Protocol' => 'RealProtocol', 'DesAddr' => 'DomainName', 'DesPort' => 'RealPort'];
            foreach ($columns as $key => $val) {
                if ($guacamoleSession[$key] !== $resInfo[$val]) {
                    T(21148035);
                }
            }
            $PolicyID = PolicyServiceProvider::getPolicyID($guacamoleSession['ResID']);
            if (!empty($PolicyID)) {
                PolicyServiceProvider::checkPolicy($session, $PolicyID);
            } else {
                PolicyServiceProvider::checkNoPolicy($session, [$guacamoleSession['ResID']]);
            }
        } catch (Exception $e) {
            return $e->getMessage();
        }
        return false;
    }

    /**
     * 设置完成数据
     *
     * @param $listRow
     * @param $updateData
     * @param $FinishReason
     */
    private static function setFinished(&$listRow, &$updateData, $FinishReason): void
    {
        $listRow['IsFinished'] = "true";
        $listRow['FinishReason'] = $FinishReason;
        $updateData = ['IsFinished' => $listRow['IsFinished'],
                       'FinishReason'=> $listRow['FinishReason']];
    }

    /**
     * 检查心跳
     */
    public static function checkHeart(): void
    {
        $LtUpdateTime = date('Y-m-d H:i:s', time() - 540);
        $cond = ['IsFinished' => 'false', 'LtUpdateTime' => $LtUpdateTime, 'column' => 'heart'];
        $list = GuacamoleSessionModel::getList($cond, false, 0, 10000);
        if (!empty($list)) {
            foreach ($list as $row) {
                $updateData = ['IsFinished' => 'true', 'FinishReason' => '长时间未检测到心跳'];
                $updateData['DateEnd'] = date('Y-m-d H:i:s');
                $updateData['LinkLen'] = strtotime($updateData['DateEnd']) - strtotime($row['DateStart']);
                GuacamoleSessionModel::update($row['GuacSessID'], $updateData);
            }
        }
    }

    /**
     * @param array $cond
     * @return array|false
     */
    public static function getInfo($cond = [])
    {
        $info = GuacamoleSessionModel::getSingle($cond, false);
        if (!empty($info)) {
           return $info;
        }
        return false;
    }
}
