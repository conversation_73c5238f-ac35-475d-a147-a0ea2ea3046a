<?php

/**
 * Description: 动态策略主表
 * User: <EMAIL>
 * Date: 2022/09/26 10:32
 * Version: $Id
 */

class DynamicSafeResultModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDynamicSafeResult';
    public const PRIMARY_KEY = 'ResultID';
    protected static $columns = [
        'one' => 'ResultID,DeviceID,UserId,DPolicyID,Result,CalculateTime,IsNeedMatch,Status',
        'res' => 'ResultID,DeviceID,UserId,TDynamicSafeResult.DPolicyID,TResPolicyRelation.ResID',
        '*' => '*',
    ];


    /**
     *
     * 根据策略ID匹配上的设备ID获取对应的资源权限信息
     * @param array $cond
     * @param string $columnName
     * @return mixed|null
     */
    public static function getResIdsByDynamicSafeResult(array $cond, $columnName = 'res')
    {
        // 设备ID如果不存在，则返回false
        if (intval($cond['DeviceID']) <= 0) {
            return false;
        }
        // 查询
        self::$data = [];
        $column = static::$columns[$columnName];
        $where = "LEFT JOIN TDynamicSafePolicyRelation ON TDynamicSafeResult.DPolicyID = TDynamicSafePolicyRelation.DPolicyID 
                  LEFT JOIN TResPolicyRelation ON TResPolicyRelation.PolicyID = TDynamicSafePolicyRelation.PolicyID WHERE TDynamicSafeResult.DeviceID = " . self::setData($cond['DeviceID']);
        $sql = "SELECT {$column} FROM TDynamicSafeResult {$where}";
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['ResultID'])) {
            $where .= " AND ResultID = " . self::setData($cond['ResultID']);
        }

        if (isset($cond['DPolicyID'])) {
            $where .= " AND DPolicyID = " . self::setData($cond['DPolicyID']);
        }

        if (isset($cond['DeviceID'])) {
            $where .= " AND DeviceID = " . self::setData($cond['DeviceID']);
        }

        if (isset($cond['IsNeedMatch'])) {
            $where .= " AND IsNeedMatch = " . self::setData($cond['IsNeedMatch']);
        }

        if (isset($cond['NoDelStatus'])) {
            $where .= " AND Status > 0";
        }
        if (isset($cond['InDeviceIDs'])) {
            $where .= " AND DeviceID IN (" . self::setArrayData($cond['InDeviceIDs']) . ") ";
        }

        if (isset($cond['CalculateTime'])) {
            $where .= " AND CalculateTime < " . self::setData($cond['CalculateTime']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
