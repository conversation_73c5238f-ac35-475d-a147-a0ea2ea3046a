<?php

/**
 * Description: 用户认证TUserLogin表
 * User: <EMAIL>
 * Date: 2022/05/16 16:42
 * Version: $Id: UserLoginModel.php 158181 2022-05-16 16:42:13Z chenpan $
 */

class UserLoginModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUserLogin';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['AuthAccount'])) {
            $where .= "AND AuthAccount = ".self::setData($cond['AuthAccount']);
        }
        if (isset($cond['IP'])) {
            $where .= "AND IP = ".self::setData($cond['IP']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
