<?php

/**
 * Description: 高级动态认证记录表
 * User: <EMAIL>
 * Date: 2021/08/24 15:53
 * Version: $Id: ResourceAuthLogModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class ResourceAuthLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResourceAuthLog';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        if (isset($cond['UserName'])) {
            $where .= "AND UserName = ".self::setData($cond['UserName']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
