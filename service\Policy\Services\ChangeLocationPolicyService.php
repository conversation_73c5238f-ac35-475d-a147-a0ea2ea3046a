<?php
/**
 * Description: 零信任访问地址变动策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;


use Services\Policy\Interfaces\PolicyServiceInterface;

class ChangeLocationPolicyService extends BasePolicyService implements PolicyServiceInterface
{
    /**
     * 检查是否通过
     *
     * @return bool
     */
    public function check(): bool
    {
        if ($this->params['Config']['IsOpen'] == 0 || !isset($this->params['session']['Token'])) {
            return true;
        }
        $ip = $this->getClientIp(true);
        $aCity = \lib_ipregion::memorySearch($ip);
        if (isset($aCity['region'])) {
            $arr = explode("|", $aCity['region']);
            $provinces = $arr[2] ?? "";
            $city = $arr[3] ?? "";
            $providers = $arr[4] ?? "";
            if ($providers === '内网IP') {
                return true;
            }
            $token = $this->params['session']['Token'];
            $uuid = $this->params['session']['Uuid'];
            $lockTime = \lib_redis::get('ASG_', 'ChangeLocationLock:' . $uuid);
            if ($lockTime && $lockTime > time()) {//还在被禁止访问时间内
                return false;
            }

            if ($lockTime && $lockTime < time()) {//超出被禁止时间 ，手动删除redis key  虽然设置了redis过期时间
                \lib_redis::del('ASG_', 'ChangeLocationLock:' . $uuid);
            }

            $redisCity = \lib_redis::get('ASG_', 'ChangeLocation:' . $token);
            $expire = $this->params['Config']['LimitTime'] * 60;
            if (!$redisCity) {
                \lib_redis::set('ASG_', 'ChangeLocation:' . $token, $provinces . '-' . $city, $expire);
            }

            if ($redisCity && $redisCity !== $provinces . '-' . $city) {
                \lib_redis::set('ASG_', 'ChangeLocation:' . $token, $provinces . '-' . $city, $expire);
                $flag = $this->_limit('ChangeLocationSort:' . $token, $this->params['Config']['LimitNum'], $expire);
                if (!$flag) {
                    //锁定多少分钟
                    $lockTime = $this->params['Config']['Action'] * 60;//分钟转为秒
                    \lib_redis::set('ASG_', 'ChangeLocationLock:' . $uuid, time() + $lockTime, $lockTime);
                    return false;
                }
            }

        }
        return true;
    }
}
