<?php

/**
 * Description: 补丁更新记录表
 * User: <EMAIL>
 * Date: 2021/08/03 10:02
 * Version: $Id
 */

class UpdateDetailModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUpdateDetail';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取补丁详情.
     *
     * @param array $cond
     * @return false|mixed|null
     */
    public static function getDetail($cond = [])
    {
        if (!isset($cond['islastbundle'], $cond['revisionid'])) {
            return false;
        }
        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        //判断是否为顶级补丁
        $where = (int)$cond['islastbundle'] === 0 ? " A.LastBundleRevID = 0 " : " A.LastBundleRevID != 0 ";
        $sql = "SELECT A.RevisionID, A.Title, A.Description, A.UpdateTime, B.ItemValue AS UpdateType, 
							func_getPatchKB(A.<PERSON>undleRevID, A.RevisionID) AS KBIDS, 
							(Case When C.DownURL IS Null Then '' Else C.DownURL END) AS DownURL, 
							C.InstallCommand_Arguments     
					FROM TUpdateDetail A LEFT JOIN TDict B ON A.UpdateType = B.ItemName 
					LEFT JOIN TUpdateFile C ON C.RevisionID = A.RevisionID 
					WHERE {$where} AND A.RevisionID = ".self::setData($cond['revisionid']);
        cutil_php_log($sql, 'model_select');
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";


        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
