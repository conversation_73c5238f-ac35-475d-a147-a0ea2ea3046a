<?php

/**
 * Description: 注册相关
 * User: <EMAIL>
 * Date: 2021/06/24 10:02
 * Version: $Id: RegisterController.php 159460 2021-10-20 09:50:32Z duanyc $
 */

!defined('IN_INIT') && exit('Access Denied');
include PATH_CONTROL . "/BaseController.php";

class RegisterController extends BaseController
{
    /**
     * 设备自动/手动注册，对应老交易 regdevsubmit.
     *
     * @return array
     * @throws Exception
     */
    public function submit()
    {
        $params = [];
        $params['sceneId'] = request('sceneId', 'request', 0, 'int');
        $params['deviceId'] = request('device_id', 'request', 0, 'int');
        $params['departId'] = request('depart_id', 'request', 0, 'int');
        $params['childDepartId'] = request('child_depart_id', 'request', 0, 'int');
        $params['username'] = request('username', 'request', '', 'origin');
        $params['tel'] = request('tel', 'request');
        $params['locationId'] = request('positions', 'request', 0, 'int');
        $params['childInputPosition'] = request('child_input_position', 'request');
        $params['customLocationInput'] = request('custom_location_input', 'request');
        $params['remark'] = request('remark', 'request');
        $params['isGuest'] = request('is_guest', 'request');
        $params['email'] = request('email', 'request');
        $params['deviceType'] = request('device_type', 'request');
        $params['newMobile'] = request('NewMobile', 'request', 0, 'int');
        $params['newHard'] = request('newHard', 'request');
        $params['deviceTypeExt'] = request('device_type_ext', 'request');
        if (!empty($params['childDepartId'])) {
            $params['departId'] = $params['childDepartId'];
        }

        // luopc 修复认证时设备已设置部门注册时被改回根部门
        if ($params['departId'] == 0) {
            $device = \DeviceModel::getOne($params['deviceId'], 'info');
            if (isset($device['DepartId'])) {
                $params['departId'] = $device['DepartId'];
            }
        }

        for ($i = 1; $i <= 10; $i++) {
            $params['requireexpand'.$i] = request('requireexpand'.$i, 'request', '', 'array');
            if (is_array($params['requireexpand'.$i])) {
                $params['requireexpand'.$i] = implode(',', $params['requireexpand'.$i]);
            }
        }
        $service = DeviceServiceProvider::initDeviceRegisterService();
        return $service->submitInfo($params);
    }

}
