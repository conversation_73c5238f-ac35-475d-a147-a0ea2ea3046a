<?php

/**
 * Description: 设备断网记录表
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: CutNetDeviceModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class CutNetDeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TCutNetDevice';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
