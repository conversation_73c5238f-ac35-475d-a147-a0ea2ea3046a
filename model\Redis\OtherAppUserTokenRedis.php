<?php

/**
 * Description: 存储一些需要权限计算的token，比如微应用相关的，不走正常入网逻辑的但需要权限计算的token
 * User: <EMAIL>
 * Date: 2024/05/16 10:32
 * Version: $Id$
 */

class OtherAppUserTokenRedis extends BaseZsetRedis
{
    
    /**
     * 表名前缀
     *
     * @var string
     */
    public const PREFIX = 'ASM_';

    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'OtherAppUserToken';

    /**
     * 是否同步到ASG
     *
     * @var string
     */
    protected static $remote = false;

    /**
     * 计算个数
     *
     * @param mixed ...$keys
     * @return float|null
     */
    public static function count(...$keys): ?float
    {
        return self::zCard(...$keys);
    }

    /**
     * 添加元素到有序集
     * @param $score
     * @param $value
     * @param ...$keys
     * @return mixed|null
     */
    public static function zAddMember($score, $value, ...$keys)
    {
        return self::zAdd($score, $value, ...$keys);
    }


    /**
     * 从有序集中删除元素
     *
     * @param $member
     * @param array $keys
     *
     * @return false|int|Redis|null
     * @throws RedisException
     */
    public static function zRemMember($member, ...$keys)
    {
        return self::zRem($member, ...$keys);
    }

    /**
     * 获取对应的有序集合中的列表
     *
     * @param int $start
     * @param int $stop
     * @param array $keys
     *
     * @return mixed
     */
    public static function zRangeData(int $start, int $stop, ...$keys)
    {
        return self::zRange($start, $stop, ...$keys);
    }

    /**
     * 删除
     *
     * @param string $key
     * @return boolean
     */
    public static function delData(string $key = ''): bool
    {
        return self::del($key);
    }

}