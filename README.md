## 入网流程API
### 一、编码规范
#### 控制器control层不能直接调用数据model层；
#### 参数接收必须放在控制control层；
#### sql语句只能放在数据model层；
#### 所有类的方法使用驼峰方式命名；
#### 业务简单的只需在service根目录提供业务方法，业务复杂的需建目录提供复杂逻辑处理，如service/Auth目录
#### 使用第三方库在library目录下提供类暴露方法，不要直接调用。
#### 写日志时，cutil_php_log方法的第一个参数为任意数据类型，方法内会自动转成字符串，第二个参数为文件名前缀，不要文件名后缀（如不要.txt）。
#### 所有路径使用PATH_前缀的常量，如PATH_HTML、PATH_ETC等。
#### 程序内部的错误码用1开头的，如11100001，用户看的用2开头的，如21138002。
## 
### 二、使用说明
#### 安装依赖：composer install
#### 更新自动加载的新增类：composer dump-autoload
#### .idea目录、vendor目录和composer.lock需加入忽略列表，不要提交到svn
#### 查询该模块日志 tail -f /tmp/logs/access_*
#### 兼容处理要在/etc/nginx/module/compatible.conf加配置，并且在access.asm.com/config/inc_route.php加配置，即可用老的入网流程跑业务测试新接口。
#### 项目下的data/api/ASM.postman_collection.json文件，可导入到postman进行接口测试。
## 
### 三、接口说明
### a、输入说明
###1. 请求地址格式：http://asmip/$module/$api_version/$os_type/$data_type/$mod/$tradecode
###2. $module表示业务模块，入网流程：access
###3. $api_version表示api版本号，目前1.0
###4. $os_type表示设备类型，可传设备类型：ios|android|windows|mac|linux|server.
##### ios表示苹果手机/iPad终端
##### android表示安卓设备终端
##### windows表示windows系统
##### mac表示MAC系统终端
##### linux表示所有linux操作系统终端
##### server表示服务器本地
###5. $data_type表示数据格式，默认json，兼容之前的php_to_js对象（传object）
###6. $mod表示模块（控制器），如device
###7. $tradecode表示交易（方法），infoprocess表示控制器内的方法
###例子：************/access/1.0/windows/json/net/auth
###8. 公共参数

|参数名|必选|类型|说明|
|:----    |:---|:----- |-----   |
|local_lguage_set |是  |string |语言【zh，cn】   |
|cache |是  |string | 时间戳，csrf校验    |

### b、输出说明：
###1. 所有接口返回errcode：0 代表返回成功
###2. 所有接口返回errmsg，默认值'ok',当errcode为0时，表示成功提示内容；当errcode不为0时，表示错误提示内容。
###3. 所有接口errcode为0时，返回data字段。放置数据内容。
###成功时返回例子
    {
    	"errcode": "0",
    	"errmsg": "ok",
    	"data": {
    		"token": "96fc7a7axxx"
    	}
    }
###失败时返回例子
    {
    	"errcode": "10000000",
    	"errmsg": "参数错误"
    }

### c、CSRF校验
###1. reqeust的cache参数为10位的时间戳，1个小时的过期时间。
![](http://172.22.46.55:8088/server/../Public/Uploads/2021-05-11/6099e80c02b5f.png)
###2. header头的SESSION-NUM参数为根据url计算出来的token，js参考算法（postman可用）代码如下：
![](http://172.22.46.55:8088/server/../Public/Uploads/2021-05-11/6099e81c48fdc.png)

		var timestamp = parseInt(new Date().getTime()/1000);
		pm.globals.set("Timestamp", timestamp);
		var url = request.url.replace(/http\:\/\/\d+(\.\d+){3}/, "");
		url = decodeURIComponent(url.replace(/\{\{Timestamp\}\}/, timestamp));
		url = url.replace(/[^0-9a-zA-Z\=\&\(\)\/\.\?\_\'\"<>]/g, "")
		var md5 = calcMd5(url);
		pm.globals.set('SESSION_NUM', md5);

		console.log(md5);

		function calcMd5(url) {
			if (JSON.stringify(request.data) != "{}") {
				var paramStr = [];
				for(var key in request.data) {
					var val = request.data[key];
					if (key === 'GET_SESSION_NUM') {
						continue;
					}
					paramStr.push(key + '=' + val);
				}
				url += "&"+decodeURIComponent(paramStr.join('&'));
			}
			console.log(url);
			var md5 = CryptoJS.MD5(url).toString().toLowerCase();
			return md5;
		}
		
###3. 客户端调用接口时，header头的Referer参数为http://172.20.23.101/$os_type/asm。其中$os_type表示设备类型，可传设备类型：ios|android|windows|mac|linux，接口有些逻辑用来判断是否客户端调用，和csrf的referer检查使用。

### 四、rpc的api说明
#### 开发规范
    提供服务方，方法必须为protected，否则会导致数据格式未包含state和message字段，如下例子：
    class NetController extends AsmYarServer
    {
   
        /**
         * 放开网络
         *
         * @param $params
         *
         * @return mixed
         * @throws Exception
         */
        protected function allowaccess($params)
        {
            
        }
        
    通用http状态码：
    参数缺失（422） 内部错误（500以上）  403（未鉴权） 401（csrf）  404（nginx）

#### 原web的rpc接口
    http://************/api/Rpc/?tradecode=Net
    
#### access.asm.com的rpc接口
    http://************/access/rpc/dasm
    
### 五、引入私有包原则
> 开发测试周期内可以用分支模式，发布之后需要切换到tag模式
>
分支引入
```
// dev 表明是开发环境 main为分支名称
"asm-web/service-sdk": "dev-main",
"asm-web/php-common": "dev-main",
```

标签引入
```
// dev 表明是开发环境 master为分支名称 # 分隔符 v0.5.0为tag标签
dev-master＃v0.5.0
```

### 六、接口单元测试
> php $AsmRootPath/var/www/access.asm.com/webroot/access/index.php
```
绿色表示通过，红色表示不通过。
```
#### 1、测试数据
> 测试数据存储在/data/test/目录下，每个控制器一个json文件，以方法名为索引，每个方法支持多个数据测试。
```
request中为请求的所有字段。
reponse中的字段值为require时，表示只要求结果返回有该字段即可。否则表示字段的值为指定值才可通过。
```
#### 2、命令行参数
```
php /opt/SJRJ/InG/var/www/access.asm.com/webroot/access/index.php -test
    -test 表示开启测试【必选项】
    -debug 表示调试，会打印详细输入数据，返回数据。【可选项】
    -ct [控制器名] 表示测试的控制器【可选项】
    -ac [方法名] 表示测试的控制器的方法【可选项】
    -ip [服务器IP] 表示测试指定服务器，否则测试本地127.0.0.1【可选项】

php /opt/SJRJ/InG/var/www/access.asm.com/webroot/access/index.php -test -ip [*************]
php /opt/SJRJ/InG/var/www/access.asm.com/webroot/access/index.php -test -ct [auth]
php /opt/SJRJ/InG/var/www/access.asm.com/webroot/access/index.php -test -ct [auth] -ac [index]
php /opt/SJRJ/InG/var/www/access.asm.com/webroot/access/index.php -test -ct [auth] -ac [index] -debug
```