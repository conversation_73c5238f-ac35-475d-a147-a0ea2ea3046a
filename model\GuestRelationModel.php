<?php

/**
 * Description: 来宾用户关联表
 * User: <EMAIL>
 * Date: 2022/10/15 10:32
 * Version: $Id: GuestRelationModel.php 166879 2022-01-11 04:50:15Z duanyc $
 */

class GuestRelationModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TGuestRelation';
    public const PRIMARY_KEY = 'UserID';
    protected static $columns = [
        'all'    => '*',
        'one'   => 'ID,UserID,GustCodeType,AllowRegionIDs,MaxNumber,AllowTime,IsNeedAudit,GuestStartTime,GuestEndTime',
        'batch'   => 'ID,UserID,TeamName,GuestEndTime,MaxNumber,ReceptionMaxNumber',
        'unused'   => 'ID,GustCodeType,UserID,TeamName,Name,Tel,GuestStartTime,GuestEndTime,MaxNumber,ReceptionMaxNumber',
        'guestinfo'   => 'AllowTime,IsNeedAudit,GuestStartTime,GuestEndTime,Name,Unit,Tel,Remark,StaffName,StaffTel,GuestExpand_1,GuestExpand_2,GuestExpand_3,GuestExpand_4,GuestExpand_5',
        '*'    => '*'
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        if (isset($cond['UserID'])) {
            $where .= "AND UserID = ".self::setData($cond['UserID']);
        }
        if (isset($cond['Tel'])) {
            $where .= "AND Tel = ".self::setData($cond['Tel']);
        }
        if (isset($cond['GustCodeType'])) {
            $where .= "AND GustCodeType = ".self::setData($cond['GustCodeType']);
        }
        if (isset($cond['GreetUserID'])) {
            $where .= "AND GreetUserID = ".self::setData($cond['GreetUserID']);
        }
        if (isset($cond['GustCodeType'])) {
            $where .= "AND GustCodeType = ".self::setData($cond['GustCodeType']);
        }
        if (isset($cond['GuestStartTime'])) {
            $where .= "AND GuestStartTime < now()";
        }
        if (isset($cond['GuestEndTime'])) {
            $where .= "AND GuestEndTime > now()";
        }
        if (isset($cond['MaxNumber'])) {
            $where .= "AND MaxNumber >= ".self::setData($cond['MaxNumber']);
        }
        if (isset($cond['DeviceId'])) {
            $where .=" AND DeviceId = ".self::setData($cond['DeviceId']);
        }
        if (isset($cond['IsCancel'])) {
            $where .= "AND IsCancel = " . self::setData($cond['IsCancel']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }


    /**
     * 修改
     *
     * @param string $userId
     * @param array $key_values
     *
     * @return boolean
     */
    public static function updateByUserId($userId, $key_values = [])
    {
        if (empty($userId) || empty($key_values)) {
            return false;
        }

        self::$data = [];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = " UserID = ".self::setData($userId);
        cutil_php_log(json_encode(['model_update', $table, $where, self::$data, $key_values]), "model_{$table['name']}");
        return lib_database::update($key_values, $where, $table['name'], $table['index'], self::$data);
    }

}
