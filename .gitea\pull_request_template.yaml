name: 代码合并
description: 发起代码合并
labels: ["kind/feature", "kind/proposal"]
body:
  - type: markdown
    attributes:
      value: |
        注意: 合并代码请遵循以下规范
  - type: markdown
    attributes:
      value: |
        1. 功能未开发完毕不得发起合并请求.
        2. 功能开发完毕后请先完成自测, 通过后方可发起合并请求.
        3. 功能合入后修复Bug发起合并请正确选择合入类型.
  - type: dropdown
    id: kind
    attributes:
      label: 代码合并类型
      description: |
        请正确选择类型
      options:
        - "合入功能"
        - "Bug修复"
    validations:
      required: true
  - type: textarea
    id: description
    attributes:
      label: 描述
      description: |
        功能合入请详细描述实现原理, 关键代码写好注释.
        Bug修复请描述清楚Bug的产生的原因和如何修复的. (如果有禅道ID请附上)