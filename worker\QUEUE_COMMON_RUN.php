<?php

/**
 * Description: 认证成功队列
 * User: <EMAIL>
 * Date: 2024/04/24 15:53
 * Version: $Id: QUEUE_COMMON_RUN.php 156563 2024-04-24 15:04:16Z xielj $
 */

use Common\Facades\EventBrokerFacade;

if (!defined('IN_ACCESS')) {
    exit('Access Denied!');
}

if (PHP_SAPI != 'cli' || !isset($this) || !$this instanceof cls_queue) {
    exit();
}

/**
 * 任务主函数
 *
 * @param $params
 */
if (!function_exists('QUEUE_COMMON_RUN')) {
    /**
     * 认证成功处理
     *
     * @param $params
     *
     * @return bool
     * @throws Exception
     */
    function QUEUE_COMMON_RUN($params)
    {
        cutil_php_log("QUEUE_COMMON_RUN Start:" . var_export($params, true), 'QUEUE_COMMON_RUN');
        if (empty($params['run_func'])) {
            return false;
        }
        //前期固定
        switch ($params['run_func']) {
            case 'setUserPower':
                $ClearTokenRedisKey = 'setUserPower';
                //从队列中获取需要刷新权限的token，然后循环抛出
                while ((int)TokenRedis::lLen($ClearTokenRedisKey) > 0) {
                    $token = TokenRedis::rPopData($ClearTokenRedisKey);
                    if (!empty($token)) {
                        ResourceServiceProvider::setUserPower($token, 'QUEUE_COMMON_RUN');
                    } else {
                        break;
                    }
                }
                break;
            case 'makeClientInstallFile':
                NacClientServiceProvider::makeClientInstallFile($params);
                break;
            default :
                break;
        }
        return true;
    }
}

QUEUE_COMMON_RUN($this->params);
