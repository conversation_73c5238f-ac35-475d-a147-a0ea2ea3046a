
# phpunit单元测试
###1、composer.json的repositories节点后面增加require-dev节点配置：
    ,
    "require-dev": {
        "phpunit/phpunit": "7"
    }

###2、执行composer update

###3、本地部署配置文件

####把服务器上的/etc/目录的AsmCache.ini、AsmDb.ini、AsmDbHost.ini拷贝到c:/etc/目录，然后把配置文件的host改为真实服务器ip，服务器secadmin账号放开限制。

###4、phpstorm设置phpunit：
#####参考https://blog.csdn.net/qq_36085872/article/details/104212351
#####1）设置php版本：setting->Languages & Frameworks->php，设置为php7.3以上；
#####2）设置phpunit版本：setting->Languages & Frameworks->php->PHPUnit(Test Framework)，选择autoload.php，自动识别。

###5、run配置
##### Run菜单->Edit Configurations：添加PHPUnit