<?php



class CertificateListModel extends BaseModel
{
    public const TABLE_NAME = 'TCertificateList';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'       => '*',
        'list'   => 'ID,Path,KeyPath'
    ];

    /**
     * 获取新的资源信息,关联多个网关
     * **/

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";
        if (isset($cond['ID'])) {
            $where .= "AND ID = ".self::setData($cond['ID']);
        }
        if (isset($cond['Type'])) {
            $where .= "AND Type = ".self::setData($cond['Type']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
