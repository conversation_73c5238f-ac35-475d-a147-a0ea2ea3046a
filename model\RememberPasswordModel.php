<?php

/**
 * Description: 用户资源记住密码TRememberPassword表
 * User: <EMAIL>
 * Date: 2022/05/16 16:42
 * Version: $Id: UserLoginModel.php 158181 2022-05-16 16:42:13Z chenpan $
 */

class RememberPasswordModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TRememberPasswords';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     * @param bool $order
     * @return string
     */
    protected static function getWhere($cond = [], $order = false): string
    {
        $where = "";

        if (isset($cond['ID'])) {
            $where .= "AND ID = " . self::setData($cond['ID']);
        }

        if (isset($cond['ResId'])) {
            $where .= "AND ResId = " . self::setData($cond['ResId']);
        }

        if (isset($cond['UserId'])) {
            $where .= "AND UserId = " . self::setData($cond['UserId']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * 插入
     *
     * @param array $key_values
     *
     * @return int
     */
    public static function insert($key_values = [])
    {
        if (empty($key_values)) {
            return false;
        }

        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $result = lib_database::duplicate($key_values, $table['name'], $table['index']);
        cutil_php_log(json_encode(['model_insert', $table, $key_values]), "model_{$table['name']}");

        if ($result) {
            return lib_database::insertId();
        }

        return $result;
    }

}
