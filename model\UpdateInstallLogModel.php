<?php

/**
 * Description: 行为记录TUserActionRecord表
 * User: <EMAIL>
 * Date: 2021/08/03 10:02
 * Version: $Id
 */

class UpdateInstallLogModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TUpdateInstallLog';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        '*'    => '*',
        'maxID' => 'Max(ID) AS LastID',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
