<?php
/**
 * Description: 双机操作类
 * User: <EMAIL>
 * Date: 2021/05/21 15:46
 * Version: $Id: ha.func.php 148815 2021-07-02 08:37:32Z duanyc $
 */

if (!function_exists('getManagerIp')) {

    /**
     * 获取管理口IP
     * @return mixed
     */
    function getManagerIp()
    {
        $deveth = parse_ini_file(PATH_ETC . "deveth.ini.noback");
        $ha_dir = PATH_ETC . "ha.ini";
        $hainfo = parse_ini_file($ha_dir);
        if (isset($hainfo['Mode'])) {
            if ($hainfo['Mode'] === 'DUALHOST' && isset($hainfo['MANAGER_VIRTUAL_IP']) && filter_var($hainfo['MANAGER_VIRTUAL_IP'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                return $hainfo['MANAGER_VIRTUAL_IP'];
            }
        }
        return $deveth['managerip'];
    }

    /**
     * 获取内网入网交互地址
     * @return mixed
     */
    function getManagerAddress()
    {
        $access = parse_ini_file(PATH_ETC . "access.ini");
        return $access['ServerIPv4In'] ?? getManagerIp();
    }
}
