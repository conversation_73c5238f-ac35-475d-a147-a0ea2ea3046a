<?php
/**
 * Description: 零信任禁止位置策略项服务
 * User: <EMAIL>
 * Date: 2022/05/17 10:32
 * Version: $Id$
 */

namespace Services\Policy\Services;



class ForbidLocationPolicyService extends AllowLocationPolicyService
{
    /**
     * 检查是否通过
     *
     * @return bool
     */
    public function check(): bool
    {
        $ip = $this->getClientIp(true);
        $aCity = \lib_ipregion::memorySearch($ip);
        if (isset($aCity['region'])) {
            $arr = explode("|", $aCity['region']);
            $city = isset($arr[3]) && $arr[3] != "0" ? $arr[3] : "";
            $providers = isset($arr[4]) && $arr[4] != "0" ? $arr[4] : "";
            if ($providers === '内网IP') {
                return true;
            }
            foreach ($this->params['Config'] as $value) {
                if ( $value === $city) {
                    return false;
                }
            }
        }
        return true;
    }
}
