<?php

class SystemEngineModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TSystemEngine';
    public const PRIMARY_KEY = 'ID';
    protected static $columns = [
        'one'  => 'ID, Engine, ShowEngine',
        '*'    => '*',
    ];

    /**
     * 通过外部显示的版本号，获取真实的内部开发版本号。
     *
     * @param string $version
     * @return array|null
     */
    public static function getOneByShowEngine(string $version)
    {
        self::$data = [];
        $column = static::$columns['one'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $where = "WHERE ShowEngine = ".self::setData($version);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        return lib_database::getOne($sql, $table['index'], false, 1, self::$data);
    }
}