<?php
/**
 * Description: 设备信息服务
 * User: renchen
 * Date: 2020/1/8 10:00
 * Version: $Id: DeviceInfoService.php 163072 2021-11-26 04:13:05Z duanyc $.
 */

namespace Services\Device\Services;

use Exception;
use hlp_common;
use NoticeServiceProvider;
use lib_yar;
use Services\Common\Services\CommonService;
use Services\Common\Services\DepartService;
use Services\Common\Services\DeviceOperationService;
use Services\Device\Traits\DeviceInfoServiceIsExistTrait;
use Services\Device\Traits\DeviceInfoServiceResultTrait;
use Services\Device\Traits\DeviceInfoServiceRoamTrait;

class DeviceInfoService extends CommonService
{
    // 判断设备是否存在相关代码块
    use DeviceInfoServiceIsExistTrait;

    // 返回结果相关代码块
    use DeviceInfoServiceResultTrait;

    // 漫游相关代码块
    use DeviceInfoServiceRoamTrait;
    /**
     * 请求参数.
     *
     * @var array
     */
    public $params;

    /**
     * 设备id.
     *
     * @var int
     */
    public $deviceId;

    /**
     * 当前设备数据.
     *
     * @var array
     */
    public $deviceInfo;

    /**
     * 是否是新设备(在没装小助手的情况下).
     *
     * @var int
     */
    public $isNewDevice = 0;

    /**
     * 设备存在但是IP变了，根据此标识是否更新设备ip.
     * ps: 用户添加了交换机管理，没装小助手，切换ip的场景（正常应该是MVG会去更新ip）。
     * @var int
     */
    public $devExistButIPChange = 0;

    /**
     * 部门信息服务对象.
     *
     * @var DepartService
     */
    public $departService;

    /**
     * 设备id统一管理服务对象.
     *
     * @var DeviceOperationService
     */
    public $deviceOperationService;

    /**
     * @var string
     */
    protected $mac;

    /**
     * 是否漫游放开网络设备
     * @var int 0否 1是
     */
    public $isWalkDevice = 0;

    /**
     * 是否本地设备通过浏览器指纹放开网络（已入网、无客户端、本地设备，IP变动场景）
     * @var int 0否 1是
     */
    public $isBrowserFingerprintDevice = 0;

    /**
     * DeviceInfoService对象构造
     *
     * @param $params
     */
    public function __construct($params)
    {
        parent::__construct();
        $this->logFileName = 'getdeviceinfoprocess';
        $this->params = $params;
        $this->deviceId = $params['deviceid'] ?? 0;
        $this->departService = new DepartService();
        $this->deviceOperationService = new DeviceOperationService();
    }

    /**
     * 获取设备信息
     * @throws Exception
     */
    public function setDeviceInfo()
    {
        if (empty($this->deviceId)) {
            return;
        }
        $this->deviceInfo = \DeviceModel::getOne($this->deviceId, 'info');
    }

    /**
     * 判断设备是否存在.
     *
     * @return mixed
     */
    public function isDeviceExist()
    {
        return false;
    }

    /**
     * 获取设备的类型id.
     *
     * @param string $osName
     *
     * @return int
     */
    public function getDeviceTypeId(string $osName): int
    {
        $deviceType = array(
            'Windows Phone OS' => 103,
            'iOS' => 103,
            'Symbian' => 103,
            'Android' => 103,  // 安卓手机
            'iPhone' => 103,  // 苹果手机
            'iPad' => 103,  // IPAD
            'HarmonyOS' => 103, // 鸿蒙手机
            'LinuxServer' => 135,  // LinuxServer
        );
        $osTypes = [
            OSTYPE_HARMANYOS => 103,
            OSTYPE_ANDROID => 103,
            OSTYPE_IOS     => 103,
        ];
        if (isset($osTypes[OS_TYPE])) {
            return $osTypes[OS_TYPE];
        }
        return $deviceType[$osName] ?? 101;
    }

    /**
     * 获取设备的子类型id.
     *
     * @param string $osName
     *
     * @return int
     */
    public function getDeviceSubTypeId(string $osName): int
    {
        $deviceSubType = [
            'Linux'     => 2,  // Linux
            'MacOS'     => 3,  // MAC
            'Android'   => 4,  // 安卓手机
            'iOS'       => 5, // 苹果手机
            'iPhone'    => 5,  // 苹果手机
            'iPad'      => 5,  // IPAD
            'HarmonyOS' => 6, // 鸿蒙手机
        ];
        $osTypes = [
            OSTYPE_LINUX     => 2,
            OSTYPE_MAC       => 3,
            OSTYPE_ANDROID   => 4,
            OSTYPE_IOS       => 5,
            OSTYPE_HARMANYOS => 6,
        ];
        if (isset($osTypes[OS_TYPE])) {
            return $osTypes[OS_TYPE];
        }
        return $deviceSubType[$osName] ?? 1;
    }

    /**
     * 更新设备在线状态.
     *
     * @return bool
     */
    public function updateDeviceOnline(): bool
    {
        try {
            //如果设备id发生改变，则更新TNacOnLineDevice记录
            if (0 !== (int)$this->params['deviceid'] && (int)$this->params['deviceid'] !== (int)$this->deviceId) {
                // 旧的设备信息还存不存在
                $res = \NacOnLineDeviceModel::getSingle(['DeviceID' => $this->params['deviceid'],'column' => 'name']);
                $this->writeLog('设备id发生改变,查询旧ID是否存在:'.var_export($res, true));

                if ($res['DeviceID'] > 0) {
                    $num = \NacOnLineDeviceModel::delete(['DeviceID' => $this->deviceId, 'UserName' => $res['UserName']]);
                    $this->writeLog('插入前先删除,删除结果：'.$num);
                    //如果第二次认证的用户名和第一次认证的用户名是同一个，并且删除成功了，则更新上一次做认证后的在线状态的对应的那条数据作为最新的在线状态记录
                    //如果更新失败了证明 用户名已经变了，则不更新，防止出现DeviceID重复的两条数据
                    if ($num > 0) {
                        \NacOnLineDeviceModel::updatePatch(
                            ['DeviceID' => $this->params['deviceid'], 'UserName' => $res['UserName']],
                            ['DeviceID' => $this->deviceId]
                        );
                        // 插入事件表，这时有可能导致设备ID为（$this->params['deviceid']）断网，Nac服务阻断时可以溯源
                        \DeviceCycleEventModel::insert([
                            'DeviceID' => $this->params['deviceid'],
                            'EventType' => 'CloneDeviceViolation',
                            'AccessStatus' => 'Cutoff',
                            'Source' => 'WebAccess:updateDeviceOnline'
                        ]);
                    }

                    //添加第三方设备联动 nielin
                    if (hlp_common::isOpenThirdServer()) {
                        \NacPreOnLineDeviceModel::updatePatch(
                            ['DeviceID' => $this->params['deviceid'], 'UserName' => $res['UserName']],
                            ['DeviceID' => $this->deviceId]
                        );
                    }
                    //删除在线表之后通知MSEP
                    NoticeServiceProvider::sendSyncStatus($this->deviceId,'deviceToUser');
                    $this->writeLog('更新第三方设备联动');
                }
            }

            return true;
        } catch (Exception $e) {
            $this->writeLog('updateDeviceOnline 出错了，错误信息为：'.$e->getMessage().PHP_EOL.'错误行数：'.$e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 设置设备的portal的服务器IP
     */
    public function setPortalServer()
    {
        if (empty($this->params['basIP'])) {
            return;
        }
        if ($this->params['basIP'] === 'clean') {
            $basIP = cache_get_info('DeviceID_', $this->deviceId, 'basIP');
            if (!empty($basIP)) {
                $this->writeLog('setPortalServer['.$this->deviceId.'] clean');
                cache_set_info('DeviceID_', $this->deviceId, ['basIP' => '']);
            }
            return;
        }
        $basIP = isHavIP($this->params['basIP'], 1);
        if (empty($basIP)) {
            return;
        }
        $setSuccess = cache_set_info('DeviceID_', $this->deviceId, ['basIP' => $basIP]);
        if (!$setSuccess) {
            $this->writeLog('写入缓存失败, 键:[DeviceID_'.$this->deviceId.'] 写入失败', 'ERROR');
        }
    }

    /**
     * 更新设备计算机名称和操作系统名称
     * 防止计算机名称和OS信息丢失.
     *
     * @return bool
     */
    public function updateDeviceComputerNameAndOsName(): bool
    {
        try {
            // 修复bugID 26170 客户端卸载小助手，管理平台删除设备，刷新入网页面，计算机名称和操作系统有时为空 add by xlf
            if ((int) $this->deviceId <= 0) {
                return false;
            }

            $res = \DeviceModel::getJoinComputer($this->deviceId);
            if (\is_array($res)) {
                if ('' === $res['ComputerName'] && '' !== $this->params['linkIP']) {
                    // 没有小助手时,计算机名称和ip保持一致
                    \DeviceModel::update($this->deviceId, ['ComputerName' => $this->params['linkIP']]);
                    $this->writeLog('更新设备['.$this->params['linkIP'].']计算机名称为：'.$this->params['linkIP']);
                }
                if (103 === $this->params['deviceType'] && '1' !== $res['IsUserDef']) {
                    if ('' === $res['ComputerName'] || filter_var($res['ComputerName'], FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
                        // 防止指纹反复修改移动终端的设备类型,需要将计算机名称为空或者为IP的改为安卓手机
                        \DeviceModel::update($this->deviceId, ['ComputerName' => 'Mobile Phone','DevName' => 'Mobile Phone']);
                        $this->writeLog('更新安卓手机['.$this->params['linkIP'].']计算机名称为：Mobile Phone');
                    }
                }
                $computerParams = [];
                if (('' === $res['OSName'] || null === $res['OSName']) && '' !== $this->params['OS']) {
                    $computerParams['OSName'] = $this->params['OS'];
                    $this->writeLog('更新设备['.$this->params['linkIP'].']操作系统版本为：'.$this->params['OS']);
                }

                if (('' === $res['IEVersion'] || null === $res['IEVersion']) && '' !== $this->params['browser']) {
                    $computerParams['IEVersion'] = $this->params['browser'];
                    $this->writeLog('更新设备['.$this->params['linkIP'].']操作浏览器版本为：'.$this->params['browser']);
                }

                // 更新设备浏览器指纹信息
                if ('' !== $this->params['BrowserFingerprint']) {
                    $computerParams['BrowserFingerprint'] = $this->params['BrowserFingerprint'];
                    $this->writeLog('更新设备[' . $this->params['linkIP'] . ']浏览器指纹为：' . $this->params['BrowserFingerprint']);
                }
                if (!empty($computerParams)) {
                    \ComputerModel::updatePatch(['DeviceID' => $this->deviceId], $computerParams);
                }
                return true;
            }

            $this->writeLog('更新设备设备名称时未找到设备信息，相关参数为：设备id：'.$this->deviceId.' 设备名称：'.$this->params['linkIP'], 'ERROR');

            return false;
        } catch (Exception $e) {
            $this->writeLog('updateDeviceComputerNameAndOsName 出错了，错误信息为：'.$e->getMessage().PHP_EOL.'错误行数：'.$e->getLine(), 'ERROR');
        }

        return false;
    }

    /**
     * 根据Mac检查重复设备
     * 如果存在重复则告知MVG，方便MVG在存在重复MAC的情况下更新最准确的一条
     * 需要向所有的asc和asm发送该消息
     * 该业务为2020-06 技术需求: 断网问题优化新增.
     *
     * @return bool
     */
    public function checkRepeatDevice(): bool
    {
        try {
            if (!IS_INTERNAL) {
                $this->writeLog("外网设备不检查重复！");
                return true;
            }
            $msg = '设备id或者mac为空，不向服务端发送UpdateDevIDByMac交易';
            if ($this->deviceId && $this->mac) {
                $total = \DeviceModel::getCount(['Mac' => $this->mac]);
                if ($total >= 2) {
                    $xml = "<SocketTelnetCmd><CmdName>UpdateDevIDByMac</CmdName><DeviceID>{$this->deviceId}</DeviceID><Mac>{$this->mac}</Mac></SocketTelnetCmd>";
                    // 这里不管asc端口状态，直接发
                    $ascList = \DevASCInfoModel::getAll();
                    foreach ($ascList as $item) {
                        // 直接发送不用等返回结果
                        UdpSend($item['LinkIP'], 36540, $xml, 1);
                    }
                    $msg = '设备id：'.$this->deviceId.'对应的mac存在重复设备，告知mvg';
                } else {
                    $msg = '设备id：'.$this->deviceId.'对应的mac不存在重复设备';
                }
            }
        } catch (Exception $e) {
            $this->writeLog('checkRepeatDevice 出错了，错误信息为：'.$e->getMessage().PHP_EOL.'错误行数：'.$e->getLine(), 'ERROR');

            return false;
        }
        $this->writeLog($msg);

        return true;
    }

    /**
     * 废弃设备
     *
     * @param $DeviceID
     * @throws Exception
     */
    public function clearDeviceInfo($DeviceID): void
    {
        $file = PATH_HTML . "/bin/CheckDeviceIsolation.php";
        $cmd = "php {$file} -a DiscardDevice -DeviceID {$DeviceID}";
        cutil_exec_no_wait($cmd);
    }

    /**
     * 更新设备版本号
     * @param        $deviceId
     * @param string $from
     * @return void
     */
    public function updatePolicyVersion($deviceId, string $from)
    {
        if (!is_numeric($deviceId) || empty($deviceId)) {
            return;
        }
        lib_yar::clients('backend', 'updateDevicePolicyVersion', [
            'type'  => 'DeviceID',
            'value' => $deviceId,
            'from'  => 'access:' . $from
        ], '127.0.0.1', 3 * 1000, 3);
    }
}
