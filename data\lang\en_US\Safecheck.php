<?php
/**
 * Description: 安检
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: Safecheck.php 151264 2021-07-26 09:54:48Z duanyc $
 */

$GLOBALS['LANG'][21104] = [
    21104001 => 'Please specify the canonical ID first',
    21104002 => 'The specification does not exist or has been deleted, please contact the administrator!',
    21104003 => 'The security check item does not exist!',
    21104004 => 'There is a problem with the device ID [{deviceId}]!',
    21104005 => 'Failed to obtain device information',
    21104006 => 'The policy you are viewing does not exist or has been deleted!',
    21104007 => 'Check item does not exist!',
    21104008 => 'Error parsing xml message!',
    21104009 => 'If it is not repaired within <span style=\'color:red;font-size:22px;\'>{interval}</span> days, the system will restrict your network access.',
    21104010 => 'Please fix it as soon as possible!',
    21104011 => 'After approval, you can continue to access the network.',
    21104012 => 'The key item that failed the security check is [{checkItem}]',
    21104013 => 'The current computer is a trusted device,',
    21104014 => 'The current computer has serious security problems, and the use of the network has been restricted.',
    21104015 => 'The current computer does not meet the inspection requirements of the administrator and the repair period has expired, and the use of the network has been restricted.',
    21104016 => 'There are currently hidden security risks on the computer, and there are {day} days left for the repair period.',
    21104017 => 'Certification has expired, please recertify',
    21104018 => 'There are multiple authentications at the same time',
    21104019 => 'Network access result not reported',
    21104020 => 'Please complete the authentication process before accessing the resources',
];
