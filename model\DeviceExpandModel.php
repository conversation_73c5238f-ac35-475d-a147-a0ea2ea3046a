<?php

/**
 * Description: 设备TDeviceExpand表
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: DeviceExpandModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class DeviceExpandModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TDeviceExpand';
    public const PRIMARY_KEY = 'DeviceID';
    protected static $columns = [
        '*' => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['DeviceID'])) {
            $where .= "AND DeviceID = ".self::setData($cond['DeviceID']);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
