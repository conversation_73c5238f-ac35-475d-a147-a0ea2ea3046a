<?php
/**
 * Description: 网络
 * User: <EMAIL>
 * Date: 2022/05/10 10:32
 * Version: $Id$
 */

!defined('IN_INIT') && exit('Access Denied');

class hlp_net
{
    /**
     * ip形式转化为对象的网关 数字
     * @param $mask
     * @return string
     */
    public static function netmaskTo($mask): string
    {
        switch ($mask) {
            case '*********':
                $bcmask = '1';
                break;
            case '*********':
                $bcmask = '2';
                break;
            case '*********':
                $bcmask = '3';
                break;
            case '240.0.0.0':
                $bcmask = '4';
                break;
            case '*********':
                $bcmask = '5';
                break;
            case '*********':
                $bcmask = '6';
                break;
            case '*********':
                $bcmask = '7';
                break;
            case '*********':
                $bcmask = '8';
                break;
            case '***********':
                $bcmask = '9';
                break;
            case '***********':
                $bcmask = '10';
                break;
            case '255.224.0.0':
                $bcmask = '11';
                break;
            case '255.240.0.0':
                $bcmask = '12';
                break;
            case '255.248.0.0':
                $bcmask = '13';
                break;
            case '255.252.0.0':
                $bcmask = '14';
                break;
            case '255.254.0.0':
                $bcmask = '15';
                break;
            case '255.255.0.0':
                $bcmask = '16';
                break;
            case '255.255.128.0':
                $bcmask = '17';
                break;
            case '255.255.192.0':
                $bcmask = '18';
                break;
            case '255.255.224.0':
                $bcmask = '19';
                break;
            case '255.255.240.0':
                $bcmask = '20';
                break;
            case '255.255.248.0':
                $bcmask = '21';
                break;
            case '255.255.252.0':
                $bcmask = '22';
                break;
            case '255.255.254.0':
                $bcmask = '23';
                break;
            case '255.255.255.0':
                $bcmask = '24';
                break;
            case '255.255.255.128':
                $bcmask = '25';
                break;
            case '255.255.255.192':
                $bcmask = '26';
                break;
            case '255.255.255.224':
                $bcmask = '27';
                break;
            case '255.255.255.240':
                $bcmask = '28';
                break;
            case '255.255.255.248':
                $bcmask = '29';
                break;
            case '255.255.255.252':
                $bcmask = '30';
                break;
            case '255.255.255.254':
                $bcmask = '31';
                break;
            case '255.255.255.255':
                $bcmask = '32';
                break;
            default:
                $bcmask = 'wrong';
                break;
        }
        return $bcmask;
    }

    /**
     * 获取代理协议
     *
     * @param $accessTypeId
     * @return string
     */
    public static function getProxyProtocol($accessTypeId): string
    {
        $ProxyProtocols = [
            ACCESS_TYPE_RDP => 'RDP',
            ACCESS_TYPE_REMOTE_APP => 'RDP',
            ACCESS_TYPE_VNC => 'VNC',
            ACCESS_TYPE_SSH => 'SSH',
            ACCESS_TYPE_TELENT => 'TELNET',
        ];
        return $ProxyProtocols[$accessTypeId] ?? '';
    }

    /**
     * 是否为内网IP地址，支持对IPv4、IPv6判断
     * @param string $ip
     * @return bool true:内网地址，false:外网地址
     */
    public static function isInternalIp(string $ip = ''): bool
    {
        if (empty($ip)) {
            //根据头部字段判断来源网关代理还是外网
            if (!empty($_SERVER['HTTP_IFZTP_GW_ACCESS'])) {
                // ngx.req.set_header("IFZTP-GW-ACCESS", 1) ,网关来的 IFZTP-GW-ACCESS 为1,注意此处要转换成HTTP_IFZTP_GW_ACCESS
                $ip = getRemoteAddress();
                $internalIPsConfig = lib_redis::getHash('ASG_', 'gateConfig', 'InternalIPs');
                if (!empty($ip) && !empty($internalIPsConfig)) {
                    return isInternalIPConf($ip, $internalIPsConfig);
                }
            } else {
                return empty($_SERVER['HTTP_IFZTP_WAN_ACCESS']);
            }
        }
        // 主要用于测试场景
        $path = PATH_ETC . "internal.ini";
        if (file_exists($path)) {
            $iconfig = parse_ini_file($path);
            // 使用net网络，模拟外网
            if (!empty($iconfig['NetIps'])) {
                if (ipInIpRange($ip, $iconfig['NetIps'])) {
                    return false;
                }
            }
            // 使用外网地址作为内网使用
            if (!empty($iconfig['InternalIps'])) {
                if (ipInIpRange($ip, $iconfig['InternalIps'])) {
                    return true;
                }
            }
        }
        return filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false;
    }

    /**
     * 是否为Ipv6地址
     * @param string $ip
     * @return bool true:ipv6地址，false:ipv4地址
     */
    public static function isIpv6Ip(string $ip = ''): bool
    {
        if (empty($ip)) {
            $ip = getServerAddr();
        }
        $ip = dealHost($ip);
        return isIP($ip, 6);
    }

    /**
     * @param $domain
     *
     * @return bool
     * 是否为域名,是返回true,否则返回false
     */
    public static function isDomain($domain): bool
    {
        $str = "/^([A-Za-z0-9-]+\.)+[A-Za-z]{1,20}(:\d+)?$/";
        if (!preg_match($str, $domain)) {
            return false;
        }
        return true;
    }

    /**
     * @param $url
     * @return string
     * 从url中获取域名
     */
    public static function getDomainByUrl($url): string
    {
        //从URL中解析出
        $hostname = parse_url($url, PHP_URL_HOST);
        // 如果是IP地址，则直接返回
        if (filter_var($hostname, FILTER_VALIDATE_IP)) {
            return $hostname;
        }
        //域名
        $urlParts = parse_url($url);
        return $urlParts['host'] ?? ($urlParts['path'] ?? '');
    }

    /**
     * @param $url
     * @return array
     * 从url中获取域名
     */
    public static function analyzeURL($url): array
    {
        // 解析 URL
        $urlParts = parse_url($url);

        // 提取协议
        $scheme = $urlParts['scheme'] ?? '';

        // 提取主机名（域名或 IP 地址）
        $host = $urlParts['host'] ?? ($urlParts['path'] ?? '');
        // 提取端口
        $port = $urlParts['port'] ?? '';
        if (empty($port)) {
            $scheme === 'http' && $port = 80;
            $scheme === 'https' && $port = 443;
        }
        // 拼接 URL
        $finalUrl = $scheme . '://' . $host . (!empty($port) ? ':' . $port : '');

        return [
            'protocol' => $scheme ? rtrim($scheme, '://') : '',
            'domain' => $host,
            'port' => $port ? ltrim((string)$port, ':') : '',
            'url' => $finalUrl
        ];
    }

    /**
     * 是否是内网并开启私有DNS请求
     * @return bool
     */
    public static function isInternalRequest(): bool
    {
        $dnsSwitch = file_get_contents(PATH_ETC . "mosdns.d/data/resolv.txt");
        return IS_INTERNAL && $dnsSwitch === 'on';
    }

    /**
     * 限制指定IP的访问请求频率
     * @param $ip string IP地址
     * @param $action string 需要限制的接口名称，为空则默认为normal
     * @param $limit int 限制次数
     * @param $time int 限制时间
     * @param $params array 扩展参数
     * @return bool
     */
    public static function limitAccessFrequency(string $ip, string $action = 'normal', int $limit = 10, int $time = 60, array $params = []): bool
    {
        if ($action === '') {
            $action = 'normal';
        }
        if ($ip === '') {
            return false;

        }
        // 本地访问不限制，如果是不同设备间的交易最好不要调用该方法
        if ($ip === '127.0.0.1' || $ip === 'localhost') {
            return true;
        }
        $prefix = 'limitAccessFrequency:'. $action. ":";
        // 检查是否存在键，如果不存在则初始化计数器
        if (!lib_redis::exists($prefix, $ip)) {
            lib_redis::set($prefix, $ip, 0, $time);
        }

        // 增加计数器，并检查是否超过限制
        $count = lib_redis::incr($prefix, $ip);

        return $count <= $limit;
    }
}
