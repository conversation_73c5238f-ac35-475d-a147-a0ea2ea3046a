<?php
/**
 * Description: 飞书认证服务
 * User: <EMAIL>
 * Date: 2021/08/19 15:53
 * Version: $Id$
 */

namespace Services\Auth\Services;

use AuthServiceProvider;
use DeviceModel;
use Exception;
use hlp_common;
use Services\Auth\Interfaces\AuthServiceInterface;

class FeiShuAuthService extends BaseAuthService implements AuthServiceInterface
{
    /**
     * 用户类型/认证方式
     * @var string
     */
    protected $userType = 'FeiShu';

    /**
     * 初始化
     *
     * @param $params
     * @throws Exception
     */
    public function __construct($params)
    {
        parent::__construct($params);
    }

    /**
     * 解析参数
     * @return array
     * @throws Exception
     */
    public function parseParams()
    {
        $this->params['isAgent'] = request('isAgent', 'request', 0, 'int');
        $this->params['code'] = request('feishucode', 'request');
        $this->params['redirectUri'] = Base64EnExt(request('redirect_uri', 'request',""));
        return $this->params;
    }

    /**
     * 移动端首页
     */
    public function main()
    {
        $code = request('feishucode', 'request');
        $state = request('state', 'request');
        $otherParams = hlp_common::otherAuthParams();
        $url =  '/mobile/ui/wel.html?route_type=feishu&feishucode=' . $code . '&appid=' . $state . "&{$otherParams}";
        $url = strlen($url) > 0 ? $url : '/mobile/ui/wel.html?1=1';
        cutil_php_log('Location:' . $url . '&t=' . time(), 'feishu');
        header('Location:' . $url . '&t=' . time());
        exit();
    }

    /**
     * 认证校验完成后
     *
     * @param $data
     * @param $userInfo
     * @param $authType
     *
     * @return int|void
     * @throws Exception
     */
    public function authAfter(&$data, $userInfo, $authType)
    {
        //等账号限制校验完成后再置验证码失效
        parent::authAfter($data, $userInfo, $authType);
        $config = AuthServiceProvider::getAuthDomainConfig('FeiShu');
        if ($config['isEnableSynchronous']) {
            $dparams = ['DepartID' => $userInfo['DepartID'], 'UserName' => $userInfo['UserName']];
            DeviceModel::update($this->deviceId, $dparams);
        }
    }

    /**
     * 定义敲门认证的参数顺序，按照数据顺序传参数
     *
     * @return mixed
     */
    public function getFwknopColumns()
    {
        return ['type', 'feishucode', 'isAgent', 'appid'];
    }

    /**
     * 定义认证的字段
     *
     * @return mixed
     */
    public function getAuthColumns()
    {
        return ['authType', 'isAgent', 'userName', 'password', 'appId',  'code', 'fwknopAuth'];
    }
}
