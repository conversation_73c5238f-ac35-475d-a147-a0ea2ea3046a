<?php

/**
 * Description: 资源
 * User: <EMAIL>
 * Date: 2021/08/24 15:53
 * Version: $Id: ResourceModel.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class ResourceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TResource';
    public const PRIMARY_KEY = 'ResID';
    protected static $columns = [
        '*' => '*',
        'user' => 'R.<PERSON>, R.ResName, R.ResType, R.GroupResID, R.Is<PERSON>nternet,R.ActionType,R.IsCopy,O.EndTime,R.<PERSON>,R.AccessTypeID',
        'one' => 'ResID,ResType,ResName,DependResIDs,DeviceType, ActionType, IsCopy, IsPaste, IsInternet, IsWatermark as Watermark,IsNat,IsApply,IsAutoPass,AccessTypeID',
        'index' => 'ResID, IsNat,ResType,ResName,IsInternet,GroupResID,ActionType,IsCopy',
        'all' => '<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>ly,R.IsWatermark as Watermark,R.IsNat,R.IsSmartRewriting,R.IsInternet,C.DomainName,C.ConnUrl,C.ProxyProtocol,C.ProxyPort,C.Url,' .
            'C.RealPort,C.AccessTypeID,C.GrantType,C.CertKeyPath,C.CertPath,C.HttpHeader,C.IsPassive,C.IsParseDomain,' .
            'C.HealthCode,C.UnHealthCode,C.SuccessNums,C.TcpFailNums,C.FailNums,C.Timeout,C.RequestUrl,C.LoginURL,C.OtherField,' .
            'C.IntervalTime1,C.IntervalTime2,C.GateWayID,C.GateWayType,C.RealProtocol,P.PolicyID,C.DependSite,C.CID',
        'all_apply' => 'R.IsNat,R.ResID,R.ResName,R.IsCopy,R.IsPaste,R.IsApply,R.IsWatermark as Watermark,R.GroupResID,G.GroupName,C.Icon,C.Remark,R.ResType',
        'ip_apply' => "R.IsNat,R.ResID,R.ResName,R.IsCopy,R.IsPaste,R.IsApply,R.IsWatermark as Watermark,'1' as GroupResID, ' 默认资源组' as GroupName,C.Icon,C.Remark,R.ResType",
        'all_res' => 'R.IsNat,R.ResID,R.ResName,R.ResType,R.IsInternet,C.DomainName,C.ConnUrl,C.ProxyProtocol,C.ProxyPort,C.Url,C.RealPort,C.AccessTypeID,C.GrantType,C.RealProtocol,C.GateWayID,C.GateWayType',
        'all_res_ip' => 'R.IsNat,R.ResID,R.ResName,R.ResType,R.IsInternet,C.IP,C.GateWayID,C.GateWayType',
        'remote_app' => 'R.IsNat,R.ResID,R.ResType,R.ResName,R.IsCopy,R.IsPaste,R.IsWatermark as Watermark,S.Addr,S.Port,S.RemotePort,S.GateWayID,S.GateWayType,' .
            'C.IsPassive,A.AppName,A.AppPath,P.PolicyID',
    ];

    /**
     * 获取用户资源列表
     *
     * @param array $cond
     * @param string $order
     * @param int $start
     * @param int $limit
     *
     * @return mixed
     */
    public static function getUserResourceList($cond = [], $order = '', $start = 0, $limit = 1000)
    {
        self::$data = [];
        $where = static::getLeftWhere($cond);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $order = !empty($order) ? "ORDER BY {$order}" : "";
        $column = static::$columns['user'];
        $sql = "select {$column} from TResObjectRelation O LEFT JOIN TResource R ON R.ResID = O.ResID  " .
            "{$where} {$order} LIMIT {$start}, {$limit}";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 通过资源组获取用户资源列表
     *
     * @param array $cond
     * @param string $order
     * @param int $start
     * @param int $limit
     *
     * @return mixed
     */
    public static function getUserGroupResourceList($cond = [], $order = '', $start = 0, $limit = 1000)
    {
        self::$data = [];
        $where = static::getLeftWhere($cond);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $order = !empty($order) ? "ORDER BY {$order}" : "";
        $column = static::$columns['user'];
        $sql = "select {$column} from TResource R LEFT JOIN TResObjectRelation O ON R.ResID = O.ResID   " .
            "{$where} {$order} LIMIT {$start}, {$limit}";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取所有资源列表
     *
     * @return mixed
     */
    public static function getAllResource()
    {
        self::$data = [];
        $column = static::$columns['all'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "select {$column} from TResource R LEFT JOIN TResConfigList C ON R.ResID = C.ResID LEFT JOIN TResPolicyRelation P ON R.ResID = P.ResID" .
            " WHERE IsInternet = " . self::setData('1') . " AND R.ResType = '0' LIMIT 0, 10000";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 根据资源类型获取所有资源列表
     *
     * @return mixed
     */
    public static function getResourceList($cond, $resType = 0)
    {
        self::$data = [];
        $column = static::$columns['all_res'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $leftWhere = "LEFT JOIN TResConfigList C ON R.ResID = C.ResID ";
        //访问域资源
        if ($resType == 1) {
            $column = static::$columns['all_res_ip'];
            $leftWhere = "LEFT JOIN TResIPList C ON R.ResID = C.ResID ";
        }
        $where = self::getResConfigWhere($cond);
        $sql = "select {$column} from TResource R  " . $leftWhere . $where . " AND R.ResType = " . self::setData($resType) . " LIMIT 0, 10000";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取所有资源列表
     *
     * @return mixed
     */
    public static function getAllApplyResource($cond, $type = 0)
    {
        self::$data = [];
        $column = static::$columns['all_apply'];
        $where = static::getWhere($cond);
        $left = " INNER JOIN TResConfigList C ON R.ResID = C.ResID ";
        if ($type == 1) {
            $left = " INNER JOIN TResIPList C ON R.ResID = C.ResID ";
        } elseif ($type == 3) {
            $left = " INNER JOIN TResRemote C ON R.ResID = C.ResID ";
        }
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "select {$column} from TResource R  INNER JOIN TResGroup G ON R.GroupResID = G.ResID " . $left .
            $where . " AND R.IsInternet =" . self::setData(1) . " AND R.IsApply = 1 AND R.ResType = '" . $type . "' LIMIT 0, 10000";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取IP申请资源列表，由于访问域没有资源组，设为默认组与授权组添加权限有冲突，此处单独处理
     *
     * @return mixed
     */
    public static function getIpApplyResource($cond)
    {
        self::$data = [];
        $column = static::$columns['ip_apply'];
        $where = static::getWhere($cond);
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "select {$column} from TResource R  INNER JOIN TResIPList C ON R.ResID = C.ResID " .
            $where . " AND R.IsInternet =" . self::setData(1) . " AND R.IsApply = 1  LIMIT 0, 10000";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取远程应用资源列表
     *
     * @return mixed
     */
    public static function getRemoteAppResource()
    {
        self::$data = [];
        $column = static::$columns['remote_app'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "select {$column} from TResource R LEFT JOIN TResRemote C ON R.ResID = C.ResID LEFT JOIN TResPolicyRelation P ON R.ResID = P.ResID" .
            " Left JOIN TRemoteService S ON S.ID = C.RemotSerID LEFT JOIN TRemoteApp A ON A.ID = C.RemotAppID" .
            " WHERE R.IsInternet = " . self::setData('1') . " AND R.ResType = '3' LIMIT 0, 10000";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取已经下架的免认证资源列表
     *
     * @return mixed
     */
    public static function getNoAuthResource()
    {
        self::$data = [];
        $column = static::$columns['all'];
        $table = hlp_common::getSplitTable(null, static::TABLE_NAME);
        $sql = "select {$column} from TResource R LEFT JOIN TResConfigList C ON R.ResID = C.ResID LEFT JOIN TResPolicyRelation P ON R.ResID = P.ResID" .
            " WHERE IsInternet = " . self::setData('0') . " AND R.ResType = '0' AND C.AccessTypeID='313' LIMIT 0, 10000";
        cutil_php_log(json_encode(['model_select', $sql, self::$data]), "model_{$table['name']}");
        return lib_database::getAll($sql, $table['index'], false, self::$data);
    }

    /**
     * 获取类型ID与名字映射
     *
     * @param $ResIDs
     *
     * @return array
     */
    public static function getResDatas($ResIDs): array
    {
        self::$data = [];
        $cond = ['InResID' => $ResIDs];
        $where = static::getWhere($cond);
        $column = self::$columns['index'];
        $table = hlp_common::getSplitTable(null, self::TABLE_NAME);
        $sql = "SELECT {$column} FROM {$table['name']} {$where}";
        $return = lib_database::getAll($sql, $table['index'], false, self::$data);
        $resNames = [];
        if (!empty($return)) {
            foreach ($return as $row) {
                $resNames[$row['ResID']] = $row;
            }
        }
        return $resNames;
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getLeftWhere($cond = []): string
    {
        $where = [];

        if (isset($cond['UserID'])) {
            $where[] = "O.UserID = " . self::setData($cond['UserID']);
        }

        if (!empty($cond['GroupResID']) && is_array($cond['GroupResID'])) {
            $where[] = " R.GroupResID IN (" . self::setArrayData($cond['GroupResID']) . ")";
        }

        if (isset($cond['DepartID'])) {
            $where[] = "O.DepartID IN (" . self::setArrayData($cond['DepartID']) . ")";
        }

        if (!empty($cond['AuthorizationID'])) {
            if (!is_array($cond['AuthorizationID'])) {
                $cond['AuthorizationID'] = explode(',', $cond['AuthorizationID']);
            }
            $where[] = "O.AuthorizationID IN (" . self::setArrayData($cond['AuthorizationID']) . ")";
        }

        if (isset($cond['RoleID'])) {
            $where[] = "O.RoleID = " . self::setData($cond['RoleID']);
        }

        if (isset($cond['DeviceID'])) {
            $where[] = "O.DeviceID = " . self::setData($cond['DeviceID']);
        }

        $leftWhere = "";

        if (!empty($where)) {
            $leftWhere .= "AND (" . implode(" OR ", $where) . ")";
        }

        if (isset($cond['IsInternet'])) {
            $leftWhere .= "AND R.IsInternet = " . self::setData($cond['IsInternet']);
        }

        //资源组权限补充时需要忽略掉有效时间
        if (!isset($cond['IgnoreEndTime'])) {
            //增加个时间判断
            $leftWhere .= "AND O.EndTime >= " . self::setData(date('Y-m-d H:i:s'));
        }

        //查询资源组
        if (isset($cond['ResType'])) {
            $leftWhere .= "AND R.ResType = " . self::setData($cond['ResType']);;
        }

        return !empty($leftWhere) ? "WHERE 1 = 1 {$leftWhere}" : "";
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []): string
    {
        $where = "";

        if (isset($cond['ResType'])) {
            $where .= "AND ResType = " . self::setData($cond['ResType']);
        }

        if (!empty($cond['InResID'])) {
            $where .= "AND ResID IN (" . self::setArrayData($cond['InResID']) . ")";
        }

        //用来筛选资源组大于0的
        if (isset($cond['GtGroupResID'])) {
            $where .= "AND GroupResID > " . self::setData($cond['GtGroupResID']);
        }

        if (isset($cond['IsInternet'])) {
            $where .= "AND IsInternet = " . self::setData($cond['IsInternet']);
        }

        if (isset($cond['ActionType'])) {
            $where .= "AND ActionType = " . self::setData($cond['ActionType']);
        }

        //资源申请相关特定条件
        if (!empty($cond['noInResId'])) {
            $where .= " AND R.ResID NOT IN (" . self::setArrayData($cond['noInResId']) . ")";
        }
        if (!empty($cond['queryStr'])) {
            $resName = "%{$cond['queryStr']}%";
            $where .= " AND R.ResName like " . self::setData($resName);
        }
        if (!empty($cond['groupResID'])) {
            $where .= " AND R.GroupResID = " . self::setData($cond['groupResID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getResConfigWhere(array $cond = []): string
    {
        $where = "";

        if (!empty($cond['DomainName'])) {
            $where .= "AND DomainName = " . self::setData($cond['DomainName']);
        }

        if (!empty($cond['URL'])) {
            $where .= "AND URL = " . self::setData($cond['URL']);
        }

        if (!empty($cond['RealPort'])) {
            $where .= "AND RealPort = " . self::setData($cond['RealPort']);
        }

        if (!empty($cond['ResID'])) {
            $where .= "AND R.ResID = " . self::setData($cond['ResID']);
        }
        if (!empty($cond['AccessTypeID'])) {
            $where .= "AND AccessTypeID = " . self::setData($cond['AccessTypeID']);
        }
        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}
