<?php
/**
 * Description: cas
 * User: <EMAIL>
 * Date: 2022/02/23 10:52
 * Version: $Id$
 */

require_once(PATH_LIBRARY . '/otheruser/sso/common/ssoCommon.php');
require_once(PATH_LIBRARY . '/otheruser/sso/Interfaces/ssoAuth.php');

class casClientService implements ssoAuth
{
    public $params;

    public function __construct($params)
    {
        $this->params = $params;
        $this->initCas($params['deviceId']);
    }

    /**
     * @inheritDoc
     */
    public function logout(): bool
    {
        if ($this->isSSOLogin()) {
            phpCAS::logout();
        }
        return true;
    }

    /**
     * @inheritDoc
     * @return bool
     * @throws Exception
     */
    public function login(): bool
    {
        if ($this->isSSOLogin()) {
            return true;
        }
        // ticket已失效，清除
        unset($_GET['ticket']);
        ssoCommon::cacheDelete($this->params['deviceId']);
        ssoCommon::recordLog('login--------');
        $casClient = phpCAS::getCasClient();
        $casClient->setTicket('');
        phpCAS::setCasClient($casClient);
        phpCAS::forceAuthentication();
        return false;
    }

    /**
     * @inheritDoc
     * @return array|false
     * @throws Exception
     */
    public function checkLoginState()
    {
        if ($this->isSSOLogin()) {
            $user = ssoCommon::cacheGet($this->params['deviceId'], 'userName');
            ssoCommon::recordLog('checkLoginState:' . var_export($user, true));
            if (!empty($user) && !empty($_SESSION)) {
                return ['username' => $user];
            }
        }
        T(21147001);
        return false;
    }

    /**
     * @inheritDoc
     */
    public function getUserInfo()
    {
        $upUserInfo = array();
        $synItemMapping = ssoCommon::getUserSynItemMapping();
        $reqParams = ssoCommon::getSSOConfig('response');
        foreach ($reqParams['ResParamKey'] as $k => $v) {
            $synItem = $synItemMapping[$reqParams['BindField'][$k]];
            if (!empty($synItem)) {
                $upUserInfo[$synItem] = phpCAS::getAttribute($v);//根据字段映射配置关系
            }
        }
        // 未配置账户ID的话使用账户名
        if (empty($upUserInfo['userId'])) {
            $upUserInfo['userId'] = phpCAS::getUser();
        }
        $upUserInfo['UserName'] = $upUserInfo['userId'];
        if (empty($upUserInfo['TrueNames'])) {
            $upUserInfo['TrueNames'] = phpCAS::getUser();
        }
        ssoCommon::cacheSet(
            $this->params['deviceId'],
            [
                'userName' => addslashes($upUserInfo['UserName'])
            ]
        );
        ssoCommon::recordLog('getUserInfo:' . var_export($upUserInfo, true));
        return $upUserInfo;
    }


    /**
     * 检测是否已经单点登录
     * @return bool
     * @throws Exception
     */
    public function isSSOLogin(): bool
    {
        $result = false;
        ob_start();
        try {
            $result = phpCAS::isAuthenticated();
        } catch (Exception $e) {
        }
        ob_end_clean();
        if (!$result) {
            $accessToken = ssoCommon::cacheGet($this->params['deviceId'], 'userName');
            ssoCommon::recordLog('userName:' . var_export($accessToken, true));
            if (!empty($accessToken)) {
                $result = true;
            } else {
                $result = false;
            }
        }
        if (!$result) {
            ssoCommon::cacheDelete($this->params['deviceId']);
        }
        return $result;
    }

    /**
     * 初始化cas客户端
     * @param $deviceId
     * @throws Exception
     */
    private function initCas($deviceId): void
    {
        $_GET['ticket'] = ssoCommon::cacheTicket($this->params['deviceId']);
        $basicConfig = ssoCommon::getSSOConfig('basicConfig');
        phpCAS::setDebug('/tmp/logs/access_cas' . '-' . date('Ymd') . '.log');
        phpCAS::setVerbose(true); //是否打开debug
        phpCAS::client(
            CAS_VERSION_3_0,
            $basicConfig['ServerAddress'],
            (int)$basicConfig['ServerPort'],
            $basicConfig['LoginUri'],
            false
        );
        // 不校验SSL
        phpCAS::setNoCasServerValidation();
        // 不去除
        phpCAS::setNoClearTicketsFromUrl();

        // 设置固定URL
        $url = ssoCommon::getRedirectUri(false, $this->params['isHttps']) .
            '?deviceId=' . $deviceId .
            '&isClient=' . $this->params['isClient'] .
            '&local_lguage_set=' . $GLOBALS['CONFIG']['LANG_MAP'][LANG];
        phpCAS::setFixedServiceURL($url);

        // 判断当前是否https，设置cas
        $loginUri = $basicConfig['LoginUri'];
        if (strpos($loginUri, '?') === false) {
            $loginUri .= '/';
        }
        $loginUri = preg_replace('/\/\//', '/', '/' . $loginUri);
        $casClient = phpCAS::getCasClient();
        $casClient->setBaseURL(
            'http' . (ssoCommon::isServerHttps() ? 's' : '') . '://' .
            $basicConfig['ServerAddress'] . ':' . $basicConfig['ServerPort'] . $loginUri
        );
        phpCAS::setCasClient($casClient);
    }

}
