<?php

/**
 * Description: TPermitDevice表
 * User: <EMAIL>
 * Date: 2021/07/5 15:53
 * Version: $Id: PermitDeviceModel.php 158181 2021-09-28 11:28:13Z duanyc $
 */

class PermitDeviceModel extends BaseModel
{
    /**
     * 表名
     *
     * @var string
     */
    public const TABLE_NAME = 'TPermitDevice';
    public const PRIMARY_KEY = 'RID';
    protected static $columns = [
        'one' => 'RID, BindID',
        '*'    => '*',
    ];

    /**
     * 获取条件
     *
     * @param array $cond
     *
     * @return string
     */
    protected static function getWhere($cond = []):string
    {
        $where = "";

        if (isset($cond['RoleID'])) {
            $RoleID = intval($cond['RoleID']);
            $where .= "AND RoleID = ".self::setData($RoleID);
        }

        if (isset($cond['DeviceID'])) {
            $DeviceID = intval($cond['DeviceID']);
            $where .= "AND DeviceID = ".self::setData($DeviceID);
        }

        if (isset($cond['InDeviceID'])) {
            $InDeviceID = explode(',', $cond['InDeviceID']);
            $where .= "AND DeviceID IN (".self::setArrayData($InDeviceID).") ";
        }

        if (isset($cond['InBindID'])) {
            $InBindID = explode(',', $cond['InBindID']);
            $where .= "AND BindID IN (".self::setArrayData($InBindID).") ";
        }

        if (isset($cond['UserID'])) {
            $UserID = intval($cond['UserID']);
            $where .= "AND UserID = ".self::setData($UserID);
        }

        return !empty($where) ? "WHERE 1 = 1 {$where}" : "";
    }
}