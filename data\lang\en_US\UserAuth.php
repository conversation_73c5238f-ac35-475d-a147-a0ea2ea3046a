<?php
/**
 * Description: 用户认证
 * 1位级别编号（1为程序错误，2为业务错误），2位模块编号（web固定为11），2位业务编号，后3位代表具体提示
 * User: <EMAIL>
 * Date: 2021/3/19
 * Version: $Id: UserAuth.php 159776 2021-10-25 03:11:24Z duanyc $
 */

$GLOBALS['LANG'][21133] = [
    21133001 => 'Authentication failed, [{user}] user has been locked for {time} minutes, please try again later!',
    21133002 => 'Authentication failed! Please enter the correct username and password!',
    21133003 => 'Authentication failed! Password must be changed!',
    21133004 => 'Authentication failed, [{user}] login error, too many login errors, the user was locked for {time} minutes, please try again later!',
    21133005 => '[{user}] Wrong password, authentication failed, you still have {time} chances!',
    ******** => 'Authentication failed! Please enter the correct username and password!',
    ********  => 'Minimum length',
    ********  => '',
    ********  => 'Can\'t be',
    ********  => 'Must not contain username',
    ******** => 'No repeated letters or numbers',
    ******** => 'Must be a combination of numbers and letters',
    ******** => 'Must be a combination of uppercase letters, lowercase letters, numbers, and special characters',
    ******** => 'Your account password strength does not meet the requirements',
    ******** => 'Your password has expired. Please change it and continue to use!',
    ******** => 'The password will expire on {expiredate}, please change it in time!',
    ******** => 'Password complexity check',
    ******** => 'The scan code has expired, please scan the code again!',
];
