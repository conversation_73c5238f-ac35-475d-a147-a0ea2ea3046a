<?php
/**
 * Description: SSO通用
 * User: <EMAIL>
 * Date: 2022/02/24 10:52
 * Version: $Id$
 */
include_once PATH_ROOT . "/library/function/device.func.php";
class ssoCommon
{

    /**
     * @var array sso配置
     */
    private static $ssoConfig = [];

    /***
     * 获取单点登录的配置
     * @param string $key
     * @return array
     */
    public static function getSSOConfig(string $key = '')
    {
        if (empty(self::$ssoConfig)) {
            $ssoConfig = array();
            if (!file_exists(SSO_CONFIG_PATH)) {
                mkdir(SSO_CONFIG_PATH, 0777);
            }
            if (file_exists(SSO_CONFIG_PATH . SSO_CONFIG_FILE)) {
                $configJson = file_get_contents(SSO_CONFIG_PATH . SSO_CONFIG_FILE);
                $ssoConfig = json_decode($configJson, true);
            }
            self::$ssoConfig = [];
            foreach (self::getBasicItem() as $item) {
                if (isset($ssoConfig[$item])) {
                    self::$ssoConfig[$item] = $ssoConfig[$item];
                } else {
                    self::$ssoConfig = [];
                }
            }
        }

        return empty($key) ? self::$ssoConfig : self::$ssoConfig[$key];
    }

    /***
     * 获取基础配置项
     * @return array
     */
    public static function getBasicItem(): array
    {
        return array('basicConfig', 'requestParam', 'response');
    }

    /***
     * 获取用户列表同步字段关系映射
     * @return array
     */
    public static function getUserSynItemMapping(): array
    {
        return array('userName' => 'TrueNames', 'mobile' => 'Tel', 'mail' => 'Email', 'userId' => 'userId');
    }

    /***
     * 记录操作日志
     * @param string|mixed $c
     */
    public static function recordLog($c = ''): void
    {
        if (!empty($c)) {
            cutil_php_log($c, 'ssoAuth');
        }
    }

    /***
     * 获取服务器地址
     * @param bool $isHttps
     * @return string
     * @throws Exception
     */
    public static function getServerUrl($isHttps = false): string
    {
        if (!empty($_SERVER['HTTP_X_FORWARDED_HOST'])) {
            $hosts = explode(',', $_SERVER['HTTP_X_FORWARDED_HOST']);
            $server_url = $hosts[0];
        } else if (!empty($_SERVER['HTTP_X_FORWARDED_SERVER'])) {
            $server_url = $_SERVER['HTTP_X_FORWARDED_SERVER'];
        } else {
            $server_url = $_SERVER['HTTP_HOST'];
        }
        if (strpos($server_url, ':') === false) {
            $port = getHttpPort($isHttps, false);
            if (!empty($port)) {
                $server_url = ':' . $port;
            }
        } else {
            // 去除默认的端口
            $urlArr = explode(':', $server_url);
            if (count($urlArr) === 2) {
                $defaultPort = $isHttps ? HTTPS_PORT : HTTP_PORT;
                if ((int)$urlArr[1] === $defaultPort) {
                    $server_url = $urlArr[0];
                }
            }
        }
        return $server_url;
    }

    /***
     * 获取基础url
     * @return bool|string
     */
    public static function getBaseUrl()
    {
        $ssoConfig = self::getSSOConfig('basicConfig');
        $baseUrl = HTTP_PROTOCOL;
        $ssoConfig['IsHttpS'] = $ssoConfig['IsHttpS'] ?? 0;
        if ((int)$ssoConfig['IsHttpS'] === 1) {
            $baseUrl = HTTPS_PROTOCOL;
        }
        if (empty($ssoConfig['ServerAddress'])) {
            return false;
        }
        $baseUrl .= $ssoConfig['ServerAddress'];
        if (!empty($ssoConfig['ServerPort'])) {
            $baseUrl .= ':' . $ssoConfig['ServerPort'];
        }
        return $baseUrl;
    }

    /**
     * 是否SSO https
     * @return bool
     */
    public static function isServerHttps(): bool
    {
        $ssoConfig = self::getSSOConfig('basicConfig');
        $ssoConfig['IsHttpS'] = $ssoConfig['IsHttpS'] ?? 0;
        return (int)$ssoConfig['IsHttpS'] === 1;
    }

    /**
     * SSO设置缓存
     * @param       $name
     * @param array $value
     * @param int   $expire
     */
    public static function cacheSet($name, array $value, int $expire = 180 * 60): void
    {
        cache_set_info('SSO_', $name, $value, $expire);
    }

    /**
     * SSO获取缓存
     * @param      $keys
     * @param string $field
     * @return array|mixed|null
     */
    public static function cacheGet($keys, $field = null)
    {
        return cache_get_info('SSO_', $keys, $field);
    }

    /**
     * SSO获取缓存
     * @param      $keys
     * @param null $field
     * @return bool
     */
    public static function cacheDelete($keys, $field = null): bool
    {
        if (is_array($keys)) {
            foreach ($keys as $key) {
                cache_del_info('SSO_', $key, $field);
            }
            return true;
        }

        return cache_del_info('SSO_', $keys, $field);
    }

    /**
     * 从缓存读取ticket，并保存
     * @param        $deviceId
     * @param string $ticket
     * @param string $type
     * @return mixed
     */
    public static function cacheTicket($deviceId, string $ticket = '', string $type = 'cas')
    {
        $cacheTicket = self::cacheGet($deviceId, $type . '_ticket');
        if ($ticket === '' && $cacheTicket !== '') {
            return $cacheTicket;
        }
        if (empty($ticket)) {
            return false;
        }

        self::cacheSet($deviceId, [$type . '_ticket' => $ticket]);
        self::recordLog("get:Ticket:" . $ticket);
        return $ticket;
    }

    /**
     * ping服务器
     *
     * @param $host
     * @param $port
     * @param $timeout
     *
     * @return bool
     */
    public static function ping($host, $port, $timeout)
    {
        $key = "{$host}_{$port}";
        $cache = self::cacheGet('Server_' . $key);
        if (!empty($cache['time'])) {
            return true;
        }
        $errstr = '';
        $errno = '';
        $fP = fSockOpen($host, $port, $errno, $errstr, $timeout);
        if (!$fP) {
            return false;
        }
        // 缓存一小时
        self::cacheSet('Server_' . $key, ['time' => true], 1 * 60 * 60);
        return true;
    }

    /**
     * @param bool      $isUrlEncode
     * @param bool|null $isHttps
     * @return string
     * @throws Exception
     */
    public static function getRedirectUri(bool $isUrlEncode = false, bool $isHttps = false): string
    {
        $ssoConfig = self::getSSOConfig('basicConfig');
        $os_type = $ssoConfig['Ostype'] ?? '';
        $otherUrl = empty($os_type) ? "/access/sso/auth" : "/access/1.0/{$os_type}/json/sso/auth";
        $str = 'http' . ($isHttps ? 's' : '') . '://' .
            self::getServerUrl($isHttps) . $otherUrl;
        return $isUrlEncode ? urlencode($str) : $str;
    }

}