<?php

/**
 * Description: 用户信息
 * User: <EMAIL>
 * Date: 2022/04/27 23:32
 * Version: $Id: UserInfoRedis.php 174874 2022-04-29 03:36:56Z duanyc $
 */

class ResultRedis extends BaseRedis
{
    /**
     * 表名
     *
     * @var string
     */
    public const PREFIX = 'ASM_';
    public const TABLE_NAME = 'Result';

    /**
     * 本地网关和控制中心共用数据的字段
     *
     * @var string
     */
    protected static $localColumns = 'AntiVirus';

    /**
     * 其他网关的字段
     *
     * @var string
     */
    protected static $remoteColumns = '';

    /**
     * 字段映射
     * @var array
     */
    protected static $columns = [
        'one' => ['AntiVirus'],
    ];

    /**
     * 字段映射
     * @var array
     */
    protected static $jsonColumns = ['AntiVirus' => true];


    /**
     * 单条
     *
     * @param $deviceID
     * @param string $column
     *
     * @return mixed
     */
    public static function getOne($deviceID, $column = 'one')
    {
        return self::get($column, $deviceID);
    }

    /**
     * 单条
     *
     * @param $deviceID
     * @param $data
     * @return mixed
     */
    public static function setOne($deviceID, $data)
    {
        return self::set($data, $deviceID);
    }
}
