<?php
/**
 * Description: guac心跳检测
 * User: <EMAIL>
 * Date: 2022/7/19 17:07
 * Version: $Id$
 */

/* 定义关键常量 */
define('PATH_CUR', __DIR__);

/** 初始化 */
require PATH_CUR . '/../init.php';


class CacheCheckUpdate
{
    /**
     * 执行
     * @throws Exception
     */
    public function handle(): void
    {
        echo 'CacheCheckUpdate start:' . PHP_EOL;
        ConfigServiceProvider::resetDictCache();
        if (ConfigServiceProvider::isNeedUpdateDictCache()) {
            ConfigServiceProvider::updateDictCache();
        }
        cutil_php_log("CacheCheckUpdate handle finish!", 'cronb');
    }
}

try {
    $obj = new CacheCheckUpdate();
    $obj->handle();
} catch (Exception $e) {
    echo "code: " . $e->getCode() . ", message: ". $e->getMessage();
}
