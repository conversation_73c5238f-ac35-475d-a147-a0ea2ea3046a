<?php
/**
 * Description: 兼容老的
 * User: <EMAIL>
 * Date: 2021/04/06 10:32
 * Version: $Id: hlp_compatible.php 173998 2022-04-21 08:34:35Z duanyc $
 */

/**
 * 兼容老系统的工具函数
 *
 * <AUTHOR>
 * @version $Id: hlp_compatible.php 173998 2022-04-21 08:34:35Z duanyc $
 */
class hlp_compatible
{
    /**
     * windows外的全部类型
     * @var array
     */
    public static $ostypes = [
        OSTYPE_LINUX,
        OSTYPE_MAC,
        OSTYPE_ANDROID,
        OSTYPE_IOS,
        OSTYPE_HARMANYOS,
    ];

    /**
     * 设备子类型
     * @var array
     */
    public static $deviceSubTypes = [
        OSTYPE_WINDOWS   => 1,
        OSTYPE_LINUX     => 2,
        OSTYPE_MAC       => 3,
        OSTYPE_ANDROID   => 4,
        OSTYPE_IOS       => 5,
        OSTYPE_HARMANYOS => 6,
    ];

    /**
     * 解析控制器
     */
    public static function parseControl()
    {
        $url = get_cururl();
        cutil_php_log($url, 'standard_warn');
        cutil_php_log(var_export(lib_request::$requests, true), 'standard_warn');
        lib_request::$method = 'POST';
        $defaultTrade = request('rewrite', 'request', '');
        $tradecode = request('tradecode', 'request', '', 'string');
        if (empty($tradecode)) {
            $tradecode = request('tradeCode', 'request', $defaultTrade, 'string');
        }
        $trademap = $GLOBALS['ROUTE']['map'][strtolower($tradecode)] ?? '';
        if (empty($trademap)) {
            cutil_php_log($url ." tradecode route config error!", 'system');
            header("location: /access/ui/");
            exit();
        }
        $GLOBALS['CT'] = $trademap['control'];
        $GLOBALS['AC'] = $trademap['action'];
    }

    /**
     * 兼容老版本返回
     *
     * @param $return
     */
    public static function parseReturn($return)
    {
        if (API_VERSION < '1.0') {
            $oldReturn = [];
            $oldReturn['code'] = ($return['errcode'] == '0') ? '200' : $return['errcode'];
            $oldReturn['errorMsg'] = $return['errmsg'];
            if (!empty($return['data'])) {
                $oldReturn = array_merge($oldReturn, $return['data']);
            }
            if (!empty($GLOBALS['HeaderContentType'])) {
                header("Content-type: {$GLOBALS['HeaderContentType']};charset=gbk", true);
            } else {
                header('Content-type: text/javascript; charset=gbk', true);
            }
            exit(json_encode($oldReturn, JSON_UNESCAPED_UNICODE));
        }
    }

    /**
     * 解析控制器
     */
    public static function parseSource()
    {
        $data_type = request('data_type', 'request', '');

        if ($data_type == 'json') {
            lib_request::$requests['source_type'] = 'oldJson';
        } else {
            lib_request::$requests['source_type'] = 'object';
        }
        $tradecode = request('tradecode', 'request', '');
        if (empty($tradecode)) {
            $tradecode = request('tradeCode', 'request', '');
        }
        if (!empty($GLOBALS['ROUTE']['json'][$tradecode]) && $data_type == 'json') {
            lib_request::$requests['source_type'] = 'ajaxJson';
        }
        if (!empty($GLOBALS['ROUTE']['msgcode'][$tradecode])) {
            lib_request::$requests['source_type'] = 'msgcode';
        }
        if (!empty($GLOBALS['ROUTE']['oldJson'][$tradecode])) {
            lib_request::$requests['source_type'] = 'oldJson';
        }
    }

    /**
     * 老方式总接口输出数据
     *
     * @param $result
     * @param $type
     * @param $e Exception|string
     */
    public static function printData($result, $type, $e = null)
    {
        if (SOURCE_TYPE == 'object') {
            self::numberToString($result);
            hlp_compatible::printObject($result, $type, $e);
        } elseif (SOURCE_TYPE == 'oldJson') {
            self::numberToString($result);
            hlp_compatible::printJson($result, $type, $e);
        } elseif (SOURCE_TYPE == 'ajaxJson') {
            self::numberToString($result);
            hlp_compatible::printAjaxJson($result, $type, $e);
        } elseif (SOURCE_TYPE == 'msgcode') {
            self::numberToString($result);
            hlp_compatible::printMsgcode($result, $type, $e);
        }
    }

    /**
     * 输出json数据
     *
     * @param $result
     * @param $type
     * @param $e Exception
     */
    public static function printObject($result, $type, $e = null)
    {
        if ($type == 'success') {
            exit(php_to_js($result));
        } else {
            exit(php_to_js($GLOBALS['CONFIG']['ALL_MESSAGE'] ?? $e->getMessage()));
        }
    }


    /**
     * 数字处理
     *
     * @param $data
     */
    public static function numberToString(&$data)
    {
        if (is_array($data)) {
            foreach ($data as $key => $val) {
                self::numberToString($data[$key]);
            }
        } elseif (!is_string($data)) {
            $data = strval($data);
        }
    }

    /**
     * 输出json数据
     *
     * @param $result
     * @param $type
     * @param $e Exception
     */
    public static function printJson($result, $type, $e = null)
    {
        if ($type == 'success') {
            if (!is_array($result)) {
                $result = [
                    'code' => '-1001',
                    'errcode' => '0',
                    'errorMsg' => $result,
                    'errorAllMsg' => $GLOBALS['CONFIG']['ALL_MESSAGE'] ?? $result,
                    'res' => $result
                ];
            } else {
                //并且增加code为200 代表返回数据成功
                //有些接口里面定义了code，所以这里判断是否有code，有，以接口返回的code为准
                if (!isset($result['code'])) {
                    $result["code"] = '-1001';
                }
                if (!isset($result['errorMsg'])) {
                    $result["errorMsg"] = $e;
                    $result["errorAllMsg"] = $GLOBALS['CONFIG']['ALL_MESSAGE'] ?? $e;
                }
                $result["errcode"] = '0';
            }
        } else {
            $result = [
                'state'    => '0',
                'errcode' => strval($e->getCode()),
                'code' => strval($e->getCode()),
                'errorMsg' => $e->getMessage(),
                'errorAllMsg' => $GLOBALS['CONFIG']['ALL_MESSAGE'] ?? $e->getMessage()
            ];
        }
        header('content-type:application/json;charset=utf-8');

        if ((!is_array($result) || !isset($result['state'])) && self::getOsType() != OSTYPE_WINDOWS) {
            $result = array(
                'state' => '1',
                'data'  => $result,
            );
        }
        exit(json_encode($result, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 兼容$AjaxResult全局变量的输出
     *
     * @param $result
     * @param $type
     * @param Exception $e
     */
    public static function printAjaxJson($result, $type, $e = null)
    {
        header('content-type:application/json;charset=utf-8');
        if ($type == 'success') {
            $return = ['status' => 'y', 'code' => '200', 'errcode' => '0', 'errorMsg' => '', 'errorAllMsg' => ''];
            if (is_array($result)) {
                $return = array_merge($return, $result);
            } else {
                $return['info'] = $result;
                $return['errorMsg'] = $e;
            }
        } else {
            $return = ['status' => 'n', 'info' => $e->getMessage(), 'code' => (string)$e->getCode(), 'errcode' => (string)$e->getCode()];
            $return['errorMsg'] = $e->getMessage();
            $return['errorAllMsg'] = $GLOBALS['CONFIG']['ALL_MESSAGE'] ?? $e->getMessage();
        }
        exit(json_encode($return, JSON_UNESCAPED_UNICODE));
    }

    /**
     * 兼容msg/code的输出
     *
     * @param $result
     * @param $type
     * @param Exception|string $e
     */
    public static function printMsgcode($result, $type, $e = null)
    {
        if ($type == 'success') {
            $result['status'] = '1';
            $result['code'] = '0';
            $result['msg'] = !empty($e) ? $e : 'success';
        } else {
            $result['status'] = '0';
            $result['code'] = '-1';
            $result['msg'] = $e->getMessage();
        }
        $data_type = request('data_type', 'request', '');
        if ($data_type == 'json') {
            header('content-type:application/json;charset=utf-8');
            if ((!is_array($result) || !isset($result['state'])) && self::getOsType() != OSTYPE_WINDOWS) {
                $result = array(
                    'state' => '1',
                    'data'  => $result,
                );
            }
            exit(json_encode($result, JSON_UNESCAPED_UNICODE));
        } else {
            exit(php_to_js($result));
        }
    }

    /**
     * csrf校验
     */
    public static function csrfCheck()
    {
        $_REQUEST = lib_request::$requests;
        $_POST = lib_request::$posts;
        $_GET = lib_request::$gets;
        require_once PATH_HTML . "/phpdir/include/csrfControll.php";
        $api_ver = $_SERVER['HTTP_API_VERSION'] ?? '';
        $cc = new csrfControll($api_ver);
        if (!$cc->verifyCsrf()) {
            $cc->write_error_log();
            $sapi_type = php_sapi_name();
            if (substr($sapi_type, 0, 3) == 'cgi') {
                header('Status: 401 Unauthorized', true, 401);
            } else {
                header('HTTP/1.1 401 Unauthorized', true, 401);
            }
            exit('100 Access Denied!');
        }
        unset($_REQUEST);
        unset($_POST);
        unset($_GET);
    }

    /**
     * 获取header头的拓展字段
     *
     * @param $column
     *
     * @return mixed|string
     */
    public static function getExtendData($column)
    {
        static $return = [];
        if (!empty($return)) {
            return $return[$column] ?? '';
        }
        $ExtendData = $_SERVER['HTTP_EXTEND_DATA'] ?? '';
        $data = explode(';', $ExtendData);
        foreach ($data as $col) {
            if (!empty($col)) {
                $tmp = explode('=', trim($col));
                if (!empty($tmp[1])) {
                    $return[trim($tmp[0])] = trim($tmp[1]);
                }
            }
        }
        return $return[$column] ?? '';
    }

    /**
     * 获取设备类型
     * @return string
     */
    public static function getOsType()
    {
        $ostype = self::getExtendData('ostype');
        if (!in_array($ostype, self::$ostypes)) {
            $ostype = OSTYPE_WINDOWS;
        }
        return $ostype;
    }

    /**
     * 获取是否客户端调用，1表示是，0表示否
     * @return string
     */
    public static function getIsClient()
    {
        // 移动端嵌入h5页面
        $isclient = self::getExtendData('isclient');
        if (!empty($isclient)) {
            return STRING_TRUE;
        }
        // PC端QT页面
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        $urlData = parse_url($referer);
        $data = explode('/', trim($urlData['path'], '/'));
        if (count($data) >= 2 && $data[1] == 'asm' && isset(self::$deviceSubTypes[$data[0]])) {
            return STRING_TRUE;
        }
        return STRING_FALSE;
    }

    /**
     * 解析控制器
     */
    public static function parseLang()
    {
        // 如果控制器是sso，则多语言需要额外处理
        if ($GLOBALS['CT'] === 'sso') {
            $state = request('state', 'request', '');
            if (!empty($state)) {
                try {
                    $state = Base64DeExt($state);
                    $state = json_decode($state, true, 10, JSON_THROW_ON_ERROR);
                    if (isset($state['local_lguage_set']) && !empty($state['local_lguage_set'])) {
                        return $state['local_lguage_set'];
                    }
                } catch (Exception $e) {
                }
            }
        }
        return request('local_lguage_set', 'request', 'zh');
    }
}
